#!/bin/bash

# Script to create feature branches with the specified workflow
# Usage: ./scripts/create-feature-branch.sh <branch_name>
# Example: ./scripts/create-feature-branch.sh voice_talkback

set -e  # Exit on any error

# Check if branch name is provided
if [ $# -eq 0 ]; then
    echo "Error: Branch name is required"
    echo "Usage: $0 <branch_name>"
    echo "Example: $0 voice_talkback"
    exit 1
fi

BRANCH_NAME=$1
FEATURE_BRANCH="fb_tlx_${BRANCH_NAME}"
DEV_BRANCH="fb_tlx_${BRANCH_NAME}_dev"
FINAL_BRANCH="fb_tlx_${BRANCH_NAME}_final"

echo "Creating feature branch workflow for: $BRANCH_NAME"
echo "Feature branch: $FEATURE_BRANCH"
echo "Dev branch: $DEV_BRANCH"
echo "Final branch: $FINAL_BRANCH"
echo ""

# Validate branch name format
if [[ ! $BRANCH_NAME =~ ^[a-zA-Z0-9_-]+$ ]]; then
    echo "Error: Branch name can only contain letters, numbers, hyphens, and underscores"
    exit 1
fi

# Check if branches already exist
echo "1. Validating branch names..."
if git show-ref --verify --quiet refs/heads/"$FEATURE_BRANCH"; then
    echo "Error: Local branch '$FEATURE_BRANCH' already exists"
    exit 1
fi

if git show-ref --verify --quiet refs/remotes/origin/"$DEV_BRANCH"; then
    echo "Error: Remote branch 'origin/$DEV_BRANCH' already exists"
    exit 1
fi

if git show-ref --verify --quiet refs/remotes/origin/"$FINAL_BRANCH"; then
    echo "Error: Remote branch 'origin/$FINAL_BRANCH' already exists"
    exit 1
fi

echo "✅ Branch names are available"

# Show confirmation with branch details
echo ""
echo "2. Confirming branch creation..."
echo "The following branches will be created:"
echo "  📍 Local branch:     $FEATURE_BRANCH"
echo "  🚀 Remote dev:       origin/$DEV_BRANCH"
echo "  🎯 Remote final:     origin/$FINAL_BRANCH"
echo ""
echo "Upstream will be set to: origin/$DEV_BRANCH"
echo ""
read -p "Proceed with branch creation? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Branch creation cancelled."
    exit 0
fi

# Ensure we're on a clean state
echo "3. Checking current Git status..."
if [ -n "$(git status --porcelain)" ]; then
    echo "Error: You have uncommitted changes. Please commit or stash them first."
    echo "Current status:"
    git status --short
    echo ""
    echo "To stash changes: git stash"
    echo "To commit changes: git add . && git commit -m 'your message'"
    exit 1
fi

echo "✅ Working directory is clean"

# Fetch latest changes
echo "4. Fetching latest changes from origin..."
git fetch origin

# Create feature branch from origin/release (without checking out)
echo "5. Creating feature branch '$FEATURE_BRANCH' from origin/release..."
git branch "$FEATURE_BRANCH" origin/release

# Push to dev branch
echo "6. Pushing to dev branch '$DEV_BRANCH'..."
git push origin "$FEATURE_BRANCH:$DEV_BRANCH"

# Push to final branch
echo "7. Pushing to final branch '$FINAL_BRANCH'..."
git push origin "$FEATURE_BRANCH:$FINAL_BRANCH"

# Set upstream to dev branch
echo "8. Setting upstream branch to '$DEV_BRANCH'..."
git branch --set-upstream-to="origin/$DEV_BRANCH" "$FEATURE_BRANCH"

# Checkout the feature branch at the end
echo "9. Checking out feature branch '$FEATURE_BRANCH'..."
git checkout "$FEATURE_BRANCH"

echo ""
echo "✅ Feature branch workflow completed successfully!"
echo ""
echo "Summary:"
echo "  - Created branch: $FEATURE_BRANCH (from origin/release)"
echo "  - Pushed to: origin/$DEV_BRANCH"
echo "  - Pushed to: origin/$FINAL_BRANCH"
echo "  - Set upstream to: origin/$DEV_BRANCH"
echo ""
echo "You are now on branch '$FEATURE_BRANCH' with upstream set to '$DEV_BRANCH'" 