module.exports = {
  root: true,
  extends: [
    'prettier',
    'plugin:diff/diff',
    'eslint:recommended',
    'plugin:import/typescript',
    'plugin:import/recommended',
    'plugin:promise/recommended',
    'plugin:@typescript-eslint/recommended',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    tsconfigRootDir: __dirname,
    project: './tsconfig.json',
    requireConfigFile: false,
  },
  plugins: ['@typescript-eslint'],
  rules: {
    // custom rules
    yoda: 'error',
    'no-unused-vars': 'warn',
    'no-unreachable': 'error',
    'no-cond-assign': 'error',
    'no-dupe-else-if': 'error',
    'no-confusing-arrow': 'warn',
    'no-duplicate-imports': 'error',
    'no-unused-private-class-members': 'off',
    'no-use-before-define': ['warn', { variables: false }],
    // typescript-eslint
    '@typescript-eslint/no-var-requires': 'off',
    '@typescript-eslint/no-floating-promises': 'off',
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    'promise/catch-or-return': ['error', { allowFinally: true, allowThen: true }],
  },
  globals: {},
  settings: {
    'import/resolver': {
      node: {
        extensions: ['.js', '.ts'],
      },
    },
  },
};
