This is a very initial setup for travelplex-ui and Mobile-mmt-react-native. Docs will be updated soon.

Setup inside Mobile-mmt-react-native :

1. Checkout **integration_main** branch of Mobile-mmt-react-native
2. Run command: **yarn android-dev**
3. Open debugger menu, set bundle location to **localhost:8086**
4. Inside MMT app, use Where2Go as the entry point for now (till the time chatbot entry point gets added in other LoBs)
5. Note: If you're not seeing the entry point, it is due to the A/B experiment configured in Where2Go.
6. TODO: add steps to whitelist uuids in Where2Go A/B experiment

Setup for travelplex-ui :
<br>For Android : </br>

1. Set env in your .zprofile or .bashrc with the following:
   **export MMT_RN_DIR=Add your MMT-RN-Repo Path**
2. Reload the terminal and check whether env variable is properly set or not via : **echo $MMT_RN_DIR**
3. Checkout **release** branch of travelplex-ui-repo
4. At the root of the project, run the following command: **yarn rn-ui-dev**

Make changes inside travelplex-ui, HMR will work fine.
