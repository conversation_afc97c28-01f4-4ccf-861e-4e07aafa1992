{"name": "@travelplex/root", "version": "0.0.1", "private": true, "scripts": {"rn-ui-dev": "yarn workspace @travelplex/react-native run watch:mmt", "rn-ui-build": "yarn workspace @travelplex/react-native run build", "rn-ui-publish": "yarn workspace @travelplex/react-native run publish", "rn-ui-test": "yarn workspace @travelplex/react-native run test", "floating-icon-web-deploy": "yarn floating-icon-web-build && yarn floating-icon-web-publish", "floating-icon-web-build": "yarn workspace @travelplex/floating-icon-web run build", "floating-icon-web-publish": "yarn workspace @travelplex/floating-icon-web run publish", "floating-icon-web-pwa-deploy": "yarn floating-icon-web-pwa-build && yarn floating-icon-web-pwa-publish", "floating-icon-web-pwa-build": "yarn workspace @travelplex/floating-icon-web-pwa run build", "floating-icon-web-pwa-publish": "yarn workspace @travelplex/floating-icon-web-pwa run publish", "verify": "yarn --frozen-lockfile && yarn rn-ui-test", "nexus_deploy": "yarn --frozen-lockfile && yarn rn-ui-build && yarn rn-ui-publish; yarn floating-icon-web-deploy; yarn floating-icon-web-pwa-deploy", "build:web": "yarn workspace @ptui/web run build", "build:docker": "yarn --frozen-lockfile && yarn build:web", "format": "prettier --write \"**/*.{ts,tsx,js,md}\"", "test": "eslint .", "package": "yarn --frozen-lockfile &&yarn workspace @ptui/web run package", "branch": "./scripts/create-feature-branch.sh"}, "workspaces": ["apps/*", "packages/*"], "devDependencies": {"@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "eslint": "8.57.1", "eslint-plugin-diff": "2.0.3", "eslint-plugin-import": "2.31.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-promise": "6.0.1", "prettier": "3.3.3", "react-query": "^3.19.0", "typescript": "5.1.3"}, "engines": {"node": ">=18"}, "packageManager": "yarn@1.22.19", "dependencies": {"react-native-fast-shadow": "^0.1.1", "react-native-image-colors": "^2.4.0", "react-native-slider": "^0.11.0", "react-native-video": "^6.15.0"}}