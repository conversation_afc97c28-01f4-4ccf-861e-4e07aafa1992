import { useState, useEffect } from "react";

import type { Persuasion } from "./props";

const usePersuasionText = (
  persuasionDelay: number,
  persuasions: Persuasion[]
) => {
  console.log("persuasionDelay", persuasionDelay);
  const [currentText, setCurrentText] = useState(0);
  useEffect(() => {
    if (!Array.isArray(persuasions) || persuasions.length <= 0) {
      return;
    }
    const persuasionTimer = setInterval(() => {
      setCurrentText((prev) => {
        if (prev >= persuasions.length - 1) {
          clearInterval(persuasionTimer); // Stop the timer
          return 0; // Reset to the first index
        }
        return prev + 1; // Move to the next index
      });
    }, persuasionDelay || 2800);
    return () => clearInterval(persuasionTimer);
  }, [persuasionDelay, persuasions]);
  return currentText;
};
export default usePersuasionText;
