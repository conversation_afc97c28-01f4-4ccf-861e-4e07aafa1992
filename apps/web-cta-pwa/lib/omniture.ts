declare global {
  interface Window {
    s_gi?: (arg: string) => any;
  }
}

export class OmnitureService {
  eventsQueue: Array<() => void> = [];
  isOmnitureLoading = false;
  isOmnitureLoaded = false;
  omniture: any = null;
  linkTrackVars = '';
  omnitureJSFile = 'https://jsak.mmtcdn.com/pwa/assets/js/libs/pwatracker.js';
  canSendMyraIconOmniture = true;
  // private methods
  /**
   * @function
   * @description fires all the events in the queue
   * (All events are stored in the queue until omniture is loaded)
   */
  fireEventsInQueue = () => {
    this.eventsQueue.forEach((cb) => cb());
    this.eventsQueue = [];
  };
  /**
   * @function
   * @description Function to set variables for omniture events
   *
   */
  setVars = (params: any) => {
    if (!this.isOmnitureLoaded || !this.omniture) {
      return;
    }
    Object.entries(params).forEach(([key, value]) => {
      this.omniture[key] = value;
    });
  };
  /**
   * @function
   * @description - initializes omniture from the window object
   */
  initializeOmniture = () => {
    this.omniture = getOmniture();
  };

  /**
   * @function
   * @description - Loads the omniture js files dynamically
   */
  loadOmniture = () => {
    if (this.isOmnitureLoading) return;
    if (
      typeof window !== 'undefined' &&
      window.s_gi &&
      typeof window.s_gi === 'function'
    ) {
      this.isOmnitureLoaded = true;
      this.isOmnitureLoading = false;
      this.fireEventsInQueue();
      return;
    }
    this.isOmnitureLoading = true;
    loadJS(
      this.omnitureJSFile,
      () => {
        this.isOmnitureLoading = false;
        this.isOmnitureLoaded = true;
        this.fireEventsInQueue();
      },
      () => {return;},
    );
  };

  // public methods
  /**
   * @function
   * @description - Function to initialize omniture service
   */
  init = () => {
    if (typeof window === 'undefined') return;
    this.loadOmniture();
  };

  /**
   * @function
   * @description - Function to track load event
   * @param params
   */
  trackLoadEvent = (params = {}) => {
    if (!this.canSendMyraIconOmniture) {
      return;
    }
    if (Object.keys(params).length === 0) {
      return;
    }
    if (!this.isOmnitureLoaded) {
      this.eventsQueue.push(() => this.trackLoadEvent(params));
      return;
    }
    if (!this.omniture) {
      this.initializeOmniture();
      if (!this.omniture) return;
    }
    this.omniture.linkTrackVars = this.linkTrackVars;
    this.setVars?.(params);
    const s_code = typeof this.omniture.t === 'function' && this.omniture.t();
    if (s_code) {
      document.write(s_code);
    }
    this.omniture?.clearVars();
  };

  /**
   * @function
   * @description - Function to track click event
   * @param params
   */
  trackClickEvent = (params = {}) => {
    if (!this.canSendMyraIconOmniture) {
      return;
    }
    if (Object.keys(params).length === 0) {
      return;
    }
    if (!this.isOmnitureLoaded) {
      this.eventsQueue.push(() => this.trackClickEvent(params));
      return;
    }
    if (!this.omniture) {
      this.initializeOmniture();
      if (!this.omniture) return;
    }
    this.omniture.linkTrackVars = this.linkTrackVars;
    this.setVars?.(params);
    typeof this.omniture.tl === 'function' && this.omniture.tl(this, 'o', params);
  };

  /**
   * @function
   * @description - Function to set if omniture events can be sent from package end
   * @param params
   */
  setCanSendMyraIconOmniture = (value: boolean) => {
    this.canSendMyraIconOmniture = value;
  };
}

/**
 * @function
 * @description - Load JS file
 *
 * @param {string} fileURL - URL of the file to be loaded
 * @param {function} onLoad - Callback function to be called on load
 * @param {function} onError - Callback function to be called on error
 */
export const loadJS = (fileURL: string, onLoad: () => void, onError: () => void) => {
  const scriptEle = document.createElement('script');
  scriptEle.setAttribute('src', fileURL);
  document.body.appendChild(scriptEle);

  if (onLoad) scriptEle.addEventListener('load', onLoad);
  if (onError) scriptEle.addEventListener('error', onError);
};

/**
 * @function
 * @description - Get Omniture object
 *
 * @returns {object} - Omniture object
 */
export const getOmniture = () => {
  let s = {};
  if (
    typeof window !== 'undefined' &&
    window.s_gi &&
    typeof window.s_gi === 'function'
  ) {
    s = window.s_gi('mmtprod');
  }
  return s;
};

export const omnitureService = new OmnitureService();
omnitureService.init();
