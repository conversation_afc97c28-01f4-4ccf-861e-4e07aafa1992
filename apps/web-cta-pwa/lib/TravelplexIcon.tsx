import React, { useCallback, useEffect, useState, useRef } from "react";
import {
  getItemBySession,
  setItemBySession,
} from "MMT-UI/storages/client/sessionStorage";
import type { TravelplexIconProps } from "./props";

import TravelplexFrame from "./TravelplexFrame";
import "./style.css";
import { omnitureService } from "./omniture";
import RightPersuasion from "./Persuasions/RightPersuasion";
import { config } from "./config";
import { TP_ANIMATION_DONE_KEY } from "./constants";

const TravelplexIcon: React.FC<TravelplexIconProps> = ({
  myraAppLink,
  onBotLOBLinkClickedEvent,
  onBotLoginEvent,
  onBotTriggerAnalyticsEvent,
  onTravelplexIconClicked,
  onBotGetMetaDataEvent,
  shouldShowBotByDefault,
  persuasions = [],
  onBotEvent,
  onBotLinkChatEvent,
  frameStyles = {},
  shouldShowOverlay,
  lob,
  brand,
  onBotJSLoadedEvent,
  onBotFrameShownEvent,
}) => {
  const animationKey = `${TP_ANIMATION_DONE_KEY}_${lob}`;
  const [showMyraFrame, setShowMyraFrame] = useState(false);
  const [myraMinimized, setMyraMinimized] = useState(true);
  const [showNewMessageIcon, setShowNewMessageIcon] = useState(false);
  const myraAnimationDoneForSession = getItemBySession(animationKey) === "true";
  const myraIconContainerRef = useRef<HTMLDivElement>(null);
  const persuasionRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (!shouldShowOverlay) {
      document.body.classList.remove("overflowHidden");
      return;
    }
    if (showMyraFrame) {
      document.body.classList.add("overflowHidden");
    }
    if (myraMinimized) {
      document.body.classList.remove("overflowHidden");
    }
  }, [showMyraFrame, myraMinimized]);

  const decreaseMyraContainerWidthAnimation = () => {
    const timer = setTimeout(() => {
      if (persuasions?.length > 0) {
        myraIconContainerRef?.current?.classList.add("animateDecrease");
      }
    }, persuasions?.length * config.persuasionDelay + 150);
    const persuasionTimer = setTimeout(() => {
      if (persuasionRef?.current) {
        persuasionRef.current.style.display = "none";
      }
    }, persuasions?.length * config.persuasionDelay + 2500);
    return [timer, persuasionTimer];
  };

  useEffect(() => {
    if (myraAnimationDoneForSession) {
      return;
    }
    setItemBySession(animationKey, true);
    myraIconContainerRef?.current?.classList.add("animateIncrease");
    let decreaseTimer: ReturnType<typeof setTimeout>;
    let persuasionTimer: ReturnType<typeof setTimeout>;
    const timer = setTimeout(() => {
      [decreaseTimer, persuasionTimer] = decreaseMyraContainerWidthAnimation();
    }, 1500);
    return () => {
      clearTimeout(timer);
      clearTimeout(decreaseTimer);
      clearTimeout(persuasionTimer);
    };
  }, []);
  useEffect(() => {
    if (shouldShowBotByDefault) {
      setMyraMinimized(!shouldShowBotByDefault);
      setShowMyraFrame(shouldShowBotByDefault);
    }
  }, []);

  const handleMyraIconClick = useCallback(() => {
    omnitureService.trackClickEvent({
      prop67: "myra_tp_click",
      prop54: "trvlplex_clicked",
    });
    onTravelplexIconClicked();
    setShowNewMessageIcon(false);
    if (!showMyraFrame) {
      setShowMyraFrame(true);
    }
    setMyraMinimized(!myraMinimized);
  }, [showMyraFrame, myraMinimized, onTravelplexIconClicked]);
  const iconStyle = {
    backgroundImage: `url(${
      config.myraIconMap[brand || "mmt_b2c"] || config.myraIconMap?.mmt_b2c
    })`,
  };

  const onCloseMyraFrame = () => {
    setShowMyraFrame(false);
    setMyraMinimized(true);
  };

  const canShowPersuasion =
    persuasions && !!persuasions?.length && !myraAnimationDoneForSession;
  console.log("canShowPersuasion", canShowPersuasion);
  return (
    <>
      <TravelplexFrame
        myraAppLink={myraAppLink}
        closeMyraFrame={onCloseMyraFrame}
        myraMinimized={myraMinimized}
        setMyraMinimized={setMyraMinimized}
        onBotLOBLinkClickedEvent={onBotLOBLinkClickedEvent}
        onBotLoginEvent={onBotLoginEvent}
        onBotTriggerAnalyticsEvent={onBotTriggerAnalyticsEvent}
        onBotGetMetaDataEvent={onBotGetMetaDataEvent}
        showMyraFrame={showMyraFrame}
        onBotEvent={onBotEvent}
        onBotLinkChatEvent={onBotLinkChatEvent}
        frameStyles={frameStyles}
        canPreWarmMyraFrame={config.canPreWarmMyraFrame}
        shouldShowOverlay={shouldShowOverlay}
        onBotJSLoadedEvent={onBotJSLoadedEvent}
        onBotFrameShownEvent={onBotFrameShownEvent}
      />
      <div
        className="tp-icon-wrapper"
        ref={myraIconContainerRef}
        onClick={handleMyraIconClick}
      >
        <div className="tp-icon-container" ref={myraIconContainerRef}>
          {showNewMessageIcon && <div className="tp-new-message" />}
          <div style={iconStyle} className="tp-icon"></div>
        </div>
        {canShowPersuasion ? (
          <div className="tp-persuasion" ref={persuasionRef}>
            <div className="tp-persuasion-left-element" />
            <RightPersuasion persuasions={persuasions} lob={lob} />
          </div>
        ) : null}
      </div>
    </>
  );
};

export default React.memo(TravelplexIcon);
