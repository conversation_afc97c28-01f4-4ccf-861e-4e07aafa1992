import React from "react";
import type { Persuasion } from "../props";
const PersuasionTextItem = React.memo(
  ({
    persuasionItem,
    index,
    currentText,
    persuasuionTextStyle,
  }: {
    persuasionItem: Persuasion;
    index: number;
    currentText: number;
    persuasuionTextStyle?: { fontSize?: number; fontWeight?: number };
  }) => {
    const className = `persuasionText-pwa ${
      index === currentText ? "tp-activeIn-pwa" : ""
    }`;

    if (!persuasionItem) {
      return null;
    }
    return (
      <p
        key={`persuasionitem_${index + 1}`}
        className={className}
        style={{ ...persuasuionTextStyle }}
      >
        {persuasionItem}
      </p>
    );
  }
);
export const PersuasionText = ({
  persuasions,
  currentText,
  canDoTextAnimation,
  persuasuionTextStyle,
}: {
  persuasions: Persuasion[];
  currentText: number;
  canDoTextAnimation: boolean;
  persuasuionTextStyle?: { fontSize?: number; fontWeight?: number };
}) => {
  if (!persuasions?.length) {
    return null;
  }
  let persuasionsToRender = persuasions;
  if (!canDoTextAnimation) {
    persuasionsToRender = persuasionsToRender.slice(0, 1);
  }
  if (persuasionsToRender?.length === 1) {
    if (!persuasionsToRender[0]) {
      return null;
    }
    return (
      <p
        className="persuasionText-pwa"
        style={{ display: "flex", ...persuasuionTextStyle }}
      >
        {persuasionsToRender[0]}
      </p>
    );
  }
  return (
    <>
      {persuasionsToRender.map((persuasionItem, index) => {
        return (
          <PersuasionTextItem
            key={`persuasionitem_${index + 1}`}
            persuasionItem={persuasionItem}
            index={index}
            currentText={currentText}
            persuasuionTextStyle={persuasuionTextStyle}
          />
        );
      })}
    </>
  );
};
