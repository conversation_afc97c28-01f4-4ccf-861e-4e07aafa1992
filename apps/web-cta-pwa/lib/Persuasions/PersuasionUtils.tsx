import React from "react";
import type { Persuasion } from "../props";
const renderPersuasionTextItem = (
  persuasionItem: Persuasion,
  index: number,
  currentText: number
) => {
  const className = `persuasionText ${index === currentText ? "activeIn" : ""}`;

  if (!persuasionItem) {
    return null;
  }
  return (
    <p key={`persuasionitem_${index + 1}`} className={className}>
      {persuasionItem}
    </p>
  );
};
export const renderPersuasionText = (
  persuasions: Persuasion[],
  currentText: number,
  canDoTextAnimation: boolean
) => {
  if (!persuasions?.length) {
    return null;
  }
  let persuasionsToRender = persuasions;
  if (!canDoTextAnimation) {
    persuasionsToRender = persuasionsToRender.slice(0, 1);
  }
  if (persuasionsToRender?.length === 1) {
    if (!persuasionsToRender[0]) {
      return null;
    }
    return (
      <p className="persuasionText" style={{ display: "flex" }}>
        {persuasionsToRender[0]}
      </p>
    );
  }
  return persuasionsToRender.map(
    (persuasionItem, index) => {
      return renderPersuasionTextItem(persuasionItem, index, currentText);
    }
    // renderPersuasionTextItem(persuasionItem, index, currentText)
  );
};
