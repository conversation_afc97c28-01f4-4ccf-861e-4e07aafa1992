import React from "react";
import { validatePersuasionsData } from "../utils";
import type { Persuasion } from "../props";
import useMyraPersuasionText from "../usePersuasionText";
import { renderPersuasionText } from "./PersuasionUtils";
import usePersuasionAnimation from "./usePersuasionAnimation";
import { config } from "../config";

const RightPersuasion = ({
  persuasions,
  lob,
}: {
  persuasions: Persuasion[];
  lob: string;
}) => {
  const currentText = useMyraPersuasionText(
    config.persuasionDelay,
    persuasions,
    lob
  );
  const [canDoTextAnimation] = usePersuasionAnimation(persuasions, lob);
  if (!validatePersuasionsData(persuasions)) {
    return null;
  }
  const persuasionsToRender = persuasions;
  return (
    <div className="rightPersuasionTextWrapper">
      {renderPersuasionText(
        persuasionsToRender,
        currentText,
        canDoTextAnimation
      )}
    </div>
  );
};

export default RightPersuasion;
