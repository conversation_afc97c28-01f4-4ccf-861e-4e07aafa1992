import React from "react";
import { validatePersuasionsData } from "../utils";
import type { Persuasion } from "../props";
import useMyraPersuasionText from "../usePersuasionText";
import { PersuasionText } from "./PersuasionUtils";
import { config } from "../config";

const RightPersuasion = React.memo(
  ({
    persuasions,
    persuasuionTextStyle,
  }: {
    persuasions: Persuasion[];
    persuasuionTextStyle?: { fontSize?: number; fontWeight?: number };
  }) => {
    const currentText = useMyraPersuasionText(
      config.persuasionDelay,
      persuasions
    );
    const canDoTextAnimation = true;
    if (!validatePersuasionsData(persuasions)) {
      return null;
    }
    const persuasionsToRender = persuasions;
    const height = !isNaN(persuasuionTextStyle?.fontSize || 0)
      ? (persuasuionTextStyle?.fontSize || 0) + 3
      : 17;
    const persuasionWrapperStyle = persuasuionTextStyle?.fontSize
      ? { height }
      : {};
    return (
      <div
        className="rightPersuasionTextWrapper-pwa"
        style={persuasionWrapperStyle}
      >
        <PersuasionText
          persuasions={persuasionsToRender}
          currentText={currentText}
          canDoTextAnimation={canDoTextAnimation}
          persuasuionTextStyle={persuasuionTextStyle}
        />
      </div>
    );
  }
);

export default RightPersuasion;
