/* My new css start */
.myraFloatingBar {
  position: fixed;
  right: 32px;
  bottom: 45px;
  z-index: 12;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-end;
  cursor: pointer;
}

.tp-icon-wrapper {
  position: relative;
  max-width: 250px;
  height: 68px;
  display: flex;
  align-items: center;
}

.tp-icon-container {
  min-width: 60px;
  height: 60px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.4);
  z-index: 1;
}
.animateIncrease {
  width: 250px;
  right: 10%;
  animation: animateIncreaseWidth 2s ease;
}
@keyframes animateIncreaseWidth {
  0% {
    width: 50px;
  }
  100% {
    width: 250px;
    right: 10%;
  }
}
.animateDecrease {
  width: 62px;
  right: 0%;
  animation: animateDecreaseWidth 2s ease;
}
@keyframes animateDecreaseWidth {
  0% {
    width: 250px;
    right: 10%;
  }
  100% {
    width: 62px;
    right: 0%;
  }
}

.tp-icon {
  width: 52px;
  height: 52px;
  background-size: contain;
  background-repeat: no-repeat;
}
.tp-new-message {
  position: absolute;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background-color: var(--new-message-icon-color, red);
  top: 2px;
  left: 5px;
  z-index: 1;
}
.tp-persuasion {
  width: calc(100% - 62px);
  position: relative;
  left: -12px;
  height: 50px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top-right-radius: 25px;
  border-bottom-right-radius: 25px;
  background: #fff;
  color: #355ff2;
  font-size: 14px;
  font-weight: 700;
  line-height: normal;
  box-shadow: 0 0 10px 0 #fff inset, 0 0 24px 0 rgba(0, 59, 140, 0.2);
}
.rightPersuasionTextWrapper {
  width: 100%;
  align-items: center;
  padding-left: 10px;
  overflow: hidden;
  max-height: 29px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
  height: 17px;
  padding-left: 2px;
  padding-right: 2px;
}

.persuasionText {
  max-height: 100%;
  display: none;
  width: 100%;
  align-items: flex-start;
  justify-content: center;
}

.persuasionText.activeIn {
  display: block;
  animation: textIn 3s ease;
}

@keyframes textIn {
  0% {
    transform: translateY(40px);
  }

  20%,
  80% {
    transform: translateY(0%);
  }

  100% {
    transform: translateY(-50px);
  }
}
.tp-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10;
}

.overflowHidden {
  overflow: hidden;
}
.tp-loader-container-wrapper {
  background-color: rgba(0, 0, 0, 0.5);
  width: 100vw;
  height: 100vh;
  position: fixed;
  bottom: 0;
  right: 0px;
  z-index: 999;
  display: flex;
  align-items: flex-end;
}
.tp-loader-container {
  height: 85vh;
  width: 100vw;
  position: relative;
  bottom: 0;
  border-top-left-radius: 25px;
  border-top-right-radius: 25px;
  background-color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tp-loader {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.tp-persuasion-left-element {
  width: 12px;
  height: 100%;
  background-color: white;
}

.tp-frame {
  position: fixed;
  bottom: -100%;
  right: 0px;
  z-index: 13;
  border: none;
  box-shadow: none;
  visibility: hidden;
  opacity: 0;
  height: 92%;
  width: 400px;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.1);
  background-color: transparent;
}

.tp-frame.myra-frame-transition {
  transition: 0.5s ease-in-out bottom 0.02s;
}

.tp-frame.showIframe {
  bottom: 0%;
  visibility: visible;
  opacity: 1;
}

.tp-close-icon {
  position: absolute;
  top: -29px;
  left: 92%;
  width: 25px;
  height: 25px;
  background-size: cover;
  background-repeat: no-repeat;
  z-index: 1;
}

/* My new css end */
