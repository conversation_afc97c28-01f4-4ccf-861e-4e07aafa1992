import React, { useEffect, useMemo } from "react";
import TravelplexIcon from "./TravelplexIcon";
import type { BrandType, TravelplexBotIconProps } from "./props";
import { omnitureService } from "./omniture";
import { getUpdatedBotLink } from "./utils";
import { config } from "./config";

import "./style.css";

export type { TravelplexBotIconProps };

const iFrameStyles = {
  height: "100vh",
  width: "100vw",
  boxShadow: "0 1px 4px 0 rgba(0, 0, 0, 0.1)",
};

const TravelplexBotIconWrapper: React.FC<TravelplexBotIconProps> = (props) => {
  const {
    platform,
    params = {},
    trackingData,
    lob,
    onBotEvent = () => {
      return;
    },
    onBotLoginEvent = () => {
      return;
    },
    onBotLOBLinkClickedEvent = () => {
      return;
    },
    onBotLinkChatEvent = () => {
      return;
    },
    onBotGetMetaDataEvent = () => {
      return;
    },
    onBotTriggerAnalyticsEvent = () => {
      return;
    },
    frameStyles = iFrameStyles,
    botStyles = {},
    shouldShowOverlay = true,
    persuasions = [],
    shouldShowBotByDefault = false,
    onTravelplexIconClicked = () => {
      return;
    },
    onBotFrameShownEvent = () => {
      return;
    },
  } = props;
  const botLink = config.botLink;
  omnitureService.setCanSendMyraIconOmniture(config.canSendMyraIconOmniture);
  useEffect(() => {
    let trackingDataFromParent;
    if (trackingData?.omnitureTrackingData) {
      trackingDataFromParent = trackingData?.omnitureTrackingData;
    }
    omnitureService.trackLoadEvent({
      prop67: "myra_tp_seen",
      prop54: "trvlplex_displayed",
      ...(trackingDataFromParent && { trackingDataFromParent }),
    });
  }, [trackingData?.omnitureTrackingData]);

  const myraLink = useMemo(
    () => getUpdatedBotLink(botLink, params, platform),
    [botLink, params, platform]
  );
  if (!myraLink) {
    return null;
  }
  if (!lob || !platform) {
    return null;
  }
  return (
    <div className="myraFloatingBar mmt_b2c" style={botStyles}>
      <TravelplexIcon
        myraAppLink={myraLink}
        onBotLoginEvent={onBotLoginEvent}
        onBotTriggerAnalyticsEvent={onBotTriggerAnalyticsEvent}
        onTravelplexIconClicked={onTravelplexIconClicked}
        onBotGetMetaDataEvent={onBotGetMetaDataEvent}
        onBotLOBLinkClickedEvent={onBotLOBLinkClickedEvent}
        shouldShowBotByDefault={shouldShowBotByDefault}
        persuasions={persuasions}
        frameStyles={frameStyles}
        onBotEvent={onBotEvent}
        onBotLinkChatEvent={onBotLinkChatEvent}
        canPreWarmMyraFrame={config.canPreWarmMyraFrame}
        shouldShowOverlay={shouldShowOverlay}
        lob={lob}
        brand={config.brand as BrandType}
        onBotFrameShownEvent={onBotFrameShownEvent}
      />
    </div>
  );
};

export default React.memo(TravelplexBotIconWrapper);
