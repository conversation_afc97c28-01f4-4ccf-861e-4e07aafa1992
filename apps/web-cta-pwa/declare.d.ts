declare module 'MMT-UI/storages/client/sessionStorage' {
  export function setItemBySession(...args: unknown[]): unknown;
  export function getItemBySession(...args: unknown[]): unknown;

  // Declaration of itemType
  export enum itemType {
    ES = 'ES',
    N_ES = 'N_ES',
  }
}

declare module '*.png' {
  const value: string;
  export default value;
}

declare module '*.jpg' {
  const value: string;
  export default value;
}

declare module '*.jpeg' {
  const value: string;
  export default value;
}

declare module '*.gif' {
  const value: string;
  export default value;
}

declare module '*.svg' {
  const value: string;
  export default value;
}

declare module '*.webp' {
  const value: string;
  export default value;
}
