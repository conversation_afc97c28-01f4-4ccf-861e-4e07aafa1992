import React from 'react';
import {
  Animated,
  TouchableWithoutFeedback,
  Dimensions,
  StyleSheet,
  ScrollView,
  TextInput,
  Pressable,
  View,
  TouchableOpacity as TouchableOpacityRN,
} from 'react-native';
import { useResponsiveWidth } from '../../chatbot-ui/src/hooks/useResponsiveWidth';

export const BottomSheetTextInput = (props) => {
  const { style } = props;
  let styleToUse = style;
  if (style) {
    styleToUse = [style, { outlineStyle: 'none' }];
  } else {
    styleToUse = { outlineStyle: 'none' };
  }
  return <TextInput {...props} style={styleToUse} />;
};
export const TouchableOpacity = TouchableOpacityRN;
export const BottomSheetScrollView = (props) => (
  <ScrollView {...props} nativeID="bottom-sheet-scrollview" />
);
export const BottomSheetFooter = ({ children, style }) => (
  <View style={[style]}>{children}</View>
);
export const BottomSheetModalProvider = ({ children }) => <>{children}</>;

export const BottomSheetView = ({ children, style }) => (
  <View style={[styles.contentContainer, style]}>{children}</View>
);

const { height } = Dimensions.get('window');

export const BottomSheetModal = React.forwardRef(
  ({
    backdropComponent: BackdropComponent,
    style,
    handleComponent: HandleComponent,
    footerComponent: FooterComponent,
    onChange,
    children,
  }) => {
    const { containerWidth } = useResponsiveWidth();

    // Check if platform URL param is 'desktop'
    const isPlatformDesktop =
      new URLSearchParams(window.location.search).get('platform') === 'desktop';

    const shouldUseDesktopMode = isPlatformDesktop;

    return (
      <TouchableWithoutFeedback>
        <Pressable
          style={[
            styles.overlay,
            {
              backgroundColor: shouldUseDesktopMode
                ? 'transparent'
                : 'rgba(0, 0, 0, 0.5)',
              height: shouldUseDesktopMode ? 'auto' : height,
            },
          ]}
          onPress={(e) => {
            if (e.target !== e.currentTarget) {
              return;
            }
            onChange(-1);
          }}
          nativeID="bottom-sheet-backdrop"
        >
          {!shouldUseDesktopMode && BackdropComponent && <BackdropComponent />}
          <Animated.View
            style={[
              styles.bottomSheet,
              {
                height: shouldUseDesktopMode ? '100%' : height * 0.85,
                width: containerWidth,
              },
              style,
            ]}
          >
            {!shouldUseDesktopMode && HandleComponent && <HandleComponent />}
            {children}
            {FooterComponent && (
              <View style={styles.footerStyle}>{<FooterComponent />}</View>
            )}
          </Animated.View>
        </Pressable>
      </TouchableWithoutFeedback>
    );
  },
);

const BottomSheet = React.forwardRef(
  (
    {
      index = 0,
      snapPoints = ['50%'],
      onChange,
      enablePanDownToClose = true,
      children,
      style,
      handleComponent,
      footerComponent,
      ...otherProps
    },
    ref,
  ) => {
    const { containerWidth } = useResponsiveWidth();

    // Handle snap points - convert percentage strings to actual heights
    const getSnapPointHeight = (snapPoint) => {
      if (typeof snapPoint === 'string' && snapPoint.includes('%')) {
        const percentage = parseFloat(snapPoint) / 100;
        return height * percentage;
      }
      return snapPoint;
    };

    const currentSnapPoint = snapPoints[index] || snapPoints[0];
    const sheetHeight = getSnapPointHeight(currentSnapPoint);

    const handleBackdropPress = (e) => {
      if (e.target !== e.currentTarget) {
        return;
      }
      if (enablePanDownToClose && onChange) {
        onChange(-1);
      }
    };

    return (
      <TouchableWithoutFeedback>
        <Pressable
          style={[
            styles.overlay,
            {
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              height: height,
            },
          ]}
          onPress={handleBackdropPress}
          nativeID="bottom-sheet-backdrop"
        >
          <Animated.View
            ref={ref}
            style={[
              styles.bottomSheet,
              {
                height: sheetHeight,
                width: containerWidth,
              },
              style,
            ]}
            {...otherProps}
          >
            {handleComponent && typeof handleComponent === 'function'
              ? React.createElement(handleComponent)
              : handleComponent}
            {children}
            {footerComponent && (
              <View style={styles.footerStyle}>
                {typeof footerComponent === 'function'
                  ? React.createElement(footerComponent)
                  : footerComponent}
              </View>
            )}
          </Animated.View>
        </Pressable>
      </TouchableWithoutFeedback>
    );
  },
);

export default BottomSheet;

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'flex-end',
  },
  bottomSheet: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    alignSelf: 'center',
  },
  contentContainer: {
    padding: 20,
    alignItems: 'center',
  },
  footerStyle: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
  },
});
