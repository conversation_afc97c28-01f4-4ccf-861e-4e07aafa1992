import React from 'react';
import { AppRegistry, View } from 'react-native';
import { createRoot } from 'react-dom/client';
import appConfig from './app.json';
import { TravelPlexBotWeb } from '../chatbot-ui/src/containers/TravelplexBotWeb';
import { useResponsiveWidth } from '../chatbot-ui/src/hooks/useResponsiveWidth';
import { HolidayChatWeb } from '../chatbot-ui/src/containers/HolidayChatWeb';

const App = () => {
  const { containerWidth } = useResponsiveWidth();
  // this is a temporary fix to handle the holiday chat
  const isHolidayChat = window?.location?.pathname?.includes('/chat');
  if (isHolidayChat) {
    // Append platform=desktop to URL if not present
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      if (!url.searchParams.has('platform')) {
        url.searchParams.set('platform', 'desktop');
        window.history.replaceState({}, '', url.toString());
      }
    }
    return (
      <View style={{ width: containerWidth }}>
        <HolidayChatWeb />
      </View>
    );
  }
  return (
    <View style={{ width: containerWidth }}>
      <TravelPlexBotWeb />
    </View>
  );
};

AppRegistry.registerComponent(appConfig.name, () => App);

const rootTag = document.getElementById('app-root');
const root = createRoot(rootTag);
root.render(<App />); 