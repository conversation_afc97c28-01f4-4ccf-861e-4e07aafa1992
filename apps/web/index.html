<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
	<meta name="description"
		content="Use coupon code MMTDEAL to get Upto 5000 Off on Domestic flight booking Find best deals at MakeMyTrip for ✅ Flight Tickets, Hotels, Holiday Packages, Bus and Train / Railway Reservations for India & International travel. Book cheap air tickets online for Domestic & International airlines, customized holiday packages and special deals on Hotel Bookings.">
	<link rel="icon" type="image/ico" href="https://imgak.mmtcdn.com/pwa_v3/pwa_commons_assets/favicon.ico">
	<title>MakeMyTrip - #1 Travel Website 50% OFF on Hotels,Flights &amp; Holiday</title>
	<style>
		body {
			margin: 0;
			padding: 0;
			font-family: Arial, sans-serif;
		}

		#app-root {
			height: 100vh;
			display: flex;
			overflow-y: scroll;
			flex-direction: column;
			justify-content: stretch;
			align-items: stretch;
		}

		@font-face {
			font-family: 'Lato-Light';
			font-style: normal;
			font-weight: 300;
			font-display: swap;
			src: local('Lato Light'), local('Lato-Light'), url(https://fonts.gstatic.com/s/lato/v14/S6u9w4BMUTPHh7USSwiPGQ3q5d0.woff2) format('woff2');
			unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
		}

		@font-face {
			font-family: 'Lato-Bold';
			font-style: normal;
			font-weight: 700;
			font-display: swap;
			src: local('Lato Bold'), local('Lato-Bold'), url(https://fonts.gstatic.com/s/lato/v14/S6u9w4BMUTPHh6UVSwiPGQ3q5d0.woff2) format('woff2');
			unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
		}

		@font-face {
			font-family: 'Lato-Regular';
			font-style: normal;
			font-weight: 400;
			font-display: swap;
			src: local('Lato Regular'), local('Lato-Regular'), url(https://fonts.gstatic.com/s/lato/v14/S6uyw4BMUTPHjx4wXiWtFCc.woff2) format('woff2');
			unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
		}

		@font-face {
			font-family: 'Lato-Medium';
			font-style: normal;
			font-weight: 400;
			font-display: swap;
			src: local('Lato Regular'), local('Lato-Regular'), url(https://fonts.gstatic.com/s/lato/v14/S6uyw4BMUTPHjx4wXiWtFCc.woff2) format('woff2');
			unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
		}

		@font-face {
			font-family: 'Lato-Semibold';
			font-style: normal;
			font-weight: 400;
			font-display: swap;
			src: local('Lato Regular'), local('Lato-Regular'), url(https://fonts.gstatic.com/s/lato/v14/S6uyw4BMUTPHjx4wXiWtFCc.woff2) format('woff2');
			unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
		}

		@font-face {
			font-family: 'Lato-Black';
			font-style: normal;
			font-weight: 900;
			font-display: swap;
			src: local('Lato Black'), local('Lato-Black'), url(https://fonts.gstatic.com/s/lato/v14/S6u9w4BMUTPHh50XSwiPGQ3q5d0.woff2) format('woff2');
			unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
		}
	</style>
</head>

<body>
	<div id="app-root"></div>
</body>

</html>