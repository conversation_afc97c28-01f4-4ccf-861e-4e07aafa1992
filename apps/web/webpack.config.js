const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const { readFileSync } = require('fs');
const CompressionPlugin = require('compression-webpack-plugin');
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

// Determine entry point based on BUILD_VARIANT (independent of webpack mode)
const getEntryPoint = () => {
  const buildVariant = process.env.BUILD_VARIANT || 'prod';
  if (buildVariant === 'qa') {
    return './index-input.web.jsx';
  } else {
    // Default for prod
    return './index.web.jsx';
  }
};

module.exports = {
  entry: getEntryPoint(),
  output: {
    path: path.resolve(__dirname, 'dist', process.env.BUILD_VARIANT || 'prod'),
    filename: 'bundle.[contenthash].js',
    clean: true,
    // Dynamic publicPath based on environment
    publicPath:
      process.env.NODE_ENV === 'production'
        ? 'https://jsak.mmtcdn.com/pwa/travelplex-ui-rnw/'
        : '/',
  },
  resolve: {
    extensions: [
      '.web.js',
      '.web.jsx',
      '.web.tsx',
      '.web.ts',
      '.js',
      '.jsx',
      '.ts',
      '.tsx',
    ],
    alias: {
      'react-native$': 'react-native-web',
      'react-native-linear-gradient$': path.resolve(
        __dirname,
        'linear-gradient-web/index.js',
      ),
      '@gorhom/bottom-sheet$': path.resolve(__dirname, 'bottom-sheet-web/index.js'),
      react$: 'react',
      '@react-native-community/masked-view$':
        '@react-native-masked-view/masked-view',
    },
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx|ts|tsx)$/,
        exclude: /node_modules\/(?!(@ptui\/chatbot-ui|react-native-.*))/,
        loader: 'babel-loader',
        options: {
          presets: ['@babel/env', '@babel/preset-react', '@babel/preset-typescript'],
        },
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader', 'file-loader'],
      },
      {
        test: /\.(png|webp)$/,
        loader: 'file-loader',
        options: {
          name: '[path][name].[ext]',
        },
      },
    ],
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: './index.html',
    }),
    new webpack.DefinePlugin({
      process: {
        env: {
          NODE_ENV: JSON.stringify(process.env.NODE_ENV || 'development'),
          BUILD_VARIANT: JSON.stringify(process.env.BUILD_VARIANT || 'prod'),
        },
      },
      __DEV__: process.env.NODE_ENV !== 'production' || true,
    }),
    new CompressionPlugin({
      filename: '[path][base].gz',
      algorithm: 'gzip',
      test: /\.(js|css|html|svg)$/,
      threshold: 10240,
      minRatio: 0.8,
    }),
    new CompressionPlugin({
      algorithm: 'brotliCompress',
      filename: '[path][base].br',
      compressionOptions: { level: 11 },
      test: /\.(js|css|html|svg)$/,
    }),
  ],
  devtool: 'source-map',
  devServer: {
    static: {
      directory: path.join(__dirname, 'dist'),
    },
    compress: true,
    port: 9000,
    historyApiFallback: true,
    hot: true,
    open: true,
  },
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: process.env.NODE_ENV === 'production',
            drop_debugger: true,
            pure_funcs:
              process.env.NODE_ENV === 'production'
                ? ['console.log', 'console.info']
                : [],
          },
        },
      }),
    ],
    usedExports: true,
    sideEffects: false,
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          enforce: true,
        },
      },
    },
  },
};
