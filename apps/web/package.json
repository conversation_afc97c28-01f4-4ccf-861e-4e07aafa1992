{"name": "@ptui/web", "version": "1.0.0", "main": "index.web.js", "scripts": {"dev:web": "BUILD_VARIANT=prod webpack serve --mode development", "dev:web:qa": "BUILD_VARIANT=qa webpack serve --mode development", "build": "yarn build:prod && yarn build:qa", "build:qa": "BUILD_VARIANT=qa webpack --mode production", "build:prod": "BUILD_VARIANT=prod NODE_ENV=production webpack --mode production", "serve": "BUILD_VARIANT=prod NODE_ENV=production webpack serve --mode production", "build-tar": "rm -rf ../../build && mkdir ../../build && node scripts/package-tar.cjs", "package": "sh ./scripts/package.sh"}, "dependencies": {"@Frontend_Ui_Lib_App/Carousel": "^0.0.4", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@ptui/chatbot-ui": "0.0.3-dev.28", "@react-native-async-storage/async-storage": "^1.19.3", "@react-native-community/masked-view": "^0.1.7", "@react-native-masked-view/masked-view": "^0.3.2", "@shopify/flash-list": "^1.7.3", "express": "^4.21.2", "react": "18.3.1", "react-cookies": "0.1.0", "react-dom": "18.3.1", "react-native": "^0.72", "react-native-blob-util": "^0.22.2", "react-native-fast-image": "^8.5.2", "react-native-fast-shadow": "^0.1.1", "react-native-gesture-handler": ">=2.18.1", "react-native-markdown-display": "^7.0.2", "react-native-reanimated": "^3.15.0", "react-native-reanimated-carousel": "^3.5.1", "react-native-safe-area-context": "^3.3.2", "react-native-shimmer-placeholder": "^1.0.35", "react-native-svg": "^12.1.1", "react-native-uuid": "^2.0.3", "react-native-web": "0.18.0", "react-native-web-linear-gradient": "^1.1.2", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@babel/core": "^7.15.5", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/preset-env": "^7.15.6", "@babel/preset-flow": "7.14.5", "@babel/preset-react": "^7.14.5", "@types/js-cookie": "^3.0.6", "@types/react-cookies": "^0.1.4", "@types/uuid": "^10.0.0", "babel-loader": "^8.2.2", "babel-plugin-react-native-web": "^0.19.13", "compression-webpack-plugin": "^11.1.0", "css-loader": "^7.1.2", "file-loader": "^6.2.0", "html-webpack-plugin": "5.6.3", "metro-react-native-babel-preset": "^0.77.0", "style-loader": "^4.0.0", "terser-webpack-plugin": "^5.3.11", "url-loader": "^4.1.1", "webpack": "^5.52.1", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^4.8.0", "webpack-dev-server": "^4.2.1"}}