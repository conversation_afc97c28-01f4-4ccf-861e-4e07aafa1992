import React from 'react';
import { AppRegistry, View } from 'react-native';
import { createRoot } from 'react-dom/client';
import appConfig from './app.json';
import { TravelPlexBotWeb as TravelPlexBotWebOriginal } from '../chatbot-ui/src/containers/TravelplexBotWeb';
import { TravelPlexBotWeb as TravelPlexBotWebWithInput } from './TravelPlexBotWebWithInput.jsx';
import { useResponsiveWidth } from '../chatbot-ui/src/hooks/useResponsiveWidth';

const App = () => {
  const { containerWidth } = useResponsiveWidth();

  // Check if we need the input version
  const urlParams = new URLSearchParams(window.location.search);
  const chatContext = urlParams.get('chatContext');
  const shouldUseInputVersion = chatContext === 'input';
  console.log('shouldUseInputVersion', shouldUseInputVersion);

  return (
    <View style={{ width: !shouldUseInputVersion ? containerWidth : 'auto' }}>
      {shouldUseInputVersion ? (
        <TravelPlexBotWebWithInput />
      ) : (
        <TravelPlexBotWebOriginal />
      )}
    </View>
  );
};

AppRegistry.registerComponent(appConfig.name, () => App);

const rootTag = document.getElementById('app-root');
const root = createRoot(rootTag);
root.render(<App />);
