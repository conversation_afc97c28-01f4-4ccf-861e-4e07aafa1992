const tar = require('tar');
const { argv } = require('yargs');
const fs = require('fs-extra');
const path = require('path');

const { tar_prefix = 1 } = argv;
const { tar_suffix = 1 } = argv;

const assetsTarContextPaths = '/opt/mmtwebapps6/ROOT/pwa/travelplex-ui-rnw/';

if (!fs.existsSync('../../build')) fs.mkdirSync('../../build');
else fs.emptyDirSync('../../build');

// Create Tar for CDN of JS
tar.create(
  {
    gzip: true,
    prefix: assetsTarContextPaths,
    file: `../../build/${tar_prefix}-cdnjs-${tar_suffix}.tar.gz`,
    cwd: './dist/prod',
  },
  ['./'],
  (err) => {
    if (err) console.error(`Error creating tar file: ${err}`);
    console.log(`${tar_prefix}-cdnjs-${tar_suffix}.tar.gz created.`);
  },
);

tar.create(
  {
    gzip: true,
    prefix: assetsTarContextPaths,
    file: `../../build/${tar_prefix}-qa-cdnjs-${tar_suffix}.tar.gz`,
    cwd: './dist/qa',
  },
  ['./'],
  (err) => {
    if (err) console.error(`Error creating tar file: ${err}`);
    console.log(`${tar_prefix}-qa-cdnjs-${tar_suffix}.tar.gz created.`);
  },
);