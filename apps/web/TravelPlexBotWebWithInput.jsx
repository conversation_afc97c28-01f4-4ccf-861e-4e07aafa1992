/* eslint-disable no-console */
import React, { useRef, useCallback, useState } from 'react';
import { TravelPlexBot } from '../chatbot-ui/src/index';

// TODO: Have to comeup with context and chat config for debug mode
const chatConfig = {
  context: {
    lob: 'COMMONS',
    lobCategory: 'COMMONS',
    view: 'my_account_landing',
    prevPage: null,
  },
  contextMetadata: {
    pageContext: {
      lob: 'COMMONS',
      lobCategory: 'COMMONS',
      pageName: 'my_account_landing',
      prevPageName: null,
    },
    searchContext: {},
  },
  expertMetadata: { summarize: true },
};

// Update the helper function to handle both JSON and JS objects
const isValidJSONOrJS = (str) => {
  if (!str.trim()) return true; // Empty string is valid (will use default)

  try {
    // First try JSON.parse
    JSON.parse(str);
    return true;
  } catch {
    try {
      // If JSON.parse fails, try evaluating as JS object
      // Wrap in parentheses to ensure it's treated as an expression
      const wrappedStr = `(${str})`;
      // Use Function constructor for safer evaluation than eval
      const result = new Function(`return ${wrappedStr}`)();
      // Check if result is an object
      return typeof result === 'object' && result !== null;
    } catch {
      return false;
    }
  }
};

// Update the parsing function to handle both formats
const parseJSONOrJS = (str, defaultValue) => {
  if (!str.trim()) return defaultValue;

  try {
    // First try JSON.parse
    return JSON.parse(str);
  } catch {
    try {
      // If JSON.parse fails, try evaluating as JS object
      const wrappedStr = `(${str})`;
      const result = new Function(`return ${wrappedStr}`)();
      if (typeof result === 'object' && result !== null) {
        return result;
      }
      throw new Error('Result is not an object');
    } catch (jsError) {
      throw new Error(`Invalid JSON or JavaScript object: ${jsError.message}`);
    }
  }
};

export const TravelPlexBotWeb = () => {
  const chatBotRef = useRef(null);
  const [context, setContext] = useState('');
  const [pageContext, setPageContext] = useState('');
  const [searchContext, setSearchContext] = useState('');
  const [expertMetadata, setExpertMetadata] = useState('');
  const [showBot, setShowBot] = useState(false);

  // Update validation states
  const [contextValid, setContextValid] = useState(true);
  const [pageContextValid, setPageContextValid] = useState(true);
  const [searchContextValid, setSearchContextValid] = useState(true);
  const [expertMetadataValid, setExpertMetadataValid] = useState(true);

  const handleLaunchBot = useCallback(() => {
    try {
      let parsedContext,
        parsedPageContext,
        parsedSearchContext,
        parsedExpertMetadata;

      // Parse context with detailed error handling
      try {
        parsedContext = parseJSONOrJS(context, chatConfig.context);
        console.log('Parsed context successfully:', parsedContext);
      } catch (contextError) {
        console.error('Error parsing context:', contextError);
        alert(`Error parsing Context: ${contextError.message}`);
        return;
      }

      // Parse pageContext with detailed error handling
      try {
        parsedPageContext = parseJSONOrJS(
          pageContext,
          chatConfig.contextMetadata.pageContext,
        );
        console.log('Parsed pageContext successfully:', parsedPageContext);
      } catch (pageContextError) {
        console.error('Error parsing pageContext:', pageContextError);
        alert(`Error parsing Page Context: ${pageContextError.message}`);
        return;
      }

      // Parse searchContext with detailed error handling
      try {
        parsedSearchContext = parseJSONOrJS(
          searchContext,
          chatConfig.contextMetadata.searchContext,
        );
        console.log('Parsed searchContext successfully:', parsedSearchContext);
      } catch (searchContextError) {
        console.error('Error parsing searchContext:', searchContextError);
        alert(`Error parsing Search Context: ${searchContextError.message}`);
        return;
      }

      // Parse expertMetadata with detailed error handling
      try {
        parsedExpertMetadata = parseJSONOrJS(
          expertMetadata,
          chatConfig.expertMetadata,
        );
        console.log('Parsed expertMetadata successfully:', parsedExpertMetadata);
      } catch (expertMetadataError) {
        console.error('Error parsing expertMetadata:', expertMetadataError);
        alert(`Error parsing Expert Metadata: ${expertMetadataError.message}`);
        return;
      }

      // Form the config object
      const customChatConfig = {
        context: parsedContext,
        contextMetadata: {
          pageContext: parsedPageContext,
          searchContext: parsedSearchContext,
        },
        expertMetadata: parsedExpertMetadata,
      };

      console.log('Final customChatConfig:', customChatConfig);

      // Show the bot component
      setShowBot(true);

      // Call expand after a short delay to ensure the bot is rendered
      setTimeout(() => {
        if (chatBotRef?.current) {
          console.log('Calling expand with config:', customChatConfig);
          chatBotRef.current.expand({
            chatConfig: customChatConfig,
          });
        }
      }, 100);
    } catch (error) {
      console.error('Unexpected error in handleLaunchBot:', error);
      alert(`Unexpected error: ${error.message}`);
    }
  }, [context, pageContext, searchContext, expertMetadata]);

  const handleStateChange = useCallback((state) => {
    if (state === 'collapsed') {
      setShowBot(false);
    }
  }, []);
  if (!showBot) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
          padding: '20px',
          backgroundColor: '#f5f5f5',
        }}
      >
        <div
          style={{
            width: '100%',
            maxWidth: '800px',
            backgroundColor: 'white',
            borderRadius: '8px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            padding: '30px',
          }}
        >
          <h3 style={{ textAlign: 'center', marginBottom: '10px', color: '#333' }}>
            Chat Context Configuration
          </h3>
          <p
            style={{
              fontSize: '14px',
              color: '#666',
              marginBottom: '30px',
              textAlign: 'center',
            }}
          >
            Enter valid JSON or JavaScript objects. Examples:
          </p>

          <div style={{ marginBottom: '20px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '8px',
                fontWeight: 'bold',
                fontSize: '16px',
              }}
            >
              Context (JSON or JS Object):
            </label>
            <textarea
              value={context}
              onChange={(e) => {
                setContext(e.target.value);
                setContextValid(isValidJSONOrJS(e.target.value));
              }}
              placeholder={`JSON: ${JSON.stringify(chatConfig.context, null, 2)}\n\nJS Object:\n{\n  lob: 'COMMONS',\n  lobCategory: 'COMMONS',\n  view: 'my_account_landing',\n  prevPage: null\n}`}
              style={{
                width: '100%',
                height: '180px',
                padding: '12px',
                border: `2px solid ${contextValid ? '#ddd' : '#ff0000'}`,
                borderRadius: '6px',
                fontFamily: 'monospace',
                fontSize: '14px',
                backgroundColor: contextValid ? 'white' : '#fff5f5',
                resize: 'vertical',
                boxSizing: 'border-box',
              }}
            />
            {!contextValid && (
              <div style={{ color: '#ff0000', fontSize: '14px', marginTop: '6px' }}>
                Invalid JSON or JavaScript object format
              </div>
            )}
          </div>

          <div style={{ marginBottom: '20px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '8px',
                fontWeight: 'bold',
                fontSize: '16px',
              }}
            >
              Page Context (JSON or JS Object):
            </label>
            <textarea
              value={pageContext}
              onChange={(e) => {
                setPageContext(e.target.value);
                setPageContextValid(isValidJSONOrJS(e.target.value));
              }}
              placeholder={`${JSON.stringify(chatConfig.contextMetadata.pageContext, null, 2)}`}
              style={{
                width: '100%',
                height: '160px',
                padding: '12px',
                border: `2px solid ${pageContextValid ? '#ddd' : '#ff0000'}`,
                borderRadius: '6px',
                fontFamily: 'monospace',
                fontSize: '14px',
                backgroundColor: pageContextValid ? 'white' : '#fff5f5',
                resize: 'vertical',
                boxSizing: 'border-box',
              }}
            />
            {!pageContextValid && (
              <div style={{ color: '#ff0000', fontSize: '14px', marginTop: '6px' }}>
                Invalid JSON or JavaScript object format
              </div>
            )}
          </div>

          <div style={{ marginBottom: '20px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '8px',
                fontWeight: 'bold',
                fontSize: '16px',
              }}
            >
              Search Context (JSON or JS Object):
            </label>
            <textarea
              value={searchContext}
              onChange={(e) => {
                setSearchContext(e.target.value);
                setSearchContextValid(isValidJSONOrJS(e.target.value));
              }}
              placeholder={`${JSON.stringify(chatConfig.contextMetadata.searchContext, null, 2)}`}
              style={{
                width: '100%',
                height: '140px',
                padding: '12px',
                border: `2px solid ${searchContextValid ? '#ddd' : '#ff0000'}`,
                borderRadius: '6px',
                fontFamily: 'monospace',
                fontSize: '14px',
                backgroundColor: searchContextValid ? 'white' : '#fff5f5',
                resize: 'vertical',
                boxSizing: 'border-box',
              }}
            />
            {!searchContextValid && (
              <div style={{ color: '#ff0000', fontSize: '14px', marginTop: '6px' }}>
                Invalid JSON or JavaScript object format
              </div>
            )}
          </div>

          <div style={{ marginBottom: '30px' }}>
            <label
              style={{
                display: 'block',
                marginBottom: '8px',
                fontWeight: 'bold',
                fontSize: '16px',
              }}
            >
              Expert Metadata (JSON or JS Object):
            </label>
            <textarea
              value={expertMetadata}
              onChange={(e) => {
                setExpertMetadata(e.target.value);
                setExpertMetadataValid(isValidJSONOrJS(e.target.value));
              }}
              placeholder={`${JSON.stringify(chatConfig.expertMetadata, null, 2)}`}
              style={{
                width: '100%',
                height: '140px',
                padding: '12px',
                border: `2px solid ${expertMetadataValid ? '#ddd' : '#ff0000'}`,
                borderRadius: '6px',
                fontFamily: 'monospace',
                fontSize: '14px',
                backgroundColor: expertMetadataValid ? 'white' : '#fff5f5',
                resize: 'vertical',
                boxSizing: 'border-box',
              }}
            />
            {!expertMetadataValid && (
              <div style={{ color: '#ff0000', fontSize: '14px', marginTop: '6px' }}>
                Invalid JSON or JavaScript object format
              </div>
            )}
          </div>

          <div style={{ display: 'flex', gap: '15px', justifyContent: 'center' }}>
            <button
              onClick={handleLaunchBot}
              disabled={
                !contextValid ||
                !pageContextValid ||
                !searchContextValid ||
                !expertMetadataValid
              }
              style={{
                padding: '12px 24px',
                backgroundColor:
                  !contextValid ||
                  !pageContextValid ||
                  !searchContextValid ||
                  !expertMetadataValid
                    ? '#ccc'
                    : '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor:
                  !contextValid ||
                  !pageContextValid ||
                  !searchContextValid ||
                  !expertMetadataValid
                    ? 'not-allowed'
                    : 'pointer',
                fontSize: '16px',
                fontWeight: 'bold',
                minWidth: '120px',
              }}
            >
              Launch Bot
            </button>

            <button
              onClick={() => {
                setContext('');
                setPageContext('');
                setSearchContext('');
                setExpertMetadata('');
                setContextValid(true);
                setPageContextValid(true);
                setSearchContextValid(true);
                setExpertMetadataValid(true);
              }}
              style={{
                padding: '12px 24px',
                backgroundColor: '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '16px',
                fontWeight: 'bold',
                minWidth: '120px',
              }}
            >
              Clear All
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ position: 'relative', height: '100vh' }}>
      {/* <button
        onClick={handleGoBack}
        style={{
          position: 'fixed',
          top: '20px',
          left: '20px',
          padding: '10px 20px',
          backgroundColor: '#28a745',
          color: 'white',
          border: 'none',
          borderRadius: '6px',
          cursor: 'pointer',
          fontSize: '14px',
          fontWeight: 'bold',
          boxShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
        }}
      >
        ← Go Back to Config
      </button> */}

      <TravelPlexBot
        ref={chatBotRef}
        onViewStateChange={handleStateChange}
        isDebugMode={true}
      />
    </div>
  );
};
