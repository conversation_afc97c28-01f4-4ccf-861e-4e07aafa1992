import React from "react";
import { validatePersuasionsData } from "../utils";
import type { Persuasion } from "../props";
import useMyraPersuasionText from "../usePersuasionText";
import { renderPersuasionText } from "./PersuasionUtils";
import usePersuasionAnimation from "./usePersuasionAnimation";
import { config } from "../config";

const RightPersuasion = ({
  persuasions,
  shouldAlwaysDoTextScrollAnimation,
  lob,
}: {
  persuasions: Persuasion[];
  shouldAlwaysDoTextScrollAnimation: boolean;
  lob: string;
}) => {
  const currentText = useMyraPersuasionText(
    config.persuasionDelay,
    persuasions,
    lob
  );
  const [canDoTextAnimation] = usePersuasionAnimation(
    persuasions,
    shouldAlwaysDoTextScrollAnimation,
    lob
  );
  if (!validatePersuasionsData(persuasions)) {
    return null;
  }
  const persuasionsToRender = persuasions;
  return (
    <div className="rightPersuasionTextWrapper">
      {renderPersuasionText(
        persuasionsToRender,
        currentText,
        canDoTextAnimation
      )}
    </div>
  );
};

export default RightPersuasion;
