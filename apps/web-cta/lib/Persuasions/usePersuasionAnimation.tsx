import { useEffect, useState } from "react";
import {
  getItemBySession,
  itemType,
  setItemBySession,
} from "MMT-UI/storages/client/sessionStorage";
import type { Persuasion } from "../props";
import { MYRA_ANIMATION_DONE_KEY } from "../constants";
import { config } from "../config";

const usePersuasionAnimation = (
  persuasions: Persuasion[],
  shouldAlwaysDoTextScrollAnimation: boolean,
  lob: string
) => {
  const isMyraTextAnimationDone = getItemBySession(
    `${MYRA_ANIMATION_DONE_KEY}_${lob}`,
    { shouldParseJson: false },
    itemType.N_ES
  );
  const [canDoTextAnimation, setCanDoTextAnimation] = useState(
    !isMyraTextAnimationDone
  );
  useEffect(() => {
    if (isMyraTextAnimationDone || shouldAlwaysDoTextScrollAnimation) {
      return;
    }
    const timeoutid = setTimeout(() => {
      if (!isMyraTextAnimationDone) {
        setItemBySession(
          `${MYRA_ANIMATION_DONE_KEY}_${lob}`,
          "true",
          { shouldStringifyJson: true },
          itemType.N_ES
        );
        setCanDoTextAnimation(false);
      }
    }, (persuasions?.length || 1) * config.persuasionDelay);
    return () => {
      clearTimeout(timeoutid);
    };
  }, []);
  return [canDoTextAnimation];
};
export default usePersuasionAnimation;
