import React from "react";
import type { Persuasion } from "../props";
const renderPersuasionTextItem = (
  persuasionItem: Persuasion,
  index: number,
  currentText: number
) => {
  const className = `persuasionText ${index === currentText ? "activeIn" : ""}`;

  if (!persuasionItem?.html) {
    return null;
  }
  return (
    <p
      key={`persuasionitem_${index + 1}`}
      className={className}
      dangerouslySetInnerHTML={{ __html: persuasionItem.html }}
    />
  );
};
export const renderPersuasionText = (
  persuasions: Persuasion[],
  currentText: number,
  canDoTextAnimation: boolean
) => {
  if (!persuasions?.length) {
    return null;
  }
  let persuasionsToRender = persuasions;
  if (!canDoTextAnimation) {
    persuasionsToRender = persuasionsToRender.slice(0, 1);
  }
  if (persuasionsToRender?.length === 1) {
    if (!persuasionsToRender[0]?.html) {
      return null;
    }
    return (
      <p
        className="persuasionText"
        style={{ display: "flex" }}
        dangerouslySetInnerHTML={{ __html: persuasionsToRender[0]?.html }}
      />
    );
  }
  return persuasionsToRender.map(
    (persuasionItem, index) => {
      return renderPersuasionTextItem(persuasionItem, index, currentText);
    }
    // renderPersuasionTextItem(persuasionItem, index, currentText)
  );
};
