/* My new css start */
.myraFloatingBar {
  position: fixed;
  right: 32px;
  bottom: 45px;
  z-index: 12;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-end;
  cursor: pointer;
}

.tp-icon-wrapper {
  width: 250px;
  height: 68px;
  display: flex;
  align-items: center;
}

@keyframes moveMe {
  0% {
    transform: translateX(-35vw);
  }

  100% {
    transform: translateX(0%);
  }
}

.tp-icon-container {
  width: 68px;
  height: 68px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.4);
  z-index: 1;
}
.tp-icon {
  width: 60px;
  height: 60px;
  background-size: contain;
}
.tp-new-message {
  position: absolute;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background-color: var(--new-message-icon-color, red);
  top: 2px;
  left: 5px;
  z-index: 1;
}
.tp-persuasion {
  position: relative;
  left: -12px;
  height: 54px;
  flex: 1;
  display: flex;
  align-items: center;
  border-top-right-radius: 25px;
  border-bottom-right-radius: 25px;
  padding-left: 15px;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.4);
  background: #fff;
}
.rightPersuasionTextWrapper {
  padding-left: 6px;
  padding-top: 5px;
  padding-bottom: 5px;
  width: 100%;
  overflow: hidden;
  max-height: 29px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
  height: 28px;
}

.persuasionText {
  max-height: 100%;
  display: none;
}

.persuasionText.activeIn {
  display: block;
  animation: textIn 3s ease;
}

@keyframes textIn {
  0% {
    transform: translateY(40px);
  }

  20%,
  80% {
    transform: translateY(0%);
  }

  100% {
    transform: translateY(-50px);
  }
}
.tp-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 10;
}

.tpFrame {
  position: relative;
  border: none;
  box-shadow: none;
  height: 100%;
  width: 100%;
  background-color: transparent;
  border-top-left-radius: 25px;
  border-top-right-radius: 25px;
}
.overflowHidden {
  overflow: hidden;
}

.tp-loader-container {
  position: fixed;
  bottom: 0;
  right: 20px;
  width: 400px;
  height: 92%;
  background-color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  border-top-right-radius: 16px;
  border-top-left-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tp-loader {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.tp-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 10;
}

.tp-iframe-container {
  position: fixed;
  bottom: -100%;
  right: 20px;
  z-index: 13;
  border: none;
  box-shadow: none;
  visibility: hidden;
  opacity: 0;
  height: 92%;
  width: 400px;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.1);
  background-color: #fff;
  border-top-left-radius: 25px;
  border-top-right-radius: 25px;
}

.tp-iframe-container.myra-frame-transition {
  transition: 0.5s ease-in-out bottom 0.02s;
}

.tp-iframe-container.showIframe {
  bottom: 0%;
  visibility: visible;
  opacity: 1;
}

.tp-close-icon {
  position: absolute;
  top: -29px;
  left: 92%;
  width: 25px;
  height: 25px;
  background-size: cover;
  background-repeat: no-repeat;
  z-index: 1;
}

/* My new css end */
