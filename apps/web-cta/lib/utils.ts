import type { Obj, Persuasion, TravelplexBotIconProps } from "./props";

export function getUpdatedBotLink(
  botLink: string,
  _params: Obj = {},
  platform: TravelplexBotIconProps["platform"]
): string {
  if (!botLink) {
    console.error("botLink is required");
    return "";
  }
  const params = { ..._params, platform };
  // Check if the input is an object
  if (typeof params !== "object" || params === null) {
    console.error("params should be an object");
    return "";
  }
  const queryParams = Object.keys(params)
    .map(
      (key) =>
        `${encodeURIComponent(key)}=${encodeURIComponent(
          String((params as Record<string, unknown>)[key])
        )}`
    )
    .join("&");
  const botLinkParams = new URLSearchParams(botLink?.split("?")?.[1]);
  const queryParamsObj = new URLSearchParams(queryParams);
  let myraLink;
  if (botLinkParams.toString()) {
    queryParamsObj.forEach((value, key) => {
      if (!botLinkParams.has(key)) {
        botLinkParams.set(key, value);
      }
    });
    myraLink = `${botLink.split("?")[0]}?${botLinkParams.toString()}`;
  } else {
    myraLink = `${botLink}?${queryParams}`;
  }
  // Convert object to query parameter string
  return myraLink;
}

export const validatePersuasionsData = (persuasions: Persuasion[]) => {
  if (
    !Array.isArray(persuasions) ||
    !persuasions?.length ||
    !persuasions[0]?.html
  ) {
    return false;
  }
  return true;
};
