import { useState, useEffect, useRef } from "react";

import { getItemBySession } from "MMT-UI/storages/client/sessionStorage";
import type { Persuasion } from "./props";
import { MYRA_ANIMATION_DONE_KEY } from "./constants";

const usePersuasionText = (
  persuasionDelay: number,
  persuasions: Persuasion[],
  lob: string
) => {
  const myraAnimationDoneForSession = useRef<boolean>(
    getItemBySession(`${MYRA_ANIMATION_DONE_KEY}_${lob}`) === "true"
  ).current;
  const [currentText, setCurrentText] = useState(0);
  useEffect(() => {
    if (
      myraAnimationDoneForSession ||
      !Array.isArray(persuasions) ||
      persuasions.length <= 0
    ) {
      return;
    }
    const persuasionTimer = setInterval(() => {
      setCurrentText((prev) => {
        if (prev >= persuasions.length - 1) {
          clearInterval(persuasionTimer); // Stop the timer
          return 0; // Reset to the first index
        }
        return prev + 1; // Move to the next index
      });
    }, persuasionDelay || 2800);
    return () => clearInterval(persuasionTimer);
  }, [persuasionDelay, persuasions]);
  return currentText;
};
export default usePersuasionText;
