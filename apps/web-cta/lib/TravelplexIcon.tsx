import React, {
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from "react";
import {
  getItemBySession,
  itemType,
  setItemBySession,
} from "MMT-UI/storages/client/sessionStorage";
import type { TravelplexIconProps } from "./props";

import TravelplexFrame from "./TravelplexFrame";
import "./style.css";
import { omnitureService } from "./omniture";
import { MYRA_ICON_TO_RIGHT_ANIMATION_DONE_KEY } from "./constants";
import RightPersuasion from "./Persuasions/RightPersuasion";
import { config } from "./config";

const TravelplexIcon = React.forwardRef<
  { expandMyraFrame: () => void },
  TravelplexIconProps
>(
  (
    {
      myraAppLink,
      onBotLOBLinkClickedEvent,
      onBotLoginEvent,
      onBotTriggerAnalyticsEvent,
      onTravelplexIconClicked,
      onBotGetMetaDataEvent,
      shouldShowBotByDefault,
      persuasions = [],
      onBotEvent,
      onBotLinkChatEvent,
      frameStyles = {},
      shouldAlwaysDoTextScrollAnimation,
      shouldShowOverlay,
      shouldDoCenterAnimation: shouldDoCenterAnimationProp,
      lob,
      brand,
      onBotJSLoadedEvent,
      shouldShowMinimizedCTA,
      onBotFrameShownEvent,
    },
    ref
  ) => {
    const [showMyraFrame, setShowMyraFrame] = useState(false);
    const [myraMinimized, setMyraMinimized] = useState(true);
    const [showNewMessageIcon, setShowNewMessageIcon] = useState(false);
    const [frameKey, setFrameKey] = useState(0);

    const isMyraIconToRightAnimationDone = getItemBySession(
      `${MYRA_ICON_TO_RIGHT_ANIMATION_DONE_KEY}_${lob}`,
      { shouldParseJson: false },
      itemType.N_ES
    );
    const doCenterAnimation = shouldDoCenterAnimationProp
      ? !isMyraIconToRightAnimationDone
      : false;
    const [shouldDoMyraCenterAnimation, setShouldDoMyraCenterAnimation] =
      useState(doCenterAnimation);
    useEffect(() => {
      if (!shouldShowOverlay) {
        document.body.classList.remove("overflowHidden");
        return;
      }
      if (showMyraFrame) {
        document.body.classList.add("overflowHidden");
      }
      if (myraMinimized) {
        document.body.classList.remove("overflowHidden");
      }
    }, [showMyraFrame, myraMinimized]);
    useEffect(() => {
      if (!shouldDoCenterAnimationProp) {
        setShouldDoMyraCenterAnimation(false);
        return;
      }
      if (!isMyraIconToRightAnimationDone) {
        setItemBySession(
          `${MYRA_ICON_TO_RIGHT_ANIMATION_DONE_KEY}_${lob}`,
          true
        );
        setShouldDoMyraCenterAnimation(true);
        const timeoutId = setTimeout(() => {
          setShouldDoMyraCenterAnimation(false);
        }, 3100);
        return () => {
          clearTimeout(timeoutId);
        };
      }
    }, []);
    useEffect(() => {
      if (shouldShowBotByDefault) {
        setMyraMinimized(!shouldShowBotByDefault);
        setShowMyraFrame(shouldShowBotByDefault);
      }
    }, []);
    const expandMyraFrame = useCallback(() => {
      setShowNewMessageIcon(false);
      if (!showMyraFrame) {
        setShowMyraFrame(true);
      }
      setMyraMinimized(!myraMinimized);
      setFrameKey((prev) => prev + 1); // Force remount of TravelplexFrame
    }, [showMyraFrame, myraMinimized]);
    useImperativeHandle(ref, () => ({
      expandMyraFrame,
    }));
    const handleMyraIconClick = useCallback(() => {
      omnitureService.trackClickEvent({
        prop67: "myra_tp_click",
        prop54: "trvlplex_clicked",
      });
      onTravelplexIconClicked();
      setShowNewMessageIcon(false);
      if (!showMyraFrame) {
        setShowMyraFrame(true);
      }
      setMyraMinimized(!myraMinimized);
    }, [showMyraFrame, myraMinimized, onTravelplexIconClicked]);
    const iconStyle = {
      backgroundImage: `url(${
        (brand && config.myraIconMap[brand]) || config.myraIconMap?.mmt_b2c
      })`,
    };

    const onCloseMyraFrame = () => {
      setShowMyraFrame(false);
      setMyraMinimized(true);
    };

    const canShowPersuasion = useMemo(() => {
      return !shouldDoMyraCenterAnimation && persuasions && persuasions?.length;
    }, [shouldDoMyraCenterAnimation, persuasions]);
    const tpIconWrapperStyles = useMemo(() => {
      const styles: Record<string, string> = {};
      if (shouldDoMyraCenterAnimation) {
        styles.animation = "moveMe 3s ease";
      }
      if (!Array.isArray(persuasions) || !persuasions.length) {
        styles.width = "auto";
      }
      return styles;
    }, [shouldDoMyraCenterAnimation, persuasions]);
    return (
      <>
        <TravelplexFrame
          key={frameKey}
          myraAppLink={myraAppLink}
          closeMyraFrame={onCloseMyraFrame}
          myraMinimized={myraMinimized}
          setMyraMinimized={setMyraMinimized}
          onBotLOBLinkClickedEvent={onBotLOBLinkClickedEvent}
          onBotLoginEvent={onBotLoginEvent}
          onBotTriggerAnalyticsEvent={onBotTriggerAnalyticsEvent}
          onBotGetMetaDataEvent={onBotGetMetaDataEvent}
          showMyraFrame={showMyraFrame}
          onBotEvent={onBotEvent || (() => {return;})}
          onBotLinkChatEvent={onBotLinkChatEvent || (() => {return;})}
          frameStyles={frameStyles}
          canPreWarmMyraFrame={config.canPreWarmMyraFrame}
          shouldShowOverlay={shouldShowOverlay}
          onBotJSLoadedEvent={onBotJSLoadedEvent}
          shouldShowMinimizedCTA={shouldShowMinimizedCTA}
          onBotFrameShownEvent={onBotFrameShownEvent}
        />
        <div
          className="tp-icon-wrapper"
          style={tpIconWrapperStyles}
          onClick={handleMyraIconClick}
        >
          <div className="tp-icon-container">
            {showNewMessageIcon && <div className="tp-new-message" />}
            <div style={iconStyle} className="tp-icon"></div>
          </div>
          {canShowPersuasion ? (
            <div className="tp-persuasion">
              <RightPersuasion
                persuasions={persuasions}
                shouldAlwaysDoTextScrollAnimation={
                  shouldAlwaysDoTextScrollAnimation || false
                }
                lob={lob}
              />
            </div>
          ) : null}
        </div>
      </>
    );
  }
);

export default React.memo(TravelplexIcon);
