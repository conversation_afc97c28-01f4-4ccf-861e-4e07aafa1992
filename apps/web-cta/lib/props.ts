export interface Obj {
  [key: string]: string | number | boolean | Obj; // Key can be any string, and value can be string, number, or boolean
}

export interface BotLinkClickedEvent {
  lobLink: string;
}

export interface BotLinkLoginEvent {
  botContext: Record<string, string>;
  deviceId: string;
  intent: "CONNECT_TO_AGENT" | "SCHEDULE_CALL";
  lob: string;
  loggedIn: boolean;
  previousChatSource: string | null;
  previousConversationId: string | null;
  previousMessage: Record<string, unknown>;
  previousMessageSource: string | null;
  source: string | null;
}

export type BotChatEvent = Record<string, unknown>;
export interface TrackingData {
  pdtTrackingData: Record<string, string>;
  omnitureTrackingData: Record<string, string>;
}

export interface FrameStyles {
  height?: string | number;
  width?: string | number;
  boxShadow?: string;
  backgroundColor?: string;
  [key: string]: string | number | undefined;
}

export interface BotStyles {
  position?: "fixed" | "absolute" | "relative";
  bottom?: string | number;
  right?: string | number;
  zIndex?: number;
  [key: string]: string | number | undefined;
}

export type Platform = "desktop" | "pwa";

export interface MyraFrameProps {
  myraAppLink: string; // The link to the Myra app
  closeMyraFrame: () => void; // Function to close the Myra frame
  setMyraMinimized: (minimized: boolean) => void; // Function to set the minimized state
  myraMinimized: boolean; // Indicates whether Myra is minimized
  onBotLoginEvent: (value: BotLinkLoginEvent) => void; // Callback function for login actions
  onBotTriggerAnalyticsEvent: (id: string) => void; // Function for tracking PDT events
  onBotGetMetaDataEvent: () => void; // Function to handle metadata messages
  onBotLOBLinkClickedEvent?: (value: BotLinkClickedEvent) => void; // Optional callback for LOB link clicks, defaults to no-op
  showMyraFrame: boolean; // Indicates whether the Myra frame is visible
  onBotEvent: (value: MessageEvent) => void;
  onBotLinkChatEvent: (value: BotChatEvent) => void;
  onBotJSLoadedEvent?: () => void; // Optional callback for JS loaded event
  frameStyles?: FrameStyles;
  canPreWarmMyraFrame?: boolean;
  shouldShowOverlay?: boolean;
  shouldShowMinimizedCTA?: boolean;
  onBotFrameShownEvent?: () => void;
}

export interface Persuasion {
  html: string; // HTML string content
}

/**
 * Type to determine the brand type for bot icon
 * Following types are supported:
 * mmt_b2c - MakeMyTrip brand and B2C variant.
 * gi_b2c - GoIbibo brand and B2C variant.
 * Optional prop, if not provided, mmt_b2c will be used as default
 */
export type BrandType = "mmt_b2c" | "gi_b2c";

export interface TravelplexBotIconProps {
  platform: Platform;
  trackingData: TrackingData;
  /**
   * Configuration link for myra app.
   * lob parameter should be passed here or in bot link, otherwise the component will not render.
   */
  params: Record<string, string>;

  /**
   * Callback for user login actions.
   */
  onBotLoginEvent?: (value: BotLinkLoginEvent) => void;

  /**
   * Function to track data to PDT.
   */
  onBotTriggerAnalyticsEvent?: (value: string) => void;

  /**
   * Function triggered when the Myra icon is clicked.
   */
  onTravelplexIconClicked?: () => void;

  /**
   * Function to handle metadata message retrieval.
   */
  onBotGetMetaDataEvent?: () => void;
  /**
   * Function to handle bot js loaded event.
   */
  onBotJSLoadedEvent?: () => void;
  /**
   * Function triggered when a line of business (LOB) link is clicked.
   */
  onBotLOBLinkClickedEvent?: (value: BotLinkClickedEvent) => void;
  /**
   * Variable to indicate whether the Myra frame is expanded even before the bot icon is clicked
   */
  shouldShowBotByDefault?: boolean;
  /**
   * Array of persuasion objects.
   * Persuasion objects contain the following properties:
   * - textColor: Array of color codes (strings)
   * - html: HTML string content
   */
  persuasions?: Persuasion[];
  /**
   * variable to determine the brand type for bot icon
   * Following types are supported:
   * mmt_b2c - MakeMyTrip brand and B2C variant.
   * gi_b2c - GoIbibo brand and B2C variant.
   * Optional prop, if not provided, mmt_b2c will be used as default
   */
  brand?: BrandType;
  /**
   * Callbak which will be invoked when bot sends any event
   */
  onBotEvent?: (value: MessageEvent) => void;
  /**
   * Callbak which will be invoked when bot sends chat event
   */
  onBotLinkChatEvent?: (value: BotChatEvent) => void;
  /**
   * Styles which will be applied to the bot frame
   */
  frameStyles?: FrameStyles;
  /**
   * Styles which will be applied to the bot icon
   */
  botStyles?: BotStyles;
  /**
   * Indicates whether the text scroll animation should always be performed.
   * By default, it will be false and text scroll animation will be performed only once for a session.
   * If set to true, the text scroll animation will be performed every time the component is rendered.
   */
  shouldAlwaysDoTextScrollAnimation?: boolean;
  /**
   * Indicates whether the overlay should be shown.
   * By default, it will be true and overlay will be shown
   * If set to false, the overlay will not be shown
   */
  shouldShowOverlay?: boolean;
  /**
   * Indicates whether the center animation should be performed.
   * By default, it will be false and the center animation will not be performed.
   * If set to true, the center animation will be performed.
   */
  shouldDoCenterAnimation?: boolean;
  /**
   * Line of Business (LOB) identifier.
   * This parameter should be passed either here or in the bot link.
   * If not provided, the component will not render.
   */
  lob: string;
  /**
   * Indicates whether the minimize CTA should be shown.
   * By default, it will be true and the minimize CTA will be shown
   * If set to false, the minimize CTA will not be shown
   */
  shouldShowMinimizedCTA?: boolean;
  onBotFrameShownEvent?: () => void;
  botRef?: React.RefObject<{ expandMyraFrame: () => void }>;
}

export interface TravelplexIconProps {
  myraAppLink: string; // The link to the Myra app
  onBotLOBLinkClickedEvent: (value: BotLinkClickedEvent) => void; // Function callback for LOB link click, accepting any type of argument
  onBotLoginEvent: (value: BotLinkLoginEvent) => void; // Function callback for login actions
  onTravelplexIconClicked: () => void; // Callback for when the Myra icon is clicked
  onBotTriggerAnalyticsEvent: (id: string) => void; // Function for tracking PDT events
  onBotGetMetaDataEvent: () => void;
  shouldShowBotByDefault: boolean; // Indicates whether the Myra frame is expanded
  persuasions: Persuasion[]; // Array of persuasion objects
  onBotEvent?: (value: MessageEvent) => void;
  onBotLinkChatEvent?: (value: BotChatEvent) => void;
  onBotJSLoadedEvent?: () => void; // Optional callback for JS loaded event
  botStyles?: BotStyles;
  frameStyles?: FrameStyles;
  canPreWarmMyraFrame?: boolean;
  shouldAlwaysDoTextScrollAnimation?: boolean;
  shouldShowOverlay?: boolean;
  shouldDoCenterAnimation?: boolean;
  persuasionType?: "cloud" | "capsule";
  lob: string;
  brand?: BrandType;
  shouldShowMinimizedCTA?: boolean;
  onBotFrameShownEvent?: () => void;
}
