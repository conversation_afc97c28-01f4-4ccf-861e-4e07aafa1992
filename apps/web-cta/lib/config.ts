export const config = {
  canPreWarmMyraFrame: false,
  brand: "mmt_b2c",
  canSendMyraIconOmniture: false,
  botLink: 'https://www.makemytrip.com/myra-v1', // TODO: This has to be updated with prod FQDN
  myraIconMap: {
    mmt_b2c:
      "https://jsak.mmtcdn.com/pwa/platform-myra-ui/static/sub_icons/tp-new-animated-without-con.webp",
    gi_b2c:
      "https://jsak.mmtcdn.com/pwa/platform-myra-ui/static/sub_icons/gia-icon-ring.webp",
  },
  persuasionDelay: 2800,
  minimizeIcon:
    "https://jsak.mmtcdn.com/pwa/platform-myra-ui/static/sub_icons/close-circle.png",
};
