import React, { useEffect, useRef, useState } from "react";
import type { MyraFrameProps } from "./props";
import { MESSAGES } from "./constants";

import { omnitureService } from "./omniture";

import "./style.css";
import { config } from "./config";

const parsePayload = (payload: string) => {
  let parsedPayload;
  try {
    parsedPayload = JSON.parse(payload);
  } catch (e) {
    console.error(e);
    parsedPayload = {};
  }
  return parsedPayload;
};
const TravelplexFrame: React.FC<MyraFrameProps> = ({
  myraAppLink,
  closeMyraFrame,
  setMyraMinimized,
  myraMinimized,
  onBotLoginEvent,
  onBotTriggerAnalyticsEvent,
  onBotGetMetaDataEvent,
  onBotLOBLinkClickedEvent,
  showMyraFrame,
  onBotEvent,
  onBotLinkChatEvent,
  frameStyles,
  canPreWarmMyraFrame,
  shouldShowOverlay,
  onBotJSLoadedEvent,
  shouldShowMinimizedCTA: _shouldShowMinimizedCTA,
  onBotFrameShownEvent,
}) => {
  const myraFrameRef = useRef<HTMLIFrameElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const getMyraAppLinkOrigin = () => {
    try {
      return new URL(myraAppLink).origin;
    } catch (e) {
      console.error(e);
      return "";
    }
  };

  /**
   *
   * @param {LOBLinkClickedPayload} payload
   */
  const handleLobLinkClicked = (payload: string) => {
    if (payload) {
      const parsedPayload = parsePayload(payload);
      const { lobLink } = parsedPayload;
      if (!lobLink) {
        return;
      }
      // Open new tab with the link
      window.open(lobLink, "_blank");
      // Store payload in session storage
      // Redirect(PWA) or open(DT) new tab with the link
      onBotLOBLinkClickedEvent?.(parsedPayload);
    }
  };

  const handleTriggerAnalyticsMessage = (_payload: string) => {
    const parsedPayload = parsePayload(_payload);
    const { payload = {} } = parsedPayload;
    const { tracking } = payload;
    if (
      onBotTriggerAnalyticsEvent &&
      Array.isArray(tracking) &&
      tracking?.length
    ) {
      tracking.forEach(({ pdtTrackingId }) => {
        onBotTriggerAnalyticsEvent(pdtTrackingId);
      });
    }
  };

  const handleJSLoaded = () => {
    onBotJSLoadedEvent?.();
  };

  const handleMyraLoginMessage = (payload: string) => {
    if (!payload) {
      return;
    }
    const parsedPayload = JSON.parse(payload);
    onBotLoginEvent?.(parsedPayload);
  };
  const messageListener = (event: MessageEvent) => {
    if (event.origin !== getMyraAppLinkOrigin()) {
      return;
    }

    if (onBotEvent && typeof onBotEvent === "function") {
      onBotEvent(event);
    }
    const { data = {} } = event;
    const { payload, type } = data;
    if (type === MESSAGES.JS_LOADED) {
      handleJSLoaded();
    }
    if (type === MESSAGES.CLOSE_VIEW) {
      if (payload?.error?.type === "RETRY") {
        window.location.reload();
        return;
      }
      closeMyraFrame();
    }
    if (type === MESSAGES.MINIMIZE_VIEW) {
      // Minimize Webview
      setMyraMinimized(true);
    }
    if (type === MESSAGES.TRIGGER_LOGIN) {
      handleMyraLoginMessage(payload);
    }
    if (type === MESSAGES.LOB_LINK_CLICKED) {
      handleLobLinkClicked(payload);
    }
    if (type === MESSAGES.TRIGGER_ANALYTICS) {
      handleTriggerAnalyticsMessage(payload);
    }
    if (type === MESSAGES.GET_META_DATA) {
      if (onBotGetMetaDataEvent) {
        onBotGetMetaDataEvent?.();
      }
    }
    if (type === MESSAGES.ON_CHAT_MESSAGE_UPDATE) {
      const parsedPayload = parsePayload(payload);
      onBotLinkChatEvent?.(parsedPayload);
    }
  };
  useEffect(() => {
    if (showMyraFrame) {
      omnitureService.trackLoadEvent({
        prop67: "myra_tp_render",
      });
      onBotFrameShownEvent?.();
    }
  }, [showMyraFrame, onBotFrameShownEvent]);
  useEffect(() => {
    window.addEventListener("message", messageListener);
    return () => window.removeEventListener("message", messageListener);
  }, [myraMinimized, messageListener]);
  useEffect(() => {
    if (myraFrameRef.current) {
      myraFrameRef.current.style.transition = "";
    }
  }, []);
  const handleMyraOverlayClick = () => {
    if (setMyraMinimized && typeof setMyraMinimized === "function") {
      setMyraMinimized(true);
      omnitureService.trackClickEvent({
        prop54: "trvlplex_closed",
      });
    }
  };
  const handleIframeLoad = () => {
    setIsLoading(false);
  };
  const visibilityState = myraMinimized ? "" : "showIframe";
  let iframeSrc = showMyraFrame ? myraAppLink : "";
  if (canPreWarmMyraFrame) {
    iframeSrc = myraAppLink;
  }
  const minimizeIcon = config.minimizeIcon;
  return (
    <>
      {shouldShowOverlay && !myraMinimized && (
        <div className="tp-overlay" onClick={handleMyraOverlayClick} />
      )}
      {isLoading && showMyraFrame && (
        <div className="tp-loader-container" style={frameStyles || {}}>
          <div className="tp-loader"></div>
        </div>
      )}
      <div
        className={`tp-iframe-container ${
          !isLoading ? "myra-frame-transition" : ""
        } ${visibilityState}`}
        style={frameStyles || {}}
      >
        {_shouldShowMinimizedCTA && (
          <div
            className="tp-close-icon"
            style={{ backgroundImage: `url(${minimizeIcon})` }}
            onClick={handleMyraOverlayClick}
          />
        )}
        <iframe
          style={{ height: "100%", width: "100%" }}
          src={iframeSrc}
          title="myraAssistiveApp"
          className="tpFrame"
          id="myra-assistive-app"
          allow="microphone"
          ref={myraFrameRef}
          onLoad={handleIframeLoad}
        />
      </div>
    </>
  );
};
export default TravelplexFrame;
