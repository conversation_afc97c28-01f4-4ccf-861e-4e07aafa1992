import React, { useEffect, useMemo } from "react";
import TravelplexIcon from "./TravelplexIcon";
import type { BrandType, TravelplexBotIconProps } from "./props";
import { omnitureService } from "./omniture";
import { getUpdatedBotLink } from "./utils";
import { config } from "./config";

import "./style.css";

export type { TravelplexBotIconProps };

const iFrameStyles = {
  height: "89%",
  width: 400,
  boxShadow: "0 1px 4px 0 rgba(0, 0, 0, 0.1)",
  backgroundColor: "white",
};

const TravelplexBotIconWrapper: React.FC<TravelplexBotIconProps> = (props) => {
  const {
    platform,
    params = {},
    trackingData,
    lob,
    onBotEvent = () => {
      return;
    },
    onBotLoginEvent = () => {
      return;
    },
    onBotLOBLinkClickedEvent = () => {
      return;
    },
    onBotLinkChatEvent = () => {
      return;
    },
    onBotGetMetaDataEvent = () => {
      return;
    },
    onBotTriggerAnalyticsEvent = () => {
      return;
    },
    frameStyles = iFrameStyles,
    botStyles = {},
    shouldDoCenterAnimation = false,
    shouldShowOverlay = true,
    shouldAlwaysDoTextScrollAnimation = false,
    persuasions = [],
    shouldShowBotByDefault = false,
    onTravelplexIconClicked = () => {
      return;
    },
    shouldShowMinimizedCTA = true,
    onBotFrameShownEvent = () => {
      return;
    },
    botRef,
  } = props;
  const botLink = config.botLink;
  omnitureService.setCanSendMyraIconOmniture(config.canSendMyraIconOmniture);
  useEffect(() => {
    let trackingDataFromParent;
    if (trackingData?.omnitureTrackingData) {
      trackingDataFromParent = trackingData?.omnitureTrackingData;
    }
    omnitureService.trackLoadEvent({
      prop67: "myra_tp_seen",
      prop54: "trvlplex_displayed",
      ...(trackingDataFromParent && { trackingDataFromParent }),
    });
  }, [trackingData?.omnitureTrackingData]);
  const myraLink = useMemo(
    () => getUpdatedBotLink(botLink, params, platform),
    [botLink, params, platform]
  );
  if (!lob || !platform) {
    return null;
  }

  if (!myraLink) {
    return null;
  }
  return (
    <div className="myraFloatingBar mmt_b2c" style={botStyles}>
      <TravelplexIcon
        myraAppLink={myraLink}
        onBotLoginEvent={onBotLoginEvent}
        onBotTriggerAnalyticsEvent={onBotTriggerAnalyticsEvent}
        onTravelplexIconClicked={onTravelplexIconClicked}
        onBotGetMetaDataEvent={onBotGetMetaDataEvent}
        onBotLOBLinkClickedEvent={onBotLOBLinkClickedEvent}
        shouldShowBotByDefault={shouldShowBotByDefault}
        persuasions={persuasions}
        frameStyles={frameStyles}
        onBotEvent={onBotEvent}
        onBotLinkChatEvent={onBotLinkChatEvent}
        canPreWarmMyraFrame={config.canPreWarmMyraFrame}
        shouldAlwaysDoTextScrollAnimation={shouldAlwaysDoTextScrollAnimation}
        shouldShowOverlay={shouldShowOverlay}
        shouldDoCenterAnimation={shouldDoCenterAnimation}
        lob={lob}
        brand={config.brand as BrandType}
        shouldShowMinimizedCTA={shouldShowMinimizedCTA}
        onBotFrameShownEvent={onBotFrameShownEvent}
        ref={botRef}
      />
    </div>
  );
};

export default React.memo(TravelplexBotIconWrapper);
