{"name": "@travelplex/floating-icon-web", "version": "0.0.8", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"publish": "yarn --frozen-lockfile && yarn build && npm publish", "build": "webpack && tsc --emitDeclarationOnly"}, "dependencies": {"MMT-UI": "^6.3.59"}, "peerDependencies": {"react": ">=16.0.0", "react-dom": ">=16.0.0"}, "devDependencies": {"@types/react": "^16.9.0", "@types/react-dom": "^16.9.0", "css-loader": "3.6.0", "react": "^18.0.0", "react-dom": "^18.0.0", "style-loader": "1.3.0", "tar": "^7.4.3", "ts-loader": "8.0.0", "typescript": "4.x", "webpack": "^4.0.0", "webpack-cli": "^3.3.12"}, "publishConfig": {"registry": "http://nexus3.mmt.com/repository/npm-hosted/"}}