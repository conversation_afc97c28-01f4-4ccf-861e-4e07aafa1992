const path = require('path');

module.exports = {
  entry: './lib/index.tsx',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'index.js',
    library: 'TravelPlexIcon',
    libraryTarget: 'umd',
    umdNamedDefine: true,
  },
  externals: [
    {
      react: {
        commonjs: 'react',
        commonjs2: 'react',
        amd: 'react',
        root: 'React',
      },
      'react-dom': {
        commonjs: 'react-dom',
        commonjs2: 'react-dom',
        amd: 'react-dom',
        root: 'ReactDOM',
      },
    },
    // Handle MMT-UI and all its subpaths as externals
    // function (context, request, callback) {
    //   if (request.startsWith('MMT-UI')) {
    //     // Return the request as external
    //     return callback(null, {
    //       commonjs: request,
    //       commonjs2: request,
    //       amd: request,
    //       root: request,
    //     });
    //   }
    //   callback();
    // },
  ],
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
  },
  module: {
    rules: [
      {
        test: /\.(ts|tsx)$/,
        exclude: /node_modules/,
        use: 'ts-loader',
      },
      {
        test: /\.css$/, // For CSS files
        use: [
          'style-loader', // Inject CSS into the DOM via <style> tags
          'css-loader', // Resolve CSS imports and URLs
        ],
      },
    ],
  },
  mode: 'production',
};
