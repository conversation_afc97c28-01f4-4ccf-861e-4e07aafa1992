{
  "compilerOptions": {
    "types": ["react", "react-dom"],
    "target": "ES5",
    "module": "ESNext",
    "jsx": "react",
    "declaration": true,
    "declarationDir": "dist",
    "outDir": "dist",
    "esModuleInterop": true,
    "strict": false,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "lib": ["dom", "dom.iterable", "esnext", "es2017"], // Add this line,
    "noImplicitAny": false
  },
  "include": ["lib/**/*", "declare.d.ts"],
  "exclude": ["node_modules", "dist"]
}
