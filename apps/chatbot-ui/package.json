{"name": "@travelplex/react-native", "version": "0.2.9", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"publish": "yarn --frozen-lockfile && yarn build && npm publish --tag alpha", "copy:assets": "echo pwd=`pwd` && mkdir -p dist/assets && cp -R ./src/assets/* dist/assets", "test": "eslint --fix .", "build": "rm -rf dist && yarn copy:assets && tsc -p tsconfig.json --outDir dist/", "dev:android": "sh run_dev.sh android", "dev:ios": "sh run_dev.sh ios", "dev:web": "sh run_dev.sh web", "watch:mmt": "sh ./scripts/watch.sh $MMT_RN_DIR", "watch:mmt-ios": "sh ./scripts/watch.sh $MMT_IOS_RN_DIR"}, "publishConfig": {"registry": "http://nexus3.mmt.com/repository/npm-hosted/"}, "peerDependencies": {"@react-native-async-storage/async-storage": ">=1.19.3", "@react-native-community/masked-view": ">=0.1.7", "react": ">=18", "react-native": ">=0.72", "react-native-fast-image": ">=8.5.2", "react-native-fast-shadow": ">=0.1.1", "react-native-gesture-handler": ">=2.16.2", "react-native-linear-gradient": "2.8.2", "react-native-markdown-display": ">=7.0.2", "react-native-reanimated": ">=3.15.0", "react-native-reanimated-carousel": ">=^3.5.1", "react-native-safe-area-context": ">=3.3.2", "react-native-shimmer-placeholder": ">=1.0.35", "react-native-svg": ">=12.1.1", "react-native-uuid": ">=2.0.3", "zustand": ">=5.0.0-rc.2", "react-cookies": ">=0.1.0"}, "devDependencies": {"@gorhom/bottom-sheet": "5.0.1", "@mmt/event-logger": "^2.0.16", "@react-native-async-storage/async-storage": "^1.19.3", "@react-native-community/masked-view": "0.1.7", "@react-native/eslint-config": "0.72.2", "@types/markdown-it": "^14.1.2", "@types/react": "^18", "@types/react-native": "^0.72", "eslint-plugin-jest": "26.5.3", "eslint-plugin-react-native": "4.1.0", "eslint-plugin-unused-imports": "^4.1.4", "react-native-fast-image": "8.5.2", "react-native-fast-shadow": "^0.1.1", "react-native-gesture-handler": "2.16.2", "react-native-image-colors": "^2.4.0", "react-native-linear-gradient": "2.8.2", "react-native-markdown-display": "7.0.2", "react-native-reanimated": "3.15.0", "react-native-reanimated-carousel": "3.5.1", "react-native-shimmer-placeholder": "1.0.35", "react-native-svg": "12.1.1", "zustand": "^5.0.0-rc.2", "react-cookies": "0.1.0"}, "dependencies": {"immer": "^10.1.1", "mutative": "^1.0.11", "react-native-image-colors": "^2.4.0", "react-native-popup-menu": "^0.16.1", "react-native-safe-area-context": "3.3.2", "react-native-uuid": "2.0.3", "zustand-mutative": "^1.0.5"}}