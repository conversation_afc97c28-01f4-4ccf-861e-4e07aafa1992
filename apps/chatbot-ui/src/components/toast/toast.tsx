import React, { useEffect } from 'react';
import {
  GestureResponderEvent,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import Animated, { SlideInDown, SlideOutDown } from 'react-native-reanimated';
import { COLORS, FONTS, Z_INDEX } from '../../constants/globalStyles';
import { create } from 'zustand';

export type Duration = 'short' | 'long' | number;
export type ToastAction = {
  text: string;
  onPress: (event: GestureResponderEvent) => void;
};

const SHORT_DURATION = 3000;
const LONG_DURATION = 5000;

type ToastState = {
  show: boolean;
  message: string | null;
  action: ToastAction | null;
  duration: Duration;
  showToast: (
    message: string,
    action?: ToastAction | null,
    duration?: Duration,
  ) => void;
  hideToast: () => void;
};

const useToastStore = create<ToastState>((set) => ({
  show: false,
  message: null,
  action: null,
  duration: 'short',
  showToast: (message, action = null, duration = 'short') =>
    set({ show: true, message, action, duration }),
  hideToast: () => set({ show: false, message: null, action: null }),
}));

export const Toast = () => {
  const { show, message, action, duration, hideToast } = useToastStore();
  useEffect(() => {
    const rawDuration =
      typeof duration === 'number'
        ? duration
        : duration === 'short'
          ? SHORT_DURATION
          : LONG_DURATION;
    if (show) {
      setTimeout(() => {
        useToastStore.getState().hideToast();
      }, rawDuration);
    }
  }, [show, duration]);

  if (!show) {
    return null;
  }

  return (
    <Animated.View
      entering={SlideInDown.duration(500)}
      exiting={SlideOutDown.duration(500)}
      style={styles.container}
    >
      <Text>
        <Text style={styles.message}>{message}</Text>
      </Text>
      {action ? (
        <TouchableOpacity
          hitSlop={hitSlop}
          onPress={(e) => {
            action.onPress(e);
            hideToast();
          }}
        >
          <Text style={styles.action}>{action.text}</Text>
        </TouchableOpacity>
      ) : null}
    </Animated.View>
  );
};

Toast.show = (
  message: string,
  action: ToastAction | null = null,
  duration: Duration = 'short',
) => {
  useToastStore.getState().showToast(message, action, duration);
};

const hitSlop = {
  top: 10,
  bottom: 10,
  left: 10,
  right: 10,
};
const styles = StyleSheet.create({
  container: {
    left: 8,
    right: 8,
    bottom: 32,
    justifyContent: 'space-between',
    backgroundColor: '#2a2a2a',
    alignItems: 'center',
    paddingHorizontal: 16,
    position: 'absolute',
    flexDirection: 'row',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 12,
    },
    shadowOpacity: 0.58,
    shadowRadius: 16.0,
    borderRadius: 6,
    elevation: 25,
    height: 48,
    zIndex: Z_INDEX.TOAST,
  },
  message: {
    color: '#fff',
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
  },
  action: {
    color: COLORS.WHITE,
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_700,
  },
});
