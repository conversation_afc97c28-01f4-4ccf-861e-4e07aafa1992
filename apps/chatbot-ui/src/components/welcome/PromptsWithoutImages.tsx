import React, { useCallback } from 'react';
import { Text, StyleSheet } from 'react-native';
import { ImageView } from '../image/ImageView';
import { COLORS, FONTS } from '../../constants/globalStyles';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import { useMessageAction } from '../../store/messages';
import { TouchableOpacity } from '@gorhom/bottom-sheet';
import PlaceholderIcon from '../../assets/placeHolderIcon';
import { Analytics, TrackingEvent } from '../../analytics';

interface PromptsWithoutImagesProps {
  data: PromptsWithoutImages['examplePromptInfos'];
}
export function PromptsWithoutImages({ data: prompts }: PromptsWithoutImagesProps) {
  const { selectInitialPrompt } = useMessageAction();
  const onPromptSelected = useCallback(
    (isClickable: boolean, description: string, isIconPresent: boolean) => {
      if (isClickable === false) {
        return;
      }
      if (description) {
        trackPDTEvent({
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: isIconPresent
            ? eventValueSchema.WELCOME_SUGGESTIVE_PROMPTS_WITH_ICON
            : 'welcome_suggestive_prompts_without_icon',
        });
        Analytics.trackClickEvent(
          TrackingEvent.payload_PromptWithoutImagesClicked(description),
        );
        selectInitialPrompt(description);
      }
    },
    [selectInitialPrompt],
  );
  if (!Array.isArray(prompts) || prompts.length === 0) {
    return null;
  }
  return prompts.map((prompt: PromptsWithoutImages['examplePromptInfos'][0]) => (
    <TouchableOpacity
      key={prompt.description}
      style={styles.promptContainer}
      onPress={() =>
        onPromptSelected(prompt?.clickable, prompt?.description, !!prompt?.icon)
      }
      disabled={prompt?.clickable === false}
    >
      {prompt?.icon ? (
        <ImageView
          source={{ uri: prompt.icon }}
          style={styles.promptIcon}
          showMmtPlaceholder={false}
        />
      ) : (
        <PlaceholderIcon />
      )}
      {prompt?.description && (
        <Text style={styles.promptDescription}>{prompt.description}</Text>
      )}
    </TouchableOpacity>
  ));
}

const styles = StyleSheet.create({
  promptContainer: {
    flexDirection: 'row',
    gap: 10,
    borderWidth: 1,
    borderColor: COLORS.ORANGE_VAR_8,
    paddingVertical: 16,
    paddingHorizontal: 12,
    width: '100%',
    borderRadius: 16,
    alignItems: 'center',
    marginBottom: 10,
    backgroundColor: COLORS.GREY_VAR_8,
  },
  promptDescription: {
    color: COLORS.TEXT_HIGH_EMPHASIS,
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    flexShrink: 1,
    flexWrap: 'wrap',
    lineHeight: 16,
  },
  promptIcon: {
    height: 32,
    width: 32,
    borderRadius: 6,
  },
});
