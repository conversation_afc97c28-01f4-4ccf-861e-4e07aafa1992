import React, { useEffect, useRef, useState } from 'react';
import { Dimensions, StyleSheet, View } from 'react-native';
import { COLORS, FONTS } from '../../constants/globalStyles';
// import { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { useConfigSettings } from '../../store/messages/newChatView';
import { MessageListLoader } from '../message-list-loader';
import Animated, {
  Easing,
  FadeInDown,
  Keyframe,
  LinearTransition,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import GradientText from '../base/GradientText';
import { WelcomeCards } from './WelcomeCards';
import MyraIcon from '../EntryPoint/MyraIcon';
import { MarkdownText } from '../message/MarkdownText';
import { ScrollView } from 'react-native-gesture-handler';
import { useMessageStore } from '../../store/messages/messageStore';

const { height: screenHeight } = Dimensions.get('window');

const Welcome = () => {
  const { isLoading, data } = useConfigSettings();
  const {
    introMsg,
    examples,
    isNewUser,
    betaMessage,
    content,
    introMsgSubTitleNewUser: introMsgSubTitle,
    introMsgSubTitleMarkdown,
  } = data || {};
  const promptPDTSent = useRef<boolean>(false);
  const { updateConversationById } = useMessageStore();
  const [showIcon, setShowIcon] = useState(false);
  const [showWelcomeMessage, setShowWelcomeMessage] = useState(false);
  const [showContent, setShowContent] = useState(false);
  const marginTop = useSharedValue(screenHeight * 0.2);
  const welcomeMessageSize = useSharedValue(1.33);
  const scale = useSharedValue(1);
  useEffect(() => {
    updateConversationById('draft', {
      hideInput: data?.content?.hideInput,
    });
  }, [data?.content?.hideInput, updateConversationById]);
  useEffect(() => {
    if (isLoading) {
      return;
    }
    setShowIcon(true);
  }, [isLoading]);

  /**
   * @description: Function to start the animation
   * TODO_VS: add all the animation timings in a single constant config
   * TODO_VS: explore using withDelay and withSequence for better readability
   * TODO_VS: use withDelay instead of setTimeout
   */
  const startAnim = (delay: number) => {
    setTimeout(() => {
      marginTop.value = withTiming(screenHeight * 0.1, {
        duration: 200,
        easing: Easing.linear,
      });
      setShowWelcomeMessage(true);
      scale.value = withTiming(
        0.7,
        {
          duration: 200,
          easing: Easing.linear,
        },
        () => {
          marginTop.value = withTiming(
            0,
            {
              duration: 200,
              easing: Easing.linear,
            },
            () => {
              runOnJS(animation2)(1000);
            },
          );
        },
      );
    }, delay);
  };

  const animation2 = (delay: number) => {
    setTimeout(() => {
      welcomeMessageSize.value = withTiming(1, {
        duration: 100,
        easing: Easing.linear,
      });
      setShowIcon(false);
      setShowContent(true);
    }, delay);
  };

  const containerStyle = useAnimatedStyle(
    () => ({
      marginTop: marginTop.value,
    }),
    [marginTop],
  );

  const animatedStyle = useAnimatedStyle(
    () => ({
      transform: [{ scale: scale.value }],
    }),
    [scale],
  );

  const welcomeAnimatedStyle = useAnimatedStyle(
    () => ({
      transform: [{ scale: welcomeMessageSize.value }],
    }),
    [welcomeMessageSize],
  );

  const exitingKeyframe = new Keyframe({
    0: {
      transform: [{ scale: 0.7 }],
      opacity: 1,
    },
    100: {
      transform: [{ scale: 0 }],
      opacity: 0,
    },
  });

  useEffect(() => {
    if (examples && !promptPDTSent.current) {
      promptPDTSent.current = true;
      trackPDTEvent({
        eventName: eventNameSchema.CHAT_INTERACTED,
        eventType: eventTypeSchema.ACTION,
        eventValue: examples?.[0]?.image
          ? eventValueSchema.WELCOME_PROMPTS_IMAGE
          : 'welcome_prompts_text',
      });
    }
  }, [examples]);

  if (isLoading) {
    return <MessageListLoader />;
  }

  const subTitle = introMsgSubTitleMarkdown || introMsgSubTitle;

  return (
    <ScrollView style={styles.scrollViewStyle}>
      <Animated.View style={[styles.container, containerStyle]}>
        {showIcon && (
          <Animated.View
            entering={FadeInDown.withInitialValues({
              opacity: 0,
              transform: [{ translateY: 100 }],
            })
              .duration(500)
              .withCallback(() => {
                runOnJS(startAnim)(700);
              })}
            exiting={exitingKeyframe.duration(100)}
            layout={LinearTransition}
            style={animatedStyle}
          >
            <MyraIcon
              showNewMessage={false}
              overrideIconStyle={styles.botIcon}
              overrideIconContainerStyle={styles.botIconContainer}
            />
          </Animated.View>
        )}
        {showWelcomeMessage && (
          <WelcomeMessage
            title={introMsg as string}
            subtitle={subTitle}
            betaMessage={betaMessage}
            isNewUser={isNewUser}
            animatedStyle={welcomeAnimatedStyle}
          />
        )}
        {showContent && content && <WelcomeCards content={content} />}
      </Animated.View>
    </ScrollView>
  );
};

function WelcomeMessage({
  title,
  subtitle,
  isNewUser,
  animatedStyle,
}: {
  title: string;
  subtitle: string | undefined | null;
  betaMessage: string | undefined | null;
  isNewUser?: boolean;
  animatedStyle: any;
}) {
  return (
    <Animated.View
      entering={FadeInDown.duration(100)}
      layout={LinearTransition}
      style={{ flex: 1 }}
    >
      <View
        style={[
          styles.textContainer,
          {
            alignSelf: 'center',
            marginTop: 32,
            maxWidth: 300,
            // gap: 4,
            marginBottom: 14,
          },
        ]}
      >
        <Animated.View style={animatedStyle}>
          <GradientText
            style={styles.gradientTextWelcome}
            gradientColors={['#51AFE6', '#355FF2', '#11287A']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            {title}
          </GradientText>
        </Animated.View>
        {!isNewUser && subtitle && (
          <MarkdownText text={subtitle} lineHeight={18} marginTop={2} />
        )}
      </View>
      {!!subtitle && isNewUser && (
        <View style={[styles.textContainer, styles.secondaryTextContainer]}>
          <MarkdownText text={subtitle} lineHeight={18} marginTop={5} />
        </View>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  scrollViewStyle: {
    height: '100%',
    flex: 1,
    flexDirection: 'column',
  },
  container: {
    position: 'relative',
    flex: 1,
    height: '100%',
    flexDirection: 'column',
    alignItems: 'center',
    paddingBottom: 70,
  },
  botIconContainer: {
    padding: 11,
  },
  botIcon: {
    height: 78,
    width: 78,
    backgroundColor: COLORS.WHITE,
    borderRadius: 39,
  },
  textContainer: {
    maxWidth: 300,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    alignSelf: 'center',
    marginTop: -8, // TODO: Need to understand why this space is coming and remove negative margin
  },
  secondaryTextContainer: {
    marginBottom: 20,
  },
  secondaryText: {
    fontSize: 14,
    lineHeight: 18,
    color: COLORS.TEXT_LOW_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_400,
    textAlign: 'center',
  },
  descriptionCell: {
    width: 235,
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginTop: 10,
    marginBottom: 30,
  },
  descriptionText: {
    fontSize: 14,
    lineHeight: 16,
    minHeight: 48,
    textAlignVertical: 'center',
    fontFamily: FONTS.FONT_FAMILY_400,
    textAlign: 'center',
    color: COLORS.BLACK,
  },
  loaderContainer: {
    flexDirection: 'column',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  maskView: {
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    alignSelf: 'center',
    width: 250,
    marginTop: 32,
  },
  gradientTextWelcome: {
    fontSize: 36,
    lineHeight: 40,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: COLORS.BLACK,
  },
});

export default Welcome;
