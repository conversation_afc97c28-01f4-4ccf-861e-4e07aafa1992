import React, { useCallback } from 'react';
import Animated, { FadeInDown, LinearTransition } from 'react-native-reanimated';
import { WelcomeCardDetailed } from './WelcomeCardDetailed';
import { WelcomeCardCompact } from './WelcomeCardCompact';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import { useMessageAction } from '../../store/messages';
import { Analytics, TrackingEvent } from '../../analytics';

interface PromptsWithImagesProps {
  data: PromptsWithImagesInContents['examplePromptInfos'];
}
export const PromptsWithImages = ({ data: prompts }: PromptsWithImagesProps) => {
  const { selectInitialPrompt } = useMessageAction();
  const onPromptSelected = useCallback(
    (clickable: boolean, index: number) => {
      if (clickable === false) {
        return;
      }
      trackPDTEvent({
        eventName: eventNameSchema.CHAT_INTERACTED,
        eventType: eventTypeSchema.ACTION,
        eventValue: prompts?.[0]?.image
          ? eventValueSchema.WELCOME_PROMPTS_IMAGE
          : 'welcome_prompts_text',
      });
      Analytics.trackClickEvent(
        TrackingEvent.payload_WelcomePromptClicked(prompts?.[index]?.description),
      );
      const description = prompts?.[index]?.description;
      if (description) {
        selectInitialPrompt(description);
      }
    },
    [prompts, selectInitialPrompt],
  );
  if (!prompts || !prompts.length) {
    return null;
  }
  return (
    <Animated.View
      style={{
        width: '100%',
        alignItems: 'center',
        flex: 1,
        marginBottom: '15%',
      }}
      entering={FadeInDown.withInitialValues({
        transform: [{ translateY: 100 }],
      }).duration(300)}
      layout={LinearTransition}
    >
      {prompts.map((item, index) => {
        const CardComponent =
          item.size === 'L' ? WelcomeCardDetailed : WelcomeCardCompact;
        return (
          <CardComponent
            key={index}
            data={item}
            index={index}
            onCardClick={() => onPromptSelected(item.clickable, index)}
            isExpanded={index === 0}
            isClickable={item.clickable}
          />
        );
      })}
    </Animated.View>
  );
};
