import React from 'react';
import { View, Platform } from 'react-native';
import ConversationCard from '../message/cards/ConversationCard';
import { useConversationsHistory } from '../../store/messages';
import { PromptsWithoutImages } from './PromptsWithoutImages';
import { GetNewChatViewResponse } from '../../network/api/newChatViewApi';
import { PromptsWithImages } from './PromptsWithImages';
import { ScrollView } from 'react-native-gesture-handler';
import HelpingHandCards from '../message/helping-hands/HelpingHandCards';
import { useConfigSettings } from '../../store/messages/newChatView';
import { isEmpty } from 'lodash';
import { useHandleConversationClick } from '../../store/messages/useConversationsHistory';

interface WelcomeCardsProps {
  content: GetNewChatViewResponse['content'];
}

interface RecentChatsProps {
  data: ConversationHistoryGroup;
}
function RecentChats({ data: _data }: RecentChatsProps) {
  const handleConversationClick = useHandleConversationClick();
  if (!Array.isArray(_data) || _data.length === 0) {
    return null;
  }
  let value = _data.map((conversation: ConversationHistoryItem, index: number) => ({
    ...conversation,
    title: conversation.title || `New Chat ${index + 1}`,
  }));
  return (
    <ScrollView>
      <View style={{ flexDirection: 'column' }}>
        {value.map((conversation: any) => (
          <ConversationCard
            key={conversation.conversationId}
            conversation={conversation}
            handleConversationClick={() => {
              handleConversationClick(
                conversation.conversationId,
                conversation.title,
              );
            }}
            compact={false}
          />
        ))}
      </View>
    </ScrollView>
  );
}

const bottomComponentMap: Record<ContentTypes, React.FC<{ data: any }>> = {
  RECENT_CHATS: RecentChats as React.FC<RecentChatsProps>,
  PROMPTS_WITHOUT_IMAGES: PromptsWithoutImages,
  CONVERSATION_VIEW: () => null, // Placeholder for CONVERSATION_VIEW
  PROMPTS_WITH_IMAGES: PromptsWithImages, // Placeholder for PROMPTS_WITH_IMAGES
  CHAT_HISTORY: () => null,
  HELPING_HAND_CARDS: () => null,
};

const bottomComponentDataNodeMap: Record<
  ContentTypes,
  'examplePromptInfos' | 'conversations' | ''
> = {
  RECENT_CHATS: 'conversations',
  PROMPTS_WITHOUT_IMAGES: 'examplePromptInfos',
  CONVERSATION_VIEW: '', // Placeholder value for CONVERSATION_VIEW
  PROMPTS_WITH_IMAGES: 'examplePromptInfos', // Placeholder value for PROMPTS_WITH_IMAGES
  CHAT_HISTORY: '',
  HELPING_HAND_CARDS: '', // No longer using examplePromptInfos
};

export const WelcomeCards: React.FC<WelcomeCardsProps> = ({
  content,
}: WelcomeCardsProps) => {
  if (!content) {
    return null;
  }

  const { isLoading, error } = useConfigSettings();

  // Handle loading and error states first
  if (isLoading) {
    return null; // Or a loading indicator
  }

  if (error) {
    console.error('Error loading config settings:', error);
    return null; // Or an error message
  }

  // Add null and error handling with optional chaining and default empty array
  const cardsData = content.value?.value?.templateInfo?.payload || [];

  // Check if content type is HELPING_HAND_CARDS and has the correct structure for HelpingHandCards
  if (content.type === 'HELPING_HAND_CARDS' && !isEmpty(cardsData)) {
    return <HelpingHandCards cardsData={cardsData} />;
  }

  const { type, value } = content;
  if (!type) {
    return null;
  }

  const BottomComponent = bottomComponentMap[type];
  const dataNode: 'examplePromptInfos' | 'conversations' | '' =
    bottomComponentDataNodeMap[type];
  let valueData:
    | ConversationHistoryGroup['conversations']
    | PromptsWithoutImages['examplePromptInfos']
    | PromptsWithImagesInContents['examplePromptInfos']
    | undefined;
  if (dataNode === 'examplePromptInfos' && 'examplePromptInfos' in value) {
    valueData = value.examplePromptInfos;
  } else if (dataNode === 'conversations' && 'conversations' in value) {
    valueData = value.conversations;
  } else {
    valueData = undefined;
  }
  if (!BottomComponent || !valueData) {
    return null;
  }
  return <BottomComponent data={valueData} />;
};
