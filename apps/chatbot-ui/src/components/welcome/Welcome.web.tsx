import React, { useEffect, useRef, useState } from 'react';
import { Animated, Dimensions, Easing, StyleSheet, Text, View } from 'react-native';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { useConfigSettings } from '../../store/messages/newChatView';
import { MessageListLoader } from '../message-list-loader';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import GradientText from '../base/GradientText';
import { WelcomeCards } from './WelcomeCards';
import MyraIcon from '../EntryPoint/MyraIcon';
import { MarkdownText } from '../message/MarkdownText';

const { height: screenHeight } = Dimensions.get('window');

// Create animated components
const AnimatedView = Animated.createAnimatedComponent(View);

const Welcome = () => {
  const { isLoading, data } = useConfigSettings();
  const {
    introMsg,
    examples,
    isNewUser,
    betaMessage,
    content,
    introMsgSubTitleNewUser: introMsgSubTitle,
    introMsgSubTitleMarkdown,
  } = data || {};
  const promptPDTSent = useRef<boolean>(false);

  const [showIcon, setShowIcon] = useState(false);
  const [showWelcomeMessage, setShowWelcomeMessage] = useState(false);
  const [showContent, setShowContent] = useState(false);

  // Animation values
  const marginTop = useRef(new Animated.Value(screenHeight * 0.2)).current;
  const welcomeMessageSize = useRef(new Animated.Value(1.33)).current;
  const scale = useRef(new Animated.Value(1)).current;
  const opacity = useRef(new Animated.Value(1)).current;
  const entryOpacity = useRef(new Animated.Value(0)).current;
  const entryTranslateY = useRef(new Animated.Value(100)).current;
  const animation2 = React.useCallback(
    (delay: number) => {
      setTimeout(() => {
        try {
          Animated.timing(welcomeMessageSize, {
            toValue: 1,
            duration: 100,
            useNativeDriver: true,
            easing: Easing.linear,
          }).start();

          // Exit animation for the icon
          Animated.parallel([
            Animated.timing(scale, {
              toValue: 0,
              duration: 100,
              useNativeDriver: true,
              easing: Easing.linear,
            }),
            Animated.timing(opacity, {
              toValue: 0,
              duration: 100,
              useNativeDriver: true,
              easing: Easing.linear,
            }),
          ]).start(() => {
            setShowIcon(false);
            setShowContent(true);
          });
        } catch (error) {
          // Log error or handle it appropriately
          // Example: send error to a monitoring service
          setShowIcon(false);
          setShowContent(true);
        }
      }, delay);
    },
    [welcomeMessageSize, scale, opacity],
  );
  const startAnim = React.useCallback(
    (delay: number) => {
      setTimeout(() => {
        // First part of animation
        try {
          Animated.parallel([
            Animated.timing(marginTop, {
              toValue: screenHeight * 0.1,
              duration: 200,
              useNativeDriver: false, // marginTop can't use native driver
              easing: Easing.linear,
            }),
            Animated.timing(scale, {
              toValue: 0.7,
              duration: 200,
              useNativeDriver: true,
              easing: Easing.linear,
            }),
          ]).start(() => {
            setShowWelcomeMessage(true);

            // Second part of animation
            Animated.timing(marginTop, {
              toValue: 0,
              duration: 200,
              useNativeDriver: false,
              easing: Easing.linear,
            }).start(() => {
              // Final animation after a delay
              animation2(1000);
            });
          });
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error('Error in startAnim:', error);
          // Fallback to ensure UI updates even if animation fails
          setShowWelcomeMessage(true);
          setTimeout(() => animation2(0), 500);
        }
      }, delay);
    },
    [marginTop, scale, animation2],
  );

  useEffect(() => {
    if (isLoading) {
      return;
    }
    setShowIcon(true);

    // Reset animation values when component mounts
    try {
      entryOpacity.setValue(0);
      entryTranslateY.setValue(100);
      scale.setValue(1);
      marginTop.setValue(screenHeight * 0.2);
      welcomeMessageSize.setValue(1.33);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error resetting animation values:', error);
    }

    // Start entry animation
    Animated.timing(entryOpacity, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
      easing: Easing.out(Easing.ease),
    }).start();

    Animated.timing(entryTranslateY, {
      toValue: 0,
      duration: 500,
      useNativeDriver: true,
      easing: Easing.out(Easing.ease),
    }).start(() => {
      // After entry animation completes, start the main animation sequence
      startAnim(700);
    });
  }, [
    isLoading,
    entryOpacity,
    entryTranslateY,
    scale,
    marginTop,
    welcomeMessageSize,
    startAnim,
  ]);

  useEffect(() => {
    if (examples && !promptPDTSent.current) {
      promptPDTSent.current = true;
      try {
        trackPDTEvent({
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: examples?.[0]?.image
            ? eventValueSchema.WELCOME_PROMPTS_IMAGE
            : 'welcome_prompts_text',
        });
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Error tracking PDT event:', error);
      }
    }
  }, [examples]);

  if (isLoading) {
    return <MessageListLoader />;
  }

  return (
    <BottomSheetScrollView style={styles.scrollViewStyle}>
      <Animated.View style={[styles.container, { marginTop }]}>
        {showIcon && (
          <AnimatedView
            style={[
              styles.iconContainer,
              {
                opacity,
                transform: [{ scale }, { translateY: entryTranslateY }],
              },
            ]}
          >
            <MyraIcon
              showNewMessage={false}
              overrideIconStyle={styles.botIcon}
              overrideIconContainerStyle={styles.botIconContainer}
            />
          </AnimatedView>
        )}
        {showWelcomeMessage && (
          <WelcomeMessage
            title={introMsg as string}
            subtitle={introMsgSubTitleMarkdown || introMsgSubTitle}
            betaMessage={betaMessage}
            isNewUser={isNewUser}
            welcomeMessageSize={welcomeMessageSize}
          />
        )}
        {showContent && content && (
          <Animated.View
            style={{
              width: '100%',
              alignItems: 'center',
              flex: 1,
              opacity: showContent ? 1 : 0,
            }}
          >
            <WelcomeCards content={content} />
          </Animated.View>
        )}
      </Animated.View>
    </BottomSheetScrollView>
  );
};

function WelcomeMessage({
  title,
  subtitle,
  isNewUser,
  welcomeMessageSize,
}: {
  title: string;
  subtitle: string | undefined | null;
  betaMessage: string | undefined | null;
  isNewUser?: boolean;
  welcomeMessageSize: Animated.Value;
}) {
  // Fade in animation for welcome message
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    try {
      Animated.timing(opacity, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
        easing: Easing.linear,
      }).start();
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error in welcome message animation:', error);
    }
  }, [opacity]);

  return (
    <Animated.View style={{ opacity }}>
      <View
        style={[
          styles.textContainer,
          {
            alignSelf: 'center',
            marginTop: 10,
            maxWidth: 300,
            marginBottom: 14,
          },
        ]}
      >
        <Animated.View
          style={{
            transform: [{ scale: welcomeMessageSize }],
          }}
        >
          <GradientText
            style={styles.gradientTextWelcome}
            gradientColors={['#51AFE6', '#355FF2', '#11287A']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            {title}
          </GradientText>
        </Animated.View>
        {!isNewUser && subtitle && (
          <MarkdownText text={subtitle} lineHeight={18} marginTop={5} />
        )}
      </View>
      {!!subtitle && isNewUser && (
        <View style={[styles.textContainer, styles.secondaryTextContainer]}>
          <MarkdownText text={subtitle} lineHeight={18} marginTop={5} />
        </View>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  scrollViewStyle: {
    height: '100%',
    flex: 1,
    flexDirection: 'column',
  },
  container: {
    position: 'relative',
    flex: 1,
    height: '100%',
    flexDirection: 'column',
    alignItems: 'center',
    paddingBottom: 70,
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  botIconContainer: {
    padding: 11,
  },
  botIcon: {
    height: 78,
    width: 78,
    backgroundColor: COLORS.WHITE,
    borderRadius: 39,
  },
  textContainer: {
    maxWidth: 300,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    alignSelf: 'center',
    marginTop: -8,
  },
  secondaryTextContainer: {
    marginBottom: 20,
  },
  secondaryText: {
    fontSize: 14,
    lineHeight: 18,
    color: COLORS.TEXT_LOW_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_400,
    textAlign: 'center',
  },
  gradientTextWelcome: {
    fontSize: 36,
    lineHeight: 40,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: COLORS.BLACK,
  },
});

export default Welcome;
