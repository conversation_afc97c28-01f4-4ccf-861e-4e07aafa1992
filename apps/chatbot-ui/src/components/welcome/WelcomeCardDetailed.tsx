import React, { FC, useCallback } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { LinearGradient } from 'react-native-linear-gradient';
import { TouchableFeedback } from '../base/TouchableFeeback';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { ImageView } from '../image/ImageView';

type WelcomeCardDetailedProps = {
  data: {
    description?: string;
    image: string;
  };
  onCardClick: (index: number) => void;
  isExpanded: boolean;
  index: number;
  isClickable: boolean;
};

export const WelcomeCardDetailed: FC<WelcomeCardDetailedProps> = (props) => {
  const {
    onCardClick: onCardClickProp,
    data,
    index,
    isClickable = true,
  } = props || {};
  const { description, image } = data || {};
  const onCardClick = useCallback(() => {
    onCardClickProp(index);
  }, [index, onCardClickProp]);

  return (
    <TouchableFeedback
      onPress={onCardClick}
      style={styles.welcomeContainer}
      containerStyle={styles.WelcomeCardDetailed}
      disabled={isClickable === false}
    >
      <View style={styles.imageContainer}>
        <ImageView
          style={styles.image}
          containerStyle={styles.image}
          source={{ uri: image }}
        />
        <LinearGradient
          colors={[
            'rgba(255,255,255,0)',
            'rgba(255,255,255,0.7)',
            'rgba(255,255,255,0.9)',
          ]}
          style={styles.imageGradient}
        />
      </View>
      <View style={styles.cardBody}>
        <View style={styles.titleSection}>
          <Text style={styles.title}>{description}</Text>
        </View>
      </View>
    </TouchableFeedback>
  );
};

const CARD_WIDTH = '70%';
const CARD_IMG_HEIGHT = 166;
const styles = StyleSheet.create({
  welcomeContainer: {
    flex: 1,
    borderRadius: 20,
    overflow: 'hidden',
  },
  borderView: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 20,
    borderColor: COLORS.GREY_VAR_5,
    backgroundColor: COLORS.WHITE,
    overflow: 'hidden',
    ...StyleSheet.absoluteFillObject,
  },
  WelcomeCardDetailed: {
    flex: 1,
    overflow: 'hidden',
    borderRadius: 20,
    width: CARD_WIDTH,
  },
  cardBody: {
    flex: 1,
    padding: 12,
    position: 'absolute',
    bottom: 10,
    left: 10,
    right: 10,
    backgroundColor: COLORS.WHITE,
    borderRadius: 15,
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  image: {
    width: '100%',
    height: CARD_IMG_HEIGHT,
    backgroundColor: COLORS.TEXT_DEFAULT,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    overflow: 'hidden',
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: CARD_IMG_HEIGHT,
  },
  imageGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    height: 80,
  },
  titleSection: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 0,
  },
  title: {
    flex: 1,
    paddingRight: 8,
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
  },
});
