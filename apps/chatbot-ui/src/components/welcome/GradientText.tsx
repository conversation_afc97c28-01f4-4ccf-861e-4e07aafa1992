import React from 'react';
import { Text, TextProps } from 'react-native';
import MaskedView from '@react-native-community/masked-view';
import LinearGradient from 'react-native-linear-gradient';

type GradientProps = {
  style?: TextProps['style'];
  gradientColors?: string[];
  children: string;
  start?: { x: number; y: number };
  end?: { x: number; y: number };
};

const DEFAULT_GRADIENT_COLORS = ['#5500FF', '#2493EB'];
const DEFAULT_START = { x: 0, y: 0 };
const DEFAULT_END = { x: 1, y: 0 };

const GradientText = (props: GradientProps) => {
  const {
    start = DEFAULT_START,
    end = DEFAULT_END,
    gradientColors = DEFAULT_GRADIENT_COLORS,
  } = props || {};
  return (
    <MaskedView maskElement={<Text {...props} />}>
      <LinearGradient colors={gradientColors} start={start} end={end}>
        <Text {...props} style={[props.style, { opacity: 0 }]} />
      </LinearGradient>
    </MaskedView>
  );
};

export default GradientText;
