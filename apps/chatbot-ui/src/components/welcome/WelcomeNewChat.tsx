import React, { useEffect } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useConfigSettings } from '../../store/messages/newChatView';
import { useAppStateStore } from '../../store/app';
import GradientText from '../base/GradientText';
import RecentChatIcon from '../../assets/RecentChatIcon';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { MessageListLoader } from '../message-list-loader';
import { WelcomeCards } from './WelcomeCards';
import { GetNewChatViewResponse } from '../../network/api/newChatViewApi';
import { INPUT_BOX_HEIGHT } from '../../const';
import { LOB } from '../../constants/lob';
import { MarkdownText } from '../message/MarkdownText';
import { isPlatformWeb } from '../../utils/getPlatform';
import { ScrollView } from 'react-native-gesture-handler';
import { useMessageStore } from '../../store/messages/messageStore';

interface WelcomeMessageProps {
  title: string;
  subTitle: string;
}

const WelcomeMessage: React.FC<WelcomeMessageProps> = ({ title, subTitle }) => (
  <View>
    <GradientText
      gradientColors={['#51AFE6', '#355FF2', '#11287A']}
      style={styles.title}
      start={{ x: 0, y: 0 }}
      end={{ x: 0.65, y: 0 }}
    >
      {title}
    </GradientText>
    <MarkdownText text={subTitle} lineHeight={18} marginTop={5} />
  </View>
);

const IconMap: Record<string, React.FC<{ value: any }>> = {
  RECENT_CHATS: RecentChatIcon,
};

interface WelcomeNewChatContentProps {
  content: GetNewChatViewResponse['content'];
  lob: string;
}

const WelcomeNewChatContent: React.FC<WelcomeNewChatContentProps> = ({
  content,
  lob,
}) => {
  if (!content) return null;

  const { value, type } = content;
  const IconToShow = 'titleIcon' in value ? IconMap[value.titleIcon] : undefined;
  const title = value?.title || content.title;

  return (
    <View
      style={[
        { flex: 1 },
        type !== 'PROMPTS_WITH_IMAGES' && {
          marginTop: '10%',
          marginBottom: INPUT_BOX_HEIGHT + 16,
        },
      ]}
    >
      {/* Only show the title row if not holidays */}
      {title && lob !== LOB.HOLIDAYS && (
        <View
          style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}
        >
          {IconToShow && <RecentChatIcon />}
          <Text style={styles.bottomPartTitle}>{title}</Text>
        </View>
      )}
      <WelcomeCards content={content} />
    </View>
  );
};

const WelcomeNewChat: React.FC = () => {
  const { isLoading, data } = useConfigSettings();
  const appState = useAppStateStore.getState();
  const lob = appState?.chatContext?.contextMetadata?.pageContext?.lob || '';
  const { updateConversationById } = useMessageStore();
  useEffect(() => {
    updateConversationById('draft', {
      hideInput: data?.content?.hideInput,
    });
  }, [data?.content?.hideInput, updateConversationById]);
  if (!data) return null;
  if (isLoading) return <MessageListLoader />;

  const {
    introMsg,
    introMsgSubTitleNew,
    introMsgSubTitleMarkdown,
    newChatContent,
    content,
  } = data;
  const newChatData = content?.type ? content : newChatContent;
  // const newChatContent = {
  //   type: 'PROMPTS_WITHOUT_IMAGES',
  //   title: 'Suggestive Prompts',
  //   value: {
  //     examplePromptInfos: [
  //       {
  //         size: 'L',
  //         clickable: true,
  //         description:
  //           'Flights from Mumbai to Sydney ksandjk kjasndjkasn kjnsajkdjn ksnakdjn ksand',
  //       },
  //       {
  //         size: 'S',
  //         clickable: true,
  //         description: 'Flights from Bangalore to Dubai',
  //       },
  //       {
  //         size: 'S',
  //         clickable: true,
  //         description: 'Flights from Bangalore to New York',
  //       },
  //     ],
  //   },
  // };

  const subTitle = introMsgSubTitleMarkdown || introMsgSubTitleNew;
  const isWebPlatform = isPlatformWeb();
  return (
    <ScrollView style={styles.container} nativeID="scroll-view">
      <WelcomeMessage title={introMsg} subTitle={subTitle} />
      {newChatData && newChatData.type && newChatData.value && (
        <WelcomeNewChatContent content={newChatData} lob={lob} />
      )}
      <View style={{ height: 100, width: '100%' }} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 24,
    flex: 1,
  },
  title: {
    fontSize: 36,
    lineHeight: 40,
    marginBottom: 2,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: COLORS.BLACK,
  },
  subTitle: {
    fontSize: 14,
    maxWidth: '80%',
    fontFamily: FONTS.FONT_FAMILY_400,
    lineHeight: 18,
  },
  bottomPartTitle: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.BLACK,
    lineHeight: 16,
  },
});
export default WelcomeNewChat;
