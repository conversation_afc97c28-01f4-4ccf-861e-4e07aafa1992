import React, { FC, useCallback } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { TouchableFeedback } from '../base/TouchableFeeback';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { ImageView } from '../image/ImageView';

type WelcomeCardCompactProps = {
  data: {
    description?: string;
    image: string;
  };
  onCardClick: (index: number) => void;
  isExpanded: boolean;
  index: number;
  isClickable: boolean;
};

export const WelcomeCardCompact: FC<WelcomeCardCompactProps> = (props) => {
  const {
    onCardClick: onCardClickProp,
    data,
    index,
    isClickable = true,
  } = props || {};
  const { description, image } = data || {};

  const onCardClick = useCallback(() => {
    onCardClickProp(index);
  }, [index, onCardClickProp]);
  return (
    <TouchableFeedback
      onPress={onCardClick}
      containerStyle={styles.containerStyle}
      style={styles.welcomeCardCompact}
      disabled={isClickable === false}
    >
      <View style={styles.imageContainer}>
        <ImageView
          style={styles.image}
          containerStyle={styles.image}
          source={{ uri: image }}
        />
      </View>
      <View style={styles.titleSection}>
        <Text style={styles.title} numberOfLines={4}>
          {description}
        </Text>
      </View>
    </TouchableFeedback>
  );
};

const CARD_WIDTH = '70%';
const IMAGE_WIDTH = 70;
const IMAGE_HEIGHT = 70;
const styles = StyleSheet.create({
  containerStyle: {
    flex: 1,
    borderRadius: 20,
    marginTop: 16,
    alignItems: 'center',
  },
  welcomeCardCompact: {
    flexDirection: 'row',
    padding: 10,
    elevation: 4,
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    backgroundColor: COLORS.WHITE,
    borderRadius: 20,
    width: CARD_WIDTH,
  },
  imageContainer: {
    borderRadius: 20,
    borderWidth: 1,
    borderColor: COLORS.WHITE,
    elevation: 4,
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  image: {
    width: IMAGE_WIDTH,
    height: IMAGE_HEIGHT,
    borderRadius: 20,
  },
  titleSection: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    flex: 1,
  },
  title: {
    paddingRight: 8,
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK_VAR_6,
  },
});
