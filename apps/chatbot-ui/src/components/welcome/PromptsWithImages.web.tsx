/* eslint-disable no-console */
import React, { useCallback, useEffect, useRef } from 'react';
import { Animated, Easing, View } from 'react-native';
import { WelcomeCardDetailed } from './WelcomeCardDetailed';
import { WelcomeCardCompact } from './WelcomeCardCompact';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import { useMessageAction } from '../../store/messages';
import { Analytics, TrackingEvent } from '../../analytics';

interface PromptsWithImagesProps {
  data: PromptsWithImagesInContents['examplePromptInfos'];
}

export const PromptsWithImages = ({ data: prompts }: PromptsWithImagesProps) => {
  const { selectInitialPrompt } = useMessageAction();
  // Animation values
  const translateY = useRef(new Animated.Value(100)).current;
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    try {
      // Reset animation values when component mounts
      translateY.setValue(100);
      opacity.setValue(0);

      // Start fade in and slide up animation
      Animated.parallel([
        Animated.timing(translateY, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
          easing: Easing.out(Easing.ease),
        }),
        Animated.timing(opacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
          easing: Easing.out(Easing.ease),
        }),
      ]).start();
    } catch (error) {
      console.error('Animation error:', error);
      // Fallback to make content visible without animation
      translateY.setValue(0);
      opacity.setValue(1);
    }
  }, [translateY, opacity]);

  const onPromptSelected = useCallback(
    (clickable: boolean, index: number) => {
      if (clickable === false) {
        return;
      }

      try {
        trackPDTEvent({
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: prompts?.[0]?.image
            ? eventValueSchema.WELCOME_PROMPTS_IMAGE
            : 'welcome_prompts_text',
        });

        Analytics.trackClickEvent(
          TrackingEvent.payload_WelcomePromptClicked(prompts?.[index]?.description),
        );

        const description = prompts?.[index]?.description;
        if (description) {
          selectInitialPrompt(description);
        }
      } catch (error) {
        console.error('Error handling prompt selection:', error);
      }
    },
    [prompts, selectInitialPrompt],
  );

  if (!prompts || !prompts.length) {
    return null;
  }

  return (
    <Animated.View
      style={{
        width: '100%',
        alignItems: 'center',
        flex: 1,
        marginBottom: '15%',
        opacity,
        transform: [{ translateY }],
      }}
    >
      {prompts.map((item, index) => {
        const CardComponent =
          item.size === 'L' ? WelcomeCardDetailed : WelcomeCardCompact;

        try {
          return (
            <View key={index} style={{ width: '100%', alignItems: 'center' }}>
              <CardComponent
                data={item}
                index={index}
                onCardClick={() => onPromptSelected(item.clickable, index)}
                isExpanded={index === 0}
                isClickable={item.clickable}
              />
            </View>
          );
        } catch (error) {
          console.error(`Error rendering prompt card ${index}:`, error);
          // Render a fallback or empty view if a specific card fails
          return <View key={index} />;
        }
      })}
    </Animated.View>
  );
};

export default PromptsWithImages;
