import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { COLORS, FONTS, Z_INDEX } from '../constants/globalStyles';
import { ButtonBackground } from './base/Button';
import { useMessageStore } from '../store/messages';
import { CloseIcon } from '../assets/CloseIcon';
import { api_reportIssue } from '../network/api/feedbackApi';
import { Toast } from './toast';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../native/tracking/pdt';
import { ModalSlot } from '../screens/ModalProvider';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { useBackAction } from '../utils/useBackHandler';

interface IssueReporterProps {
  msg: Message;
}

const title = 'Help us improve. Share your feedback';
const CommonIssues = [
  'Inaccurate information',
  'Forgot previous details',
  'Unrelated suggestions provided',
];

export function IssueReporter({ msg }: IssueReporterProps) {
  const [showPromptInput, setShowPromptInput] = useState(true);
  const [selectedIssue, setSelectedIssue] = useState<number>(-1);
  const selectedIndexRef = useRef(selectedIssue);
  const [inputValue, setInputValue] = useState('');
  const inputValueRef = useRef<string>('');
  const onBack = useCallback(() => {
    setShowPromptInput(false);
    return true;
  }, []);
  useBackAction(onBack, { enabled: showPromptInput });
  useEffect(() => {
    selectedIndexRef.current = selectedIssue;
  }, [selectedIssue]);
  const onSubmit = useCallback(() => {
    if (!inputValueRef.current) {
      return;
    }
    const feedbackText = [inputValueRef.current].join('\n').trim();
    const { activeConversationId } = useMessageStore.getState();
    api_reportIssue({
      conversationId: activeConversationId as string,
      messageId: msg.id,
      feedbackText,
      debugParameters: msg.debugData,
    })
      .then((data) => {
        if (data.success) {
          Toast.show('Issue reported successfully');
        } else {
          Toast.show('Failed to report issue!');
        }
      })
      .catch((e) => {
        Toast.show('Failed to report issue!');
      })
      .finally(() => {
        trackPDTEvent({
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: eventValueSchema.SUBMITTED,
        });
        setSelectedIssue(-1);
        setInputValue('');
        inputValueRef.current = '';
        setShowPromptInput(false);
      });
  }, [msg]);
  const enableBtn = inputValue.length > 0 || selectedIssue !== -1;

  if (!showPromptInput) {
    return null;
  }
  return (
    <ModalSlot>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <Animated.View
          entering={FadeIn}
          exiting={FadeOut}
          style={{
            width: '100%',
            flexDirection: 'column',
            zIndex: Z_INDEX.MODAL,
            ...StyleSheet.absoluteFillObject,
            backgroundColor: '#0002',
            alignItems: 'flex-end',
            justifyContent: 'flex-end',
          }}
        >
          <TouchableWithoutFeedback onPress={(e) => e.stopPropagation()}>
            <KeyboardAvoidingView
              behavior={Platform.OS === 'ios' ? 'padding' : undefined}
              keyboardVerticalOffset={Platform.OS === 'ios' ? 130 : 0}
              style={{
                flexDirection: 'column',
                width: '100%',
                maxHeight: '80%',
              }}
            >
              <View style={styles.modalContent}>
                <Text style={styles.modalTitle}>{title}</Text>
                <TouchableOpacity
                  onPress={() => setShowPromptInput(false)}
                  style={{ padding: 8, position: 'absolute', top: 4, right: 4 }}
                >
                  <CloseIcon />
                </TouchableOpacity>
                <TextInput
                  style={styles.textInput}
                  placeholder="Please provide additional details"
                  numberOfLines={4}
                  multiline={true}
                  maxLength={300}
                  onChangeText={(text) => {
                    setInputValue(text);
                    inputValueRef.current = text;
                  }}
                />
                <TouchableOpacity disabled={!enableBtn} onPress={onSubmit}>
                  <ButtonBackground>
                    <Text
                      style={{
                        color: COLORS.WHITE,
                        // color: enableBtn ? COLORS.WHITE : COLORS.TEXT_MEDIUM_EMPHASIS,
                        fontFamily: FONTS.FONT_FAMILY_700,
                      }}
                    >
                      SUBMIT
                    </Text>
                  </ButtonBackground>
                </TouchableOpacity>
              </View>
            </KeyboardAvoidingView>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableWithoutFeedback>
    </ModalSlot>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingHorizontal: 8,
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  modalContent: {
    position: 'relative',
    width: '100%',
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 16,
  },
  modalTitle: {
    fontSize: 16,
    color: COLORS.TEXT_HIGH_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_700,
    marginBottom: 16,
  },
  textInput: {
    width: '100%',
    height: 100,
    fontSize: 14,
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_400,
    borderColor: COLORS.BLACK_VAR_5,
    borderWidth: 1,
    textAlignVertical: 'top',
    borderRadius: 5,
    paddingHorizontal: 10,
    marginBottom: 16,
  },
  issueOption: {
    flexDirection: 'row',
    gap: 8,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.BLACK_VAR_5,
  },
  issueOptionText: {
    color: COLORS.TEXT_HIGH_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 14,
  },
});
