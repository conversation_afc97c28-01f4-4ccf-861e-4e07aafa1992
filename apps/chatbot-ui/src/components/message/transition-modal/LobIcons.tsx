import * as React from 'react';
import Svg, { SvgProps, Path, Rect } from 'react-native-svg';

export const FlightLobIcon = (props: SvgProps) => (
  <Svg viewBox={'0 0 30 30'} width={30} height={30} fill="none" {...props}>
    <Path
      fill="#008CFF"
      d="M13.2 13.79 6.356 9.08 7.75 7.9l10.916 1.38-5.466 4.51Z"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.761}
      d="M19.477 7.16c-4.265 2.926-11.023 8.233-15.629 13.303.813.926 2.135 1.195 3.22.626 5.63-2.96 15.149-8.602 20.101-12.29.556-.415.715-.982.596-1.48-.12-.507-.538-.984-1.225-1.17-2.448-.664-5.005-.4-7.063 1.011Z"
    />
    <Path
      fill="#20364B"
      stroke="#20364B"
      strokeWidth={0.612}
      d="m5.945 17.57-1.849 1.796-3.059-2.017.7-.599 4.208.82Z"
    />
    <Path
      fill="#008CFF"
      stroke="#008CFF"
      strokeWidth={0.737}
      d="m20.194 12.018-4.759 3.505 2.15 8.429 1.081-1.103 1.528-10.83Z"
    />
    <Path
      stroke="#20364B"
      strokeLinecap="round"
      strokeWidth={0.841}
      d="M26.288 6.522c-.105.36-.453.673-.954 1.015-.501.341-1.06.656-1.964 1.02"
    />
  </Svg>
);

export const HotelLobIcon = (props: SvgProps) => (
  <Svg viewBox={'0 0 32 32'} width={32} height={32} fill="none" {...props}>
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.935}
      d="M2.205 7.887c0-.024.016-.034.037-.023l10.166 5.52a.083.083 0 0 1 .04.066v15.219c0 .811-.573 1.137-1.276.725l-8.025-4.702c-.385-.225-.697-.762-.704-1.21L2.205 7.887Z"
    />
    <Path
      fill="#20364B"
      d="M22.277 5.911c0-.323-.256-.417-.53-.194l-9.054 7.35c-.206.168-.354.463-.354.706v15.593c0 .318.249.415.52.202l8.04-6.293c.798-.624 1.378-1.76 1.378-2.695V5.911Z"
    />
    <Rect
      width={3.519}
      height={3.519}
      fill="#20364B"
      rx={0.511}
      transform="matrix(.87 .5 0 1 3.643 19.4)"
    />
    <Rect
      width={3.519}
      height={3.519}
      fill="#20364B"
      rx={0.511}
      transform="matrix(.87 .5 0 1 3.645 14.957)"
    />
    <Rect
      width={3.519}
      height={3.519}
      fill="#008CFF"
      rx={0.511}
      transform="matrix(.87 .5 0 1 3.645 10.516)"
    />
    <Rect
      width={3.519}
      height={3.519}
      fill="#20364B"
      rx={0.511}
      transform="matrix(.87 .5 0 1 7.816 21.827)"
    />
    <Rect
      width={3.519}
      height={3.519}
      fill="#20364B"
      rx={0.511}
      transform="matrix(.87 .5 0 1 7.816 17.383)"
    />
    <Rect
      width={3.519}
      height={3.519}
      fill="#20364B"
      rx={0.511}
      transform="matrix(.87 .5 0 1 7.816 12.941)"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.935}
      d="M2.542 7.792c-.217-.121-.247-.228-.25-.25 0-.008-.01-.084.147-.202l8.332-6.237c.32-.24.827-.396 1.392-.419.563-.023 1.116.09 1.512.314l7.627 4.308c.349.197.463.404.483.535.018.115-.018.292-.27.497l-7.076 5.737a3.271 3.271 0 0 1-3.654.316l-8.243-4.6Z"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeLinejoin="round"
      strokeWidth={1.246}
      d="M17.137 27.022a.638.638 0 0 0-.04-.024c-.189-.102-.402-.313-.57-.599-.17-.284-.256-.58-.257-.808l-.031-11.416 7.313 3.93a.623.623 0 0 0 .572.008l5-2.484-.102 10.292a.467.467 0 0 1-.155.278l-.005.004-3.992 3.05a3.484 3.484 0 0 1-3.996.164l-3.737-2.395Zm-.868-13.043-.002.002.002-.002Z"
    />
    <Path
      fill="#008CFF"
      d="M28.911 16.35c.004-.27-.172-.394-.403-.284l-5.39 2.563-.058 10.705c.244.107.568.037.892-.193l4.418-3.138c.236-.168.415-.5.418-.775l.123-8.878Z"
    />
    <Rect
      width={8.204}
      height={7.887}
      y={0.352}
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.704}
      rx={1.408}
      transform="matrix(.87 .5 -.87 .5 23.186 10.806)"
    />
  </Svg>
);

export const CabLobIcon = (props: SvgProps) => (
  <Svg viewBox={'0 0 32 32'} width={32} height={32} fill="none" {...props}>
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.805}
      d="M9.215 21.497c0 1.228-.92 2.794-2.097 3.474-1.178.68-2.098.176-2.098-1.053s.92-2.794 2.098-3.474c1.177-.68 2.097-.176 2.097 1.053Z"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.805}
      d="M10.821 9.068c-1.848 1.758-2.792 5.007-3.504 5.83-1.527 1.074-2.511 1.97-3.254 2.632-.742.662-.878 1.513-.853 1.856-.008.418-.02 1.424 0 2.107.02.682.71 1.35 1.053 1.6 1.226.746 4.005 2.414 5.31 3.116 1.631.877 2.913.875 3.962.74.09-1.96 1.193-2.864 1.753-3.298 2.077-1.637 2.718.091 2.78 1.16l6.846-4.389c-.204-.753-.134-2.65 1.771-4.214 1.906-1.563 2.57.056 2.663 1.061.092-.076.578-.327.34-4.7-.078-1.444.013-1.81-.34-3.572-.698-2.75-3.92-3.44-4.948-3.837-1.79-.688-2.567-.877-3.635-.64-5.394 1.191-8.503 2.995-9.944 4.548Z"
    />
    <Path
      fill="#008CFF"
      d="M12.95 26.54c.16-1.956.093-2.788.861-3.716 1.682-2.03 3.416-2.35 4.28-3.683.582-.9 1.068-3.287 1.461-4.218 1.535-3.633 3.102-4.315 5.091-5.45 3.184-1.816 3.68-2.858 4.385 0 .564 2.286.335 3.423.335 6.713v1.038-.01c-.005-.93-1.615-.958-2.31-.362-1.597 1.369-1.465 1.592-2.168 3.194l-7.082 4.49c-.04-.919-.598-1.787-1.028-1.566-1.38.708-2.42 2.415-3.22 3.523l-.606.048Z"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.805}
      d="M8.638 15.801c-.42-.403-.959-1.22-.859-1.42.285-.809 1.684-3.598 1.956-3.965.219-.293.074.213 1.418 1.188 1.44 1.044 5.096 1.502 5.863 1.826.613.26 1.349.025.726 1.615-.39.995-.833 3.028-1.08 3.423-.246.396-2.966-.273-3.281-.361-2.42-.388-4.049-1.641-4.743-2.306ZM17.67 24.745c0 1.209-.848 2.679-1.895 3.283-1.047.604-1.895.114-1.895-1.094 0-1.209.848-2.678 1.895-3.283 1.047-.604 1.895-.114 1.895 1.094Zm11.457-6.514c0 1.237-.868 2.74-1.939 3.36-1.07.617-1.939.116-1.939-1.12s.868-2.74 1.94-3.359c1.07-.618 1.938-.117 1.938 1.12ZM15.202 4.802l4.219-2.495a.895.895 0 0 1 .427-.096c.176 0 .33.04.43.097l4.309 2.488c.053.031.068.053.068.053a.327.327 0 0 1 .009.018v2.478a.263.263 0 0 1-.04.112.266.266 0 0 1-.076.089l-4.53 2.565a2.246 2.246 0 0 1-2.23-.01l-3.316-1.914a.776.776 0 0 1-.258-.28.776.776 0 0 1-.114-.364v-.808c0-.794.419-1.529 1.102-1.933Z"
    />
    <Path
      stroke="#20364B"
      strokeWidth={0.805}
      d="m14.547 5.512 4.037 2.355a.734.734 0 0 0 .731.004l5.135-2.91"
    />
    <Path stroke="#20364B" strokeWidth={0.805} d="m17.297 3.696 4.218 2.75v2.568" />
    <Path
      fill="#008CFF"
      stroke="#20364B"
      strokeLinecap="round"
      strokeWidth={0.805}
      d="M19.13 9.366v-.872c0-.241.132-.463.343-.58l3.994-2.203a.662.662 0 0 1 .982.58v.871a.662.662 0 0 1-.342.58l-3.994 2.204a.662.662 0 0 1-.982-.58Z"
    />
  </Svg>
);

export const BusLobIcon = (props: SvgProps) => (
  <Svg viewBox={'0 0 32 32'} width={32} height={32} fill="none" {...props}>
    <Path
      fill="#008CFF"
      d="m13.313 17.569-.903-1.326L28.348 6.087c-.004-.047.178-.035.933.39.756.425 1.08 1.59 1.05 1.876L15.025 16.8l-1.713.768Z"
    />
    <Path
      fill="#20364B"
      fillRule="evenodd"
      d="M17.439 27.102c.439-.64.722-1.425.722-2.13 0-1.198-.815-1.714-1.82-1.154-1.005.56-1.82 1.984-1.82 3.18 0 .704.282 1.173.718 1.326.486.205 1.273.33 1.854-.001.451-.257.451-.797.346-1.22Zm10.753-5.455c.408-.642.668-1.431.668-2.164 0-1.296-.815-1.924-1.82-1.403-1.005.521-1.82 1.994-1.82 3.29 0 1.297.815 1.925 1.82 1.404.197-.102.386-.24.563-.407a1.45 1.45 0 0 0 .19-.083c.27-.144.379-.384.399-.637Z"
      clipRule="evenodd"
    />
    <Path
      fill="#008CFF"
      stroke="#20364B"
      strokeWidth={0.929}
      d="m13.144 18.89.002-.017v-.018c0-.388.14-.85.387-1.28.248-.43.579-.781.916-.976l15.209-8.777c.14-.081.207-.072.223-.***************.065.052.227v8.884c0 .389-.14.851-.387 1.28-.248.43-.579.781-.916.976l-14.646 8.452c-.021.013-.072.04-.215-.043-.152-.086-.336-.261-.519-.512a3.812 3.812 0 0 1-.448-.815c-.11-.278-.164-.517-.168-.68l.51-6.632Zm16.74-11.134Zm-.005-.003-.001-.001Z"
    />
    <Path
      fill="#fff"
      d="M14.641 19.01a.44.44 0 0 1 .199-.333l1.958-1.121c.11-.063.2-.01.198.118l-.091 7.535a.44.44 0 0 1-.196.339l-2.18 1.29c-.114.068-.206.011-.2-.124l.312-7.704Zm3.566-2.034c0-.126.088-.279.197-.343l9.808-5.717c.11-.064.2-.013.2.114v3.491a.438.438 0 0 1-.198.343l-9.808 5.717c-.11.064-.199.013-.199-.114v-3.491Z"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.929}
      d="M3.378 12.174a5.881 5.881 0 0 1-.137-.075l14.628-9.207.005-.004c.978-.635 1.624-.848 2.247-.822.646.027 1.342.31 2.43.861l6.868 4.077c.329.195.388.383.387.477-.001.097-.068.287-.404.481L13.095 17.37 3.84 12.42c-.1-.055-.213-.115-.317-.17l-.144-.075Zm-.418.102.004-.003a.025.025 0 0 1-.004.003ZM18.903 25.462c0 1.013-.66 2.162-1.417 2.599-.758.438-1.418.05-1.418-.963 0-1.014.66-2.162 1.418-2.6.758-.437 1.417-.05 1.417.964Zm10.362-5.854c0 1.014-.66 2.162-1.418 2.6-.758.437-1.417.05-1.417-.964 0-1.013.659-2.162 1.417-2.6.758-.437 1.417-.05 1.417.964Z"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.929}
      d="m2.85 12.492.003-.025v-.024c0-.067.07-.224.33-.349.253-.121.483-.108.584-.05l8.452 4.892c.196.113.399.326.554.594.152.263.234.538.238.761l-.545 7.413-.006.08.022.077.527 1.917a.461.461 0 0 1-.035.162c-.017.034-.03.042-.03.042-.001 0-.014.009-.053.005a.526.526 0 0 1-.207-.077l-9.04-5.217c-.275-.158-.408-.247-.553-.399-.151-.159-.324-.398-.627-.868L2.07 19.85l.78-7.36Z"
    />
    <Path
      fill="#20364B"
      stroke="#20364B"
      strokeWidth={1.017}
      d="M4.14 14.57c.044-.39.35-.526.712-.316l6.02 3.479c.419.242.748.846.712 1.308l-.238 3.083c-.03.4-.328.553-.691.357l-6.14-3.326c-.439-.237-.786-.875-.734-1.348l.36-3.236Z"
    />
    <Path
      fill="#20364B"
      d="M3.2 20.156c.07-.18.252-.22.455-.103l.847.488c.24.14.436.478.436.756 0 .256-.166.375-.386.277l-.841-.373c-.36-.16-.636-.725-.511-1.045Zm5.42 3.391c.06-.193.248-.254.477-.153l.658.289c.395.173.713.725.61 1.06-.059.192-.248.253-.476.153l-.659-.29c-.395-.173-.713-.725-.61-1.059Z"
    />
  </Svg>
);

export const RailLobIcon = (props: SvgProps) => (
  <Svg viewBox={'0 0 32 32'} width={32} height={32} fill="none" {...props}>
    <Path
      stroke="#20364B"
      strokeLinecap="round"
      strokeWidth={0.58}
      d="m2.101 25.932 22.655-13.443M7.298 27.505 3.47 25.322m2.278 3.177 22.654-13.442"
    />
    <Path
      fill="#20364B"
      d="M18.35 21.988c0 .738-.502 1.617-1.122 1.962-.62.345-1.123.027-1.123-.712s.503-1.617 1.123-1.962c.62-.346 1.123-.027 1.123.712Zm6.598-3.391c0 .8-.502 1.708-1.122 2.03-.62.321-1.123-.066-1.123-.866s.503-1.709 1.123-2.03c.62-.322 1.122.066 1.122.866Z"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.651}
      d="M5.702 20.73c-2.146 5.786 3.797 6.326 7.81 4.192l14.632-8.367-.097-6.416-6.51-3.184s-9.489 4.43-11.232 6.112C8.562 14.75 6.78 17.823 5.702 20.73Z"
    />
    <Path
      fill="#EAF5FF"
      d="M21.531 7.83V6.335l1.283-.33 7.199 3.445-1.949 1.068-6.533-2.69Z"
    />
    <Path
      fill="#20364B"
      stroke="#20364B"
      strokeWidth={0.715}
      d="M8.014 16.318s-1.187 1.868-1.622 3.003c-.436 1.134.11 2.47 1.094 2.895 2.356 1.092 6.697-.75 9.572-2.427l.06-1.914c-3.329 1.463-6.704 1.686-8.118.94-1.414-.747-1.015-1.923-.986-2.497Z"
    />
    <Path
      fill="#008CFF"
      stroke="#20364B"
      strokeWidth={0.651}
      d="M14.062 21.274c1.367-2.573 3.831-5.462 5.265-6.309l8.769-4.912.144 6.544c-1.373.702-12.976 7.282-14.219 7.78-1.242.499-.735-1.643.041-3.103Z"
    />
    <Path
      stroke="#20364B"
      strokeLinecap="round"
      strokeWidth={0.58}
      d="M10.559 12.9c-.467 1.609.609 4.162 8.773 2.05"
    />
    <Path
      fill="#20364B"
      d="M21.824 6.864c0 .19-.138.437-.285.437s-.268-.154-.268-.343.12-.343.268-.343c.147 0 .285.06.285.25Zm6.805 2.9c0 .204-.11.471-.228.471-.118 0-.213-.166-.213-.37 0-.205.095-.37.213-.37s.228.064.228.269Zm.139 6.553c0 .21-.11.483-.228.483-.118 0-.214-.17-.214-.38s.096-.379.214-.379.228.066.228.276Z"
    />
    <Path fill="#EAF5FF" d="m27.646 10.68 1.123-.639.013 5.878-1.136.63v-5.87Z" />
  </Svg>
);
