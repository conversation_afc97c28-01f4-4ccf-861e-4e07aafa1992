import * as React from 'react';
import Svg, { SvgProps, Circle, Defs, LinearGradient, Stop } from 'react-native-svg';

export const Ring1 = (props: SvgProps) => (
  <Svg width={82} height={82} fill="none" {...props}>
    <Circle
      cx={41}
      cy={41}
      r={40}
      stroke="url(#a)"
      strokeDasharray="4 4"
      strokeLinecap="round"
      opacity={0.4}
    />
    <Defs>
      {/* @ts-ignore */}
      <LinearGradient
        id="a"
        x1={81}
        x2={1}
        y1={1}
        y2={81}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#3023AE" />
        <Stop offset={1} stopColor="#C86DD7" />
      </LinearGradient>
    </Defs>
  </Svg>
);

export const Ring2 = (props: SvgProps) => (
  <Svg width={102} height={102} fill="none" {...props}>
    <Circle
      cx={51}
      cy={51}
      r={50}
      stroke="url(#a)"
      strokeDasharray="4 4"
      strokeLinecap="round"
      opacity={0.2}
    />
    <Defs>
      {/* @ts-ignore */}
      <LinearGradient
        id="a"
        x1={101}
        x2={1}
        y1={1}
        y2={101}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#3023AE" />
        <Stop offset={1} stopColor="#C86DD7" />
      </LinearGradient>
    </Defs>
  </Svg>
);

export const Ring3 = (props: SvgProps) => (
  <Svg width={122} height={122} fill="none" {...props}>
    <Circle
      cx={61}
      cy={61}
      r={60}
      stroke="url(#a)"
      strokeDasharray="4 4"
      strokeLinecap="round"
      opacity={0.1}
    />
    <Defs>
      {/* @ts-ignore */}
      <LinearGradient
        id="a"
        x1={121}
        x2={1}
        y1={1}
        y2={121}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#3023AE" />
        <Stop offset={1} stopColor="#C86DD7" />
      </LinearGradient>
    </Defs>
  </Svg>
);
