import React, { useEffect } from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import { COLORS, FONTS, Z_INDEX } from '../../../constants/globalStyles';
import { Ring1, Ring2, Ring3 } from './Rings';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withRepeat,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import {
  BusLobIcon,
  CabLobIcon,
  FlightLobIcon,
  HotelLobIcon,
  RailLobIcon,
} from './LobIcons';
import { SvgProps } from 'react-native-svg';
import MyraIcon from '../../EntryPoint/MyraIcon';
import GradientText from '../../base/GradientText';
import IconForex from '../../../assets/IconForex';

export type LobCards =
  | 'FLT'
  | 'HTL'
  | 'where2go'
  | 'CAB'
  | 'TRN'
  | 'BUS'
  | 'MyTrips'
  | 'forex';

export const formattedLobMap: Record<LobCards, string> = {
  HTL: 'Hotels',
  FLT: 'Flights',
  where2go: 'Where2Go',
  CAB: 'Cabs',
  TRN: 'Trains',
  BUS: 'Bus',
  MyTrips: 'My Trips',
  forex: 'Forex',
};
export const lobIconMap: Record<LobCards, React.FC<SvgProps> | null> = {
  HTL: HotelLobIcon,
  where2go: HotelLobIcon,
  FLT: FlightLobIcon,
  CAB: CabLobIcon,
  TRN: RailLobIcon,
  BUS: BusLobIcon,
  MyTrips: null,
  forex: IconForex,
};

type TransitionOverlayProps = {
  lob: LobCards;
};

export const shouldShowTransition = (lob: string): lob is LobCards =>
  lob in formattedLobMap;

export function TransitionOverlay({ lob }: TransitionOverlayProps) {
  return (
    <View style={styles.root}>
      <TransitionAnimation lob={lob} />
      <GradientText
        style={styles.titleText}
        gradientColors={['#51AFE6', '#355FF2', '#11287A']}
        start={{ x: 0, y: 0 }}
        end={{ x: 0.6, y: 0 }}
      >{`Redirecting you to ${formattedLobMap[lob]}`}</GradientText>
      {/* <Text style={styles.descText}>
        You can return to Where2Go or My Account to resume your chat
      </Text> */}
    </View>
  );
}

function TransitionAnimation({ lob }: { lob: LobCards }) {
  const angle = useSharedValue(0);

  const botIconAnimatedStyle = useAnimatedStyle(() => {
    const radius = 50; // radius of the circular path
    const x = radius * Math.cos(angle.value);
    const y = radius * Math.sin(angle.value);
    return {
      width: 64,
      height: 64,
      position: 'absolute',
      transform: [{ translateX: x }, { translateY: y }],
    };
  }, [angle]);
  const lobIconAnimatedStyle = useAnimatedStyle(() => {
    const radius = 50; // radius of the circular path
    const x = radius * Math.cos(angle.value);
    const y = radius * Math.sin(angle.value);
    return {
      width: 64,
      height: 64,
      position: 'absolute',
      transform: [{ translateX: -x }, { translateY: -y }],
    };
  }, [angle]);

  useEffect(() => {
    angle.value = withRepeat(
      withTiming(2 * Math.PI, { duration: 6000, easing: Easing.linear }),
      -1,
    );
  }, [angle]);
  const LobIcon = lobIconMap[lob];
  return (
    <View style={styles.animationContainer}>
      <Rings />
      <Animated.View style={botIconAnimatedStyle}>
        <MyraIcon
          overrideIconStyle={{
            width: 64,
            height: 64,
          }}
          shouldAnimate={false}
          showNewMessage={false}
        />
      </Animated.View>
      {LobIcon && (
        <Animated.View style={lobIconAnimatedStyle}>
          <View
            style={{
              backgroundColor: COLORS.BLUE_VAR_2,
              height: 64,
              width: 64,
              borderRadius: 32,
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <LobIcon height={36} width={36} />
          </View>
        </Animated.View>
      )}
    </View>
  );
}

function Rings() {
  const ring1Opacity = useSharedValue(0);
  const ring2Opacity = useSharedValue(0);
  const ring3Opacity = useSharedValue(0);

  const ring1Style = useAnimatedStyle(
    () => ({
      opacity: ring1Opacity.value,
    }),
    [ring1Opacity],
  );
  const ring2Style = useAnimatedStyle(
    () => ({
      opacity: ring2Opacity.value,
    }),
    [ring2Opacity],
  );
  const ring3Style = useAnimatedStyle(
    () => ({
      opacity: ring3Opacity.value,
    }),
    [ring3Opacity],
  );

  useEffect(() => {
    ring1Opacity.value = withRepeat(
      withSequence(
        withTiming(1, { duration: 600 }),
        withDelay(2200, withTiming(0, { duration: 100 })),
      ),
      -1,
    );
    ring2Opacity.value = withRepeat(
      withSequence(
        withDelay(600, withTiming(1, { duration: 600 })),
        withDelay(1600, withTiming(0, { duration: 100 })),
      ),
      -1,
    );
    ring3Opacity.value = withRepeat(
      withSequence(
        withDelay(1200, withTiming(1, { duration: 600 })),
        withDelay(1000, withTiming(0, { duration: 100 })),
      ),
      -1,
    );
  }, [ring1Opacity, ring2Opacity, ring3Opacity]);

  return (
    <View style={styles.ringsContainer}>
      <Animated.View style={[styles.ring1, ring1Style]}>
        <Ring1 />
      </Animated.View>
      <Animated.View style={[styles.ring2, ring2Style]}>
        <Ring2 />
      </Animated.View>
      <Animated.View style={[styles.ring3, ring3Style]}>
        <Ring3 />
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  root: {
    ...StyleSheet.absoluteFillObject,
    zIndex: Z_INDEX.LOADING,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleText: {
    fontSize: 16,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: COLORS.COSMOS_GRAD_START,
    // maxWidth: '66%',
    marginVertical: 8,
    textAlign: 'center',
  },
  descText: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_DEFAULT,
    textAlign: 'center',
    // maxWidth: '66%',
  },
  animationContainer: {
    height: 200,
    width: 200,
    alignItems: 'center',
    justifyContent: 'center',
  },
  ringsContainer: {
    height: 125, // 122 is ring3's size, adding 3px as padding
    width: 125,
    position: 'relative',
  },
  ring1: {
    position: 'absolute',
    top: 20,
    left: 20,
    backgroundColor: 'transparent',
  },
  ring2: {
    position: 'absolute',
    top: 10,
    left: 10,
    backgroundColor: 'transparent',
  },
  ring3: {
    position: 'absolute',
    top: 0,
    left: 0,
    backgroundColor: 'transparent',
  },
});
