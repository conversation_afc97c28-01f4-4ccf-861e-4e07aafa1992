import React, { memo, useEffect } from 'react';
import { View } from 'react-native';
import { messageBubbleStyles } from './messageStyles';
import { LoadingDots } from './DotLoader';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { useLoaderText } from '../../store/messages';
import Animated, {
  SlideInLeft,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
  Easing,
  withDelay,
} from 'react-native-reanimated';
import { trackOmnitureClickEvent } from '../../native/omniture';
import MyraIcon from '../EntryPoint/MyraIcon';
import GradientText from '../base/GradientText';
import { LoaderStar } from '../../assets/loaderStars';

const AnimatedStars = () => {
  const translateY = useSharedValue(0); // Start from bottom
  const translateX = useSharedValue(0); // Start from left
  const rotation = useSharedValue(0);
  const rotation1 = useSharedValue(0);
  const scale = useSharedValue(0);
  const opacity1 = useSharedValue(1);
  React.useEffect(() => {
    rotation.value = withRepeat(withTiming(-180, { duration: 1200 }), -1, false);
    rotation1.value = withRepeat(withTiming(-180, { duration: 1200 }), -1, false);

    translateX.value = withRepeat(
      withDelay(
        500,
        withSequence(
          withTiming(1, { duration: 300, easing: Easing.linear }, () => {
            translateY.value = withTiming(-5, {
              duration: 300,
              easing: Easing.linear,
            });
            scale.value = withTiming(0.2, { duration: 100 });
          }), // Move right
          withTiming(3, { duration: 300, easing: Easing.linear }, () => {
            translateY.value = withTiming(-5, {
              duration: 300,
              easing: Easing.linear,
            });
            scale.value = withTiming(0.8, { duration: 100 });
          }),
          withTiming(5, { duration: 300, easing: Easing.linear }, () => {
            translateY.value = withTiming(-8, {
              duration: 300,
              easing: Easing.linear,
            });
            scale.value = withTiming(1, { duration: 100 });
          }),
          withDelay(
            100,
            withTiming(7, { duration: 400, easing: Easing.linear }, () => {
              // translateY.value = withTiming(-10, {
              //   duration: 400,
              //   easing: Easing.linear,
              // });
              opacity1.value = withTiming(0, {
                duration: 10,
                easing: Easing.linear,
              });
              scale.value = withTiming(0, { duration: 100, easing: Easing.linear });
            }),
          ),
        ),
      ),
      -1,
      false,
    );
  }, [opacity1, rotation, rotation1, scale, translateX, translateY]);

  const animatedStyle = useAnimatedStyle(
    () => ({
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        // { rotate: `${rotation.value}deg` },
      ],
    }),
    [translateX, translateY],
  );
  const animatedStyle1 = useAnimatedStyle(
    () => ({
      transform: [{ rotate: `${rotation1.value}deg` }, { scale: scale.value }],
    }),
    [scale, rotation1] /* Add rotation1 to dependencies */,
  );
  const animatedStyle2 = useAnimatedStyle(
    () => ({
      transform: [{ rotate: `${rotation.value}deg` }, { scale: scale.value }], // Convert rotation value to degrees,
    }),
    [scale, rotation],
  );

  return (
    <Animated.View style={animatedStyle}>
      <Animated.View style={[animatedStyle1, { marginLeft: 6 }]}>
        <LoaderStar height={12} width={12} />
      </Animated.View>
      <Animated.View style={[animatedStyle2, { width: 10, height: 10 }]}>
        <LoaderStar height={10} width={10} />
      </Animated.View>
    </Animated.View>
  );
};

export const BotMessageLoader: React.FC<unknown> = memo(() => {
  const loaderText = useLoaderText();
  useEffect(() => {
    if (loaderText) {
      trackOmnitureClickEvent('LOADER_TEXT_SHOWN');
    }
  }, [loaderText]);
  return (
    <Animated.View
      entering={SlideInLeft.duration(600)}
      style={[
        messageBubbleStyles.botMessageContainer,
        messageBubbleStyles.botMessageBubble,
      ]}
    >
      <MyraIcon
        showNewMessage={false}
        overrideIconStyle={{ height: 28, width: 28 }}
        overrideIconContainerStyle={{ padding: 0, marginTop: 8 }}
      />
      <View
        style={[
          {
            marginLeft: 0,
            width: '80%',
            flexDirection: 'column',
            gap: 16,
            paddingVertical: 0,
            paddingHorizontal: 0,
            alignItems: 'flex-start',
            marginTop: 12,
          },
        ]}
      >
        <View style={{ flexDirection: 'row' }}>
          <GradientText
            gradientColors={['#51AFE6', '#355FF2', '#11287A']}
            style={{
              color: COLORS.BLACK,
              fontSize: 14,
              fontFamily: FONTS.FONT_FAMILY_500,
              lineHeight: 16,
            }}
            start={{ x: 0, y: 0 }}
            end={{ x: 0.6, y: 0 }}
          >
            {loaderText || 'Finding the perfect results for you!'}
          </GradientText>
          <AnimatedStars />
        </View>
        <LoadingDots />
      </View>
    </Animated.View>
  );
});
