import React from 'react';
import { StyleSheet, View } from 'react-native';
import { SystemTextMessageType } from '../../store/messages/listItemTypes';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { defaultSystemMessagePalette, MarkdownText } from './MarkdownText';

export const SystemMessage: React.FC<SystemTextMessageType> = ({ data }) => {
  return (
    <View style={styles.container}>
      <MarkdownText
        size="small"
        color={defaultSystemMessagePalette}
        text={data.value ?? ''}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.YELLOW_FLESH,
    borderRadius: 8,
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '94%',
    marginTop: 32,
  },
  text: {
    color: COLORS.ORANGE_VAR_2,
    fontSize: 2,
    fontFamily: FONTS.FONT_FAMILY_500,
  },
  ticketId: {
    fontFamily: FONTS.FONT_FAMILY_700,
  },
});
