/* eslint-disable react-native/no-unused-styles */
import React, { useCallback, useMemo } from 'react';
import Markdown, { renderRules } from 'react-native-markdown-display';
import { COLORS, FONTS } from '../../constants/globalStyles';
import {
  Image,
  ImageProps,
  LayoutChangeEvent,
  Platform,
  Pressable,
  StyleSheet,
  Text,
  TextStyle,
  View,
} from 'react-native';
import { openDeepLink } from '../../native/deepLinks';
import { getInnerMsgWidth } from './messageStyles';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { MarkdownStream, StreamConfig } from './AnimatedMarkdownStream';
import { useConfigSettings } from '../../store/messages/newChatView';
import { useMessageStore } from '../../store/messages';
import useGetFeatureFlags from '../../hooks/useGetFeatureFlags';
import { getPlatform, isPlatformWeb } from '../../utils/getPlatform';
import { AnimatedStreamLoadingIndicator } from '../message-list-loader/AnimateStreamLoader';
import { useResponsiveWidth } from '../../hooks/useResponsiveWidth';
import { trackOmnitureClickEvent } from '../../native/omniture';
import MarkdownIt from 'markdown-it';
import LinearGradient from 'react-native-linear-gradient';

const markdownIt = MarkdownIt();

markdownIt.inline.ruler.after('emphasis', 'superscript', (state, silent) => {
  const start = state.pos;
  const marker = '[^]';
  const endMarker = '[/^]';

  if (state.src.slice(start, start + marker.length) !== marker) return false;
  const end = state.src.indexOf(endMarker, start);
  if (end === -1) return false;

  if (!silent) {
    const token = state.push('superscript', '', 0);
    token.content = state.src.slice(start + marker.length, end);
  }

  state.pos = end + endMarker.length;
  return true;
});

type MarkdownTextProps = {
  text: string;
  size?: 'default' | 'small';
  color?: ColorPalette;
  adjustHeight?: boolean;
  onLayout?: (event: LayoutChangeEvent) => void;
  weight?: 'normal' | 'semibold' | 'bold' | 'dark';
  streamMarkdown?: boolean;
  isStreaming?: boolean;
  streamConfig?: StreamConfig;
  forceCompleteStreaming?: boolean;
  lineHeight?: number;
  fontSize?: number;
  marginTop?: number;
  marginBottom?: number;
  numberOfLines?: number;
  shouldIncreaseFontSize?: boolean;
};

type ColorPalette = {
  light?: string;
  default: string;
  dark: string;
};

function getStyle({
  color: colors = defaultDarkColorPalette,
  size = 'default',
  weight = 'normal',
  lineHeight,
  fontSize,
  marginTop,
  marginBottom,
  // numberOfLines,
  shouldIncreaseFontSize,
}: MarkdownTextProps) {
  const baseFontSize =
    fontSize || (size === 'small' ? 12 : shouldIncreaseFontSize ? 15 : 14);
  const h1Size = size === 'small' ? 20 : 24;
  const h2Size = size === 'small' ? 18 : 21;
  const h3Size = size === 'small' ? 16 : 18;
  const h4Size = size === 'small' ? 14 : 16;
  const h5Size = size === 'small' ? 12 : 14;
  const h6Size = size === 'small' ? 10 : 12;
  const fontMap = {
    normal: FONTS.FONT_FAMILY_400,
    semibold: FONTS.FONT_FAMILY_500,
    bold: FONTS.FONT_FAMILY_700,
    dark: FONTS.FONT_FAMILY_900,
  };
  const defaultTextStyle: TextStyle = {
    fontSize: baseFontSize,
    lineHeight: lineHeight || h1Size,
    margin: 0,
    padding: 0,
    fontFamily: fontMap[weight] || FONTS.FONT_FAMILY_400,
  };
  return StyleSheet.create({
    body: {
      ...defaultTextStyle,
    },
    p: {
      color: colors.dark,
      margin: 0,
      padding: 0,
      marginTop: 0,
      height: 12,
      flexWrap: 'wrap', // force inline text behavior
    },
    image: {
      width: 14,
      height: 14,
    },
    paragraph: {
      color: colors.dark,
      fontSize: baseFontSize,
      flexWrap: 'wrap', // force inline text behavior
      ...(marginTop !== undefined && { marginTop }),
      ...(marginBottom !== undefined && { marginBottom }),
    },
    heading1: {
      color: colors.dark,
      fontSize: h1Size,
      fontFamily: FONTS.FONT_FAMILY_400,
      marginVertical: 10,
    },
    heading2: {
      color: colors.dark,
      fontSize: h2Size,
      fontFamily: FONTS.FONT_FAMILY_400,
      marginVertical: 10,
    },
    heading3: {
      color: colors.dark,
      fontSize: h3Size,
      fontFamily: FONTS.FONT_FAMILY_400,
      marginVertical: 10,
      fontWeight: '700',
    },
    heading4: {
      color: colors.dark,
      fontSize: h4Size,
      fontFamily: FONTS.FONT_FAMILY_400,
      marginVertical: 10,
    },
    heading5: {
      color: colors.dark,
      fontSize: h5Size,
      fontFamily: FONTS.FONT_FAMILY_400,
      marginVertical: 10,
    },
    heading6: {
      color: colors.dark,
      fontSize: h6Size,
      fontFamily: FONTS.FONT_FAMILY_400,
      marginVertical: 10,
    },

    list_item: {
      color: colors.dark,
      marginVertical: 4,
    },
    blockquote: {
      color: colors.dark,
    },
    text: {
      ...defaultTextStyle,
      flexWrap: 'wrap', // force inline text behavior
    },
    em: {
      color: colors.dark,
    },
    strong: {
      color: colors.dark,
      fontFamily: FONTS.FONT_FAMILY_900,
    },
    del: {
      color: colors.dark,
    },
    u: {
      color: colors.dark,
    },
    s: {
      color: colors.dark,
    },
    mark: {
      color: colors.dark,
    },
    sup: {
      color: colors.dark,
    },
    sub: {
      color: colors.dark,
    },
    textgroup: {
      ...defaultTextStyle,
    },
    hardBreak: {
      color: colors.dark,
    },
    bullet_list: {
      paddingVertical: 6,
    },
    link: {
      color: COLORS.BLUE_VAR_11,
      textDecorationLine: 'none',
      backgroundColor: COLORS.BLUE_VAR_12,
      borderBottomWidth: 0,
      paddingHorizontal: 4,
      borderRadius: 6,
      fontFamily: FONTS.FONT_FAMILY_700,
      marginBottom: Platform.OS === 'web' ? -2 : Platform.OS === 'ios' ? -3 : -6,
    },
    blocklink: {
      color: COLORS.BLUE_VAR_11,
      textDecorationLine: 'none',
      backgroundColor: COLORS.BLUE_VAR_12,
      borderBottomWidth: 0,
      paddingHorizontal: 4,
      borderRadius: 6,
      fontFamily: FONTS.FONT_FAMILY_700,
    },
    table: {
      marginTop: 10,
      marginBottom: 10,
    },
  });
}

export const defaultDarkColorPalette: ColorPalette = {
  light: COLORS.TEXT_LOW_EMPHASIS,
  default: COLORS.BLACK,
  dark: COLORS.BLACK,
};
export const defaultWhiteColorPalette: ColorPalette = {
  light: COLORS.TEXT_LOW_EMPHASIS,
  default: COLORS.WHITE,
  dark: COLORS.WHITE,
};
export const defaultSystemMessagePalette: ColorPalette = {
  light: COLORS.ORANGE_VAR_2,
  default: COLORS.YELLOW_FLESH,
  dark: COLORS.ORANGE_VAR_2,
};
export const defaultAgentMessagePalette: ColorPalette = {
  light: COLORS.BROWN_VAR_1,
  default: COLORS.BROWN_VAR_1,
  dark: COLORS.BROWN_VAR_1,
};
export const defaultLeadingQuestionPalette: ColorPalette = {
  light: COLORS.BLUE_VAR_10,
  default: COLORS.BLUE_VAR_10,
  dark: COLORS.BLUE_VAR_10,
};

export const defaultTravellerInsightPalette: ColorPalette = {
  light: COLORS.BLUE_VAR_15,
  default: COLORS.BLUE_VAR_15,
  dark: COLORS.BLUE_VAR_15,
};

interface MarkdownNode {
  key: string;
  attributes: Record<string, any> & {
    href?: string;
  };
  content?: string;
}

const ImageComp = React.memo(({ imageProps }: { imageProps: any }) => {
  if (isPlatformWeb() && imageProps.source?.uri) {
    return (
      <div
        style={{
          width: 14,
          height: 14,
          marginRight: 2,
          backgroundImage: `url(${imageProps.source.uri})`,
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          display: 'inline-block',
          marginBottom: -3,
        }}
      />
    );
  }

  return (
    <Image
      {...imageProps}
      style={{
        width: 14,
        height: 14,
        marginRight: 2,
        marginBottom: Platform.OS === 'ios' ? -3 : -6,
      }}
    />
  );
});

const createRules = (numberOfLines?: number) => ({
  // Links
  link: (
    node: MarkdownNode,
    children: React.ReactNode[],
    parent: any,
    styles: Record<string, any>,
    onLinkPress: ((url: string) => boolean) | undefined,
  ) => {
    return (
      <TouchableOpacity
        key={node.key}
        style={styles.link}
        onPress={() => node.attributes.href && onLinkPress?.(node.attributes.href)}
      >
        {children && children[0] && React.isValidElement(children[0])
          ? React.cloneElement(children[0], {
              ...((children[0] as React.ReactElement).props.style
                ? {
                    style: [
                      (children[0] as React.ReactElement).props.style,
                      { fontFamily: FONTS.FONT_FAMILY_700 },
                    ],
                  }
                : {}),
            })
          : children}
      </TouchableOpacity>
    );
  },
  blocklink: (
    node: MarkdownNode,
    children: React.ReactNode[],
    parent: any,
    styles: Record<string, any>,
    onLinkPress: ((url: string) => boolean) | undefined,
  ) => {
    let imageNode = null;
    let label = null;
    const contentNode = children[0];
    // Case 1: Direct image object with properties

    if (isPlatformWeb() && React.isValidElement(contentNode)) {
      imageNode = contentNode;
      label = (
        <TouchableOpacity
          key={node.key}
          style={[styles.link, { marginLeft: -2 }]}
          onPress={() => node.attributes.href && onLinkPress?.(node.attributes.href)}
        >
          {contentNode.props?.imageProps?.accessibilityLabel || ''}
        </TouchableOpacity>
      );
    } else if (
      contentNode &&
      typeof contentNode === 'object' &&
      'props' in contentNode &&
      !contentNode?.props?.children
    ) {
      // Handle direct image object case
      const { props } = contentNode;
      imageNode = (
        <Image
          source={props.source}
          style={{
            height: 14,
            width: 14,
            marginTop: 2,
          }}
          accessible={props.accessible}
          accessibilityLabel={props.accessibilityLabel}
        />
      );
      label = (
        <TouchableOpacity
          key={node.key}
          style={[styles.link, { marginLeft: -2 }]}
          onPress={() => node.attributes.href && onLinkPress?.(node.attributes.href)}
        >
          {props.accessibilityLabel}
        </TouchableOpacity>
      );
    }
    // Case 2: React element with children
    else if (React.isValidElement(contentNode) && contentNode?.props) {
      const nestedChildren = (contentNode.props as { children?: React.ReactNode })
        ?.children;
      if (nestedChildren) {
        if (Array.isArray(nestedChildren)) {
          nestedChildren.forEach((child: any) => {
            if (
              child?.type?.displayName === 'Image' ||
              child?.type?.name === 'Image'
            ) {
              imageNode = child;
            } else if (typeof child === 'string' || child?.type === Text) {
              label = child;
            } else if (child?.props?.children) {
              label = child.props.children;
            }
          });
        }
      }
    }

    return (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}
        key={node.key}
      >
        {imageNode}
        <Pressable
          style={{
            backgroundColor:
              Platform.OS !== 'web' ? COLORS.BLUE_VAR_12 : COLORS.TRANSPARENT,
            borderBottomWidth: 0,
            paddingHorizontal: 4,
            borderRadius: 6,
            marginBottom: Platform.OS === 'ios' ? -3 : -6,
          }}
          onPress={() => node.attributes.href && onLinkPress?.(node.attributes.href)}
        >
          {label}
        </Pressable>
      </View>
    );
  },
  image: (
    node: MarkdownNode,
    children: React.ReactNode[],
    parent: any,
    styles: Record<string, any>,
    allowedImageHandlers: string[],
    defaultImageHandler: string | null,
  ) => {
    const { src, alt } = node.attributes;
    // we check that the source starts with at least one of the elements in allowedImageHandlers
    const show =
      allowedImageHandlers.filter((value) => {
        return src.toLowerCase().startsWith(value.toLowerCase());
      }).length > 0;
    if (show === false && defaultImageHandler === null) {
      return null;
    }
    const imageProps: ImageProps = {
      style: styles.image,
      source: {
        uri: show === true ? src : `${defaultImageHandler}${src}`,
      },
    };
    if (alt) {
      imageProps.accessible = true;
      imageProps.accessibilityLabel = alt;
    }
    return <ImageComp imageProps={imageProps} key={node.key} />;
  },
  paragraph: (
    node: MarkdownNode,
    children: React.ReactNode[],
    parent: any,
    styles: Record<string, any>,
  ) => (
    <View key={node.key} style={styles._VIEW_SAFE_paragraph}>
      <Text key={node.key} numberOfLines={numberOfLines}>
        {children.flat()}
      </Text>
    </View>
  ),
  loader: () => <AnimatedStreamLoadingIndicator />,
  superscript: (
    node: MarkdownNode,
    children: React.ReactNode[],
    parent: any,
    styles: Record<string, any>,
  ) => {
    const content = node.content || '';

    // Parse content for color pattern: text|#colorcode or text|colorname
    const colorPattern = /^(.+?)\|(.+)$/;
    const match = content.match(colorPattern);

    let displayText = content;
    let textColor = COLORS.BLUE_VAR_11; // default color

    if (match) {
      displayText = match[1].trim();
      const colorSpec = match[2].trim();

      // Check if it's a hex color
      if (colorSpec.startsWith('#') && /^#[0-9A-Fa-f]{6}$/.test(colorSpec)) {
        textColor = colorSpec;
      }
      // Check if it's a predefined color from COLORS
      else if (COLORS[colorSpec as keyof typeof COLORS]) {
        textColor = COLORS[colorSpec as keyof typeof COLORS];
      }
      // Handle common color names
      else {
        const commonColors: Record<string, string> = {
          red: '#FF0000',
          green: '#00FF00',
          blue: '#0000FF',
          orange: '#FFA500',
          purple: '#800080',
          pink: '#FFC0CB',
          yellow: '#FFFF00',
          gray: '#808080',
          black: '#000000',
          white: '#FFFFFF',
        };
        textColor = commonColors[colorSpec.toLowerCase()] || COLORS.BLUE_VAR_11;
      }
    }
    const platform = getPlatform();
    if (platform === 'ios' || platform === 'android') {
      return (
        <View>
          <Text
            style={{
              fontSize: 12,
              color: textColor,
              lineHeight: 18,
              textAlignVertical: 'top',
              includeFontPadding: false,
              marginTop: -12,
              fontFamily: FONTS.FONT_FAMILY_700,
            }}
          >
            {displayText}
          </Text>
        </View>
      );
    }
    return (
      <Text
        style={{
          fontSize: 12,
          color: textColor,
          lineHeight: 12,
          textAlignVertical: 'top',
          includeFontPadding: false,
          marginTop: -2,
          fontFamily: FONTS.FONT_FAMILY_700,
        }}
      >
        {displayText}
      </Text>
    );
  },
  heading3: (
    node: MarkdownNode,
    children: React.ReactNode[],
    parent: any,
    styles: Record<string, any>,
  ) => {
    return (
      <View key={node.key} style={styles._VIEW_SAFE_heading3}>
        <Text>{children}</Text>
      </View>
    );
  },
  list_item: (
    node: MarkdownNode,
    children: React.ReactNode[],
    parent: any,
    styles: Record<string, any>,
    inheritedStyles = {},
  ) => {
    const original = renderRules.list_item;
    const customStyles =
      Platform.OS === 'web'
        ? {
            ...styles,
            _VIEW_SAFE_bullet_list_content: {
              ...styles._VIEW_SAFE_bullet_list_content,
              ...styles._VIEW_SAFE_paragraph,
              marginBottom: 0,
              marginTop: 0,
              marginVertical: 0,
            },
          }
        : styles;
    if (typeof original === 'function') {
      const output = original(
        node as any,
        children,
        parent,
        customStyles,
        inheritedStyles,
      );
      return output;
    }
    // Fallback: render a default list item if original is undefined
    return (
      <View key={node.key} style={customStyles._VIEW_SAFE_bullet_list_content}>
        <Text>{children}</Text>
      </View>
    );
  },
  text: (
    node: MarkdownNode,
    children: React.ReactNode[],
    parent: any,
    styles: Record<string, any>,
    inheritedStyles = {},
  ) => {
    const content = node.content || '';

    // Matches [![alt](image-url)Label](<broken or missing closing part
    // Explanation:
    // \[!\[(.*?)\]\(.*?        -> matches the alt text inside ![alt](image-url
    // \)(.*?)                  -> matches label text after image markdown, non-greedy
    // \]\(<.*                  -> matches start of broken link (](
    const brokenLinkWithImageLabel = content.match(
      /\[!\[(.*?)\]\(.*?\)(.*?)\]\(<.*/,
    );

    if (brokenLinkWithImageLabel) {
      const altText = brokenLinkWithImageLabel[1].trim();
      const label = brokenLinkWithImageLabel[2].trim();

      // Prefer the label after image markdown if present; else fallback alt
      const textToShow = label || altText;

      return (
        <Text
          key={node.key}
          style={[inheritedStyles, styles.text]}
          numberOfLines={numberOfLines}
        >
          {textToShow}
        </Text>
      );
    }

    // Matches broken image markdown only without closing ), e.g. [![alt](incomplete-url
    const brokenImageOnly = content.match(/\[!\[(.*?)\]\(.*$/);

    if (brokenImageOnly) {
      const output = content.replace(/!\[([^\]]+)\]\([^\)]*$/, '[$1]');
      return (
        <Text
          key={node.key}
          style={[inheritedStyles, styles.text]}
          numberOfLines={numberOfLines}
        >
          {output}
        </Text>
      );
    }

    const brokenLinkStartIndex = content.indexOf('](');

    if (brokenLinkStartIndex !== -1) {
      // Keep all text **up to and including the closing ]**
      // So slice up to brokenLinkStartIndex + 1
      const safeText = content.slice(0, brokenLinkStartIndex + 1);
      return (
        <Text
          key={node.key}
          style={[inheritedStyles, styles.text]}
          numberOfLines={numberOfLines}
        >
          {safeText}
        </Text>
      );
    }

    // Fallback: render text as-is
    return (
      <Text
        key={node.key}
        style={[inheritedStyles, styles.text]}
        numberOfLines={numberOfLines}
      >
        {content}
      </Text>
    );
  },
});
export function MarkdownText(props: MarkdownTextProps) {
  const { streamMarkdown: streamMarkdownFlag = true, shouldIncreaseFontSize } =
    useGetFeatureFlags();
  const { data } = useConfigSettings();
  const streamConfig = React.useMemo(
    () => data?.streamConfig || {},
    [data?.streamConfig],
  );

  const { containerWidth } = useResponsiveWidth();
  const style = getStyle({
    ...props,
    shouldIncreaseFontSize,
  });
  const {
    streamMarkdown,
    isStreaming,
    streamConfig: msgStreamConfig = {},
    numberOfLines,
  } = props;
  const rules = createRules(numberOfLines);

  const streamMarkdownConfig = useMemo(() => {
    return { ...streamConfig, ...msgStreamConfig };
  }, [msgStreamConfig, streamConfig]);

  const isStreamingEnabled = useMemo(
    () => streamMarkdown && streamMarkdownFlag,
    [streamMarkdown, streamMarkdownFlag],
  );

  const handlePress = (link: string) => {
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.LINKS_CLICKED,
    });
    trackOmnitureClickEvent('LINK_IN_TEXT_CLICKED');
    return openDeepLink(link);
  };
  const cleanedText = props?.text?.replace(/\\n/g, '\n');

  const onAnimateComplete = useCallback(() => {
    const activeConversationId =
      useMessageStore.getState().activeConversationId || 'draft';
    const conversationById =
      useMessageStore.getState().conversationById[activeConversationId];
    if (conversationById?.messages?.length > 0) {
      // If the last message is a stream message, update it to indicate streaming is complete
      const lastMessage =
        conversationById.messages[conversationById.messages.length - 1];
      if (lastMessage?.streamMessage) {
        lastMessage.streamMessage = false;
        useMessageStore.getState().updateConversationById(activeConversationId, {
          ...conversationById,
          messages: [...conversationById.messages.slice(0, -1), lastMessage],
        });
      }
    }
  }, []);
  if (!props?.text) {
    return null;
  }
  return (
    <View
      onLayout={props.onLayout}
      style={{
        flexDirection: 'column',
        overflow: 'hidden',
        width: props.adjustHeight ? containerWidth - 40 : undefined,
        flexShrink: 1,
      }}
    >
      {isStreamingEnabled ? (
        <MarkdownStream
          content={cleanedText}
          style={style}
          autoPlay={true}
          streamConfig={streamMarkdownConfig}
          onLinkPress={handlePress}
          isContentPending={!!isStreaming}
          rules={rules}
          onAnimateComplete={onAnimateComplete}
        />
      ) : (
        <Markdown
          markdownit={markdownIt}
          onLinkPress={handlePress}
          style={style}
          rules={rules}
        >
          {cleanedText}
        </Markdown>
      )}
    </View>
  );
}
