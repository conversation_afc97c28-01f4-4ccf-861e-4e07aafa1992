import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import WhatsAppIcon from '../../../assets/WhatsAppIcon';
import ChevronRight from '../../../assets/ChevronRight';
import { waShare } from '../../../native/shareUtils/share';
import { trackOmnitureClickEvent } from '../../../native/omniture';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../../native/tracking/pdt';
import { Toast } from '../../toast';

interface WAShareProps {
  shareInfo: ShareWhatsappCta;
}

interface IconRendererProps {
  icon?: string;
}

const IconRenderer = ({ icon }: IconRendererProps) => {
  const iconMap = {
    whatsapp: <WhatsAppIcon />,
  };

  // If icon matches predefined types
  if (icon && iconMap[icon as keyof typeof iconMap]) {
    return iconMap[icon as keyof typeof iconMap];
  }

  // If icon is provided but not in map, treat as URL
  if (icon && icon !== 'download' && icon !== 'whatsapp') {
    // Simple URL validation
    const isValidUrl = (url: string) => {
      try {
        new URL(url);
        return true;
      } catch {
        return false;
      }
    };

    if (isValidUrl(icon)) {
      return <Image source={{ uri: icon }} style={{ width: 36, height: 36 }} />;
    }
  }

  return <WhatsAppIcon />;
};

export const WAShare = ({ shareInfo }: WAShareProps) => {
  // If cta_title is not present, return null
  // If wa_message is not present, then destination_tag_name and link must be present
  // If wa_message is present, then destination_tag_name and link must not be present
  if (
    !shareInfo?.cta_title ||
    (!shareInfo?.wa_message &&
      (!shareInfo?.destination_tag_name || !shareInfo?.link))
  ) {
    return null;
  }
  const handleShare = async () => {
    const waMessage = shareInfo.wa_message || `${shareInfo.destination_tag_name}`;
    try {
      const result = await waShare(waMessage);
      if (result === 'WA_NOT_INSTALLED') {
        Toast.show('WhatsApp is not installed');
      }
    } catch (error) {
      // do nothing
    }
    trackOmnitureClickEvent('ASYNC_SHARE_CLICKED', {
      CONTENT_TYPE: `media_package`,
    });
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.ASYNC_SHARE_CLICKED,
    });
  };

  return (
    <TouchableOpacity style={styles.container} onPress={handleShare}>
      <View style={styles.iconContainer}>
        <IconRenderer icon={shareInfo?.icon} />
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.title}>{shareInfo.cta_title}</Text>
      </View>
      <View style={styles.arrowContainer}>
        <ChevronRight height={24} width={24} color="#757575" />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    borderRadius: 18,
    padding: 16,
    marginTop: 8,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  title: {
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: COLORS.BLACK_VAR_1,
    marginBottom: 4,
  },
  arrowContainer: {
    marginLeft: 8,
  },
});
