import { View } from 'react-native';
import React, { useEffect, useMemo, useRef } from 'react';
import { COLORS, Z_INDEX } from '../../../constants/globalStyles';
import { getPlatformSpecificUrl } from '../../../utils/getPlatform';

interface MediaInfo {
  media_type: string;
  url: string;
  thumbnail_url?: string;
  title: string;
  duration?: string;
  subtitle: string;
}

// Type definitions for HLS.js
interface HlsConfig {
  enableWorker?: boolean;
  lowLatencyMode?: boolean;
}

interface HlsErrorData {
  type: string;
  details: string;
  fatal: boolean;
}

interface HlsEvents {
  MANIFEST_PARSED: string;
  ERROR: string;
}

interface HlsErrorTypes {
  NETWORK_ERROR: string;
  MEDIA_ERROR: string;
}

interface HlsInstance {
  loadSource: (url: string) => void;
  attachMedia: (video: HTMLVideoElement) => void;
  on: (event: string, callback: (event?: any, data?: any) => void) => void;
  destroy: () => void;
  startLoad: () => void;
  recoverMediaError: () => void;
  Events: HlsEvents;
  ErrorTypes: HlsErrorTypes;
}

interface HlsConstructor {
  new (config?: HlsConfig): HlsInstance;
  isSupported: () => boolean;
  Events: HlsEvents;
  ErrorTypes: HlsErrorTypes;
}

interface DashInstance {
  initialize: (video: HTMLVideoElement, url: string, autostart?: boolean) => void;
  on: (event: string, callback: (event?: any, data?: any) => void) => void;
  reset: () => void;
  Events: DashEvents;
  MediaPlayer: () => DashInstance;
}

interface DashEvents {
  PLAYBACK_STARTED: string;
  PLAYBACK_ENDED: string;
  ERROR: string;
  MANIFEST_LOADED: string;
}

interface DashConstructor {
  new (): DashInstance;
  MediaPlayer: () => DashInstance;
}

declare global {
  interface Window {
    Hls?: HlsConstructor;
    dashjs?: DashConstructor;
  }
}

const VideoPlayerModal = ({
  shouldShowVideoPlayer,
  closeVideoPlayer,
  media,
}: {
  shouldShowVideoPlayer: boolean;
  closeVideoPlayer: () => void;
  media: MediaInfo;
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const hlsRef = useRef<HlsInstance | null>(null);
  const dashRef = useRef<DashInstance | null>(null);

  const platformUrl = useMemo(() => getPlatformSpecificUrl(media.url), [media.url]);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    // Initialize video player for different formats
    const initializeVideo = async () => {
      const isHLS = platformUrl.includes('.m3u8');
      const isDASH = platformUrl.includes('.mpd');

      if (isDASH) {
        try {
          // Load dash.js if not already loaded
          let dashjs: DashConstructor;

          if (window.dashjs) {
            dashjs = window.dashjs;
          } else {
            // Load dash.js from CDN
            const script = document.createElement('script');
            script.src = 'https://cdn.dashjs.org/latest/dash.all.min.js';
            document.head.appendChild(script);

            await new Promise<void>((resolve, reject) => {
              script.onload = () => {
                if (window.dashjs) {
                  resolve();
                } else {
                  reject(new Error('dash.js failed to load'));
                }
              };
              script.onerror = () =>
                reject(new Error('Failed to load dash.js script'));
            });

            dashjs = window.dashjs!;
          }

          // Initialize DASH player
          const dash = dashjs.MediaPlayer();
          dashRef.current = dash;

          dash.initialize(video, platformUrl, true);

          // Handle DASH events
          dash.on('playbackStarted', () => {
            // Handle playback started
          });

          dash.on('error', () => {
            // Handle DASH error and fallback to regular video src
            video.src = platformUrl;
          });
        } catch (error) {
          // DASH initialization failed, fallback to regular video src
          video.src = platformUrl;
        }
      } else if (isHLS) {
        try {
          // Try to load HLS.js
          let Hls: HlsConstructor;

          if (window.Hls) {
            Hls = window.Hls;
          } else {
            // Fallback: try to load from CDN
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/hls.js@latest';
            document.head.appendChild(script);

            await new Promise<void>((resolve, reject) => {
              script.onload = () => {
                if (window.Hls) {
                  resolve();
                } else {
                  reject(new Error('HLS.js failed to load'));
                }
              };
              script.onerror = () =>
                reject(new Error('Failed to load HLS.js script'));
            });

            Hls = window.Hls!;
          }

          if (Hls.isSupported()) {
            const hls = new Hls({
              enableWorker: true,
              lowLatencyMode: true,
            });
            hlsRef.current = hls;

            hls.loadSource(platformUrl);
            hls.attachMedia(video);

            hls.on(Hls.Events.MANIFEST_PARSED, () => {
              // Auto-play when manifest is loaded
              video.play().catch(() => {
                // Handle autoplay restriction
              });
            });

            hls.on(Hls.Events.ERROR, (_event: any, data: HlsErrorData) => {
              if (data.fatal) {
                switch (data.type) {
                  case Hls.ErrorTypes.NETWORK_ERROR:
                    hls.startLoad();
                    break;
                  case Hls.ErrorTypes.MEDIA_ERROR:
                    hls.recoverMediaError();
                    break;
                  default:
                    hls.destroy();
                    break;
                }
              }
            });
          } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            // Safari native HLS support
            video.src = platformUrl;
            video.play().catch(() => {
              // Handle autoplay restriction
            });
          }
        } catch (error) {
          // Fallback to regular video src
          video.src = platformUrl;
        }
      } else {
        // Regular video file
        video.src = platformUrl;
        video.play().catch(() => {
          // Handle autoplay restriction
        });
      }
    };

    // Function to enter fullscreen
    const enterFullscreen = async () => {
      try {
        if (video.requestFullscreen) {
          await video.requestFullscreen();
        } else if ((video as any).webkitRequestFullscreen) {
          await (video as any).webkitRequestFullscreen();
        } else if ((video as any).msRequestFullscreen) {
          await (video as any).msRequestFullscreen();
        }
      } catch (error) {
        // Silently handle fullscreen error
      }
    };

    // Function to handle fullscreen change
    const handleFullscreenChange = () => {
      const isFullscreen =
        document.fullscreenElement === video ||
        (document as any).webkitFullscreenElement === video ||
        (document as any).msFullscreenElement === video;

      if (!isFullscreen) {
        // User exited fullscreen, unmount the component
        closeVideoPlayer();
      }
    };

    // Add event listeners for fullscreen changes
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('msfullscreenchange', handleFullscreenChange);

    // Initialize video and enter fullscreen when component mounts
    if (shouldShowVideoPlayer) {
      initializeVideo();
      // Small delay to ensure video is loaded
      setTimeout(() => {
        enterFullscreen();
      }, 100);
    }

    // Cleanup function
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('msfullscreenchange', handleFullscreenChange);

      // Cleanup HLS instance
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }

      // Cleanup DASH instance
      if (dashRef.current) {
        try {
          // dash.js uses reset() method for cleanup, not destroy()
          if (typeof dashRef.current.reset === 'function') {
            dashRef.current.reset();
          }
        } catch (error) {
          // Silently handle cleanup error
        }
        dashRef.current = null;
      }
    };
  }, [platformUrl, closeVideoPlayer, shouldShowVideoPlayer]);

  if (!shouldShowVideoPlayer) {
    return null;
  }

  return (
    <View
      style={{
        position: 'fixed' as any,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: COLORS.BLACK,
        zIndex: Z_INDEX.MODAL,
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <video
        ref={videoRef}
        controls
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'contain',
        }}
        onError={() => {
          // Silently handle video loading error
        }}
      >
        Your browser does not support the video tag.
      </video>

      {/* Fallback close button for cases where fullscreen API doesn't work */}
      <button
        onClick={() => closeVideoPlayer()}
        style={{
          position: 'absolute',
          top: '20px',
          right: '20px',
          background: 'rgba(0, 0, 0, 0.7)',
          color: COLORS.WHITE,
          border: 'none',
          borderRadius: '50%',
          width: '40px',
          height: '40px',
          cursor: 'pointer',
          fontSize: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 10000,
        }}
      >
        ×
      </button>
    </View>
  );
};

export default VideoPlayerModal;
