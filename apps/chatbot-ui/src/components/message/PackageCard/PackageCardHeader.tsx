import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import { LoaderStar } from '../../../assets/loaderStars';

interface PackageCardHeaderProps {
  title: string;
  subtitle: string;
}

export const PackageCardHeader = ({ title, subtitle }: PackageCardHeaderProps) => {
  return (
    <View style={styles.container}>
      <View style={styles.headerLineContainer}>
        <View style={styles.headerLine} />
        <LoaderStar width={17} height={17} color={COLORS.GREY_VAR_1} />
        <View style={styles.headerLine} />
      </View>
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.subtitle}>{subtitle}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 24,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: COLORS.BLACK_VAR_1,
    marginBottom: 6,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.GREY_VAR_2,
    textAlign: 'center',
  },
  headerLine: {
    backgroundColor: COLORS.GREY_VAR_1,
    height: 1,
    width: 30,
  },
  headerLineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: 85,
    marginBottom: 4,
  },
});
