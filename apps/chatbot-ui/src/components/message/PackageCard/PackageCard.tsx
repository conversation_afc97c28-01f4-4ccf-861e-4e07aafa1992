import React, { useCallback, useEffect } from 'react';
import { View, Text, StyleSheet, Image, Pressable } from 'react-native';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import { ShadowedView } from 'react-native-fast-shadow';
import { LinearGradient } from 'react-native-linear-gradient';
import { MarkdownText } from '../MarkdownText';
import { openDeepLink } from '../../../native/deepLinks';
import { useMessageAction, useMessageStore } from '../../../store/messages';
import { useAppStateStore } from '../../../store/app';
import { isPlatformWeb } from '../../../utils/getPlatform';
import { trackOmnitureClickEvent } from '../../../native/omniture';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../../native/tracking/pdt';

type PackageCardSource = 'listing' | 'preview';

interface PackageCardProps {
  package: BookablePackage;
  msg: Message;
  cardPayload?: CardWidget;
  source?: PackageCardSource;
}

export const PackageCard = ({
  package: packageData,
  msg,
  cardPayload,
  source = 'listing',
}: PackageCardProps) => {
  const cardData = cardPayload?.value?.templateInfo?.payload[0]?.data;

  const { sendMessage } = useMessageAction();
  const updateConversationById = useMessageStore(
    (state) => state.updateConversationById,
  );
  const activeConversationId = useMessageStore(
    (state) => state.activeConversationId || 'draft',
  );
  const chatContext = useAppStateStore((state) => state.chatContext);
  const lobname = chatContext?.context?.lob || 'unknown';

  const isPreview = source === 'preview';

  const getVariantValue = (listingValue: number, previewValue: number) => {
    return isPreview ? previewValue : listingValue;
  };

  const handleCtaClick = useCallback(() => {
    if (isPreview) return;
    if (packageData.cta_link) {
      openDeepLink(packageData.cta_link as string);
    }
    trackOmnitureClickEvent('CARD_CLICKED', { CONTENT_TYPE: `media_package` });
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.MEDIA_CLICKED,
    });
  }, [packageData.cta_link, isPreview]);

  const handleConnectToAgentCtaClick = useCallback(() => {
    if (isPreview) return;
    if (packageData.connect_cta?.action.type === 'MESSAGE') {
      sendMessage(packageData.connect_cta.action.value, {
        eventType: 'CONNECT_TO_AGENT',
        dataProps: { metadata: packageData.connect_cta?.meta_data },
        botMetadata: { metadata: packageData.connect_cta?.meta_data },
      });
    }
  }, [packageData.connect_cta, sendMessage, isPreview]);

  const handleAskQuestionCtaClick = useCallback(() => {
    trackOmnitureClickEvent('ASK_QUESTION_CLICKED', {
      CONTENT_TYPE: `ask_question_${lobname}`,
    });
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.ASK_QUESTION_CLICKED,
    });
    if (isPreview) return;
    if (packageData.ask_question_cta?.action.type === 'REPLY') {
      updateConversationById(activeConversationId, {
        sessionContext: {
          type: 'REPLY',
          data: packageData,
          conversationMetadata: {
            messageId: msg?.id,
            contentId: cardData?.id || '',
            cardId: cardData?.id || '',
            quoteMetadata: cardData?.quote_metadata,
            templatePayload: { data: packageData },
            type: 'CARD',
          },
        },
      });
    }
  }, [
    updateConversationById,
    activeConversationId,
    packageData,
    msg,
    isPreview,
    cardData,
  ]);

  useEffect(() => {
    trackOmnitureClickEvent('CARDS_SHOWN', { CONTENT_TYPE: `media_package` });
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.MEDIA_SHOWN,
    });
  }, []);

  return (
    <ShadowedView style={isPreview ? styles.previewShadow : styles.card}>
      <View style={[styles.cardContent, isPreview && { padding: 8 }]}>
        <View style={[styles.headerSection, isPreview && { marginBottom: 6 }]}>
          {!isPreview && (
            <View style={[styles.imageContainer, isPreview && { marginRight: 6 }]}>
              <Image
                source={{
                  uri: packageData.image_url,
                }}
                style={[styles.packageImage, isPreview && { width: 42, height: 52 }]}
              />
            </View>
          )}
          <View style={styles.headerInfo}>
            <View style={styles.titleRow}>
              <Text
                style={[
                  styles.packageTitle,
                  isPreview && { fontSize: 14, marginBottom: 4, lineHeight: 14 },
                ]}
              >
                {packageData.title}
              </Text>
              <Text
                style={[
                  styles.priceTag,
                  isPreview && { fontSize: 12, lineHeight: 14 },
                ]}
              >
                {packageData.info.text}
              </Text>
            </View>

            <View style={styles.locationRow}>
              {!!packageData?.sub_title_icon && (
                <Image
                  style={[
                    styles.subtitleIcon,
                    isPreview && { height: 10, width: 8 },
                  ]}
                  source={{ uri: packageData.sub_title_icon }}
                />
              )}
              <Text
                numberOfLines={1}
                style={[
                  styles.subtitle,
                  isPreview && { fontSize: 12, marginTop: 0, lineHeight: 14 },
                ]}
              >
                {packageData.sub_title}
              </Text>
            </View>

            <View style={styles.descriptionContainer}>
              <MarkdownText
                text={packageData.description}
                lineHeight={getVariantValue(16, 12)}
                size="small"
                adjustHeight
                fontSize={isPreview ? 12 : 14}
                numberOfLines={2}
                marginBottom={0}
                marginTop={0}
              />
            </View>
          </View>
        </View>

        {isPreview ? (
          <View style={{ marginTop: 6 }}>
            <Text style={styles.previewCtaText}>{packageData.cta_title}</Text>
          </View>
        ) : (
          <Pressable
            onPress={handleCtaClick}
            style={[styles.ctaButton, isPreview && { height: 20 }]}
          >
            <LinearGradient
              colors={['rgb(83,178, 254)', 'rgb(6,90, 243)']}
              useAngle
              angle={90}
              style={styles.gradientButton}
            >
              <Text style={[styles.ctaText, isPreview && { fontSize: 9 }]}>
                {packageData.cta_title}
              </Text>
            </LinearGradient>
          </Pressable>
        )}

        {packageData.connect_cta?.action.type === 'MESSAGE' && !isPreview && (
          <Pressable
            onPress={handleConnectToAgentCtaClick}
            style={[
              styles.ctaButton,
              styles.secondaryButton,
              isPreview && { height: 20 },
            ]}
          >
            <Text
              style={[
                styles.ctaText,
                styles.secondaryButtonText,
                isPreview && { fontSize: 9 },
              ]}
            >
              {packageData.connect_cta.text}
            </Text>
          </Pressable>
        )}

        {packageData?.ask_question_cta?.action.type === 'REPLY' && !isPreview && (
          <Pressable
            onPress={handleAskQuestionCtaClick}
            style={[
              styles.ctaButton,
              styles.secondaryButton,
              isPreview && { height: 20 },
            ]}
          >
            <Text
              style={[
                styles.ctaText,
                styles.secondaryButtonText,
                isPreview && { fontSize: 9 },
              ]}
            >
              {packageData.ask_question_cta.text}
            </Text>
          </Pressable>
        )}
      </View>
    </ShadowedView>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 24,
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    backgroundColor: '#F8F8F8',
    marginBottom: 20,
    borderWidth: 1,
    borderColor: COLORS.WHITE,
  },
  previewShadow: {
    borderRadius: 12,
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    backgroundColor: '#F8F8F8',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: COLORS.WHITE,
    maxWidth: 260,
    alignSelf: 'flex-start',
    ...(!isPlatformWeb() && {
      minWidth: 260,
    }),
  },
  cardContent: {
    padding: 16,
  },
  headerSection: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  imageContainer: {
    marginRight: 12,
  },
  packageImage: {
    width: 60,
    height: 80,
    borderRadius: 8,
  },
  headerInfo: {
    flex: 1,
    justifyContent: 'space-between',
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  packageTitle: {
    fontSize: 16,
    lineHeight: 18,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: COLORS.BLACK,
    marginBottom: 4,
    flex: 1,
  },
  priceTag: {
    fontSize: 14,
    lineHeight: 16,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: '#0C58B4',
    position: 'relative',
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  subtitleIcon: {
    height: 12,
    width: 12,
    marginRight: 4,
  },
  subtitle: {
    fontSize: 14,
    lineHeight: 16,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.GREY_VAR_1,
  },
  descriptionContainer: {
    flex: 1,
    flexDirection: 'row',
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'flex-start',
    marginTop: 8,
  },
  ctaButton: {
    height: 40,
  },
  gradientButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  ctaText: {
    color: COLORS.WHITE,
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_900,
    textTransform: 'uppercase',
  },
  secondaryButton: {
    borderWidth: 1,
    borderColor: COLORS.BLUE_VAR_1,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 8,
  },
  secondaryButtonText: {
    color: COLORS.BLUE_VAR_1,
  },
  previewCtaText: {
    fontSize: 12,
    lineHeight: 14,
    color: COLORS.BLUE_VAR_1,
    fontFamily: FONTS.FONT_FAMILY_900,
  },
});
