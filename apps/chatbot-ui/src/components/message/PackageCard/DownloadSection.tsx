import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Linking,
} from 'react-native';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import DownloadPdf from '../../../assets/download_pdf';
import WhatsAppIcon from '../../../assets/WhatsAppIcon';
import ChevronRight from '../../../assets/ChevronRight';
import PdfDownloadUtils from '../../../utils/downloadPdf';

interface DownloadItinerary {
  cta_title: string;
  description: string;
  icon: string;
  link: string;
}

interface DownloadSectionProps {
  downloadInfo: DownloadItinerary;
}

interface IconRendererProps {
  icon?: string;
}

const IconRenderer = ({ icon }: IconRendererProps) => {
  const iconMap = {
    download: <DownloadPdf />,
    whatsapp: <WhatsAppIcon />,
  };

  // If icon matches predefined types
  if (icon && iconMap[icon as keyof typeof iconMap]) {
    return iconMap[icon as keyof typeof iconMap];
  }

  // If icon is provided but not in map, treat as URL
  if (icon && icon !== 'download' && icon !== 'whatsapp') {
    // Simple URL validation
    const isValidUrl = (url: string) => {
      try {
        new URL(url);
        return true;
      } catch {
        return false;
      }
    };

    if (isValidUrl(icon)) {
      return <Image source={{ uri: icon }} style={{ width: 36, height: 36 }} />;
    }
  }

  // Fallback to DownloadPdf
  return <DownloadPdf />;
};

export const DownloadSection = ({ downloadInfo }: DownloadSectionProps) => {
  const handleDownload = () => {
    PdfDownloadUtils.downloadPdf({
      url: downloadInfo.link,
      filename: downloadInfo.cta_title,
      title: downloadInfo.cta_title,
      description: downloadInfo.description,
    });
  };

  return (
    <TouchableOpacity style={styles.container} onPress={handleDownload}>
      <View style={styles.iconContainer}>
        <IconRenderer icon={downloadInfo?.icon} />
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.title}>{downloadInfo.cta_title}</Text>
        <Text style={styles.subtitle}>{downloadInfo.description}</Text>
      </View>
      <View style={styles.arrowContainer}>
        <ChevronRight height={24} width={24} color="#757575" />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    borderRadius: 18,
    padding: 16,
    marginTop: 8,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: COLORS.BLACK_VAR_1,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.GREY_VAR_2,
  },
  arrowContainer: {
    marginLeft: 8,
  },
});
