import React, { useEffect } from 'react';
import { StyleSheet, ScrollView } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { PackageCardHeader } from './PackageCardHeader';
import { VideoCard } from './VideoCard';
import { PackageSection } from './PackageSection';
import { PackageCard } from './PackageCard';
import { DownloadSection } from './DownloadSection';
import { WAShare } from './WAShare';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../../native/tracking/pdt';
import { trackOmnitureClickEvent } from '../../../native/omniture';

export const PackageCardMessage = (props: {
  data: CardWidget;
  msg: Message;
  setVideoModalData?: SetVideoModalDataType;
}) => {
  useEffect(() => {
    if (
      props?.data?.value?.templateInfo?.payload?.length &&
      props.data.value.templateInfo.payload[0]?.data
    ) {
      trackOmnitureClickEvent('CARDS_SHOWN', { CONTENT_TYPE: `media_package` });
      trackPDTEvent({
        eventName: eventNameSchema.CHAT_INTERACTED,
        eventType: eventTypeSchema.ACTION,
        eventValue: eventValueSchema.MEDIA_SHOWN,
      });
    }
  }, [
    props?.data?.value?.templateInfo?.payload?.length,
    props.data?.value?.templateInfo?.payload?.[0]?.data,
  ]);

  if (
    !props?.data?.value?.templateInfo?.payload?.length ||
    !props.data.value.templateInfo.payload[0]?.data
  ) {
    return null;
  }

  const data = props.data.value.templateInfo.payload[0].data as ItineraryCardData;

  return (
    <LinearGradient
      colors={['#FFFFFF', '#B2DCFF', '#FFFFFF']}
      locations={[0, 0.6, 1]}
      style={styles.container}
    >
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {data.itinerary_title && (
          <PackageCardHeader
            title={data.itinerary_title}
            subtitle={data.itinerary_subtitle}
          />
        )}
        {data.media && (
          <VideoCard
            media={data.media}
            setVideoModalData={props.setVideoModalData}
          />
        )}
        {data.bookable_package_title && (
          <PackageSection title={data.bookable_package_title} />
        )}
        {data.bookable_package && (
          <PackageCard
            cardPayload={props.data}
            msg={props.msg}
            package={data.bookable_package}
          />
        )}
        {data.download_itinerary && (
          <DownloadSection downloadInfo={data.download_itinerary} />
        )}
        {data?.share_whatsapp_cta && <WAShare shareInfo={data.share_whatsapp_cta} />}
      </ScrollView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // minHeight: 600,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
  },
});
