import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, FONTS } from '../../../constants/globalStyles';

interface PackageSectionProps {
  title: string;
}

export const PackageSection = ({ title }: PackageSectionProps) => {
  return (
    <View style={styles.lineContainer}>
      <View style={styles.line} />
      <Text style={styles.title}>{title}</Text>
      <View style={styles.line} />
    </View>
  );
};

const styles = StyleSheet.create({
  lineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
  },
  line: {
    backgroundColor: COLORS.GREY_VAR_1,
    height: 1,
    width: 30,
  },
  title: {
    fontSize: 16,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: "#404040",
    textAlign: 'center',
    marginHorizontal: 20,
    borderWidth: 1,
    borderColor:"transparent",
  },
});
