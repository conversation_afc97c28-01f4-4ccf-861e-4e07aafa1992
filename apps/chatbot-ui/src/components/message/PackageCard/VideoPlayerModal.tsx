import {
  View,
  Modal,
  StyleSheet,
  TouchableOpacity,
  Text,
  Animated,
  Dimensions,
  ActivityIndicator,
  ImageBackground,
  StatusBar,
  Platform,
} from 'react-native';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { COLORS } from '../../../constants/globalStyles';
import Video from 'react-native-video';
import { TouchableScale } from '../../base/TouchableScale';
import { CloseIcon } from '../../../assets/CloseIcon';
import Slider from 'react-native-slider';
import {
  PanGestureHandler,
  State,
  GestureHandlerRootView,
} from 'react-native-gesture-handler';
import FastImageWrapper from '../../image/FastImageWrapper';
import SoundOn from '../../../assets/SoundOn';
import SoundOff from '../../../assets/SoundOff';
import { getPlatformSpecificUrl } from '../../../utils/getPlatform';
import { stopTtsIfPlaying } from '../../../store/audio-talkback/talkbackStore';

// Custom icons for video controls
const PauseIcon = ({ size = 24 }) => (
  <FastImageWrapper
    source={require('../../../assets/IconPause.webp')}
    style={{ width: size, height: size }}
  />
);

const PlayIcon = ({ size = 24 }) => (
  <FastImageWrapper
    source={require('../../../assets/IconPlay.webp')}
    style={{ width: size, height: size }}
  />
);

const BackwardIcon = ({ size = 24 }) => (
  <FastImageWrapper
    source={require('../../../assets/IconReplayBackWard.webp')}
    style={{ width: size, height: size }}
  />
);

const ForwardIcon = ({ size = 24 }) => (
  <FastImageWrapper
    source={require('../../../assets/IconReplayForward.webp')}
    style={{ width: size, height: size }}
  />
);

const VideoPlayerModal = ({
  shouldShowVideoPlayer,
  closeVideoPlayer,
  media,
}: {
  shouldShowVideoPlayer: boolean;
  closeVideoPlayer: () => void;
  media: MediaInfo;
}) => {
  const [paused, setPaused] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showControls, setShowControls] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [isSliding, setIsSliding] = useState(false);
  const [muted, setMuted] = useState(false);
  const [playbackId, setPlaybackId] = useState<string>('');

  const videoRef = useRef<any>(null);
  const controlsOpacity = useRef(new Animated.Value(1)).current;
  const controlsTimeout = useRef<NodeJS.Timeout | null>(null);
  const modalTranslateY = useRef(new Animated.Value(0)).current;
  const modalOpacity = useRef(new Animated.Value(1)).current;
  const isDragging = useRef(false);

  useEffect(() => {
    if (videoRef.current && typeof videoRef.current.stop === 'function') {
      videoRef.current?.stop();
    }
  }, []);

  const screenHeight = Dimensions.get('window').height;

  // Generate unique playback ID
  const generatePlaybackId = () => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `pb_${timestamp}_${random}`;
  };

  const hideControls = useCallback(() => {
    Animated.timing(controlsOpacity, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setShowControls(false);
    });
  }, [controlsOpacity]);

  const showControlsAnimated = () => {
    setShowControls(true);
    Animated.timing(controlsOpacity, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  // Auto-hide controls after 3 seconds
  useEffect(() => {
    if (showControls && !paused) {
      if (controlsTimeout.current) {
        clearTimeout(controlsTimeout.current);
      }
      controlsTimeout.current = setTimeout(() => {
        hideControls();
      }, 3000);
    }

    return () => {
      if (controlsTimeout.current) {
        clearTimeout(controlsTimeout.current);
      }
    };
  }, [showControls, paused, hideControls]);

  const onCloseClick = () => {
    closeVideoPlayer();
    setPaused(true);
    setIsLoading(true);
    // Reset modal position
    modalTranslateY.setValue(0);
    modalOpacity.setValue(1);
  };

  const onVideoPress = () => {
    if (showControls) {
      hideControls();
    } else {
      showControlsAnimated();
    }
  };

  const onPlayPause = () => {
    setPaused(!paused);
    if (!paused) {
      showControlsAnimated();
    }
  };

  const onBackward = () => {
    try {
      if (!videoRef.current || isLoading || isError || duration === 0) {
        return;
      }

      const newTime = Math.max(0, currentTime - 10);

      // SEEK FUNCTIONALITY DISABLED FOR TESTING
      // setTimeout(() => {
      //   try {
      //     videoRef.current?.seek(newTime);
      //     setCurrentTime(newTime);
      //   } catch (error) {
      //     // Silent error handling - seek operation failed
      //   }
      // }, 50);

      showControlsAnimated();
    } catch (error) {
      // Silent error handling - backward operation failed
    }
  };

  const onForward = () => {
    try {
      if (!videoRef.current || isLoading || isError || duration === 0) {
        return;
      }

      const newTime = Math.min(duration, currentTime + 10);

      // SEEK FUNCTIONALITY DISABLED FOR TESTING
      // setTimeout(() => {
      //   try {
      //     videoRef.current?.seek(newTime);
      //     setCurrentTime(newTime);
      //   } catch (error) {
      //     // Silent error handling - seek operation failed
      //   }
      // }, 50);

      showControlsAnimated();
    } catch (error) {
      // Silent error handling - forward operation failed
    }
  };

  const onProgress = (data: any) => {
    stopTtsIfPlaying('video_player_playing');
    setCurrentTime(data.currentTime);
  };

  const onLoad = (data: any) => {
    setDuration(data.duration);
    stopTtsIfPlaying('video_player_started');
    setIsLoading(false);
  };

  const onLoadStart = () => {
    setIsLoading(true);
    // Generate new playback ID for this session
    setPlaybackId(generatePlaybackId());
  };

  const onSliderValueChange = (value: number) => {
    if (!isSliding) return;
    const newTime = value * duration;
    // setCurrentTime(newTime);
  };

  const onSlidingStart = () => {
    setIsSliding(true);
    showControlsAnimated();
  };

  const onSlidingComplete = (value: number) => {
    const newTime = value * duration;

    try {
      if (!videoRef.current || isLoading || isError || duration === 0) {
        setIsSliding(false);
        return;
      }

      // SEEK FUNCTIONALITY DISABLED FOR TESTING
      // setTimeout(() => {
      //   try {
      //     videoRef.current?.seek(newTime);
      //     setCurrentTime(newTime);
      //   } catch (error) {
      //     // Silent error handling - seek operation failed
      //   }
      // }, 50);

      setIsSliding(false);
    } catch (error) {
      // Silent error handling - sliding operation failed
      setIsSliding(false);
    }
  };

  const onToggleMute = () => {
    setMuted(!muted);
    showControlsAnimated();
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const sliderValue = duration > 0 ? currentTime / duration : 0;

  // Drag to close functionality
  const onPanGestureEvent = Animated.event(
    [{ nativeEvent: { translationY: modalTranslateY } }],
    { useNativeDriver: true },
  );

  const onPanHandlerStateChange = (event: any) => {
    if (event.nativeEvent.oldState === State.ACTIVE) {
      const { translationY, velocityY } = event.nativeEvent;

      // Close if dragged down more than 100px or has high downward velocity
      if (translationY > 100 || velocityY > 1000) {
        // Animate out
        Animated.parallel([
          Animated.timing(modalTranslateY, {
            toValue: screenHeight,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(modalOpacity, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start(() => {
          onCloseClick();
        });
      } else {
        // Animate back to original position
        Animated.parallel([
          Animated.spring(modalTranslateY, {
            toValue: 0,
            useNativeDriver: true,
          }),
          Animated.spring(modalOpacity, {
            toValue: 1,
            useNativeDriver: true,
          }),
        ]).start();
      }
      isDragging.current = false;
    } else if (event.nativeEvent.state === State.BEGAN) {
      isDragging.current = true;
    }
  };

  const platformUrl = getPlatformSpecificUrl(media.url);
  const platformThumbnailUrl = getPlatformSpecificUrl(media.thumbnail_url);
  if (!media) {
    return null;
  }
  return (
    <Modal
      visible={shouldShowVideoPlayer}
      onRequestClose={onCloseClick}
      style={styles.modal}
      animationType="none"
      transparent
    >
      <StatusBar
        barStyle="light-content"
        backgroundColor="black"
        translucent={true}
      />
      <GestureHandlerRootView style={styles.gestureContainer}>
        <PanGestureHandler
          onGestureEvent={onPanGestureEvent}
          onHandlerStateChange={onPanHandlerStateChange}
          minPointers={1}
          maxPointers={1}
        >
          <Animated.View
            style={[
              styles.container,
              {
                opacity: modalOpacity,
                transform: [{ translateY: modalTranslateY }],
              },
            ]}
          >
            <CloseIcon style={styles.closeButton} onPress={onCloseClick} />
            <TouchableOpacity
              style={styles.videoContainer}
              onPress={onVideoPress}
              activeOpacity={1}
            >
              {platformUrl ? (
                <Video
                  ref={videoRef}
                  source={{ uri: platformUrl }}
                  style={styles.video}
                  controls={false}
                  paused={paused}
                  muted={muted}
                  resizeMode="contain"
                  repeat={false}
                  onProgress={onProgress}
                  onLoad={onLoad}
                  onLoadStart={onLoadStart}
                  onError={() => {
                    setIsLoading(false);
                    setIsError(true);
                  }}
                />
              ) : (
                <View style={styles.loadingContainer}>
                  <Text style={styles.loadingText}>
                    Video not available for this platform
                  </Text>
                </View>
              )}

              {/* Loading State with Thumbnail */}
              {isError ? (
                <View style={styles.loadingContainer}>
                  <Text style={styles.loadingText}>Something went wrong</Text>
                  {playbackId && (
                    <Text style={styles.playbackIdText}>
                      Playback ID: {playbackId}
                    </Text>
                  )}
                </View>
              ) : isLoading ? (
                <View style={styles.loadingContainer}>
                  <View style={styles.thumbnailContainer}>
                    <ImageBackground
                      source={{
                        uri: platformThumbnailUrl || '',
                      }}
                      style={styles.backgroundImage}
                      imageStyle={styles.imageStyle}
                    ></ImageBackground>
                    <ActivityIndicator
                      size="large"
                      color={COLORS.WHITE}
                      style={styles.loadingSpinner}
                    />
                  </View>
                </View>
              ) : (
                <Animated.View
                  style={[styles.overlay, { opacity: controlsOpacity }]}
                >
                  {/* Center Controls */}
                  <View style={styles.centerControls}>
                    <TouchableScale
                      style={styles.controlButton}
                      onPress={onBackward}
                    >
                      <BackwardIcon size={30} />
                    </TouchableScale>

                    <TouchableScale
                      style={styles.playPauseButton}
                      onPress={onPlayPause}
                    >
                      {paused ? <PlayIcon size={40} /> : <PauseIcon size={40} />}
                    </TouchableScale>

                    <TouchableScale style={styles.controlButton} onPress={onForward}>
                      <ForwardIcon size={30} />
                    </TouchableScale>
                  </View>

                  {/* Bottom Controls */}
                  <View style={styles.bottomControls}>
                    <Text style={styles.timeText}>{formatTime(currentTime)}</Text>

                    <View style={styles.sliderContainer}>
                      <Slider
                        style={styles.slider}
                        minimumValue={0}
                        maximumValue={1}
                        value={sliderValue}
                        onValueChange={onSliderValueChange}
                        onSlidingStart={onSlidingStart}
                        onSlidingComplete={onSlidingComplete}
                        minimumTrackTintColor={COLORS.RED}
                        maximumTrackTintColor={COLORS.WHITE_VAR_1}
                        thumbStyle={styles.sliderThumb}
                        trackStyle={styles.sliderTrack}
                      />
                    </View>

                    <Text style={styles.timeText}>{formatTime(duration)}</Text>
                    <TouchableScale
                      style={styles.soundButton}
                      onPress={onToggleMute}
                    >
                      {muted ? (
                        <SoundOff width={24} height={24} color={COLORS.WHITE} />
                      ) : (
                        <SoundOn width={24} height={24} color={COLORS.WHITE} />
                      )}
                    </TouchableScale>
                  </View>
                </Animated.View>
              )}
            </TouchableOpacity>
          </Animated.View>
        </PanGestureHandler>
      </GestureHandlerRootView>
    </Modal>
  );
};

export default VideoPlayerModal;

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    backgroundColor: COLORS.BLACK,
  },
  gestureContainer: {
    flex: 1,
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.BLACK,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoContainer: {
    width: '100%',
    aspectRatio: 16 / 9,
    position: 'relative',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: COLORS.BLACK,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 5,
  },
  loadingText: {
    color: COLORS.WHITE,
    fontSize: 16,
    fontWeight: '500',
    marginTop: 10,
  },
  playbackIdText: {
    color: COLORS.WHITE,
    fontSize: 12,
    fontWeight: '400',
    marginTop: 8,
    textAlign: 'center',
    opacity: 0.8,
  },
  thumbnailContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    width: '100%',
  },
  loadingSpinner: {
    marginTop: 20,
    position: 'absolute',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: COLORS.BLACK_VAR_5,
  },
  closeButton: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 50 : 20,
    left: 20,
    zIndex: 10,
  },
  centerControls: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    transform: [{ translateY: -20 }],
    gap: 40,
  },
  controlButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  playPauseButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomControls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 10,
    gap: 0,
  },
  timeText: {
    color: COLORS.WHITE,
    fontSize: 14,
    fontWeight: '500',
    minWidth: 45,
    textAlign: 'center',
  },
  sliderContainer: {
    flex: 1,
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 10,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  sliderTrack: {
    height: 4,
    borderRadius: 2,
  },
  sliderThumb: {
    width: 16,
    height: 16,
    backgroundColor: COLORS.WHITE,
    borderRadius: 8,
  },
  backgroundImage: {
    height: '100%',
    justifyContent: 'space-between',
    backgroundColor: COLORS.BLACK,
    width: '100%',
  },
  imageStyle: {},
  soundButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
