import React, { useEffect, useState } from 'react';
import {
  ImageBackground,
  Pressable,
  StyleProp,
  StyleSheet,
  Text,
  View,
  ViewStyle,
} from 'react-native';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import { ShadowedView } from 'react-native-fast-shadow';
import { LinearGradient } from 'react-native-linear-gradient';
import PlayButton from '../../../assets/PlayButton';
import VideoPlayerModal from './VideoPlayerModal';
import { getPlatformSpecificUrl } from '../../../utils/getPlatform';
import { Toast } from '../../toast';
import { trackOmnitureClickEvent } from '../../../native/omniture';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../../native/tracking/pdt';
import { stopTtsIfPlaying } from '../../../store/audio-talkback/talkbackStore';

interface MediaInfo {
  media_type: string;
  url: {
    ios: string;
    android: string;
    web: string;
  };
  thumbnail_url?: {
    ios: string;
    android: string;
    web: string;
  };
  title: string;
  duration?: string;
  subtitle: string;
  titleColor?: string;
  subtitleColor?: string;
}

interface VideoCardProps {
  media: MediaInfo;
  containerStyle?: StyleProp<ViewStyle>;
  setVideoModalData?: SetVideoModalDataType;
}

export const VideoCard = ({
  media,
  containerStyle = {},
  setVideoModalData,
}: VideoCardProps) => {
  const imageUri =
    getPlatformSpecificUrl(media.thumbnail_url) || getPlatformSpecificUrl(media.url);

  const onViewPlayClick = () => {
    const videoUrl = getPlatformSpecificUrl(media.url);
    if (!videoUrl) {
      Toast.show('Video not available for this platform');
      return;
    }
    stopTtsIfPlaying('video_playback_started');
    setVideoModalData?.({ show: true, media });
    trackOmnitureClickEvent('CARD_CLICKED', {
      CONTENT_TYPE: `media_itinerary_video`,
    });
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.MEDIA_CLICKED,
    });
  };

  const [showVideoPlayer, setShowVideoPlayer] = useState(false);

  useEffect(() => {
    trackOmnitureClickEvent('CARDS_SHOWN', {
      CONTENT_TYPE: `media_itinerary_video`,
    });
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.MEDIA_SHOWN,
    });
    // trackPDTEvent({
    //   eventName: 'chat_activity',
    //   eventType: 'page_view',
    //   eventValue: 'async_video_autoplayed',
    // });
    return () => {
      setShowVideoPlayer(false);
    };
  }, []);

  return (
    <ShadowedView style={[styles.card, containerStyle]}>
      <ImageBackground
        source={{ uri: imageUri }}
        style={styles.backgroundImage}
        imageStyle={styles.imageStyle}
      >
        <LinearGradient
          colors={[
            'rgba(0, 0, 0, 0.6)',
            'rgba(0, 0, 0, 0.1)',
            'rgba(0, 0, 0, 0.0)',
            'rgba(0, 0, 0, 0.6)',
            'rgba(0, 0, 0, 1)',
          ]}
          locations={[0, 0.2, 0.5, 0.8, 1]}
          style={styles.gradientOverlay}
        />
        <View style={styles.content}>
          {media.duration && media.media_type === 'video' && (
            <View style={styles.durationContainer}>
              <Text style={styles.duration}>{media.duration}</Text>
            </View>
          )}
          <View style={styles.titleContainer}>
            <Text
              style={[styles.title, { color: media.titleColor || COLORS.WHITE }]}
            >
              {media.title}
            </Text>
            <Text
              style={[
                styles.subtitle,
                { color: media.subtitleColor || COLORS.WHITE },
              ]}
            >
              {media.subtitle}
            </Text>
            {media.media_type === 'video' && (
              <Pressable
                style={styles.playButtonContainer}
                onPress={onViewPlayClick}
              >
                <PlayButton />
              </Pressable>
            )}
          </View>
        </View>
      </ImageBackground>
    </ShadowedView>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 24,
    borderWidth: 1.5,
    borderColor: COLORS.WHITE,
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    backgroundColor: COLORS.WHITE,
    marginBottom: 26,
    overflow: 'hidden',
  },
  backgroundImage: {
    height: 200,
    justifyContent: 'space-between',
    backgroundColor: COLORS.BLACK,
  },
  imageStyle: {
    borderRadius: 16,
  },
  gradientOverlay: {
    ...StyleSheet.absoluteFillObject,
    borderRadius: 16,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  durationContainer: {
    alignSelf: 'flex-end',
    backgroundColor: COLORS.BLACK_VAR_5,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    position: 'absolute',
    top: 16,
    right: 16,
  },
  duration: {
    color: COLORS.WHITE,
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_500,
  },
  titleContainer: {
    alignSelf: 'flex-start',
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    width: '100%',
  },
  title: {
    color: COLORS.WHITE,
    fontSize: 20,
    fontFamily: FONTS.FONT_FAMILY_700,
    marginBottom: 4,
    textAlign: 'center',
  },
  subtitle: {
    color: COLORS.WHITE,
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
  },
  playButtonContainer: {
    position: 'absolute',
  },
});
