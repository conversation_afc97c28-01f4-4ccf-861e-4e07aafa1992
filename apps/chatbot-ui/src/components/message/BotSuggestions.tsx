import React, { useEffect, useRef, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { TouchableFeedback } from '../base/TouchableFeeback';
import { MSG_OUTER_PADDING_VERT } from './messageStyles';
import { trackOmnitureClickEvent } from '../../native/omniture';
import { COLORS, FONTS } from '../../constants/globalStyles';
import Animated, { FadeIn, SlideInRight } from 'react-native-reanimated';
import { ScrollView } from 'react-native-gesture-handler';
import { useKeyboardState } from '../../utils/screenUtils';
import { Analytics, TrackingEvent } from '../../analytics';
import LinearGradient from 'react-native-linear-gradient';
import { sleep } from '../../utils/functionUtils';
// import { Text } from 'react-native-svg';

const textEnterAnim = FadeIn.delay(450).duration(300);

export const BotSuggestions: React.FC<{
  suggestions: Conversation['suggestions'];
  requestScrollToBottom: () => void;
  showOnAction: boolean;
  onSuggestionClick: (
    suggestion: string,
    allSuggestions: NonNullable<Conversation['suggestions']>,
  ) => void;
}> = ({ suggestions, showOnAction, onSuggestionClick, requestScrollToBottom }) => {
  const keyboardState = useKeyboardState();
  const keyboardStateRef = useRef(useKeyboardState());
  keyboardStateRef.current = keyboardState;
  const isKeyboardShown = keyboardState === 'SHOWN';
  const [showSuggestions, setShowSuggestions] = useState(
    !showOnAction || isKeyboardShown, // if showOnAction=false, show when keyboard is shown
  );
  useEffect(() => {
    if (!suggestions?.items?.length) {
      return;
    }
    if (!showSuggestions && keyboardState === 'SHOWN') {
      setShowSuggestions(true);
    }
    if (keyboardState === 'SHOWN') {
      // scroll behavior is not deterministic, so retrying pessimistically with back-offs
      (async () => {
        for (let i = 1; i <= 3; i++) {
          await sleep(i * 100);
          if (keyboardStateRef.current === 'SHOWN') {
            requestScrollToBottom();
          }
        }
      })();
    }
  }, [showSuggestions, keyboardState, suggestions]);

  if (!suggestions?.items?.length || !showSuggestions) {
    return null;
  }
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      nativeID="bot-suggestions-scrollview"
    >
      <View style={styles.suggestions}>
        {suggestions.items?.map((suggestion, index) => {
          return (
            <Animated.View
              entering={SlideInRight.duration(600).delay(index * 100)}
              style={{
                borderRadius: 15,
                overflow: 'hidden',
                position: 'relative',
                justifyContent: 'center',
                // alignItems: 'center',
              }}
              key={`${suggestion}-${index}`}
            >
              <LinearGradient
                colors={['#66B0F0', '#718FF5']}
                style={styles.gradientColor}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              />
              <View style={styles.suggestionItem}>
                <TouchableFeedback
                  containerStyle={styles.suggestionItemButtonContainer}
                  activeOpacity={0.5}
                  style={styles.suggestionItemButton}
                  onPress={() => {
                    trackOmnitureClickEvent('SUGGESTION_PROMPTS_CLICKED');
                    onSuggestionClick(suggestion, suggestions);
                    Analytics.trackClickEvent(TrackingEvent.payload_PromptClicked());
                  }}
                >
                  <Animated.Text
                    entering={textEnterAnim}
                    style={styles.suggestionText}
                  >
                    {suggestion}
                  </Animated.Text>
                </TouchableFeedback>
              </View>
            </Animated.View>
          );
        })}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  suggestions: {
    // display: keyboardState === 'SHOWN' ? 'flex' : 'none',
    flexDirection: 'row',
    gap: 12,
    flex: 1,
    paddingHorizontal: 15,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    paddingVertical: MSG_OUTER_PADDING_VERT,
    marginTop: -8,
  },
  suggestionText: {
    paddingVertical: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 14,
    lineHeight: 16,
    color: COLORS.GREY_VAR_2,
    textAlign: 'center',
  },
  gradientColor: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 16,
    // alignSelf: 'center',
  },
  suggestionItem: {
    padding: 1.2,
    paddingHorizontal: 1.2,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 0.15,
    marginLeft: 0.16,
    borderRadius: 16,
  },
  suggestionItemButtonContainer: {
    backgroundColor: COLORS.GREY_VAR_8,
    borderRadius: 15,
    flex: 1,
    paddingHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  suggestionItemButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
