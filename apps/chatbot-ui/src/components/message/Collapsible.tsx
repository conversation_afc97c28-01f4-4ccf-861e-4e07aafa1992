import React, { useCallback, useState } from 'react';
import {
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewProps,
} from 'react-native';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { LinearGradient } from 'react-native-linear-gradient';
import Animated, {
  CurvedTransition,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { trackOmnitureClickEvent } from '../../native/omniture';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import useGetFeatureFlags from '../../hooks/useGetFeatureFlags';
import { isPlatformWeb } from '../../utils/getPlatform';

type CollapsibleProps = {
  children: React.ReactNode;
  containerStyle?: ViewProps['style'];
  maxHeight: number;
  showMoreText?: string;
  showLessText?: string;
  onChange?: (isExpanded: boolean) => void;
};

const COLLAPSE_BAR_HEIGHT = 70;

export function Collapsible(props: CollapsibleProps) {
  const {
    children,
    containerStyle,
    maxHeight,
    showMoreText = 'Read More',
    showLessText = 'Read Less',
    onChange,
  } = props;
  const sv = useSharedValue(COLLAPSE_BAR_HEIGHT);
  const [enableCollapse, setEnableCollapse] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const handleToggle = () => {
    if (isExpanded) {
      sv.value = withTiming(COLLAPSE_BAR_HEIGHT, { duration: 300 });
      onChange && onChange(false);
    } else {
      sv.value = withTiming(0, { duration: 0 });
      onChange && onChange(false);
    }
    setIsExpanded(!isExpanded);
    trackOmnitureClickEvent(isExpanded ? 'VIEW_LESS_CLICKED' : 'VIEW_MORE_CLICKED');
    if (!isExpanded) {
      trackPDTEvent({
        eventName: eventNameSchema.CHAT_INTERACTED,
        eventType: eventTypeSchema.ACTION,
        eventValue: eventValueSchema.READ_MORE,
      });
    }
  };

  const onLayout: ViewProps['onLayout'] = useCallback(
    (event: { nativeEvent: { layout: { height: number } } }) => {
      event.nativeEvent.layout.height > maxHeight
        ? setEnableCollapse(true)
        : setEnableCollapse(false);
    },
    [],
  );
  const getCollapsibleViewStyle = () => {
    const style = {
      maxHeight: isExpanded ? undefined : maxHeight,
      overflow: Platform.select({
        web: 'hidden' as const,
        default: 'scroll' as const,
      }),
    };
    return style;
  };

  return (
    <View style={[containerStyle, { flexDirection: 'column' }]}>
      {!isPlatformWeb() ? (
        <Animated.View
          layout={CurvedTransition.duration(300)}
          style={getCollapsibleViewStyle()}
        >
          <View onLayout={onLayout}>{children}</View>
        </Animated.View>
      ) : (
        <View style={getCollapsibleViewStyle()}>
          <View onLayout={onLayout}>{children}</View>
        </View>
      )}
      <View>
        <TouchableOpacity
          activeOpacity={1}
          onPress={handleToggle}
          style={[
            {
              display: enableCollapse ? 'flex' : 'none',
            },
            styles.collapsibleTextContainer,
          ]}
        >
          <View style={styles.toggleButton}>
            <Animated.View
              style={[
                {
                  height: sv,
                },
                styles.animatedGradientContainer,
              ]}
            >
              <LinearGradient
                colors={['#ffffffff', '#ffffff00']}
                useAngle
                angle={0}
                pointerEvents={'none'}
                angleCenter={{ x: 0.5, y: 0.1 }}
                style={{
                  height: COLLAPSE_BAR_HEIGHT,
                }}
              />
            </Animated.View>

            <Text style={styles.toggleButtonText}>
              {isExpanded ? showLessText : showMoreText}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
}

export function CollapsibleWithFeatureFlag(props: CollapsibleProps) {
  const featureConfig = useGetFeatureFlags();

  if (!featureConfig.showReadMore) {
    return (
      <View style={[props.containerStyle, styles.columnLayout]}>
        <View>{props.children}</View>
      </View>
    );
  }

  return <Collapsible {...props} />;
}

const styles = StyleSheet.create({
  toggleButton: {
    position: 'relative',
    // backgroundColor: 'red',
    height: COLLAPSE_BAR_HEIGHT,
    alignSelf: 'stretch',
    justifyContent: 'flex-end',
  },
  columnLayout: {
    flexDirection: 'column',
  },
  toggleButtonText: {
    backgroundColor: 'transparent',
    color: COLORS.BLUE_VAR_1,
    fontFamily: FONTS.FONT_FAMILY_700,
    zIndex: 2,
  },
  animatedGradientContainer: {
    bottom: 0,
    left: 0,
    right: 0,
    marginHorizontal: -16,
    position: 'absolute',
    zIndex: 1,
  },
  collapsibleTextContainer: {
    backgroundColor: COLORS.TRANSPARENT,
    alignSelf: 'stretch',
  },
});
