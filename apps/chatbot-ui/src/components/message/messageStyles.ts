import {
  Dimensions,
  StyleSheet,
  useWindowDimensions,
  ViewStyle,
} from 'react-native';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { CARDS_WIDTH_MAP } from '../../const';

export function getMaxMsgWidth() {
  return Dimensions.get('screen').width;
}

export function getInnerMsgWidth() {
  return getMaxMsgWidth() - 40;
}

export function useSuggestionsWidth() {
  return useWindowDimensions().width - 32;
}

export function useCardsWidth() {
  return useWindowDimensions().width - 20;
}
const baseMessageBubbleStyles: ViewStyle = {
  maxWidth: getMaxMsgWidth(),
  borderRadius: 16,
  borderWidth: 1,
  borderColor: COLORS.BLACK_VAR_2,
  overflow: 'hidden',
  paddingHorizontal: 20,
  flexDirection: 'row',
  marginTop: 20,
};

const BOT_ICON_SIZE = 30;
const BOT_ICON_START_GAP = 0;
const BOT_MSG_START_GAP = 16;
export const MSG_OUTER_PADDING_VERT = 16;
export const MSG_INNER_GAP = 14;
export const BOT_MSG_START_X = BOT_MSG_START_GAP;

export const messageBubbleStyles = StyleSheet.create({
  botMessageContainer: {
    position: 'relative',
    alignItems: 'flex-start',
    flexDirection: 'row',
    marginTop: 20,
    // paddingVertical: MSG_OUTER_PADDING_VERT,
  },
  botMessageInnerContainer: {
    flexDirection: 'column',
    gap: MSG_INNER_GAP,
    width: '100%',
  },
  botIcon: {
    width: BOT_ICON_SIZE,
    height: BOT_ICON_SIZE,
    marginRight: BOT_MSG_START_GAP,
    marginLeft: BOT_ICON_START_GAP,
  },
  botMessage: {
    ...baseMessageBubbleStyles,
    marginTop: 0,
    borderWidth: 0,
  },
  agentMessage: {
    ...baseMessageBubbleStyles,
    // borderColor: 'red',
    backgroundColor: COLORS.BLUE_VAR_5,
    marginTop: 0,
    // backgroundColor: 'red',
    // paddingVertical: 8,
    alignSelf: 'flex-start',
    alignItems: 'flex-start',
    borderColor: 'transparent',
    borderTopLeftRadius: 0,
  },

  agentMessageContainer: {
    alignSelf: 'flex-start',
    flexDirection: 'row',
    marginHorizontal: 16,
  },
  agentMessageHeader: {
    flexDirection: 'row',
    marginTop: 32,
  },
  agentNameText: {
    fontSize: 16,
    color: COLORS.BROWN_VAR_1,
    fontFamily: FONTS.FONT_FAMILY_900,
    marginLeft: 8,
  },

  userMessageContainer: {
    alignSelf: 'flex-end',
    flexDirection: 'row',
    marginRight: 20,
    maxWidth: getMaxMsgWidth() * 0.9,
  },
  userMessageFailedContainer: {
    alignSelf: 'flex-end',
    flexDirection: 'row',
    marginRight: 20,
    marginTop: 4,
    maxWidth: getMaxMsgWidth() * 0.9,
  },
  userMessageFailedText: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.RED_VAR_2,
  },
  tableMessageContainer: {
    marginLeft: 20,
  },
  userMessage: {
    ...baseMessageBubbleStyles,
    paddingVertical: 11,
    borderRadius: 16,
    borderTopRightRadius: 0,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.02)',
    alignSelf: 'flex-start',
    alignItems: 'flex-start',
    backgroundColor: COLORS.BLUE_VAR_3,
    flexShrink: 1,
    display: 'flex',
    padding: 0,
    flexDirection: 'column',
  },
  messageWithCard: {
    borderTopRightRadius: 0,
    paddingBottom: 11,
    maxWidth: getMaxMsgWidth(),
    overflow: 'hidden',
    paddingHorizontal: 12,
    marginTop: 20,
    width: CARDS_WIDTH_MAP['itinerary-card'] + 22,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.02)',
    alignSelf: 'flex-end',
    alignItems: 'flex-end',
    backgroundColor: COLORS.BLUE_VAR_3,
    flexShrink: 1,
    display: 'flex',
    flexDirection: 'column',
  },
  userMessageText: {
    fontSize: 14,
    lineHeight: 22,
    textAlign: 'right',
    color: COLORS.BLACK_VAR_1,
    fontFamily: FONTS.FONT_FAMILY_400,
  },
  botMessageBubble: {
    flexDirection: 'row',
    marginLeft: 20,
    alignContent: 'center',
    gap: 6,
  },
});
