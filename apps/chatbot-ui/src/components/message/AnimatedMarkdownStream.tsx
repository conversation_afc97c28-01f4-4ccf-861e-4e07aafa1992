import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Markdown, { MarkdownIt } from 'react-native-markdown-display';
import { triggerSuccessHaptic } from '../../utils/hapticUtils';

const markdownIt = MarkdownIt();

// Configure markdown custom loading indicator
markdownIt.inline.ruler.after('emphasis', 'loader', (state, silent) => {
  const start = state.pos;
  const marker = '[loader]';
  const endMarker = '[/loader]';

  if (state.src.slice(start, start + marker.length) !== marker) return false;
  const end = state.src.indexOf(endMarker, start);
  if (end === -1) return false;

  if (!silent) {
    const token = state.push('loader', '', 0);
    token.content = state.src.slice(start + marker.length, end);
  }

  state.pos = end + endMarker.length;
  return true;
});

export interface StreamConfig {
  chunkCount: number;
  chunkIntervalMs: number;
}

interface AnimatedMarkdownStreamProps {
  content: string;
  autoPlay?: boolean;
  streamConfig?: StreamConfig | {};
  onLinkPress?: ((url: string) => boolean) | undefined;
  style?: any;
  rules?: any;
  isContentPending: boolean;
  forceCompleteStreaming?: boolean;
  onAnimateComplete: () => void;
}

const DEFAULT_STREAMING_CONFIG = {
  chunkCount: 5,
  chunkIntervalMs: 70,
};

const resolveConfig = (streamingConfig = {} as StreamConfig) => {
  const { chunkCount, chunkIntervalMs } = {
    ...DEFAULT_STREAMING_CONFIG,
    ...streamingConfig,
  };

  const clampedChunkCount = Math.max(1, chunkCount);
  const clampedChunkIntervalMs = Math.max(20, chunkIntervalMs);

  return {
    chunkCount: clampedChunkCount,
    chunkIntervalMs: clampedChunkIntervalMs,
  };
};

export const MarkdownStream = ({
  content,
  isContentPending,
  autoPlay = true,
  streamConfig,
  forceCompleteStreaming = false,
  onAnimateComplete,
  ...markdownProps
}: AnimatedMarkdownStreamProps) => {
  const contentRef = useRef(content);
  const contentPendingRef = useRef(isContentPending);

  const currentAnimatedIndexRef = useRef(0);
  const [displayContent, setDisplayContent] = useState('');
  const [streamingStatus, setStreamingStatus] = useState('idle');
  const isStreamingIdle = streamingStatus === 'idle';
  const isStreamingDone = streamingStatus === 'done';
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const config = useMemo(
    () => resolveConfig(streamConfig as StreamConfig | undefined),
    [streamConfig],
  );

  const onAnimationComplete = useCallback(() => {
    intervalRef.current && clearInterval(intervalRef.current);
    setStreamingStatus('done');
    if (!contentPendingRef.current) {
      onAnimateComplete();
    }
  }, [onAnimateComplete]);

  const startAnimation = useCallback(() => {
    setStreamingStatus('streaming');

    // Start the animation logic here
    intervalRef.current = setInterval(() => {
      if (currentAnimatedIndexRef.current >= contentRef.current.length) {
        onAnimationComplete();
        return;
      }

      setDisplayContent((prev) => {
        const newContent = contentRef.current.slice(
          currentAnimatedIndexRef.current,
          currentAnimatedIndexRef.current + config.chunkCount,
        );
        const newDisplayContent = prev + newContent;

        currentAnimatedIndexRef.current = newDisplayContent.length;

        return newDisplayContent;
      });
    }, config.chunkIntervalMs); // Adjust the interval as needed
  }, [onAnimationComplete, config]);

  useEffect(() => {
    contentRef.current = content;
    if (forceCompleteStreaming) {
      setDisplayContent(content);
      onAnimationComplete();
      return;
    }

    if (
      (isStreamingIdle || isStreamingDone) &&
      autoPlay &&
      contentRef.current.length > currentAnimatedIndexRef.current
    ) {
      startAnimation();
    }
  }, [
    startAnimation,
    autoPlay,
    isStreamingIdle,
    isStreamingDone,
    forceCompleteStreaming,
    content,
    onAnimationComplete,
    streamingStatus,
  ]);

  useEffect(() => {
    contentPendingRef.current = isContentPending;
  }, [isContentPending]);

  useEffect(() => {
    return () => {
      //end haptic
      if (contentRef.current.length > 0) triggerSuccessHaptic();

      setStreamingStatus('idle');
      currentAnimatedIndexRef.current = 0;
      setDisplayContent('');
      intervalRef.current && clearInterval(intervalRef.current);
    };
  }, []);

  return (
    <Markdown markdownit={markdownIt} {...markdownProps}>
      {`${displayContent} ${isContentPending ? '[loader].[/loader]' : ''}`}
    </Markdown>
  );
};
