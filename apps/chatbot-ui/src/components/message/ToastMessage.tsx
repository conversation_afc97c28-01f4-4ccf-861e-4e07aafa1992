import React, { FC, useEffect, useRef } from 'react';
import { Animated, Pressable, StyleSheet, Text, View } from 'react-native';
import { COLORS, FONTS, Z_INDEX } from '../../constants/globalStyles';
import { config } from '../../config';

type ToastMessageProps = {
  primaryText?: string;
  duration?: number;
  autoDismiss?: boolean;
  allowDismiss?: boolean;
  handleDismiss?: () => void;
  isError?: boolean;
};

const ToastMessage: FC<ToastMessageProps> = (data) => {
  const {
    isError,
    primaryText,
    autoDismiss = false,
    allowDismiss = false,
    duration = 1500,
    handleDismiss,
  } = data;
  const timeoutRef = useRef<any>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const CROSS_HITSLOPE = { top: 16, bottom: 16, right: 16, left: 16 };

  useEffect(() => {
    if (!autoDismiss) {
      fadeAnim.stopAnimation();
      fadeAnim.setValue(1);
    } else {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: duration,
        useNativeDriver: true,
      }).start(() => {
        timeoutRef.current = setTimeout(() => {
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: duration,
            useNativeDriver: true,
          }).start(() => {
            timeoutRef.current = null;
            if (autoDismiss) handleDismiss?.();
          });
        }, duration);
      });
    }

    return () => {
      clearTimeout(timeoutRef.current);
    };
  }, [JSON.stringify(data)]);

  const onPress = () => {
    fadeAnim.stopAnimation();
    fadeAnim.setValue(0);
    handleDismiss?.();
  };

  return isError ? (
    <Animated.View style={[styles.errorContainer, { opacity: fadeAnim }]}>
      <View style={[styles.errorToastContainer]}>
        <Text style={[styles.errorMessage]}>
          {primaryText || config.deafultBottomErrorMsg}
        </Text>
        {allowDismiss && (
          <Pressable
            hitSlop={CROSS_HITSLOPE}
            onPress={onPress}
            style={[{ marginLeft: 'auto' }]}
          >
            <Text style={[styles.errorCta]}>X</Text>
          </Pressable>
        )}
      </View>
    </Animated.View>
  ) : (
    <Animated.View style={[styles.warningContainer, { opacity: fadeAnim }]}>
      <View style={[styles.warningToastContainer]}>
        <Text style={[styles.warningMessage]}>
          {primaryText || config.defaultBottomWarningMsg}
        </Text>
        <Pressable hitSlop={CROSS_HITSLOPE} onPress={onPress}>
          <Text style={[styles.warningCta]}>Reconnect</Text>
        </Pressable>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  errorContainer: {
    position: 'absolute',
    bottom: 24,
    left: 16,
    right: 16,
    zIndex: Z_INDEX.TOAST,
  },
  errorToastContainer: {
    position: 'relative',
    backgroundColor: COLORS.GREY_VAR_2,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  errorMessage: {
    color: COLORS.WHITE,
    fontSize: 14,
    lineHeight: 16,
    fontFamily: FONTS.FONT_FAMILY_400,
  },
  errorCta: {
    color: COLORS.WHITE,
    fontSize: 14,
    lineHeight: 16,
    fontFamily: FONTS.FONT_FAMILY_900,
  },
  warningContainer: {
    position: 'absolute',
    bottom: 24,
    left: 24,
    right: 24,
    zIndex: Z_INDEX.TOAST,
  },
  warningToastContainer: {
    position: 'relative',
    backgroundColor: COLORS.YELLOW_FLESH,
    borderRadius: 12,
    paddingVertical: 18,
    paddingHorizontal: 12,
    gap: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  warningMessage: {
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
    fontSize: 12,
    lineHeight: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    flex: 1,
  },
  warningCta: {
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
    fontSize: 12,
    lineHeight: 12,
    fontFamily: FONTS.FONT_FAMILY_700,
  },
});

export default React.memo(ToastMessage);
