import React, { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Text, TouchableWithoutFeedback, View } from 'react-native';
import { messageBubbleStyles } from './messageStyles';
import { MESSAGES } from '../../constants/messages';
import useGetFeatureFlags from '../../hooks/useGetFeatureFlags';
import {
  defaultLeadingQuestionPalette,
  defaultWhiteColorPalette,
  MarkdownText,
} from './MarkdownText';
import { BotMessageCards } from './BotMessageCards';
import { MessageFeedbackView } from './MessageFeedbackView';
import { CollapsibleWithFeatureFlag } from './Collapsible';
import Animated, { SlideInRight } from 'react-native-reanimated';
import { trackOmnitureClickEvent } from '../../native/omniture';
import {
  AgentTextMessageType,
  BotCardMessageType,
  BotLeadingQuestionType,
  BotTableMessageType,
  BotTextMessageType,
  UserQuotedTextMessageType,
  UserTextMessageType,
} from '../../store/messages/listItemTypes';
import { useConfigSettings } from '../../store/messages/newChatView';
import AgentIcon from '../../assets/AgentIcon';
import { MyraBotIcon } from '../../assets/MyraBotIcon';
import { SelectedCard } from './cards/SelectedCard';
import { TableRenderer } from '../base/table/TableRenderer';
import { PopoverMenu } from '../PopoverMenu';
import clipboardUtils from '../../utils/clipboardUtils';
import { Logger } from '../../utils/logger';
import { Toast } from '../toast';

const USER_MSG_LINE_HEIGHT = 22;

// Shared TargetElementComponent for both user message types
const createTargetElementComponent = (
  data: any,
  msg: any,
  baseStyle: any,
  allowCustomStyle: boolean = false
) => {
  return function SharedTargetElement(props: { style?: any }) {
    const finalStyle = allowCustomStyle && props.style ? [baseStyle, props.style] : baseStyle;

    return (
      <View style={finalStyle}>
        <SelectedCard data={data} msg={msg} />
        <MarkdownText
          color={defaultWhiteColorPalette}
          lineHeight={USER_MSG_LINE_HEIGHT}
          text={data.value}
          marginTop={0}
          marginBottom={0}
        />
      </View>
    );
  };
};

// Custom hook for long press functionality
const useLongPress = (messageRef: React.RefObject<View>, data: any, setMenuState: any, showCopyIcon: boolean | undefined) => {
  return useCallback(() => {
    if (showCopyIcon === false) return;

    try {
      if (!messageRef.current) {
        Logger.createLogger({ tag: 'ChatMessageBubble' }).warn('Message ref is not available for long press');
        return;
      }

      (messageRef.current as any)?.measureInWindow(
        (x: number, y: number, width: number, height: number) => {
          if (typeof y === 'number' && !isNaN(y)) {
            setMenuState({ target: { x, y: y - 14, width: width + 1, height }, text: data.value });
          } else {
            Logger.createLogger({ tag: 'ChatMessageBubble' }).warn('Invalid y coordinate received from measureInWindow', { x, y, width, height });
          }
        },
        (error: any) => {
          Logger.createLogger({ tag: 'ChatMessageBubble' }).error('Error measuring message position for long press', error);
        }
      );
    } catch (error) {
      Logger.createLogger({ tag: 'ChatMessageBubble' }).error('Unexpected error in onLongPress', error);
    }
  }, [data.value, setMenuState, showCopyIcon]);
};


export const UserQuotedMessageBubble: React.FC<UserQuotedTextMessageType> = memo(
  ({ data, msg }) => {
    const messageRef = useRef<View>(null);
    const { showCopyIcon } = useGetFeatureFlags();
    const [menuState, setMenuState] = useState<
      | null
      | {
        target: { x: number; y: number; width: number; height: number };
        text: string;
      }
    >(null);

    const bubbleStyle = !data?.quotedContentInfo?.templatePayload?.data
      ? messageBubbleStyles.userMessage
      : messageBubbleStyles.messageWithCard;

    const TargetElementComponent = useMemo(() =>
      createTargetElementComponent(data, msg, bubbleStyle, true),
      [bubbleStyle, data, msg]
    );

    const onLongPress = useLongPress(messageRef, data, setMenuState, showCopyIcon);

    return (
      <>
        <Animated.View style={messageBubbleStyles.userMessageContainer}>
          <TouchableWithoutFeedback onLongPress={onLongPress}>
            <View
              ref={messageRef}
              style={bubbleStyle}
            >
              <SelectedCard data={data} msg={msg} />
              <MarkdownText
                color={defaultWhiteColorPalette}
                lineHeight={USER_MSG_LINE_HEIGHT}
                text={data.value}
                marginTop={0}
                marginBottom={0}
              />
            </View>
          </TouchableWithoutFeedback>
        </Animated.View>
        {(showCopyIcon ?? true) && menuState && (
          <PopoverMenu
            targetElementData={menuState.target}
            TargetElementComponent={TargetElementComponent}
            targetElementProps={{ data, msg }}
            onClose={() => setMenuState(null)}
            MenuItemList={[
              {
                text: 'Copy',
                icon: 'copy',
                action: 'open',
                onPress: async () => {
                  await clipboardUtils.copyTextWithFeedback(
                    menuState.text,
                    () => {
                      const shouldShowToast = clipboardUtils.shouldShowCustomToast();
                      if (shouldShowToast) {
                        Toast.show(MESSAGES.COPIED_TO_CLIPBOARD);
                      }
                      setMenuState(null);
                    },
                    (error) => {
                      Toast.show(MESSAGES.UNABLE_TO_COPY_CONTENT);
                    }
                  );
                },
              },
            ]}
          />
        )}
      </>
    );
  },
);

export const UserMessageBubble: React.FC<UserTextMessageType> = memo(
  ({ data, msg }) => {
    const messageRef = useRef<View>(null);
    const { isSendFailed, retrySend } = msg;
    const { showCopyIcon } = useGetFeatureFlags();
    const onPress = useCallback(() => {
      if (retrySend) {
        retrySend();
      }
    }, [retrySend]);

    const [menuState, setMenuState] = useState<
      | null
      | {
        target: { x: number; y: number; width: number; height: number };
        text: string;
      }
    >(null);

    const TargetElementComponent = useMemo(() =>
      createTargetElementComponent(data, msg, messageBubbleStyles.userMessage, false),
      [data, msg]
    );

    const onLongPress = useLongPress(messageRef, data, setMenuState, showCopyIcon);

    return (
      <>
        <Animated.View
          entering={msg.isDraft ? SlideInRight.duration(300) : undefined}
          style={[
            messageBubbleStyles.userMessageContainer,
            { opacity: isSendFailed ? 0.5 : 1 },
          ]}
        >
          <TouchableWithoutFeedback onPress={onPress} onLongPress={onLongPress}>
            <View ref={messageRef} style={messageBubbleStyles.userMessage}>
              <SelectedCard data={data} msg={msg} />
              <MarkdownText
                color={defaultWhiteColorPalette}
                text={data.value}
                lineHeight={USER_MSG_LINE_HEIGHT}
                marginTop={0}
                marginBottom={0}
              />
            </View>
          </TouchableWithoutFeedback>
        </Animated.View>
        {(showCopyIcon ?? true) && menuState && (
          <PopoverMenu
            targetElementData={menuState.target}
            TargetElementComponent={TargetElementComponent}
            targetElementProps={{ data, msg }}
            onClose={() => setMenuState(null)}
            MenuItemList={[
              {
                text: 'Copy',
                icon: 'copy',
                action: 'open',
                onPress: async () => {
                  await clipboardUtils.copyTextWithFeedback(
                    menuState.text,
                    () => {
                      const shouldShowToast = clipboardUtils.shouldShowCustomToast();
                      if (shouldShowToast) {
                        Toast.show(MESSAGES.COPIED_TO_CLIPBOARD);
                      }
                      setMenuState(null);
                    },
                    (error) => {
                      Toast.show(MESSAGES.UNABLE_TO_COPY_CONTENT);
                    }
                  );
                },
              },
            ]}
          />
        )}
        {isSendFailed && (
          <View style={messageBubbleStyles.userMessageFailedContainer}>
            <Text style={messageBubbleStyles.userMessageFailedText}>
              {MESSAGES.SEND_FAILED_TRY_AGAIN}
            </Text>
          </View>
        )}
      </>
    );
  },
);

export const BotCardMessage: React.FC<BotCardMessageType> = memo(
  (listItem: BotCardMessageType) => (
    <View style={[messageBubbleStyles.botMessageContainer, { marginTop: 10 }]}>
      <View style={messageBubbleStyles.botMessageInnerContainer}>
        {'isFirstItem' in listItem && listItem.isFirstItem && (
          <MyraBotIcon style={{ marginLeft: 20 }} />
        )}
        <BotMessageCards
          card={listItem.data}
          msg={listItem.msg}
          setVideoModalData={listItem.setVideoModalData}
        />
        {listItem.showFeedback && (
          <MessageFeedbackView
            msg={listItem.msg}
            isLastMessage={listItem.isLastMessage}
          />
        )}
      </View>
    </View>
  ),
);

export const TableMessage: React.FC<BotTableMessageType> = memo((listItem) => {
  //FIXME
  // @ts-ignore
  const tableData = listItem?.data?.value?.templateInfo?.payload?.[0];
  if (!tableData) {
    return null;
  }
  const tableLayout = tableData.layout;
  return (
    <View
      style={[
        messageBubbleStyles.tableMessageContainer,
        { marginRight: tableLayout !== 'advanced' ? 20 : 0 },
      ]}
    >
      <TableRenderer data={tableData} />
    </View>
  );
});
export const BotTextMessage: React.FC<BotTextMessageType> = memo((listItem) => {
  return (
    <View
      style={[messageBubbleStyles.botMessageContainer, { flexDirection: 'column' }]}
    >
      {listItem.isFirstItem && <MyraBotIcon style={{ marginLeft: 20 }} />}
      <CollapsibleWithFeatureFlag
        maxHeight={250}
        containerStyle={[messageBubbleStyles.botMessage]}
      >
        <MarkdownText
          streamMarkdown={listItem.msg.streamMessage}
          isStreaming={!listItem.msg.isCompleted}
          streamConfig={listItem.msg.streamConfig}
          text={listItem.data.value}
          adjustHeight
        />
      </CollapsibleWithFeatureFlag>
      {listItem.showFeedback && listItem.msg.isCompleted && (
        <MessageFeedbackView
          msg={listItem.msg}
          isLastMessage={listItem.isLastMessage}
        />
      )}
    </View>
  );
});

export const AgentMessage: React.FC<AgentTextMessageType> = memo((listItem) => (
  <View
    style={[messageBubbleStyles.agentMessageContainer, { flexDirection: 'column' }]}
  >
    {listItem.showAvatar && (
      <View style={messageBubbleStyles.agentMessageHeader}>
        <AgentIcon />
        <Text style={messageBubbleStyles.agentNameText}>
          {listItem.msg?.meta?.currentTicketInfo?.agentName ?? ''}
        </Text>
      </View>
    )}
    <View style={[messageBubbleStyles.agentMessage, { marginTop: 12 }]}>
      <MarkdownText text={listItem.data.value} />
    </View>
    {listItem.showFeedback && (
      <MessageFeedbackView
        msg={listItem.msg}
        isLastMessage={listItem.isLastMessage}
      />
    )}
  </View>
));

export const LeadingQuestion: React.FC<BotLeadingQuestionType> = memo((listItem) => {
  useEffect(() => {
    trackOmnitureClickEvent('LEAD_QUESTION_SHOWN');
  }, []);
  return (
    <View
      style={[
        messageBubbleStyles.botMessageContainer,
        { marginTop: 12, flexDirection: 'column' },
      ]}
    >
      {listItem.isFirstItem && <MyraBotIcon style={{ marginLeft: 20 }} />}
      <View style={messageBubbleStyles.botMessage}>
        <MarkdownText
          color={defaultLeadingQuestionPalette}
          text={listItem.data}
          adjustHeight
          weight="bold"
        />
      </View>
    </View>
  );
});

const DEFAULT_GREETING =
  '**Hi there! I’m Myra, your personal travel assistant.** I’m still in beta, learning and improving, but ready to help you plan your next adventure. What’s your dream destination today?';

export const NewChatPrompt: React.FC<unknown> = memo(() => {
  const { data } = useConfigSettings();
  const greetingMsg = data?.greetingMessage || DEFAULT_GREETING;
  return <BotTextBubble text={greetingMsg} />;
});

function BotTextBubble({ text }: { text: string }) {
  return (
    <View style={messageBubbleStyles.botMessageContainer}>
      <View style={messageBubbleStyles.botMessage}>
        <MarkdownText text={text} adjustHeight />
      </View>
    </View>
  );
}
