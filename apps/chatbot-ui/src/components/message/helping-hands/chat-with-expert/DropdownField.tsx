import React from 'react';
import { Text, TextInput, TouchableOpacity, View } from 'react-native';
import { DropdownFieldProps } from './types';
import { styles } from './styles';
import { COLORS } from '../../../../constants/globalStyles';
import SearchIcon from '../../../../assets/SearchIcon';
import {ScrollView} from "react-native-gesture-handler";

export const DropdownField: React.FC<DropdownFieldProps> = ({
  question,
  value,
  isExpanded,
  searchQuery,
  onValueChange,
  onToggleExpanded,
  onSearchChange,
}) => {
  // Filter options based on search query
  const filteredOptions = React.useMemo(() => {
    if (!searchQuery) return question.values || [];

    return (question.values || []).filter(option =>
      option.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [question.values, searchQuery]);

  const handleSelectOption = (option: string) => {
    onValueChange(option);
  };

  return (
    <View style={styles.dropdownContainer}>
      <TouchableOpacity
        style={[
          styles.dropdownButton,
          isExpanded ? styles.dropdownButtonExpanded : null,
          value ? styles.dropdownButtonSelected : null
        ]}
        onPress={onToggleExpanded}
        accessibilityLabel={`Select ${question.question}`}
        accessibilityRole="button"
        accessibilityState={{ expanded: isExpanded }}
      >
        <View style={styles.dropdownContent}>
          <Text style={styles.dropdownLabel}>
            {question.question}
            {question.mandatory && <Text style={styles.mandatory}> *</Text>}
          </Text>
          <Text style={[
            styles.dropdownValue,
            !value && styles.dropdownPlaceholder
          ]}>
            {value || question.placeholder}
          </Text>
        </View>
        {question.question.toLowerCase().includes('destination') && (
            <View style={{alignItems: 'center', justifyContent: 'center'}}>
              <SearchIcon width={24} height={24} color={COLORS.BLACK_VAR_5}/>
            </View>
        )}
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.dropdownList}>
          {/* Search Input */}
          <View style={styles.searchContainer}>
            <TextInput
              style={styles.searchInput}
              value={searchQuery}
              onChangeText={onSearchChange}
              placeholder={`Search ${question.question.toLowerCase()}...`}
              placeholderTextColor={COLORS.BLACK_VAR_5}
              autoFocus={true}
            />
          </View>

          {/* Results Header */}
          <View style={styles.resultsHeader}>
            <Text style={styles.resultsHeaderText}>
              {searchQuery ? `${filteredOptions.length} results` : 'Suggested'}
            </Text>
          </View>

          {/* Options List */}
          <ScrollView
            style={styles.dropdownScrollView}
            nestedScrollEnabled={true}
            showsVerticalScrollIndicator={true}
            keyboardShouldPersistTaps="handled"
          >
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.dropdownItem,
                    option === value && styles.dropdownItemSelected
                  ]}
                  onPress={() => handleSelectOption(option)}
                  accessibilityLabel={`Select ${option}`}
                  accessibilityRole="button"
                >
                  <Text style={[
                    styles.dropdownItemText,
                    option === value && styles.dropdownItemTextSelected
                  ]}>
                    {option}
                  </Text>
                  {option === value && (
                    <Text style={styles.checkmark}>✓</Text>
                  )}
                </TouchableOpacity>
              ))
            ) : (
              <View style={styles.noResultsContainer}>
                <Text style={styles.noResultsText}>
                  No results found for "{searchQuery}"
                </Text>
              </View>
            )}
          </ScrollView>
        </View>
      )}
    </View>
  );
};
