import React, { useEffect, useState } from 'react';
import { Text, TouchableOpacity, View, Keyboard } from 'react-native';
import { BottomModalPopup } from '../../../BottomModalPopup';
import { ButtonBackground } from '../../../base';
import { ChatWithExpertModalProps, FormFieldType } from './types';
import { useFormState } from './useFormState';
import { TextInputField } from './TextInputField';
import { DropdownField } from './DropdownField';
import { styles } from './styles';

export const ChatWithExpertModal: React.FC<ChatWithExpertModalProps> = ({
  visible,
  onClose,
  onSubmit,
  cardData,
  heightPercentage = 0.7,
}) => {
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setIsKeyboardVisible(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setIsKeyboardVisible(false);
    });

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const formData = cardData.metadata || { questions: [] };
  const {
    formState,
    handleInputChange,
    handleSearchChange,
    toggleDropdown,
    selectDropdownValue,
    isFormValid,
    resetForm
  } = useFormState(formData);

  const handleSubmit = () => {
    if (isFormValid()) {
      onSubmit(formState.responses);
      resetForm();
    }
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const isSubmitDisabled = !isFormValid();

  const renderQuestion = (question: any) => {
    const response = formState.responses[question.question_id] || '';
    const isExpanded = formState.expandedDropdowns[question.question_id] || false;
    const searchQuery = formState.searchQuery[question.question_id] || '';

    const isDropdownExpanded = question.type === FormFieldType.DROPDOWN && isExpanded;

    return (
      <View key={question.question_id} style={[
        styles.fieldContainer,
        isDropdownExpanded && styles.fieldContainerExpanded
      ]}>
        {renderFieldByType(question, response, isExpanded, searchQuery)}
      </View>
    );
  };

  const renderFieldByType = (question: any, response: string, isExpanded: boolean, searchQuery: string) => {
    console.log(question);
    switch (question.type) {
      case FormFieldType.TEXTBOX:
        return (
          <TextInputField
            question={question}
            value={response}
            onValueChange={(value) => handleInputChange(question.question_id, value)}
          />
        );

      case FormFieldType.DROPDOWN:
        return (
          <DropdownField
            question={question}
            value={response}
            isExpanded={isExpanded}
            searchQuery={searchQuery}
            onValueChange={(value) => selectDropdownValue(question.question_id, value)}
            onToggleExpanded={() => toggleDropdown(question.question_id)}
            onSearchChange={(query) => handleSearchChange(question.question_id, query)}
          />
        );
      default:
        return null;
    }
  };

  return (
    <BottomModalPopup
      enabled={visible}
      title="Share details for live assistance"
      onClose={handleClose}
      heightPercentage={heightPercentage}
      useFixedHeight={true}
    >
      <View style={styles.container}>
        <View style={styles.formContent}>
          {formData.questions.map(question => renderQuestion(question))}
        </View>
      </View>

        <TouchableOpacity
          disabled={isSubmitDisabled}
          onPress={handleSubmit}
          style={[styles.submitButton, isSubmitDisabled && styles.submitButtonDisabled]}
        >
          <ButtonBackground disabled={isSubmitDisabled} style={{marginBottom: 56}}>
            <Text style={[styles.submitButtonText, isSubmitDisabled && styles.submitButtonTextDisabled]}>
              SUBMIT
            </Text>
          </ButtonBackground>
        </TouchableOpacity>

    </BottomModalPopup>
  );
};
