import { useState } from 'react';
import { FormData, FormState, Question } from './types';

export const useFormState = (formData: FormData) => {
  // Initialize form responses with prefilled values
  const [formState, setFormState] = useState<FormState>(() => {
    const initialResponses: { [key: number]: string } = {};
    formData.questions.forEach(question => {
      initialResponses[question.question_id] = question.prefilled_value || '';
    });

    return {
      responses: initialResponses,
      expandedDropdowns: {},
      searchQuery: {}
    };
  });

  const handleInputChange = (questionId: number, value: string) => {
    setFormState(prev => ({
      ...prev,
      responses: {
        ...prev.responses,
        [questionId]: value
      }
    }));
  };

  const handleSearchChange = (questionId: number, query: string) => {
    setFormState(prev => ({
      ...prev,
      searchQuery: {
        ...prev.searchQuery,
        [questionId]: query
      }
    }));
  };

  const toggleDropdown = (questionId: number) => {
    setFormState(prev => {
      const isCurrentlyExpanded = prev.expandedDropdowns[questionId];

      return {
        ...prev,
        expandedDropdowns: {
          ...prev.expandedDropdowns,
          [questionId]: !isCurrentlyExpanded
        },
        // Clear search when closing dropdown
        searchQuery: isCurrentlyExpanded ? {
          ...prev.searchQuery,
          [questionId]: ''
        } : prev.searchQuery
      };
    });
  };

  const selectDropdownValue = (questionId: number, value: string) => {
    handleInputChange(questionId, value);
    toggleDropdown(questionId);
    // Clear search
    setFormState(prev => ({
      ...prev,
      searchQuery: {
        ...prev.searchQuery,
        [questionId]: ''
      }
    }));
  };

  const validateResponse = (response: string, regex?: string): boolean => {
    if (!regex) return true;
    
    try {
      const regexPattern = new RegExp(regex);
      return regexPattern.test(response);
    } catch (error) {
      console.error('Invalid regex pattern:', regex, error);
      return true; // If regex is invalid, consider it valid
    }
  };

  const isQuestionValid = (question: Question): boolean => {
    const response = formState.responses[question.question_id];
    const hasResponse = response && response.trim().length > 0;
    
    // Non-mandatory questions are always valid
    if (!question.mandatory) return true;
    
    // Mandatory questions need a response
    if (!hasResponse) return false;
    
    // If there's a response, validate it against regex (if provided)
    const { regex } = question;
    return validateResponse(response, regex);
  };

  const isFormValid = () => {
    return formData.questions.every(isQuestionValid);
  };

  const resetForm = () => {
    const initialResponses: { [key: number]: string } = {};
    formData.questions.forEach(question => {
      initialResponses[question.question_id] = question.prefilled_value || '';
    });

    setFormState({
      responses: initialResponses,
      expandedDropdowns: {},
      searchQuery: {}
    });
  };

  return {
    formState,
    handleInputChange,
    handleSearchChange,
    toggleDropdown,
    selectDropdownValue,
    isFormValid,
    resetForm
  };
};
