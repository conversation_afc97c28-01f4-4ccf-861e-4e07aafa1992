import React from 'react';
import { Text, TextInput, View } from 'react-native';
import { TextInputFieldProps } from './types';
import { styles } from './styles';

export const TextInputField: React.FC<TextInputFieldProps> = ({
  question,
  value,
  onValueChange,
}) => {
  return (
    <View style={styles.textInputContainer}>
      <Text style={styles.textInputLabel}>
        {question.question}
        {question.mandatory && <Text style={styles.mandatory}> *</Text>}
      </Text>
      <TextInput
        style={styles.textInput}
        value={value}
        onChangeText={(text) => onValueChange(text)}
        keyboardType={question.question.toLowerCase().includes('phone') ? 'phone-pad' : 'default'}
        placeholder={question.placeholder}
        placeholderTextColor="#999"
      />
    </View>
  );
}; 