// Types and interfaces for ChatWithExpert modal
export declare type HelpingHandCardChatWithExpert = {
  id: string;
  type?: string;
  data: {
    header: string;
    sub_header: string;
    call_back_type: 'chat_us' | 'CHAT_US' | 'chat-card-with-form';
    call_back_content: string;
    webViewHeader: string;
    meta_data?: object;
    card_name?: string;
    contact_no?: string;
    type?: string;
    icon: string;
    metadata?: FormData;
  };
};

export enum FormFieldType {
  TEXTBOX = 'textbox',
  DROPDOWN = 'dropdown',
  RADIO = 'radio',
  CHECKBOX = 'checkbox',
  DATE_PICKER = 'date_picker',
  NUMBER_INPUT = 'number_input',
  EMAIL_INPUT = 'email_input',
  PHONE_INPUT = 'phone_input',
}

export interface Question {
  question_id: number;
  question: string;
  type: FormFieldType;
  values?: string[];
  prefilled_value?: string;
  placeholder: string;
  mandatory: boolean;
  regex?: string;
}

export interface FormData {
  questions: Question[];
}

export interface ChatWithExpertModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (collectedFormData: object) => void;
  cardData: HelpingHandCardChatWithExpert['data'];
  heightPercentage?: number;
}

export interface FormState {
  responses: { [key: number]: string };
  expandedDropdowns: { [key: number]: boolean };
  searchQuery: { [key: number]: string };
}

export interface DropdownFieldProps {
  question: Question;
  value: string;
  isExpanded: boolean;
  searchQuery: string;
  onValueChange: (value: string) => void;
  onToggleExpanded: () => void;
  onSearchChange: (query: string) => void;
}

export interface TextInputFieldProps {
  question: Question;
  value: string;
  onValueChange: (value: string) => void;
}
