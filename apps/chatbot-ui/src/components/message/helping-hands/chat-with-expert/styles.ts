import { StyleSheet } from 'react-native';
import { COLORS, FONTS } from '../../../../constants/globalStyles';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  formContent: {
    flex: 1,
  },
  fieldContainer: {
    marginBottom: 24,
    position: 'relative',
  },
  fieldContainerExpanded: {
    marginBottom: 280, // Extra space for expanded dropdown with search
  },
  mandatory: {
    color: '#E53E3E',
    fontSize: 14,
  },

  // Text Input Styles
  textInputContainer: {
    borderWidth: 1.5,
    borderColor: COLORS.GREY_VAR_4,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: COLORS.CEMENT_WHITE,
    minHeight: 64,
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  textInputLabel: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.GREY_VAR_2,
    marginBottom: 2,
    letterSpacing: 0.5,
  },
  textInput: {
    fontSize: 16,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.TEXT_HIGH_EMPHASIS,
    padding: 0,
    margin: 0,
    borderWidth: 0,
  },

  // Dropdown Styles
  dropdownContainer: {
    position: 'relative',
    zIndex: 10,
  },
  dropdownButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: COLORS.GREY_VAR_4,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: COLORS.WHITE,
    minHeight: 64,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  dropdownContent: {
    flex: 1,
    justifyContent: 'center',
  },
  dropdownLabel: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.GREY_VAR_2,
    marginBottom: 2,
    letterSpacing: 0.5,
  },
  dropdownValue: {
    fontSize: 16,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.TEXT_HIGH_EMPHASIS,
  },
  dropdownPlaceholder: {
    color: COLORS.GREY_VAR_3,
    fontStyle: 'italic',
  },
  dropdownButtonExpanded: {
    borderColor: COLORS.BLACK_VAR_3,
    shadowColor: COLORS.BLACK_VAR_3,
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  dropdownButtonSelected: {
    borderColor: COLORS.BLACK_VAR_3,
    backgroundColor: COLORS.CEMENT_WHITE,
  },
  dropdownList: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: COLORS.CEMENT_WHITE,
    borderWidth: 1.5,
    borderColor: COLORS.BLACK_VAR_3,
    borderRadius: 12,
    maxHeight: 240,
    zIndex: 999,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    marginTop: 4,
    overflow: 'hidden',
  },

  // Search Styles
  searchContainer: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.GREY_VAR_5,
    backgroundColor: '#F8F9FA',
  },
  searchInput: {
    borderWidth: 1,
    borderColor: COLORS.GREY_VAR_4,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 12,
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_HIGH_EMPHASIS,
    backgroundColor: COLORS.WHITE,
  },
  resultsHeader: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomColor: COLORS.GREY_VAR_5,
  },
  resultsHeaderText: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.TEXT_LOW_EMPHASIS,
    letterSpacing: 0.5,
  },

  // Dropdown List Styles
  dropdownScrollView: {
    maxHeight: 160,
  },
  dropdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.GREY_VAR_5,
    minHeight: 48,
  },
  dropdownItemSelected: {
    backgroundColor: '#E3F2FD',
  },
  dropdownItemText: {
    fontSize: 16,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_HIGH_EMPHASIS,
    flex: 1,
  },
  dropdownItemTextSelected: {
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLUE_VAR_1,
  },
  checkmark: {
    fontSize: 16,
    color: COLORS.BLUE_VAR_1,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  noResultsContainer: {
    paddingVertical: 24,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  noResultsText: {
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.GREY_VAR_2,
    textAlign: 'center',
  },

  // Submit Button Styles
  submitButton: {
    marginHorizontal: 16,
    marginBottom: 0,
    marginTop: 16,
  },
  submitButtonDisabled: {
    opacity: 0.6,
  },
  submitButtonText: {
    color: COLORS.WHITE,
    fontSize: 16,
    fontFamily: FONTS.FONT_FAMILY_900,
    textAlign: 'center',
  },
  submitButtonTextDisabled: {
    color: COLORS.GREY_VAR_3,
  },
});
