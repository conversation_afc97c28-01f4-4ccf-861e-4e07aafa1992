

/**
 * Converts form data from a key-value object format to a structured responses format
 * required by the chat agent system.
 *
 * This utility function is primarily used in the "Chat with Expert" flow where user form
 * responses need to be transformed from the UI state format (question_id -> answer mapping)
 * into the API-expected format with explicit question_id and answer fields.
 *
 * @param formData - An object containing form responses where:
 *   - Keys are question IDs (string or number)
 *   - Values are the user's answers (string, number, boolean, or object)
 *
 * @returns An object with a 'responses' array containing structured response objects:
 *   - Each response has a 'question_id' (string) and 'answer' (any type)
 *   - Question IDs are always converted to strings for consistency
 *
 * @example
 * ```typescript
 * // Input form data from UI state
 * const formData = {
 *   1: "<PERSON>",
 *   2: "<EMAIL>",
 *   3: true,
 *   "destination": "Paris"
 * };
 *
 * // Convert to API format
 * const result = convertFormDataToResponsesFormat(formData);
 *
 * // Output:
 * // {
 * //   responses: [
 * //     { question_id: "1", answer: "<PERSON>" },
 * //     { question_id: "2", answer: "<EMAIL>" },
 * //     { question_id: "3", answer: true },
 * //     { question_id: "destination", answer: "Paris" }
 * //   ]
 * // }
 * ```
 *
 * @see {@link HelpingHandCards} - Where this function is used to format form data before sending to agent
 * @see {@link ChatWithExpertModal} - The modal component that collects the original form data
 */
export const convertFormDataToResponsesFormat = (formData: FormDataMap) => {
    const responses = Object.entries(formData).map(([questionId, answer]) => ({
        question_id: String(questionId), // Ensure question_id is a string
        answer: answer
    }));

    return { responses };
};

/**
 * Checks if a value is empty. Arrays, objects, maps, and sets are considered empty
 * if they have no elements. Strings are empty if they have zero length.
 * Numbers, booleans, null, and undefined are considered empty.
 */
export const isEmpty = (value: any): boolean =>  {
    // null or undefined
    if (value == null) {
        return true;
    }

    // Arrays and array-like objects (strings, arguments, etc.)
    if (Array.isArray(value) || typeof value === 'string') {
        return value.length === 0;
    }

    // Maps and Sets
    if (value instanceof Map || value instanceof Set) {
        return value.size === 0;
    }

    // Objects (including plain objects, functions, etc.)
    if (typeof value === 'object') {
        // For objects, check if they have any enumerable own properties
        return Object.keys(value).length === 0;
    }

    // Primitives (numbers, booleans, symbols, etc.) are considered empty
    return true;
}
