// Types for the Chat with Expert Modal components

export declare type HelpingHandCardChatWithExpert = {
  id: string;
  type?: string;
  data: {
    header: string;
    sub_header: string;
    call_back_type: 'chat_us' | 'CHAT_US' | 'chat-card-with-form';
    call_back_content: string;
    webViewHeader: string;
    meta_data?: object;
    card_name?: string;
    contact_no?: string;
    type?: string;
    icon: string;
    metadata?: any;
  };
};

export interface Question {
  question_id: number;
  question: string;
  type: 'textbox' | 'dropdown';
  values?: string[];
  prefilled_value?: string;
  placeholder: string;
  mandatory: boolean;
}

export interface FormData {
  questions: Question[];
}

export interface ChatWithExpertModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (destination: string, phoneNumber: string) => void;
  cardData: HelpingHandCardChatWithExpert['data'];
  heightPercentage?: number;
}

export interface FormResponses {
  [key: number]: string;
}

export interface ExpandedDropdowns {
  [key: number]: boolean;
}

export interface SearchQuery {
  [key: number]: string;
}

export interface TextInputFieldProps {
  question: Question;
  value: string;
  onValueChange: (value: string) => void;
}

export interface DropdownFieldProps {
  question: Question;
  value: string;
  onValueChange: (value: string) => void;
  isExpanded: boolean;
  onToggle: () => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
}
