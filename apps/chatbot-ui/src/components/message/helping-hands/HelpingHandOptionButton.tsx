import React, { useEffect } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import { TouchableFeedback } from '../../base/TouchableFeeback';

interface HelpingHandOptionButtonProps {
  header: string;
  sub_header: string;
  Icon: React.FC;
  onClick: () => void;
  autoClick: boolean
}

export const HelpingHandOptionButton: React.FC<HelpingHandOptionButtonProps> = (
  props,
) => {
  const { header, sub_header, onClick, Icon, autoClick = false } = props;

  useEffect(() => {
    if (autoClick){
      onClick();
    }
  }, [autoClick]);

  return (
    <TouchableFeedback style={styles.buttonContainer} onPress={onClick}>
      <View style={styles.iconPlaceholder}>
        <Icon />
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.title}>{header || ''}</Text>
        <Text style={styles.description}>{sub_header || ''}</Text>
      </View>
    </TouchableFeedback>
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.WHITE,
    padding: 16,
    borderRadius: 16,
    alignItems: 'center',
    marginBottom: 15,
    borderWidth: 1,
    borderColor: COLORS.GREY_VAR_5,
  },
  iconPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.BLUE_VAR_2,
    marginRight: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: COLORS.BLUE_VAR_1,
  },
  description: {
    fontSize: 12,
    color: COLORS.GREY_VAR_2,
    fontFamily: FONTS.FONT_FAMILY_400,
  },
});
