import React, { useEffect, useState } from 'react';
import { Image, Linking, Platform, StyleSheet, View } from 'react-native';
import { useMessageAction } from '../../../store/messages';
import { config } from '../../../config';
import { FlatList } from 'react-native-gesture-handler';
import { HelpingHandOptionButton } from './HelpingHandOptionButton';
import { CallUsIcon } from '../../../assets/CallUsIcon';
import { ChatIconBlue } from '../../../assets/ChatIconBlue';
import { CallBackIcon } from '../../../assets/CallBackIcon';
import { useAppStateStore } from '../../../store/app';
import { ActionTypes } from '../../../containers/types';
import { Analytics, TrackingEvent } from '../../../analytics';
import { isEmpty } from 'lodash';
import { FormData } from './chat-with-expert';
import { openDeepLink } from '../../../native/deepLinks';
import { convertFormDataToResponsesFormat } from './HelpingHandUtils';
import ChatWithExpertModalV2 from './chat-with-expertV2';
import { trackOmnitureClickEvent } from '../../../native/omniture';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../../native/tracking/pdt';
import { useHolidayTracking } from '../../../hooks/useHolidayTracking';

export const CALLBACK_TYPES = {
  CALL_US: 'call_us',
  CHAT_US: 'chat_us',
  SCHEDULE: '1',
  SCHEDULE_CALLBACK: 'schedule-callback-lob-flow',
  CHAT_CARD_WITH_FORM: 'chat-card-with-form',
};

// Define an icon map with common types and a default handler
const IconMap: Record<string, () => JSX.Element> = {
  [CALLBACK_TYPES.CALL_US]: () => <CallUsIcon />,
  [CALLBACK_TYPES.CHAT_US]: () => <ChatIconBlue />,
  [CALLBACK_TYPES.SCHEDULE]: () => <CallBackIcon />,
  [CALLBACK_TYPES.SCHEDULE_CALLBACK]: () => <CallBackIcon />,
  [CALLBACK_TYPES.CHAT_CARD_WITH_FORM]: () => <ChatIconBlue />,
};

// Get icon for a callback type with fallback
const getIconForType = (type: string, icon: string): React.FC => {
  if (!isEmpty(icon)) {
    return () => <Image style={styles.fallbackIcon} source={{ uri: icon }} />;
  }
  return IconMap[type] || IconMap[CALLBACK_TYPES.CALL_US];
};

function HelpingHandCards({
  cardsData,
}: {
  cardsData: HelpingHandsDetailsResponseData['data']['cards'];
}) {
  const { sendMessage } = useMessageAction();
  const helpingHandCardsData = cardsData;
  const onAction = useAppStateStore((state) => state.onAction);
  const chatContext = useAppStateStore((state) => state.chatContext);
  const lob = chatContext?.context?.lob || '';
  const { expertMetadata } = chatContext || {};
  const { uniqChatId = '' } = expertMetadata || {};

  // Holiday tracking hook
  const { trackMediaShown, trackCallClick, trackChatClick, trackQueryClick } =
    useHolidayTracking();

  // State for Chat with Expert modal
  const [showChatWithExpertModal, setShowChatWithExpertModal] = useState(false);
  const [selectedChatCard, setSelectedChatCard] = useState<
    HelpingHandCardChatWithExpert['data'] | null
  >(null);

  useEffect(() => {
    Analytics.trackClickEvent(TrackingEvent.payload_AgentHelpOptionsShown());
    trackOmnitureClickEvent('CARDS_SHOWN', {
      CONTENT_TYPE: 'media_connect_to_agent',
    });
    trackPDTEvent({
      eventName: eventNameSchema.BUTTON_CLICKED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.CONNECT_TO_AN_AGENT,
    });
    trackMediaShown(cardsData);
  }, [cardsData, trackMediaShown]);

  const onCallPress = (card: HelpingHandCardCallUs['data']) => {
    //sendMessage(card?.call_back_content || config.callAnAgentText);
    Linking.openURL(`tel:${card.contact_no}`); // FIXME use native module for this
    Analytics.trackClickEvent(TrackingEvent.payload_CallNowClicked());
    trackCallClick(autoClick);
  };

  // Unified function to handle connecting to agent
  const connectToAgent = (
    card: HelpingHandCardChatWithExpert['data'],
    additionalMetadata?: FormDataMap,
  ) => {
    let botMetadata;
    if (additionalMetadata?.responses) {
      botMetadata = additionalMetadata;
    } else if (card.metadata) {
      botMetadata = { metadata: card.metadata };
    } else {
      botMetadata = {};
    }

    const metadata = { ...card.meta_data };
    sendMessage(card?.call_back_content || config.connectMeToAgentText, {
      eventType: 'CONNECT_TO_AGENT',
      dataProps: { metadata },
      botMetadata,
    });
  };

  const onChatPress = (card: HelpingHandCardChatWithExpert['data']) => {
    connectToAgent(card);
  };

  const onChatCardWithFormPress = (card: HelpingHandCardChatWithExpert['data']) => {
    // Track analytics event regardless of path
    Analytics.trackClickEvent(TrackingEvent.payload_ChatWithAgentClicked());

    // Check if the card has questions
    const formData = card?.metadata as FormData;
    if (formData?.questions && formData?.questions?.length > 0) {
      // Set the selected card and show modal directly
      setSelectedChatCard(card);
      setShowChatWithExpertModal(true);
    } else {
      // No questions, connect to agent directly
      connectToAgent(card);
    }

    trackChatClick(uniqChatId, autoClick);
  };

  const handleChatWithExpertSubmit = (collectedFormData: object) => {
    if (selectedChatCard) {
      const formattedData = convertFormDataToResponsesFormat(
        collectedFormData as FormDataMap,
      );
      connectToAgent(selectedChatCard, formattedData);
    }
    setShowChatWithExpertModal(false);
    setSelectedChatCard(null);
  };

  const handleChatWithExpertClose = () => {
    setShowChatWithExpertModal(false);
    setSelectedChatCard(null);
  };

  const onSchedulePress = async (card: HelpingHandCardScheduleCallback['data']) => {
    const showCallbackList = () => {
      sendMessage(card?.call_back_content || config.scheduleCallbackText, {
        dataProps: { metadata: card.meta_data },
        eventType: 'CALLBACK_SLOTS',
        uiMetadataProps: { metadata: card.meta_data },
      });
    };
    showCallbackList();
    Analytics.trackClickEvent(TrackingEvent.payload_ScheduleCallbackClicked());
  };

  const onScheduleCallBackLobFlowPress = (
    card: HelpingHandCardScheduleCallback['data'],
  ) => {
    const { deeplink } = card;
    if (deeplink) {
      openDeepLink(deeplink);
    }
    trackQueryClick(autoClick);
  };

  const handleCardClick = (card: HelpingHandCard['data']) => {
    // CALLBACK TO PARENT
    onAction?.({
      lob,
      actionType: ActionTypes.OptionsClick,
      actionPayload: {
        option: card.call_back_type,
        metadata: card,
      },
    });
    switch (card.call_back_type) {
      case CALLBACK_TYPES.CALL_US:
        trackOmnitureClickEvent('CARD_CLICKED', { CONTENT_TYPE: 'media_call_us' });
        trackPDTEvent({
          eventName: eventNameSchema.BUTTON_CLICKED,
          eventType: eventTypeSchema.ACTION,
          eventValue: eventValueSchema.CALL_NOW_CLICKED,
        });
        onCallPress(card as HelpingHandCardCallUs['data']);
        break;
      case CALLBACK_TYPES.CHAT_US:
        onChatPress(card as HelpingHandCardChatWithExpert['data']);
        break;
      case CALLBACK_TYPES.CHAT_CARD_WITH_FORM:
        onChatCardWithFormPress(card as HelpingHandCardChatWithExpert['data']);
        break;
      case CALLBACK_TYPES.SCHEDULE:
        trackOmnitureClickEvent('CARD_CLICKED', {
          CONTENT_TYPE: 'media_schedule_a_call',
        });
        trackPDTEvent({
          eventName: eventNameSchema.BUTTON_CLICKED,
          eventType: eventTypeSchema.ACTION,
          eventValue: eventValueSchema.CTA_SCHEDULE_CALLBACK_CLICKED,
        });
        onSchedulePress(card as HelpingHandCardScheduleCallback['data']);
        break;
      case CALLBACK_TYPES.SCHEDULE_CALLBACK:
        onScheduleCallBackLobFlowPress(
          card as HelpingHandCardScheduleCallback['data'],
        );
        break;
      default:
        console.log(`Unhandled callback type: ${card.call_back_type}`);
        break;
    }
  };

  // The purpose of this is to auto click the button if there is only one card
  const autoClick =
    cardsData?.length === 1 && helpingHandCardsData?.[0]?.data?.auto_select === true;
  // @ts-ignore
  const questionsLength = selectedChatCard?.metadata?.questions?.length
    ? // @ts-ignore
      selectedChatCard.metadata.questions.length
    : 1;

  const modalHeightPercentage =
    (Platform.OS === 'ios' ? 0.25 : 0.2) + // Base modal height (25% of screen)
    questionsLength * 0.1 + // Dynamic height based on the number of cards (10% per card)
    (Platform.OS === 'ios' ? 0.4 : 0); // Additional height for iOS to account for safe area and keyboard

  return (
    <View style={styles.container}>
      <FlatList
        data={helpingHandCardsData}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => {
          const data = item.data;
          return (
            <HelpingHandOptionButton
              {...data}
              Icon={getIconForType(data.call_back_type, data?.icon)}
              onClick={() => handleCardClick(data)}
              autoClick={autoClick}
            />
          );
        }}
      />

      {/*{showChatWithExpertModal && selectedChatCard && (
        <ChatWithExpertModal
          visible={showChatWithExpertModal}
          onClose={handleChatWithExpertClose}
          onSubmit={handleChatWithExpertSubmit}
          cardData={{
            ...selectedChatCard,
            metadata: selectedChatCard.metadata as FormData
          }}
          heightPercentage={modalHeightPercentage}
        />
      )}*/}

      {showChatWithExpertModal && selectedChatCard && (
        <ChatWithExpertModalV2
          onClose={handleChatWithExpertClose}
          onSubmit={handleChatWithExpertSubmit}
          cardData={{
            ...selectedChatCard,
            metadata: selectedChatCard.metadata as FormData,
          }}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  fallbackIcon: {
    height: 35,
    width: 35,
  },
  container: {
    paddingHorizontal: 16,
  },
});

export default HelpingHandCards;
