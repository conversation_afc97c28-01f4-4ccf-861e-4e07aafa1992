import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Modal,
  TextInput,
  KeyboardTypeOptions,
} from 'react-native';
import { GestureHandlerRootView, ScrollView } from 'react-native-gesture-handler';
import BottomSheet, {
  BottomSheetView,
  BottomSheetBackdrop,
  BottomSheetSectionList,
} from '@gorhom/bottom-sheet';
import { COLORS, FONTS } from '../../../../constants/globalStyles';
import { CloseIcon } from '../../../../assets/CloseIcon';
import { ButtonBackground } from '../../../base';
import { isEmpty } from 'lodash';
import SearchIcon from '../../../../assets/SearchIcon';
import { HelpingHandCardChatWithExpert } from '../chat-with-expert';
import { useHolidayTracking } from '../../../../hooks/useHolidayTracking';
import { useAppStateStore } from "../../../../store/app";

// Type definitions
interface Question {
  question_id: string;
  question: string;
  prefilled_value?: string;
  input_type?: 'text' | 'numeric';
  mandatory: boolean;
  placeholder?: string;
  regex?: string;
  type: 'dropdown' | 'textbox';
  values?: string[];
}

interface Metadata {
  questions: Question[];
}

interface CardData {
  metadata: Metadata;
}

interface ChatWithExpertModalV2Props {
  onClose?: () => void;
  visible?: boolean;
  height?: string;
  onSubmit: (formData: Record<string, string>) => void;
  cardData: HelpingHandCardChatWithExpert['data'];
}

interface FormData {
  [key: string]: string;
}

const ChatWithExpertModalV2: React.FC<ChatWithExpertModalV2Props> = (props) => {
  const {
    onClose,
    visible = true,
    height = '75%',
    onSubmit,
    cardData,
  } = props || {};
  const { metadata } = cardData;
  // @ts-ignore
  const questions: Question[] = [
    {
      "question_id": "ASK_DESTINATION",
      "question": "Destination",
      "type": "dropdown",
      "values": [
        "Kerala",
        "Thailand",
        "Goa",
        "Himachal Pradesh",
        "Andaman",
        "Maldives",
        "Vietnam",
        "Tamil Nadu",
        "Dubai",
        "Karnataka",
        "Rajasthan",
        "Singapore",
        "Indonesia",
        "Malaysia",
        "Sikkim",
        "Bali",
        "Ladakh",
        "Uttar Pradesh",
        "Uttarakhand",
        "Europe",
        "Sri Lanka",
        "Kashmir",
        "Maharashtra",
        "South India",
        "Mauritius",
        "North East",
        "Bhutan",
        "Gujarat",
        "Nepal",
        "Madhya Pradesh",
        "Delhi",
        "Odisha",
        "Hong Kong",
        "Japan",
        "Meghalaya",
        "Andhra Pradesh",
        "West Bengal",
        "Australia",
        "New Delhi",
        "Georgia",
        "Char Dham",
        "Almaty",
        "Orissa",
        "Switzerland",
        "America",
        "France",
        "Kenya",
        "United Kingdom",
        "Punjab",
        "United Arab Emirates",
        "Greece",
        "Around Chennai",
        "Kazakhstan",
        "South Africa",
        "Seychelles",
        "Assam",
        "Austria",
        "Azerbaijan",
        "Netherlands",
        "Egypt",
        "Turkey",
        "Italy",
        "New Zealand",
        "Cambodia",
        "United States",
        "Jammu",
        "Uzbekistan",
        "South Korea",
        "Norway",
        "Iceland",
        "Spain",
        "Bihar",
        "Czech Republic",
        "Around Mumbai",
        "Oman",
        "Finland",
        "Macau",
        "China",
        "Germany",
        "Hungary",
        "Jordan",
        "Croatia",
        "Philippines",
        "Lakshadweep",
        "Denmark",
        "East India",
        "Arunachal Pradesh",
        "Canada",
        "Mexico",
        "Khajuraho",
        "Around Kolkata",
        "North East India",
        "Amarnath",
        "Qatar",
        "Morocco",
        "Signature Journeys",
        "Tanzania",
        "Portugal",
        "Abu Dhabi",
        "Ras Al Khaimah",
        "Mumbai",
        "Bangalore",
        "Chennai",
        "Hyderabad",
        "Kolkata",
        "Mathura",
        "Mahabalipuram",
        "Wayanad",
        "Rishikesh",
        "Bhopal",
        "Taj Holidays",
        "Katra",
        "Bandung",
        "Jamnagar",
        "Vadodara",
        "Negombo",
        "Manchester",
        "Rome",
        "Milan",
        "Gurgaon",
        "Mossel Bay",
        "Cappadocia",
        "Phi Phi Island",
        "North India",
        "Coorg",
        "Rajpipla",
        "Kumarakom",
        "Madurai",
        "Rameshwaram",
        "Pattaya",
        "Varanasi",
        "Ahmedabad",
        "Mount Abu",
        "Kasol",
        "Koh Samui",
        "Diu",
        "Gwalior",
        "Somnath",
        "Hampi",
        "Armenia",
        "Oslo",
        "Madrid",
        "Toronto",
        "Berlin",
        "Rotorua",
        "Darwin",
        "Mahabaleshwar",
        "Copenhagen",
        "San Francisco",
        "Granada",
        "Porto",
        "Munnar",
        "Dharamshala",
        "Jaipur",
        "Galle",
        "Corbett",
        "Lansdowne",
        "Ajmer",
        "Jodhpur",
        "Chail",
        "Kovalam and Poovar",
        "Franz Josef",
        "Golden Triangle",
        "Lachung",
        "Nameri",
        "Haridwar",
        "Manas",
        "Dambulla",
        "Kausani",
        "Kanyakumari",
        "Orchha",
        "Johannesburg",
        "Chiang Mai",
        "Nadi",
        "Venice",
        "Masai Mara",
        "Ubud",
        "Yogyakarta",
        "Nuwara Eliya",
        "Colombo",
        "Udaipur",
        "Srinagar",
        "Bandipur",
        "Queenstown",
        "Leh",
        "Namchi",
        "Nawalgarh",
        "Middle East",
        "Cape Town",
        "Kumbalgarh",
        "Kuala Lumpur",
        "Tokyo",
        "Athirappilly",
        "Chikmangalur",
        "Paro",
        "Las Vegas",
        "Fiji",
        "Vienna",
        "Phuket",
        "Osaka",
        "Chicago",
        "Cruise",
        "Moscow",
        "Athens",
        "Cochin",
        "Manali",
        "Dalhousie",
        "Albania",
        "Jakarta",
        "Bentota",
        "Pahalgam",
        "Kathmandu",
        "Miami",
        "Sydney",
        "Bhubaneshwar",
        "Lachen",
        "Yerevan",
        "Mount Cook",
        "Kota Kinabalu",
        "Pamukkale",
        "New York",
        "London",
        "Barcelona",
        "Budapest",
        "Eastern Europe",
        "Lisbon",
        "Melbourne",
        "Langkawi",
        "Bhavnagar",
        "Russia",
        "Harsil",
        "Scotland",
        "Thekkady",
        "Shimla",
        "Asia",
        "Mysore",
        "Dwarka",
        "Mussoorie",
        "Kabini",
        "Yala",
        "Africa",
        "Ooty",
        "Gold Coast",
        "Auckland",
        "Pushkar",
        "Pilgrimage",
        "Kalimpong",
        "Jaisalmer",
        "Kutch",
        "Prague",
        "Parwanoo",
        "Alwar",
        "Kanha",
        "Lucknow",
        "Nubra Valley",
        "Colombia",
        "Ho Chi Minh City",
        "Nasik",
        "Glasgow",
        "Kasauli",
        "Weekend Getaways",
        "Pelling",
        "Kodaikanal",
        "Guwahati",
        "Gangtok",
        "Bangkok",
        "Chandigarh",
        "Luxury - Domestic",
        "Shillong",
        "Christchurch",
        "Agra",
        "Paris",
        "Matheran",
        "Sasan Gir",
        "Chittorgarh",
        "Seminyak",
        "Shirdi",
        "North America",
        "Vancouver",
        "Ayers Rock",
        "Helsinki",
        "Penang",
        "Badrinath",
        "Rovaniemi",
        "Dehradun",
        "Amritsar",
        "Nainital",
        "Kandy",
        "Ranikhet",
        "Kaziranga",
        "Gulmarg",
        "Darjeeling",
        "Bikaner",
        "Ranthambhore",
        "Srinagar Houseboat",
        "Palampur",
        "Puri",
        "Nairobi",
        "Hanoi",
        "Istanbul",
        "Zurich",
        "Innsbruck",
        "Montreal",
        "Sun City",
        "Siem Reap",
        "Bandhavgarh",
        "Orlando",
        "Mykonos",
        "Tawang",
        "Varkala",
        "Sariska",
        "Lavasa",
        "Harrisburg",
        "Punakha",
        "Port Blair",
        "Manila",
        "Florence",
        "Pangong",
        "Igatpuri",
        "Saputara",
        "Sigiriya",
        "Nagarkot",
        "Aswan",
        "Heraklion",
        "Doha",
        "Tezpur",
        "Lucerne",
        "Kumbakonam",
        "Izmir",
        "Amarkantak",
        "Mangalore",
        "Kannur",
        "Nusa Dua",
        "Almora",
        "Hunter Valley",
        "Hassan",
        "Havelock",
        "Mandawa",
        "Argentina",
        "TASHKENT",
        "Beijing",
        "Jerusalem",
        "Brisbane",
        "JIspa",
        "Belfast",
        "Chintpurni",
        "Jabalpur",
        "Kangra",
        "Mandu",
        "Bekal",
        "Peru",
        "Lima",
        "Vagamon",
        "Vaishno Devi",
        "Barkot",
        "Sweden",
        "Lake Naivasha",
        "Valencia",
        "Bodrum",
        "Kaza",
        "Ottawa",
        "Indore",
        "Port Douglas",
        "Trincomalee",
        "La Digue Island",
        "Pench",
        "Patna",
        "Kanchipuram",
        "Kusadasi",
        "Nagpur",
        "Murudeshwar",
        "Thrissur",
        "Bhalukpong",
        "Joshimath",
        "Cherrapunjee",
        "Kuta",
        "Stockholm",
        "Mandarmoni",
        "Lonavala And Khandala",
        "Gili",
        "Los Angeles",
        "Aurangabad",
        "Saint Petersburg",
        "Ecuador",
        "Praslin Island",
        "Siliguri",
        "Ujjain",
        "Kargil",
        "Kataragama",
        "California",
        "Nassau",
        "Daman",
        "Alleppey",
        "Tsomoriri",
        "Antalya",
        "Bahamas",
        "Zimbabwe",
        "Rio De Janeiro",
        "Pune",
        "Sangla",
        "Tanjore",
        "Mt Kenya",
        "Washington DC",
        "Seville",
        "Thimpu",
        "Calgary",
        "Shanghai",
        "Baku",
        "Galillee",
        "Israel",
        "Lake Louise",
        "Phuentsholing",
        "Kamloops",
        "Dead Sea",
        "Alibagh",
        "Cebu",
        "Rajkot",
        "Canakkale",
        "Gangotri",
        "Rann Utsav",
        "Spiti Valley",
        "Dubrovnik",
        "Panna",
        "Tbilisi",
        "Dandeli",
        "Padova",
        "Engelberg",
        "Liverpool",
        "Santorini",
        "Konya",
        "Nice",
        "Hoi An",
        "Nile River",
        "Amman",
        "Petra",
        "Ahmednagar",
        "Srisailam",
        "Hua Hin",
        "Kedarnath",
        "Scandinavia",
        "Zagreb",
        "Badami",
        "Ziro",
        "Rudraprayag",
        "Jibhi",
        "Uttarkashi",
        "Sonmarg",
        "Udupi",
        "Binsar",
        "Brazil",
        "Narkanda",
        "Raipur",
        "Amsterdam",
        "Kyoto",
        "Cairns",
        "Trivandrum",
        "Bharatpur",
        "Tirupati",
        "Mahe Island",
        "Kullu",
        "Digha",
        "Gokarna",
        "Wadi rum",
        "Chitrakoot",
        "Muscat",
        "Agartala",
        "Kohima",
        "Dirang",
        "Chiang Rai",
        "Jharkhand",
        "Deoghar",
        "Guptkashi",
        "Tadoba",
        "Saudi Arabia",
        "Central And South America",
        "Nahan",
        "Pokhara",
        "Cairo",
        "Lake Elementaita",
        "Munich",
        "Vishakhapatnam",
        "Phnom Penh",
        "Tel Aviv",
        "Konkan",
        "Ganpatipule",
        "Konark",
        "Bhuj",
        "Gaya",
        "Patnitop",
        "Bomdila",
        "Ayodhya",
        "Estonia",
        "Auli",
        "Chopta",
        "Velankanni",
        "Manesar",
        "Puducherry",
        "Neemrana",
        "Mawlynnong",
        "Seoul",
        "Vellore",
        "Sirsi",
        "Bir Biling",
        "Yercaud",
        "Telangana",
        "Petaling Jaya",
        "Jamaica",
        "Chambery",
        "Chamba HP",
        "Railay Beach",
        "Prayagraj",
        "Warangal",
        "Tirunelveli",
        "Nathdwara",
        "Slovenia",
        "Adventure",
        "Luxury",
        "Mexico City",
        "Canary Islands",
        "Hawaiian Islands",
        "AlUla",
        "Florida",
        "Solan",
        "Rampur",
        "Gopalpur",
        "Chakrata",
        "Kukke",
        "Kanpur",
        "Koh Samet",
        "Yas",
        "Mahakaleshwar",
        "Dooars",
        "Zuluk",
        "Hikkaduwa",
        "Wildlife",
        "Fethiye-Oludeniz",
        "Rajahmundry",
        "Pachmarhi",
        "Shimoga",
        "Guruvayoor",
        "Kalpa",
        "Honeymoon",
        "Kufri",
        "Kollur",
        "Sanchi",
        "Riyadh",
        "Caribbean Islands",
        "Montego Bay",
        "South America",
        "Nagaland",
        "Ko Lanta",
        "Mukteshwar",
        "Shoghi",
        "Calicut",
        "Aritar",
        "Sakleshpur",
        "Kanatal",
        "Mashobra",
        "Zanzibar",
        "French Polynesia",
        "Mizoram",
        "Silvassa",
        "Banjar",
        "Valparai",
        "Kushinagar",
        "Palakkad",
        "Dhanaulti",
        "Marmaris",
        "Busan",
        "Jeju",
        "Bora Bora",
        "Khao Lak",
        "Khatu",
        "Jhansi",
        "Kolhapur",
        "Omkareshwar",
        "Sivasagar",
        "Krabi",
        "Hanle",
        "Tenerife",
        "Malvan",
        "Seven Sisters",
        "Guangzhou",
        "Tiruchirappalli",
        "Coonoor",
        "Naldehra",
        "Khajjiar",
        "Gorakhpur",
        "Kottayam",
        "karsog",
        "Vrindavan",
        "Jeddah",
        "Lyon",
        "Bahrain",
        "Sapa",
        "Rouen",
        "Honolulu",
        "Tripura"
      ],
      "prefilled_value": "Kerala",
      "placeholder": "Enter your destination",
      "mandatory": true
    },
    {
      "question_id": "ASK_PHONE",
      "question": "Phone",
      "type": "textbox",
      "prefilled_value": "",
      "placeholder": "Enter your phone number",
      "regex": "^[0-9]{10}$",
      "input_type": "numeric",
      "mandatory": true
    },
    {
      "question_id": "ASK_EMAIL",
      "question": "Email Address",
      "placeholder": "EMAIL ADDRESS",
      "input_type": "text",
      "mandatory": true,
      "type": "textbox",
      "regex": "^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$"
    }
  ];
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const snapPoints = useMemo(() => [height], [height]);
  const [hasUserInteracted, setHasUserInteracted] = useState<boolean>(false);

  // Initialize form data with prefilled values
  const [formData, setFormData] = useState<FormData>(() => {
    const initialData: FormData = {};
    questions.forEach((question) => {
      initialData[question.question_id] = question.prefilled_value || '';
    });
    return initialData;
  });
  const chatContext = useAppStateStore.getState().chatContext;
  const { expertMetadata } = chatContext || {};
  const { uniqChatId = ''} = expertMetadata || {};

  // Holiday tracking hook
  const {
    trackDestinationShown,
    trackDestinationChange,
    trackPhoneClick,
    trackDestinationSelected,
    trackSubmitClick,
  } = useHolidayTracking();

  useEffect(() => {
    const destinationValue = formData['ASK_DESTINATION'];
    if (destinationValue) {
      trackDestinationShown(destinationValue);
    }
  }, []);

  // Generic function to filter dropdown options based on search query
  const getFilteredOptions = (question: Question): string[] => {
    const options: string[] = question?.values || [];

    // Return all options when search query is empty
    if (!searchQuery.trim()) {
      return options;
    }

    // Return filtered options
    return options.filter((option) =>
      option.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  };

  const handleSheetChange = useCallback(
    (index: number) => {
      if (index === -1) {
        onClose?.();
      }
    },
    [onClose],
  );

  const handleSubmit = (): void => {
    // Log form data for debugging
    questions.forEach((question) => {
      const { question_id } = question;
      const value = formData[question_id] || '';
      console.log(`Question ID: ${question_id}, Value: ${value}`);
    });

    onSubmit(formData);
    //Track Submit
    const destinationValue = formData['ASK_DESTINATION'] || '';
    trackSubmitClick(destinationValue, uniqChatId);
  };

  // useMemo hook to calculate if submit should be enabled
  const isSubmitDisabled = useMemo(() => {
    for (const question of questions) {
      const { question_id, mandatory, regex } = question;

      // Skip non-mandatory questions
      if (!mandatory) continue;

      // Get the current value for this question from formData
      const currentValue = formData[question_id] || '';

      // Check if mandatory field is empty
      if (!currentValue || currentValue.trim() === '') {
        return true; // Disable submit
      }

      // Check regex validation if regex is provided
      if (regex && currentValue) {
        const regexPattern = new RegExp(regex);
        if (!regexPattern.test(currentValue)) {
          return true; // Disable submit
        }
      }
    }

    return false; // Enable submit
  }, [questions, formData]);

  // renders
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.7}
        onPress={onClose}
      />
    ),
    [onClose],
  );

  const handleTextChange = (
    text: string,
    question_id: string,
    type: string,
  ): void => {
    // Update form data for any field
    setFormData(prev => ({
      ...prev,
      [question_id]: text
    }));

    // Special handling for dropdown (searchable) fields
    if (type === 'dropdown') {
      setSearchQuery(text);
      setHasUserInteracted(true); // Mark that user has interacted
    }
  };

  const handleFocus = (type: string, question_id: string): void => {
    const destinationValue = formData['ASK_DESTINATION'] || '';

    if (type === 'dropdown') {
      setShowDropdown(true);
      //Track destination dropdown shown
      trackDestinationChange(destinationValue);
    }
    if (type === 'textbox') {
      // Track based on question type - for now keeping phone tracking for ASK_PHONE
      if (question_id === 'ASK_PHONE') {
        trackPhoneClick(destinationValue);
      }
    }
  };

  const handleBlur = (type: string): void => {
    if (type === 'dropdown') {
      // Small delay to allow selection before closing
      setTimeout(() => {
        setShowDropdown(false);
      }, 450);
    }
  };

  const handleDropdownSelect = (value: string, question_id: string): void => {
    // Update form data for any dropdown field
    setFormData(prev => ({
      ...prev,
      [question_id]: value
    }));
    setSearchQuery(value);
    setShowDropdown(false);

    // Track selection - for now keeping destination tracking for ASK_DESTINATION
    if (question_id === 'ASK_DESTINATION') {
      trackDestinationSelected(value);
    }
  };

  const renderCards = (): JSX.Element[] => {
    return questions.map((q, index) => {
      const {
        prefilled_value,
        input_type,
        mandatory,
        placeholder = '',
        question,
        regex,
        question_id,
        type,
        values,
      } = q;
      if (type === 'dropdown') {
        // Generic dropdown logic - shows search query if user interacted, otherwise shows form value
        const inputValue = hasUserInteracted
          ? searchQuery
          : formData[question_id] || '';

        return (
          <View key={index}>
            <View style={styles.inputContainer}>
              <Text style={styles.questionLabel}>
                {question?.toUpperCase()}
                {mandatory && <Text style={styles.mandatory}> *</Text>}
              </Text>
              <View style={styles.inputWithIcon}>
                <TextInput
                  value={!isEmpty(inputValue) ? inputValue : ''}
                  onChangeText={(text) => handleTextChange(text, question_id, type)}
                  onFocus={() => handleFocus(type, question_id)}
                  placeholderTextColor={COLORS.BLACK_VAR_5}
                  style={styles.valueInputWithIcon}
                  placeholder={placeholder || `Select ${question}`}
                />
                <View style={styles.searchIconContainer}>
                  <SearchIcon width={24} height={24} color={COLORS.BLACK_VAR_5} />
                </View>
              </View>
            </View>

            {showDropdown && (
              <View style={styles.dropdownContainer}>
                <ScrollView
                  style={{ width: '100%', maxHeight: 191 }}
                  nestedScrollEnabled={true}
                  showsVerticalScrollIndicator={false}
                  keyboardShouldPersistTaps="handled"
                >
                  <View style={styles.suggestedContainer}>
                    <Text style={styles.suggestedText}>
                      {getFilteredOptions(q).length > 0
                        ? 'Popular Options'
                        : 'No results found'}
                    </Text>
                  </View>
                  {getFilteredOptions(q).length > 0 ? (
                    getFilteredOptions(q).map((item, itemIndex) => (
                      <TouchableOpacity
                        key={itemIndex}
                        style={[
                          styles.dropdownItem,
                          item === formData[question_id] &&
                            styles.dropdownItemSelected,
                        ]}
                        onPress={() => handleDropdownSelect(item, question_id)}
                      >
                        <Text
                          style={[
                            styles.dropdownItemText,
                            item === formData[question_id] &&
                              styles.dropdownItemTextSelected,
                          ]}
                        >
                          {item}
                        </Text>
                        {item === formData[question_id] && (
                          <Text style={styles.checkmark}>✓</Text>
                        )}
                      </TouchableOpacity>
                    ))
                  ) : (
                    <View style={styles.noResultsContainer}>
                      <Text style={styles.noResultsText}>
                        No results found for "{searchQuery}"
                      </Text>
                    </View>
                  )}
                </ScrollView>
              </View>
            )}
          </View>
        );
      }
      if (type === 'textbox') {
        const {
          prefilled_value,
          input_type,
          mandatory,
          placeholder = '',
          question,
          regex,
          question_id,
          type,
        } = q;
        return (
          <View key={index}>
            <View style={styles.inputContainer}>
              <Text style={styles.questionLabel}>
                {placeholder}
                {mandatory && <Text style={styles.mandatory}> *</Text>}
              </Text>
              <TextInput
                value={formData[question_id] || ''}
                onChangeText={(text) => handleTextChange(text, question_id, type)}
                onFocus={() => handleFocus(type, question_id)}
                onBlur={() => handleBlur(type)}
                placeholderTextColor={COLORS.BLACK_VAR_5}
                style={styles.valueInput}
                keyboardType={
                  input_type === 'numeric' ? 'number-pad' :
                  question_id.includes('EMAIL') ? 'email-address' :
                  'default'
                }
              />
            </View>
          </View>
        );
      }
      return (
        <View key={index}>
          <Text>{'Not found'}</Text>
        </View>
      );
    });
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <GestureHandlerRootView style={styles.container}>
        <BottomSheet
          index={0}
          snapPoints={snapPoints}
          onChange={handleSheetChange}
          backdropComponent={renderBackdrop}
          enableDynamicSizing={false}
          enablePanDownToClose={true}
          keyboardBehavior="fillParent"
        >
          <BottomSheetView style={styles.contentContainer}>
            <View style={styles.headerContainer}>
              <View style={styles.headerRow}>
                <Text style={styles.headerTitle}>
                  Share details to connect with an agent
                </Text>
                <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                  <CloseIcon
                    width={24}
                    height={24}
                    color={COLORS.TEXT_LOW_EMPHASIS}
                  />
                </TouchableOpacity>
              </View>

              {renderCards()}

              <TouchableOpacity
                disabled={isSubmitDisabled}
                onPress={handleSubmit}
                style={[
                  styles.submitButton,
                  isSubmitDisabled && styles.submitButtonDisabled,
                ]}
              >
                <ButtonBackground disabled={isSubmitDisabled}>
                  <Text style={[styles.submitButtonText]}>SUBMIT</Text>
                </ButtonBackground>
              </TouchableOpacity>
            </View>
          </BottomSheetView>
        </BottomSheet>
      </GestureHandlerRootView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flex: 1,
  },
  sectionHeaderContainer: {
    backgroundColor: COLORS.WHITE,
    padding: 6,
  },
  sectionHeader: {
    color: COLORS.BLACK,
    padding: 10,
    marginLeft: 18,
    backgroundColor: COLORS.WHITE,
    fontSize: 16,
    fontWeight: '600',
  },
  crossIcon: {
    height: 24,
    width: 24,
    marginLeft: 18,
  },
  border: {
    backgroundColor: COLORS.BLACK_VAR_4,
    width: '100%',
    height: 1,
    marginTop: 10,
    marginBottom: 10,
  },
  sectionListContainer: {
    paddingBottom: 16,
    paddingHorizontal: 8,
  },
  searchIcon: {
    height: 15,
    width: 15,
    marginLeft: 10,
    marginRight: 10,
    tintColor: COLORS.GREY,
  },
  headerContainer: {
    backgroundColor: 'white',
    height: '100%',
    width: '100%',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingRight: 16,
    paddingVertical: 16,
    marginLeft: 16,
  },
  headerTitle: {
    fontSize: 22,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: COLORS.BLACK,
    flex: 1,
    marginRight: 16,
  },
  closeButton: {
    padding: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerSeparator: {
    height: 1,
    backgroundColor: COLORS.BLACK_VAR_4,
    marginHorizontal: 16,
    marginBottom: 8,
  },
  textInput: {
    marginTop: 10,
    backgroundColor: COLORS.WHITE,
    borderWidth: 1,
    borderColor: COLORS.BLACK_VAR_4,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    marginHorizontal: 16,
  },
  inputContainer: {
    marginTop: 10,
    backgroundColor: COLORS.GREY_VAR_3,
    borderWidth: 1,
    borderColor: COLORS.GREY_VAR_4,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    marginHorizontal: 16,
  },
  questionLabel: {
    fontSize: 12,
    color: COLORS.BLACK_VAR_5,
    fontFamily: FONTS.FONT_FAMILY_700,
    marginBottom: 4,
  },
  valueInput: {
    fontSize: 16,
    color: COLORS.BLACK,
    fontFamily: FONTS.FONT_FAMILY_700,
    padding: 0,
    margin: 0,
  },
  itemContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BLACK_VAR_4,
    backgroundColor: COLORS.WHITE,
    marginHorizontal: 8,
  },
  itemText: {
    fontSize: 16,
    color: COLORS.BLACK,
  },
  // Submit Button Styles
  submitButton: {
    marginHorizontal: 16,
    marginBottom: 0,
    marginTop: 16,
  },
  submitButtonDisabled: {},
  submitButtonText: {
    color: COLORS.WHITE,
    fontSize: 16,
    fontFamily: FONTS.FONT_FAMILY_900,
    textAlign: 'center',
  },
  submitButtonTextDisabled: {
    color: COLORS.GREY_VAR_3,
  },
  // Dropdown Container Styles
  dropdownContainer: {
    marginHorizontal: 16,
    marginTop: 5,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.GREY_VAR_4,
    backgroundColor: COLORS.WHITE,
    overflow: 'hidden',
  },
  // Dropdown Item Styles
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BLACK_VAR_4,
    backgroundColor: COLORS.WHITE,
  },
  dropdownItemSelected: {
    backgroundColor: COLORS.WHITE,
  },
  dropdownItemText: {
    flex: 1,
    fontSize: 16,
    color: COLORS.BLACK,
  },
  dropdownItemTextSelected: {
    color: COLORS.BLUE_VAR_1,
    fontWeight: '600',
  },
  checkmark: {
    fontSize: 16,
    color: COLORS.BLUE_VAR_1,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  // Suggested Section Styles
  suggestedContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: COLORS.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BLACK_VAR_4,
  },
  suggestedText: {
    fontSize: 12,
    color: COLORS.TEXT_LOW_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_400,
  },
  // No Results Style
  noResultsContainer: {
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noResultsText: {
    fontSize: 14,
    color: COLORS.BLACK_VAR_5,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  mandatory: {
    color: '#E53E3E',
    fontSize: 14,
  },
  inputWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  valueInputWithIcon: {
    fontSize: 16,
    color: COLORS.BLACK,
    fontWeight: '600',
    padding: 0,
    margin: 0,
    flex: 1,
  },
  searchIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 8,
  },
});

export default ChatWithExpertModalV2;
