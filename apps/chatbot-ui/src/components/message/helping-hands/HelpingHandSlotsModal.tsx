import React, { useEffect, useState } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

import { RightIcon } from '../../../assets/RightIcon';

import { ScrollView } from 'react-native-gesture-handler';
import { useMessageAction, useMessageStore } from '../../../store/messages';
import { config } from '../../../config';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import CallbackSlotGroupsList from '../../helping-hand-options/CallbackSlotGroupsList';
import { ButtonBackground } from '../../base/Button';
import { BottomModalPopup } from '../../BottomModalPopup';
import {
  WsHelpingHandCallbackSlotsResponseData,
  WsSubSuccessResponse,
} from '../../../store/socket/socketType';
import { trackOmnitureClickEvent } from '../../../native/omniture';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../../native/tracking/pdt';

export function CallbackSlots({
  slotsSocketResponseData,
}: {
  slotsSocketResponseData: WsSubSuccessResponse<WsHelpingHandCallbackSlotsResponseData>;
}) {
  const [userSelectedSlot, setUserSelectedSlot] = React.useState<{
    slotText: '';
    timestamp: number;
  } | null>(null);
  const { data, uiMetadata } = slotsSocketResponseData;
  const { callBackSlots = [], cardHeader, ctaText, configId } = data?.data || {};

  const { sendMessage } = useMessageAction();

  // region > Derived States
  const title = cardHeader || config.defaultScheduleCallbackPageTitle;
  const ctaTextValue = ctaText || config.defaultScheduleCallbackCTATitle;

  const handleConfirmSlot = async () => {
    if (!userSelectedSlot) return;
    // trackClickEvent(TrackingEvent.payload_ScheduleCallbackSlotConfirmed());
    sendMessage(`${userSelectedSlot?.slotText}`, {
      eventType: 'SCHEDULE_CALLBACK',
      dataProps: {
        timeSlot: userSelectedSlot.timestamp,
        configId,
        metadata: uiMetadata?.metadata,
      },
    });
  };

  return (
    <View style={{ flex: 1, padding: 16 }}>
      <Text
        style={{
          fontSize: 18,
          fontFamily: FONTS.FONT_FAMILY_900,
          color: COLORS.TEXT_HIGH_EMPHASIS,
          marginBottom: 10,
        }}
      >
        {title}
      </Text>
      <ScrollView nestedScrollEnabled style={{ height: 250 }}>
        <CallbackSlotGroupsList
          callbackSlots={callBackSlots}
          selectedSlot={userSelectedSlot}
          onSelectSlot={setUserSelectedSlot}
        />
      </ScrollView>
      <TouchableOpacity
        disabled={false}
        onPress={handleConfirmSlot}
        style={{ marginTop: 14 }}
      >
        <ButtonBackground disabled={false}>
          <Text
            style={{
              color: COLORS.WHITE,
              fontSize: 16,
              fontFamily: FONTS.FONT_FAMILY_900,
            }}
          >
            {ctaTextValue}
          </Text>
        </ButtonBackground>
      </TouchableOpacity>
    </View>
  );
}

export function HelpingHandSlotsModal({
  slotsData,
}: {
  slotsData: WsSubSuccessResponse<WsHelpingHandCallbackSlotsResponseData>;
}) {
  const [showModal, setShowModal] = useState(true);
  const activeConversationId = useMessageStore.getState().activeConversationId || '';
  const conversation =
    useMessageStore.getState().conversationById[activeConversationId];
  const { updateConversationById } = useMessageStore.getState();

  const onClose = () => {
    setShowModal(false);
    // reset helpingHands slots
    if (conversation) {
      updateConversationById(activeConversationId, {
        ...(conversation || {}),
        helpingHands: null,
      });
    }
  };
  const modalProps = {
    enabled: showModal,
    title: 'Schedule a Callback',
    icon: <RightIcon fill={'#3944A3'} />,
    onClose,
  };

  useEffect(() => {
    if (showModal) {
      trackOmnitureClickEvent('HELPING_HAND_SLOTS_SHOWN');
      trackPDTEvent({
        eventName: eventNameSchema.BUTTON_CLICKED,
        eventType: eventTypeSchema.ACTION,
        eventValue: eventValueSchema.CALL_SLOT_SHOWN,
      });
    }
  }, [showModal]);

  return (
    <BottomModalPopup {...modalProps}>
      <CallbackSlots slotsSocketResponseData={slotsData} />
    </BottomModalPopup>
  );
}
