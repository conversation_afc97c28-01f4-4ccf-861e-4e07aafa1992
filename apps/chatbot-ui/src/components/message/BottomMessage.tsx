import React, { FC, Fragment, useEffect } from 'react';
import { StyleSheet, Text } from 'react-native';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { INPUT_BOX_HEIGHT } from '../../const';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import { TouchableFeedback } from '../base/TouchableFeeback';
import { ShadowedView } from 'react-native-fast-shadow';
import { triggerErrorHaptic } from '../../utils/hapticUtils';
import { isEvaluationMode } from '../../utils/webUtils';

type BottomMessageProps = {
  isConnecting?: boolean;
  reconnectAttempt?: number;
  handlePress?: () => void;
};

const BottomMessage: FC<BottomMessageProps> = ({
  isConnecting,
  reconnectAttempt,
  handlePress = () => {},
}) => {
  useEffect(() => {
    triggerErrorHaptic();
    trackPDTEvent({
      eventName: eventNameSchema.PAGE_ENTRY,
      eventType: eventTypeSchema.PAGE,
      eventValue: eventValueSchema.RECONNECT_CTA_SHOWN,
    });
  }, []);
  if (isEvaluationMode()) {
    return null;
  }
  return (
    <ShadowedView style={styles.container}>
      {isConnecting ? (
        <Text style={styles.progressText}>
          {reconnectAttempt === 0 ? 'Connecting...' : 'Reconnecting...'}
        </Text>
      ) : (
        <Fragment>
          <Text style={styles.infoText}>
            Uh Oh! Connection lost with Myra. Try reconnecting the chat
          </Text>
          <TouchableFeedback
            containerStyle={{
              borderRadius: 12,
              overflow: 'hidden',
            }}
            onPress={handlePress}
          >
            <Text style={styles.ctaText}>Reconnect</Text>
          </TouchableFeedback>
        </Fragment>
      )}
    </ShadowedView>
  );
};

const styles = StyleSheet.create({
  container: {
    maxHeight: INPUT_BOX_HEIGHT,
    minHeight: INPUT_BOX_HEIGHT,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.WHITE,
    paddingTop: 20,
    paddingBottom: 12,
    // shadow effect
    shadowColor: 'rgba(53, 95, 242, 0.8)',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.18,
    shadowRadius: 12,
    elevation: 1,
  },
  progressText: {
    color: COLORS.BLACK_VAR_5,
    fontFamily: FONTS.FONT_FAMILY_700,
    fontSize: 16,
    lineHeight: 19,
    textAlign: 'center',
  },
  infoText: {
    color: COLORS.TEXT_LOW_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 12,
    lineHeight: 14,
    textAlign: 'center',
    width: 250,
  },
  ctaText: {
    color: COLORS.BLUE_VAR_1,
    fontFamily: FONTS.FONT_FAMILY_700,
    fontSize: 16,
    textAlignVertical: 'center',
    lineHeight: 19,
    textAlign: 'center',
    minHeight: 44,
    paddingHorizontal: 16,
  },
});

export default BottomMessage;
