import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS } from '../../../constants/globalStyles';
import { ItineraryOptionCard } from './ItineraryOptionCard';
import { PackageCard } from '../PackageCard/PackageCard';

export function SelectedCard({
  data,
  msg,
}: {
  scale?: number;
  data: TextWidget;
  msg: Message;
}) {
  if (!data?.quotedContentInfo?.templatePayload?.data) {
    return null;
  }

  const templateData = data.quotedContentInfo.templatePayload.data;

  // Check if it's an ItineraryDataType with card_name 'itinerary-card'
  const isItineraryCard =
    'card_name' in templateData && templateData.card_name === 'itinerary-card';

  // Check if it's a BookablePackage with ask_question_cta
  const isPackageCard =
    'card_name' in templateData && templateData.card_name === 'package-card';

  // if (!isItineraryCard && !isPackageCard) return null;
  return (
    <View style={styles.container}>
      <View style={styles.card}>
        {isItineraryCard ? (
          <ItineraryOptionCard
            source="preview"
            data={data.quotedContentInfo.templatePayload.data as ItineraryDataType}
            onCardClick={() => undefined}
            msg={msg}
          />
        ) : isPackageCard ? (
          <PackageCard
            source="preview"
            msg={msg}
            package={data.quotedContentInfo.templatePayload.data as BookablePackage}
          />
        ) : null}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingTop: 12,
    paddingBottom: 11,
    marginLeft: 'auto',
  },
  card: {
    backgroundColor: COLORS.BLUE_VAR_3,
  },
  title: {
    fontSize: 14,
    marginTop: 8,
    color: COLORS.WHITE,
    textAlign: 'right',
  },
});
