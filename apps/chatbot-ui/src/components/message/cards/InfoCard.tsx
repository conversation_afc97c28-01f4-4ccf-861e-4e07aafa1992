import React, { FC, useCallback, useState } from 'react';
import {
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import { COLORS, FONTS, Z_INDEX } from '../../../constants/globalStyles';
import { MarkdownText } from '../MarkdownText';
import { CloseIcon } from '../../../assets/CloseIcon';
import { ModalSlot } from '../../../screens/ModalProvider';
import { useBackAction } from '../../../utils/useBackHandler';
import Animated, {
  FadeIn,
  FadeOut,
  SlideInDown,
  SlideOutDown,
} from 'react-native-reanimated';
import { ScrollView } from 'react-native-gesture-handler';
import { LayoutChangeEvent } from 'react-native/Libraries/Types/CoreEventTypes';
import { CARDS_WIDTH_MAP } from '../../../const';

export type InfoCardProps = InfoCardType & { msg: Message };

export const InfoCard: FC<InfoCardProps> = (props) => {
  const { title, icon_url, sub_title: subTitle, description } = props.data;
  const [expandModal, setExpandModal] = useState<boolean>(false);
  const handleClick = useCallback(() => setExpandModal(true), []);
  const closeModal = useCallback(() => {
    setExpandModal(false);
    return true;
  }, []);
  useBackAction(closeModal, {
    enabled: expandModal,
  });

  const MAX_CARD_HEIGHT = 64;
  const READ_MORE_TOP_OVERLAP = -24;
  const [showReadMore, setShowReadMore] = useState<boolean>(false);

  const onLayout = useCallback((event: LayoutChangeEvent) => {
    setShowReadMore(
      event.nativeEvent.layout.height > MAX_CARD_HEIGHT + READ_MORE_TOP_OVERLAP,
    );
  }, []);
  return (
    <View style={styles.destinationCard}>
      <View style={styles.borderView} />
      <View style={[styles.cardBody, { flexDirection: 'row', gap: 8 }]}>
        <Image
          source={{ uri: icon_url }}
          style={{ borderRadius: 8, height: 36, width: 36 }}
        />
        <View style={{ flexDirection: 'column', flex: 1 }}>
          <View style={styles.titleSection}>
            <Text numberOfLines={1} style={styles.title}>
              {title}
            </Text>
          </View>
          <Text numberOfLines={1} style={styles.subtitle}>
            {subTitle}
          </Text>
          <View style={{ flex: 1 }}>
            {!!description && (
              <View>
                <View style={{ maxHeight: MAX_CARD_HEIGHT, overflow: 'hidden' }} nativeID='text-card'>
                  <View onLayout={onLayout}>
                    <MarkdownText text={description} size={'small'} />
                  </View>
                </View>
                <TouchableOpacity
                  activeOpacity={0.7}
                  disabled={!showReadMore}
                  style={{
                    paddingTop: 12,
                    marginTop: READ_MORE_TOP_OVERLAP,
                    zIndex: Z_INDEX.OVERLAP_1,
                    opacity: showReadMore ? 1 : 0,
                    backgroundColor: 'rgba(255,255,255,0.66)',
                  }}
                  onPress={() => {
                    handleClick();
                  }}
                >
                  <Text
                    style={{
                      color: COLORS.BLUE_VAR_1,
                      fontFamily: FONTS.FONT_FAMILY_700,
                      fontSize: 12,
                      lineHeight: 16,
                    }}
                  >
                    Read more
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </View>
      {expandModal && (
        <ModalSlot>
          <InfoCardExpanded {...props} onClose={closeModal} />
        </ModalSlot>
      )}
    </View>
  );
};

export function InfoCardExpanded(
  cardProps: InfoCardProps & { onClose: () => void },
) {
  const { title, icon_url, sub_title, description } = cardProps.data;
  const { height } = useWindowDimensions();
  const MODAL_ICON_SIZE = 36;
  return (
    <Animated.View
      entering={FadeIn}
      exiting={FadeOut}
      style={{
        flexDirection: 'column',
        zIndex: Z_INDEX.MODAL,
        ...StyleSheet.absoluteFillObject,
        backgroundColor: '#0002',
        alignItems: 'flex-end',
        justifyContent: 'flex-end',
      }}
    >
      <Animated.View
        entering={SlideInDown}
        exiting={SlideOutDown}
        style={[
          styles.cardBody,
          {
            flex: 0,
            zIndex: Z_INDEX.MODAL,
            minHeight: 200,
            maxHeight: Math.round(height * 0.6),
            position: 'relative',
            backgroundColor: '#fff',
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
          },
        ]}
      >
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <Image
            source={{ uri: icon_url }}
            style={{
              borderRadius: 8,
              height: MODAL_ICON_SIZE,
              width: MODAL_ICON_SIZE,
            }}
          />
          <View style={{ flexDirection: 'column', flex: 1 }}>
            <View style={styles.titleSection}>
              <Text numberOfLines={1} style={styles.title}>
                {title}
              </Text>
            </View>
            <Text numberOfLines={1} style={styles.subtitle}>
              {sub_title}
            </Text>
          </View>
          <View>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => {
                cardProps.onClose();
              }}
            >
              <CloseIcon />
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView>
          <View
            style={{
              flexDirection: 'column',
              marginLeft: MODAL_ICON_SIZE + 8,
            }}
          >
            {!!description && <MarkdownText text={description} size={'small'} />}
          </View>
        </ScrollView>
      </Animated.View>
    </Animated.View>
  );
}

const CARD_WIDTH = CARDS_WIDTH_MAP['info-card'];
const styles = StyleSheet.create({
  borderView: {
    borderWidth: 1,
    borderRadius: 20,
    borderColor: 'rgb(243,229,238)',
    backgroundColor: COLORS.WHITE,
    overflow: 'hidden',
    // elevation: 2,
    ...StyleSheet.absoluteFillObject,
  },
  destinationCard: {
    // backgroundColor: 'red',
    width: CARD_WIDTH,
  },
  cardBody: {
    width: '100%',
    flex: 1,
    padding: 12,
    overflow: 'hidden',
    flexDirection: 'column',
  },
  titleSection: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 0,
  },
  title: {
    fontSize: 16,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
  },
  subtitle: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_LOW_EMPHASIS,
    marginVertical: 4,
  },
});
