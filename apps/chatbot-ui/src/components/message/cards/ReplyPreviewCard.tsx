import React, { memo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import LinearGradient from 'react-native-linear-gradient';
import { CloseIcon } from '../../../assets/CloseIcon';
import { MyraBotIcon } from '../../../assets/MyraBotIcon';
import { TouchableFeedback } from '../../../components/base';
import { INPUT_BOX_HEIGHT, REPLY_PREVIEW_ADDITIONAL_HEIGHT } from '../../../const';

const GRADIENT_COLORS = ['#51AFE6', '#355FF2', '#11287A'];
const GRADIENT_WIDTH = 5;

interface ReplyPreviewCardProps {
  title: string;
  subtitle: string;
  onClose: () => void;
  inputBoxHeight?: number;
}

export const ReplyPreviewCard = memo(
  ({ title, subtitle, onClose, inputBoxHeight=0 }: ReplyPreviewCardProps) => {
    // Calculate dynamic height based on input box height
    
    return (
      <View style={[styles.container, { height: inputBoxHeight }]}>
        <View style={styles.content}>
          <LinearGradient colors={GRADIENT_COLORS} style={styles.leftGradient} />

          <View style={styles.middle}>
            <MyraBotIcon />
            <Text style={styles.title} numberOfLines={1}>
              {title}
            </Text>
            <Text style={styles.subtitle} numberOfLines={1}>
              {subtitle}
            </Text>
          </View>

          <TouchableFeedback
            style={styles.closeButton}
            onPress={onClose}
            accessibilityLabel="Close reply preview"
            accessibilityRole="button"
          >
            <CloseIcon color={COLORS.BLUE_VAR_1} />
          </TouchableFeedback>
        </View>
      </View>
    );
  },
);

ReplyPreviewCard.displayName = 'ReplyPreviewCard';

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F5F5F5',
    justifyContent: 'flex-start',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftGradient: {
    width: GRADIENT_WIDTH,
    height: REPLY_PREVIEW_ADDITIONAL_HEIGHT,
    marginRight: 12,
    borderRadius: 2.5,
  },
  middle: {
    flex: 1,
    paddingBottom: 8,
  },
  title: {
    fontSize: 14,
    lineHeight: 16,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
    marginTop: 4,
  },
  subtitle: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
    marginTop: 4,
    lineHeight: 14,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    marginRight: 12,
  },
});
