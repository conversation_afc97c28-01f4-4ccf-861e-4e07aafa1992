import React, { FC, useCallback, useMemo } from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import { COLORS, FONTS, Z_INDEX } from '../../../constants/globalStyles';
import { TouchableFeedback } from '../../base/TouchableFeeback';
import { ShadowedView } from 'react-native-fast-shadow';
import { useResponsiveWidth } from '../../../hooks/useResponsiveWidth';
import { safeGet } from '../../../utils/functionUtils';

type DocumentCardData = {
  header?: {
    left_section?: {
      title?: string;
    };
    right_section?: {
      title?: string;
    };
  };
  main?: {
    title?: string;
    icon?: string;
    sub_title?: string;
    description?: string;
    text?: string;
    options_title?: string;
    options?: string[];
  };
  footer?: {
    title?: string;
    description?: string;
    url?: string;
  };
};

type DocumentCardProps = {
  id: string;
  type: 'documents-card';
  data: DocumentCardData;
  msg: Message;
  onCardClick?: (cardData: any) => void;
} & BookmarkEntity;

const validateDocumentCardData = (data: DocumentCardData): boolean => {
  if (!data || typeof data !== 'object') return false;

  return !!(data.header || data.main || data.footer);
};

export const DocumentCard: FC<DocumentCardProps> = React.memo((props) => {
  const { containerWidth } = useResponsiveWidth();

  // Memoize card width calculation
  const cardWidth = useMemo(() => containerWidth - 70, [containerWidth]);

  const { data, onCardClick: onCardClickProp } = props;

  // Memoize data validation
  const isDataValid = useMemo(() => validateDocumentCardData(data), [data]);

  // Memoize extracted data with fallbacks
  const extractedData = useMemo(
    () => ({
      headerLeftTitle: safeGet(data, ['header', 'left_section', 'title'], ''),
      headerRightTitle: safeGet(data, ['header', 'right_section', 'title'], ''),
      mainTitle: safeGet(data, ['main', 'title'], ''),
      mainIcon: safeGet(data, ['main', 'icon'], ''),
      mainSubTitle: safeGet(data, ['main', 'sub_title'], ''),
      mainDescription: safeGet(data, ['main', 'description'], ''),
      mainText: safeGet(data, ['main', 'text'], ''),
      mainOptionsTitle: safeGet(data, ['main', 'options_title'], ''),
      mainOptions: safeGet(data, ['main', 'options'], []),
      footerTitle: safeGet(data, ['footer', 'title'], ''),
      footerDescription: safeGet(data, ['footer', 'description'], ''),
    }),
    [data],
  );

  // Memoize section visibility flags
  const sectionVisibility = useMemo(
    () => ({
      hasHeader:
        data.header &&
        (extractedData.headerLeftTitle || extractedData.headerRightTitle),
      hasMain:
        data.main &&
        (extractedData.mainTitle ||
          extractedData.mainSubTitle ||
          extractedData.mainDescription),
      hasFooter:
        data.footer &&
        (extractedData.footerTitle || extractedData.footerDescription),
    }),
    [data, extractedData],
  );

  // Memoize shadow style
  const shadowStyle = useMemo(
    () => ({
      shadowColor: COLORS.GREY_VAR_1,
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.18,
      shadowRadius: 5,
      borderRadius: 16,
      width: cardWidth,
      backgroundColor: COLORS.WHITE,
    }),
    [cardWidth],
  );

  // Memoize onCardClick callback
  const onCardClick = useCallback(() => {
    if (onCardClickProp) {
      onCardClickProp({ data: props.data });
    }
  }, [onCardClickProp, props.data]);

  // Early return if data is invalid
  if (!isDataValid) {
    return null;
  }

  const {
    headerLeftTitle,
    headerRightTitle,
    mainTitle,
    mainIcon,
    mainSubTitle,
    mainDescription,
    mainText,
    mainOptionsTitle,
    mainOptions,
    footerTitle,
    footerDescription,
  } = extractedData;

  const { hasHeader, hasMain, hasFooter } = sectionVisibility;

  return (
    <TouchableFeedback
      onPress={onCardClick}
      style={{ flex: 1 }}
      containerStyle={styles.documentCard}
      nativeID="touchable-feedback-document-card"
    >
      <ShadowedView style={shadowStyle} nativeID="shadowed-view-document-card">
        {/* Header Section */}
        {hasHeader && (
          <HeaderSection leftTitle={headerLeftTitle} rightTitle={headerRightTitle} />
        )}

        {/* Main Content */}
        {hasMain && (
          <MainSection
            title={mainTitle}
            icon={mainIcon}
            subTitle={mainSubTitle}
            description={mainDescription}
            text={mainText}
            optionsTitle={mainOptionsTitle}
            options={mainOptions}
          />
        )}

        {/* Footer Section */}
        {hasFooter && (
          <FooterSection title={footerTitle} description={footerDescription} />
        )}
      </ShadowedView>
    </TouchableFeedback>
  );
});

// Memoized Header Section Component
const HeaderSection = React.memo<{
  leftTitle: string;
  rightTitle: string;
}>(({ leftTitle, rightTitle }) => (
  <View style={styles.headerSection}>
    <Text style={styles.headerLeft}>{leftTitle}</Text>
    <Text style={styles.headerRight}>{rightTitle}</Text>
  </View>
));

// Memoized Main Section Component
const MainSection = React.memo<{
  title: string;
  icon: string;
  subTitle: string;
  description: string;
  text: string;
  optionsTitle: string;
  options: string[];
}>(({ title, icon, subTitle, description, text, optionsTitle, options }) => {
  // Memoize image error handler
  const handleImageError = useCallback(() => {
    return;
  }, []);

  return (
    <View style={styles.mainContent}>
      {/* Title with Icon */}
      {(title || icon) && (
        <View style={styles.titleSection}>
          {icon && (
            <Image
              style={styles.titleIcon}
              source={{ uri: icon }}
              onError={handleImageError}
            />
          )}
          {title && <Text style={styles.mainTitle}>{title}</Text>}
        </View>
      )}

      {/* Sub title */}
      {subTitle && <Text style={styles.subTitle}>{subTitle}</Text>}

      {/* Description and Date */}
      {(description || text) && (
        <View style={styles.descriptionSection}>
          {description && <Text style={styles.description}>{description}</Text>}
          {text && <Text style={styles.dateText}>{text}</Text>}
        </View>
      )}

      {/* Options Section */}
      {(optionsTitle || (Array.isArray(options) && options.length > 0)) && (
        <OptionsSection title={optionsTitle} options={options} />
      )}
    </View>
  );
});

// Memoized Options Section Component
const OptionsSection = React.memo<{
  title: string;
  options: string[];
}>(({ title, options }) => (
  <View style={styles.optionsSection}>
    {title && <Text style={styles.optionsTitle}>{title}</Text>}
    {Array.isArray(options) &&
      options.map((option, index) =>
        option && typeof option === 'string' ? (
          <OptionItem key={index} option={option} />
        ) : null,
      )}
  </View>
));

// Memoized Option Item Component
const OptionItem = React.memo<{
  option: string;
}>(({ option }) => (
  <View style={styles.optionItem}>
    <View style={styles.optionBullet} />
    <Text style={styles.optionText}>{option}</Text>
  </View>
));

// Memoized Footer Section Component
const FooterSection = React.memo<{
  title: string;
  description: string;
}>(({ title, description }) => (
  <View style={styles.footerSection}>
    <View style={styles.footerContainer}>
      <View style={styles.footerContent}>
        <View style={styles.footerTextContainer}>
          {title && <Text style={styles.footerTitle}>{title}</Text>}
          {description && (
            <Text style={styles.footerDescription}>{description}</Text>
          )}
        </View>
        <Text style={styles.uploadButton}>UPLOAD</Text>
      </View>
    </View>
  </View>
));

DocumentCard.displayName = 'DocumentCard';

const styles = StyleSheet.create({
  documentCard: {
    flex: 1,
    overflow: 'hidden',
    width: '100%',
    borderRadius: 16,
    padding: 3,
    alignSelf: 'center',
  },
  headerSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  headerLeft: {
    fontSize: 12,
    lineHeight: 16,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
  },
  headerRight: {
    fontSize: 12,
    lineHeight: 16,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
  },
  mainContent: {
    width: '100%',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  titleSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  titleIcon: {
    height: 36,
    width: 36,
    marginRight: 8,
    marginTop: 2,
    borderRadius: 7,
  },
  mainTitle: {
    flex: 1,
    fontSize: 16,
    lineHeight: 20,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
  },
  subTitle: {
    fontSize: 12,
    lineHeight: 16,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
    marginBottom: 4,
  },
  descriptionSection: {
    marginBottom: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderColor: COLORS.GREY_VAR_5,
  },
  description: {
    fontSize: 12,
    lineHeight: 18,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
    marginBottom: 4,
  },
  dateText: {
    fontSize: 12,
    lineHeight: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_LOW_EMPHASIS,
  },
  optionsSection: {
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderColor: COLORS.GREY_VAR_5,
  },
  optionsTitle: {
    fontSize: 12,
    lineHeight: 16,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
    marginBottom: 8,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  optionBullet: {
    height: 6,
    width: 6,
    borderRadius: 3,
    backgroundColor: COLORS.GREY_VAR_5,
    marginRight: 8,
  },
  optionText: {
    fontSize: 12,
    lineHeight: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.BLACK,
  },
  footerSection: {
    width: '100%',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  footerContainer: {
    backgroundColor: COLORS.FOREX_FOOTER_BG,
    marginHorizontal: 10,
    marginBottom: 12,
    marginTop: 2,
    borderRadius: 8,
  },
  footerContent: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  footerTextContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  footerTitle: {
    fontSize: 14,
    lineHeight: 16,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.FOREX_FOOTER_TEXT,
    marginBottom: 4,
  },
  footerDescription: {
    fontSize: 12,
    lineHeight: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.BLACK,
  },
  uploadButton: {
    fontSize: 14,
    lineHeight: 16,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: '#2196F3',
    marginLeft: 16,
  },
});
