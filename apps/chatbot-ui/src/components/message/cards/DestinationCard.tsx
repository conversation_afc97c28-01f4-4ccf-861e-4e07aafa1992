import React, { FC, useCallback, useEffect, useRef, useState } from 'react';
import { Image, NativeModules, StyleSheet, Text, View } from 'react-native';
import { COLORS, FONTS, Z_INDEX } from '../../../constants/globalStyles';
import {
  BookmarkIconImageCards,
  BookmarkIconImageCardsFilled,
} from '../../../assets/BookmarkIconImageCards';
import { TouchableFeedback } from '../../base/TouchableFeeback';
import { useBookmarkCard } from '../../../store/messages';
import { ImageView } from '../../image/ImageView';
import { LinearGradient } from 'react-native-linear-gradient';
import { ShadowedView } from 'react-native-fast-shadow';
import { CARDS_WIDTH_MAP } from '../../../const';
import { MarkdownText } from '../MarkdownText';

type DestinationCardProps = DetailedCardType & {
  msg: Message;
  shouldApplyGradient?: boolean;
  onCardClick: (data: DetailedCardType) => void;
};
const FETCH_DOMINANT_COLOR = true;
const colorPriorityOrder = [
  'background',
  'muted',
  'secondary',
  'detail',
  'primary',
  'lightMuted',
  'dominant',
  'vibrant',
  'lightVibrant',
];
const defaultGradient = [
  '#F2F2F200',
  '#F2F2F200',
  '#F2F2F2f0',
  '#F2F2F2ff',
  '#F2F2F2ff',
];

export const DestinationCard: FC<DestinationCardProps> = React.memo((props) => {
  const isDominantColorFound = useRef(false);
  const {
    data: {
      title,
      sub_title,
      sub_title_icon,
      image_url: image,
      info,
      description: description,
      cta_title,
      cta_link,
      currency_rate,
      currency_tag,
      description_icon,
      lob = 'destination',
    },
    onCardClick: onCardClickProp,
    shouldApplyGradient,
  } = props;
  const [colors, setColors] = useState<string[]>(defaultGradient);
  const onCardClick = useCallback(() => {
    onCardClickProp(props);
  }, [onCardClickProp, props]);
  const { onBookmark, isBookmarked } = useBookmarkCard(props, lob);
  useEffect(() => {
    if (shouldApplyGradient === false) {
      return;
    }
    const { ImageUtilModule } = NativeModules;
    if (typeof ImageUtilModule?.getColorPalette !== 'function') {
      return;
    }
    (async () => {
      try {
        const swatches = await ImageUtilModule.getColorPalette(image, 100, 100);
        if (swatches) {
          isDominantColorFound.current = true;
          const parsedSwatches =
            typeof swatches === 'string' ? JSON.parse(swatches) : swatches;
          const color =
            colorPriorityOrder.reduce<string[] | null>((acc, key) => {
              const selectedColor = parsedSwatches[key];
              if (acc || !selectedColor) {
                return acc;
              }
              return [
                selectedColor + '00',
                selectedColor + '80',
                selectedColor + 'ff',
                selectedColor + 'ff',
                selectedColor + 'ff',
              ];
            }, null) || defaultGradient;
          setColors(color);
        }
      } catch (e) {
        setColors(defaultGradient);
      }
    })();
  }, []);

  const hasCurrency = !!currency_rate || !!currency_tag;
  const hasDescription = !!description;

  return (
    <TouchableFeedback
      onPress={onCardClick}
      style={{ flex: 1 }}
      containerStyle={styles.destinationCard}
    >
      <ShadowedView
        style={{
          shadowColor: COLORS.GREY_VAR_1,
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.18,
          shadowRadius: 5,
          borderRadius: 24,
          alignItems: 'center',
          justifyContent: 'center',
          width: CARD_WIDTH,
        }}
      >
        {/* <View style={styles.borderView} /> */}
        <TouchableFeedback
          onPress={onBookmark}
          containerStyle={styles.bookmarkContainer}
        >
          {isBookmarked && <BookmarkIconImageCardsFilled height={16} width={16} />}
          {!isBookmarked && <BookmarkIconImageCards height={16} width={16} />}
        </TouchableFeedback>
        <View style={styles.imageContainer}>
          <ImageView
            style={styles.image}
            containerStyle={styles.image}
            source={{ uri: image }}
          />
        </View>
        <LinearGradient
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          angle={90}
          angleCenter={{ x: 0, y: 0.5 }}
          colors={[...colors]}
          style={{
            height: 180,
            marginTop: -80,
            width: CARD_WIDTH + 2,
            borderBottomRightRadius: 24,
            borderBottomLeftRadius: 24,
            borderWidth: 1,
            borderTopWidth: 0,
            borderColor: COLORS.WHITE,
            backgroundColor: COLORS.TRANSPARENT,
          }}
        />
        <View style={styles.cardBody}>
          <View style={styles.titleSection}>
            <Text numberOfLines={1} style={styles.title}>
              {title}
            </Text>
            {!!info && (
              <View style={styles.rating}>
                <Text style={styles.ratingCount}>{info.text}</Text>
                {info.sub_text && (
                  <Text style={styles.ratingText}>{`(${info.sub_text})`}</Text>
                )}
              </View>
            )}
          </View>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: !hasCurrency && !hasDescription ? 13 : 0,
            }}
          >
            {sub_title_icon && (
              <Image style={styles.subtitleIcon} source={{ uri: sub_title_icon }} />
            )}
            <Text numberOfLines={1} style={styles.subtitle}>
              {sub_title}
            </Text>
          </View>
          {hasCurrency ? (
            <View style={styles.currencyContainer}>
              {!!currency_rate && (
                <Text style={styles.currencyRate}>{currency_rate}</Text>
              )}
              {!!currency_tag && (
                <LinearGradient
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  colors={[COLORS.GREEN_VAR_2, COLORS.GREEN_VAR_3]}
                  style={styles.currencyTagContainer}
                >
                  <Text style={styles.currencyTag}>{currency_tag}</Text>
                </LinearGradient>
              )}
            </View>
          ) : null}
          {!!description && (
            <View style={styles.descriptionContainer}>
              {!!description_icon && (
                <Image
                  style={styles.descriptionIcon}
                  source={{ uri: description_icon }}
                />
              )}
              <MarkdownText
                text={description}
                fontSize={12}
                lineHeight={14}
                size="small"
                adjustHeight
                marginBottom={0}
                marginTop={0}
                numberOfLines={2}
              />
            </View>
          )}
          {cta_title && <Text style={styles.link}>{cta_title}</Text>}
        </View>
      </ShadowedView>
    </TouchableFeedback>
  );
});

const CARD_WIDTH = CARDS_WIDTH_MAP['detailed-card'];
const CARD_IMG_HEIGHT = 173;
const styles = StyleSheet.create({
  destinationCard: {
    flex: 1,
    overflow: 'hidden',
    width: CARD_WIDTH,
    borderRadius: 24,
    borderWidth: 1,
    borderColor: COLORS.WHITE,
  },
  cardBody: {
    flex: 1,
    padding: 12,
    position: 'absolute',
    bottom: 10,
    left: 10,
    right: 10,
    backgroundColor: 'rgb(255,255,255)',
    borderRadius: 15,
  },
  bookmarkContainer: {
    position: 'absolute',
    height: 24,
    width: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.4)',
    right: 10,
    top: 10,
    zIndex: Z_INDEX.OVERLAP_1,
  },
  image: {
    width: CARD_WIDTH,
    height: CARD_IMG_HEIGHT,
    backgroundColor: COLORS.TEXT_DEFAULT,
    borderTopRightRadius: 24,
    borderTopLeftRadius: 24,
    overflow: 'hidden',
  },
  imageContainer: {
    position: 'relative',
    width: CARD_WIDTH + 2,
    height: CARD_IMG_HEIGHT,
    borderWidth: 1,
    marginBottom: -10,
    borderColor: COLORS.WHITE,
    borderBottomWidth: 0,
    borderTopRightRadius: 24,
    borderTopLeftRadius: 24,
  },
  imageGradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: -1,
    height: 20,
    borderTopEndRadius: 30,
    borderTopStartRadius: 150,
  },
  titleSection: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 0,
    marginBottom: 4,
  },
  title: {
    flex: 1,
    paddingRight: 8,
    fontSize: 14,
    lineHeight: 16,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
  },
  rating: {
    display: 'flex',
    flexDirection: 'row',
    alignContent: 'center',
  },
  ratingCount: {
    fontSize: 12,
    lineHeight: 14,
    marginLeft: 2,
    color: '#0C58B4',
    fontFamily: FONTS.FONT_FAMILY_900,
  },
  ratingText: {
    fontSize: 12,
    lineHeight: 14,
    marginLeft: 4,
    color: COLORS.TEXT_LOW_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_400,
  },
  subtitleIcon: {
    height: 12,
    width: 12,
    marginRight: 2,
    position: 'relative',
    right: 1,
  },
  subtitle: {
    fontSize: 12,
    lineHeight: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.GREY_VAR_1,
  },
  starIcon: {
    height: 10,
    width: 10,
    marginTop: 2,
  },
  text: {
    fontSize: 12,
    lineHeight: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.BLACK,
  },
  link: {
    color: COLORS.BLUE_VAR_1,
    fontSize: 12,
    lineHeight: 14,
    fontFamily: FONTS.FONT_FAMILY_900,
  },
  currencyContainer: {
    flexDirection: 'row',
    marginTop: 13,
    marginBottom: 2,
    alignItems: 'center',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    gap: 3,
  },
  currencyRate: {
    fontFamily: FONTS.FONT_FAMILY_700,
    fontSize: 13,
    color: COLORS.GREEN_VAR_2,
    maxWidth: '70%',
    overflow: 'hidden',
    height: 14,
  },
  currencyTagContainer: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  currencyTag: {
    fontFamily: FONTS.FONT_FAMILY_700,
    fontSize: 12,
    color: COLORS.WHITE,
    textAlign: 'center',
  },
  descriptionIcon: {
    height: 16,
    width: 16,
    marginRight: 4,
    marginTop: 2,
  },
  descriptionContainer: {
    flex: 1,
    flexDirection: 'row',
    maxHeight: 54,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 4,
  },
});
