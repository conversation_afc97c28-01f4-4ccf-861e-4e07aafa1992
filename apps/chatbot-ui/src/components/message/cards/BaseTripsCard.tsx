import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  useWindowDimensions,
} from 'react-native';
import React, { useCallback } from 'react';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import { ShadowedView } from 'react-native-fast-shadow';
import LinearGradient from 'react-native-linear-gradient';
import FastImageWrapper from '../../image/FastImageWrapper';
import IconHoliday from '../../../assets/IconHoliday';
import IconCabs from '../../../assets/IconCabs';
import IconInsurance from '../../../assets/IconInsurance';
import IconFlights from '../../../assets/IconFlights';
import IconRails from '../../../assets/iconRails';
import IconBus from '../../../assets/IconBus';
import HotelIcon from '../../../assets/HotelIcon';
import { useResponsiveWidth } from '../../../hooks/useResponsiveWidth';

export type IconName = keyof typeof IconMap;

const withIconProps = (IconComponent: React.ComponentType<any>) => (
  <IconComponent width={22} height={22} viewBox="0 0 28 28" />
);

export const IconMap = {
  bus: withIconProps(IconBus),
  train: withIconProps(IconRails),
  flight: withIconProps(HotelIcon),
  cab: withIconProps(IconCabs),
  hotel: <HotelIcon />,
  holiday: withIconProps(IconHoliday),
  insurance: withIconProps(IconInsurance),
};

type BaseTripsCardProps = {
  data: {
    tag?: {
      text: string;
      colors?: string[];
    };
    header?: {
      left_section?: {
        icon?: {
          type: string;
          url?: string;
        };
        title?: string;
        title_color?: string;
        description?: string;
      };
      right_section?: {
        title?: string;
      };
    };
    main?: {
      image_url?: string;
      title?: string;
      sub_title?: string;
      left_section?: {
        title?: string;
        sub_title?: string;
        text?: string;
      };
      right_section?: {
        title?: string;
        sub_title?: string;
        text?: string;
      };
    };
  };
  onCardClick: (data: any) => void;
  renderIcon?: (iconType: string, iconDetails: any) => React.ReactNode;
  renderMainContent?: (main: any) => React.ReactNode;
};

const Card: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { containerWidth } = useResponsiveWidth();
  return (
    <ShadowedView style={[styles.card, { width: containerWidth - 55 }]}>
      {children}
    </ShadowedView>
  );
};

const BaseTripsCard = (props: BaseTripsCardProps) => {
  const {
    data,
    onCardClick: onCardClickProp,
    renderIcon,
    renderMainContent,
  } = props;
  const { tag, header: cardHeader, main: cardMain } = data;

  const onCardClick = useCallback(() => {
    onCardClickProp(props);
  }, [onCardClickProp, props]);

  const headerIconDetails = cardHeader?.left_section?.icon;
  const headerIconType = headerIconDetails?.type?.toLowerCase();

  return (
    <Card>
      <TouchableOpacity onPress={onCardClick} style={styles.touchableContainer}>
        {tag && tag.text && tag.colors && (
          <View style={styles.tagSectionContainer}>
            <View style={styles.tagBadge}>
              <LinearGradient
                colors={tag.colors}
                useAngle
                angle={0}
                angleCenter={{ x: 0.5, y: 0.1 }}
                style={styles.tagGradient}
              />
              <Text style={styles.tagText}>{tag.text}</Text>
            </View>
          </View>
        )}

        <View style={styles.cardContentContainer}>
          {cardHeader && (
            <View style={styles.tripHeaderContainer}>
              <View style={styles.tripHeaderLeftSection}>
                {headerIconType && renderIcon && (
                  <View style={styles.tripModeIconWrapper}>
                    {headerIconType === 'url' && headerIconDetails?.url ? (
                      <FastImageWrapper
                        source={{ uri: headerIconDetails.url }}
                        style={styles.urlIcon}
                        resizeMode={FastImageWrapper.resizeMode.contain}
                      />
                    ) : (
                      renderIcon(headerIconType, headerIconDetails)
                    )}
                  </View>
                )}
                <View>
                  {cardHeader.left_section?.title && (
                    <Text
                      style={[
                        styles.tripHeaderTitle,
                        cardHeader.left_section.title_color
                          ? { color: cardHeader.left_section.title_color }
                          : {},
                      ]}
                    >
                      {cardHeader.left_section.title}
                    </Text>
                  )}
                  {cardHeader.left_section?.description && (
                    <Text style={styles.tripHeaderDescription}>
                      {cardHeader.left_section.description}
                    </Text>
                  )}
                </View>
              </View>
              {cardHeader.right_section?.title && (
                <Text style={styles.tripHeaderRightText}>
                  {cardHeader.right_section.title}
                </Text>
              )}
            </View>
          )}

          {cardMain && renderMainContent && renderMainContent(cardMain)}
        </View>
      </TouchableOpacity>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 24,
    shadowColor: 'rgba(0, 0, 0, 0.70)',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    backgroundColor: COLORS.GREY_VAR_8,
    flex: 1,
    marginBottom: 3,
  },
  touchableContainer: {
    flex: 1,
    width: '100%',
  },
  tagSectionContainer: {},
  tagBadge: {
    width: 77,
    height: 22,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    overflow: 'hidden',
  },
  tagGradient: {
    ...StyleSheet.absoluteFillObject,
    height: 22,
    borderTopLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  tagText: {
    color: COLORS.WHITE,
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_700,
    zIndex: 1,
  },
  cardContentContainer: {
    marginHorizontal: 10,
    marginVertical: 15,
  },
  tripHeaderContainer: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 10,
  },
  tripHeaderLeftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
  },
  tripModeIconWrapper: {
    backgroundColor: COLORS.BLUE_VAR_13,
    borderRadius: 4,
    marginRight: 8,
    padding: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  urlIcon: {
    width: 24,
    height: 24,
  },
  tripHeaderTitle: {
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 14,
    color: COLORS.BLACK,
  },
  tripHeaderDescription: {
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 12,
    color: COLORS.GREY_VAR_2,
    marginTop: 2,
  },
  tripHeaderRightText: {
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 14,
    color: COLORS.GREY_VAR_2,
    textAlign: 'right',
    alignSelf: 'center',
  },
});

export default BaseTripsCard;
