import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import IconBus from '../../../assets/IconBus';
import IconRails from '../../../assets/iconRails';
import IconFlights from '../../../assets/IconFlights';
import IconCabs from '../../../assets/IconCabs';
import HotelIcon from '../../../assets/HotelIcon';
import IconHoliday from '../../../assets/IconHoliday';
import IconInsurance from '../../../assets/IconInsurance';
import FastImageWrapper from '../../image/FastImageWrapper';
import BaseTripsCard, { IconMap, IconName } from './BaseTripsCard';

type MyTripsCardProps = MyTripsCardType & {
  msg: Message;
  onCardClick: (data: MyTripsCardType) => void;
};

const MyTripsCard = (props: MyTripsCardProps) => {
  const renderIcon = (iconType: string) => {
    return IconMap[iconType as IconName] || null;
  };

  const renderMainContent = (main: any) => {
    return (
      <>
        {(main.image_url || main.title || main.sub_title) && (
          <View style={styles.tripMainInfoContainer}>
            {main.image_url && (
              <FastImageWrapper
                source={{
                  uri: main.image_url,
                  priority: FastImageWrapper.priority.normal,
                }}
                style={styles.tripMainImage}
                resizeMode={FastImageWrapper.resizeMode.cover}
              />
            )}
            <View style={styles.tripMainTextContainer}>
              {main.title && (
                <Text style={styles.tripMainTitleText}>{main.title}</Text>
              )}
              {main.sub_title && (
                <Text style={styles.tripMainSubtitleText}>{main.sub_title}</Text>
              )}
            </View>
          </View>
        )}

        {(main.left_section || main.right_section) && (
          <View style={styles.tripDetailsRowContainer}>
            {main.left_section && (
              <View style={styles.tripDetailColumn}>
                {main.left_section.title && (
                  <Text style={styles.tripDetailColumnTitle}>
                    {main.left_section.title}
                  </Text>
                )}

                {main.left_section.sub_title && (
                  <Text style={styles.tripDetailColumnSubtitle}>
                    {main.left_section.sub_title}
                  </Text>
                )}
              </View>
            )}

            {main.right_section && (
              <View style={styles.tripDetailColumn}>
                {main.right_section.title && (
                  <Text style={styles.tripDetailColumnTitle}>
                    {main.right_section.title}
                  </Text>
                )}

                {main.right_section.sub_title && (
                  <Text style={styles.tripDetailColumnSubtitle}>
                    {main.right_section.sub_title}
                  </Text>
                )}
              </View>
            )}
          </View>
        )}
      </>
    );
  };

  return (
    <BaseTripsCard
      {...props}
      renderIcon={renderIcon}
      renderMainContent={renderMainContent}
    />
  );
};

const styles = StyleSheet.create({
  tripMainInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
    marginBottom: 10,
  },
  tripMainImage: {
    width: 36,
    height: 36,
    marginRight: 8,
    borderRadius: 4,
  },
  tripMainTextContainer: {
    flex: 1,
  },
  tripMainTitleText: {
    fontFamily: FONTS.FONT_FAMILY_900,
    fontSize: 14,
    color: COLORS.BLACK,
  },
  tripMainSubtitleText: {
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 12,
    color: COLORS.GREY_VAR_2,
    marginTop: 2,
  },
  tripDetailsRowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginTop: 8,
  },
  tripDetailColumn: {},
  tripDetailColumnTitle: {
    color: COLORS.BLACK,
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_900,
  },
  tripDetailColumnSubtitle: {
    color: COLORS.GREY_VAR_2,
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_700,
    marginTop: 2,
  },
});

export default MyTripsCard;
