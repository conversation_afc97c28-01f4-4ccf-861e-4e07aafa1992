import { View, Text, StyleSheet } from 'react-native';
import React, { useEffect, memo, useMemo } from 'react';
import LinearGradient from 'react-native-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import CheckCircleIcon from '../../../assets/CheckCircle';
import { isPlatformWeb } from '../../../utils/getPlatform';
import { useResponsiveWidth } from '../../../hooks/useResponsiveWidth';
import { trackOmnitureClickEvent } from '../../../native/omniture';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../../native/tracking/pdt';

// Custom colors for the component
const PROGRESS_COLORS = {
  FILL_BG: 'rgba(169, 202, 255, 0.3)',
  TRANSPARENT: 'transparent',
  PROGRESS_BG: '#008CFF',
  SPINNER_BORDER: 'rgba(0, 140, 255, 0.2)',
  SHIMMER_TRANSPARENT: 'transparent',
  SHIMMER_LIGHT: 'rgba(255, 255, 255, 0.4)',
  SHIMMER_BRIGHT: 'rgba(255, 255, 255, 0.6)',
};

// Web Loading Indicator with concentric circles
const WebLoadingIndicator: React.FC = memo(() => {
  const pulseValue = useSharedValue(0);

  const pulseStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: 0.8 + pulseValue.value * 0.4 }], // Scale between 0.8 and 1.2
    };
  }, []);

  useEffect(() => {
    pulseValue.value = withRepeat(
      withTiming(1, {
        duration: 1200,
        easing: Easing.inOut(Easing.ease),
      }),
      -1, // Infinite repetition
      true, // Reverse animation
    );
  }, [pulseValue]);

  return (
    <View style={styles.webLoadingContainer}>
      <View style={styles.outerCircle}>
        <Animated.View style={[styles.innerCircle, pulseStyle]} />
      </View>
    </View>
  );
});

WebLoadingIndicator.displayName = 'WebLoadingIndicator';

// Mobile Loading Spinner for React Native
const MobileLoadingSpinner: React.FC = memo(() => {
  const rotation = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => {
    'worklet';
    return {
      transform: [{ rotate: `${rotation.value}deg` }],
    };
  }, []);

  useEffect(() => {
    rotation.value = withRepeat(
      withTiming(360, {
        duration: 1000,
        easing: Easing.linear,
      }),
      -1, // Infinite repetition
      false, // Don't reverse
    );
  }, [rotation]);

  return (
    <View style={styles.loadingSpinnerContainer}>
      <Animated.View style={[styles.loadingSpinner, animatedStyle]}>
        <View style={styles.spinnerRing} />
      </Animated.View>
    </View>
  );
});

MobileLoadingSpinner.displayName = 'MobileLoadingSpinner';

// Memoized Status Icon Components
interface StatusIconProps {
  status: 'completed' | 'pending';
}

const StatusIcon: React.FC<StatusIconProps> = memo(({ status }) => {
  const renderIcon = () => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon />;
      case 'pending':
        return isPlatformWeb() ? <WebLoadingIndicator /> : <MobileLoadingSpinner />;
      default:
        return isPlatformWeb() ? <WebLoadingIndicator /> : <MobileLoadingSpinner />;
    }
  };

  return <View style={styles.iconContainer}>{renderIcon()}</View>;
});

StatusIcon.displayName = 'StatusIcon';

// Memoized Progress Bar Component with Animation
interface ProgressBarProps {
  percentage: number;
  showPercentage: boolean;
}

const ProgressBar: React.FC<ProgressBarProps> = memo(
  ({ percentage, showPercentage }) => {
    const shimmerValue = useSharedValue(-50);
    const isWeb = isPlatformWeb();

    // Validate percentage value
    const validPercentage = useMemo(
      () =>
        typeof percentage === 'number' && !isNaN(percentage)
          ? Math.max(0, Math.min(100, percentage))
          : 0,
      [percentage],
    );

    const shimmerStyle = useAnimatedStyle(() => {
      return {
        transform: [{ translateX: shimmerValue.value }],
      };
    }, []);

    useEffect(() => {
      if (!isWeb) {
        shimmerValue.value = withRepeat(
          withTiming(200, {
            duration: 2000,
            easing: Easing.linear,
          }),
          -1, // Infinite repetition
          false, // Don't reverse, restart from left
        );
      }
    }, [shimmerValue, isWeb]);

    return (
      <View style={styles.progressContainer}>
        <View style={styles.progressTrack}>
          <View
            style={[
              styles.progressFillContainer,
              {
                width: `${validPercentage}%`,
              },
            ]}
          >
            <View style={styles.progressFill} />
            {!isWeb && (
              <Animated.View style={[styles.shimmerOverlay, shimmerStyle]}>
                <LinearGradient
                  colors={[
                    PROGRESS_COLORS.SHIMMER_TRANSPARENT,
                    PROGRESS_COLORS.SHIMMER_LIGHT,
                    PROGRESS_COLORS.SHIMMER_BRIGHT,
                    PROGRESS_COLORS.SHIMMER_LIGHT,
                    PROGRESS_COLORS.SHIMMER_TRANSPARENT,
                  ]}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={styles.shimmerGradient}
                />
              </Animated.View>
            )}
          </View>
        </View>
        {showPercentage && (
          <Text style={styles.percentageText}>{validPercentage}%</Text>
        )}
      </View>
    );
  },
);

ProgressBar.displayName = 'ProgressBar';

// Memoized Progress Item Component
interface ProgressItemProps {
  item: ProgressItem;
}

const ProgressItemComponent: React.FC<ProgressItemProps> = memo(({ item }) => {
  const status = item?.status || 'pending';

  const textStyle = useMemo(() => {
    switch (status) {
      case 'completed':
        return styles.completedText;
      case 'pending':
        return styles.pendingText;
      default:
        return styles.pendingText;
    }
  }, [status]);

  // Validate item data - moved after hooks
  if (!item || !item.title) {
    return null;
  }

  return (
    <View style={styles.progressItem}>
      <StatusIcon status={status} />
      <View style={styles.progressItemContent}>
        <View style={styles.progressItemTextContainer}>
          <Text style={[styles.progressItemText, textStyle]}>{item.title}</Text>
          {item.time && <Text style={styles.timeText}>{item.time}</Text>}
        </View>
      </View>
    </View>
  );
});

ProgressItemComponent.displayName = 'ProgressItemComponent';

// Memoized Card Header Component
interface CardHeaderProps {
  title?: string;
  subtitle?: string;
}

const CardHeader: React.FC<CardHeaderProps> = memo(({ title, subtitle }) => {
  if (!title && !subtitle) return null;

  return (
    <View style={styles.header}>
      {title && <Text style={styles.title}>{title}</Text>}
      {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
    </View>
  );
});

CardHeader.displayName = 'CardHeader';

// Memoized Progress Items Container
interface ProgressItemsContainerProps {
  items: ProgressItem[];
}

const ProgressItemsContainer: React.FC<ProgressItemsContainerProps> = memo(
  ({ items }) => {
    if (!items.length) return null;

    return (
      <View style={styles.progressItemsContainer}>
        {items.map((item, index) => (
          <ProgressItemComponent key={item.id || `item-${index}`} item={item} />
        ))}
      </View>
    );
  },
);

ProgressItemsContainer.displayName = 'ProgressItemsContainer';

type ItineraryStatusCardProps = ItineraryStatusCardType & {
  msg: Message;
};

const Card: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { containerWidth } = useResponsiveWidth();
  return (
    <LinearGradient
      colors={['#d9f8ff', '#eef8ff']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={[styles.card, { width: containerWidth - 40 }]}
    >
      {children}
    </LinearGradient>
  );
};

// Main Itinerary Status Card Component
const ItineraryStatusCard = memo((props: { data: CardWidget }) => {
  useEffect(() => {
    // Only track if we have valid data
    if (
      props?.data?.value?.templateInfo?.payload?.length &&
      props.data.value.templateInfo.payload[0]?.data
    ) {
      trackOmnitureClickEvent('ASYNC_ITINERARY_INITIATED');
      trackPDTEvent({
        eventName: eventNameSchema.CHAT_INTERACTED,
        eventType: eventTypeSchema.ACTION,
        eventValue: eventValueSchema.ASYNC_FLOW_TRIGGERED,
      });
    }
  }, [props?.data?.value?.templateInfo?.payload]);

  // Extract data for use in useMemo
  const data = props?.data?.value?.templateInfo?.payload?.[0]?.data as
    | ItineraryProgressCardData
    | undefined;

  // Memoize computations to prevent unnecessary recalculations
  const { sortedItems, progressEnabled, progressPercentage, showPercentage } =
    useMemo(() => {
      // Return empty values if no data
      if (!data?.title && !data?.subtitle && !data?.progress_items?.length) {
        return {
          sortedItems: [],
          progressEnabled: false,
          progressPercentage: 0,
          showPercentage: false,
        };
      }

      // Safe access to progress items with validation
      const progressItems = Array.isArray(data.progress_items)
        ? data.progress_items
        : [];
      const validProgressItems = progressItems.filter(
        (item) =>
          item &&
          typeof item === 'object' &&
          item.title &&
          typeof item.title === 'string',
      );

      // Sort progress items by order, with fallback for missing order
      const sortedItems = validProgressItems.sort((a, b) => {
        const orderA = typeof a.order === 'number' ? a.order : 999;
        const orderB = typeof b.order === 'number' ? b.order : 999;
        return orderA - orderB;
      });

      // Safe access to progress data
      const progress = data?.progress || {};
      const progressEnabled = progress.enabled === true;
      const progressPercentage =
        typeof progress.percentage === 'number' ? progress.percentage : 0;
      const showPercentage = progress.show_percentage === true;

      return { sortedItems, progressEnabled, progressPercentage, showPercentage };
    }, [data]);

  // ✅ CONDITIONAL CHECKS AFTER ALL HOOKS
  if (
    !props?.data?.value?.templateInfo?.payload?.length ||
    !props.data.value.templateInfo.payload[0]?.data
  ) {
    return null;
  }

  // Early return if no valid data
  if (!sortedItems.length && !data?.title && !data?.subtitle) {
    return null;
  }

  return (
    <Card>
      <CardHeader title={data?.title} subtitle={data?.subtitle} />

      {progressEnabled && (
        <ProgressBar
          percentage={progressPercentage}
          showPercentage={showPercentage}
        />
      )}

      <ProgressItemsContainer items={sortedItems as ProgressItem[]} />

      {/* Fallback content if no valid data to display */}
      {!data?.title && !data?.subtitle && sortedItems.length === 0 && (
        <View style={styles.fallbackContainer}>
          <Text style={styles.fallbackText}>No itinerary data available</Text>
        </View>
      )}
    </Card>
  );
});

ItineraryStatusCard.displayName = 'ItineraryStatusCard';

const styles = StyleSheet.create({
  card: {
    // margin: 16,
    marginHorizontal: 20,
    padding: 20,
    borderRadius: 24,
    backgroundColor: COLORS.WHITE,
    marginTop: 10,
    // shadowColor: COLORS.BLACK,
    // shadowOffset: {
    //   width: 0,
    //   height: 2,
    // },
    // shadowOpacity: 0.1,
    // shadowRadius: 8,
    // elevation: 4,
  },
  header: {
    marginBottom: 10,
  },
  title: {
    fontSize: 16,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: COLORS.BLACK,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.GREY_VAR_2,
    lineHeight: 20,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  progressTrack: {
    flex: 1,
    height: 6,
    backgroundColor: PROGRESS_COLORS.FILL_BG,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFillContainer: {
    height: '100%',
    borderRadius: 4,
    overflow: 'hidden',
    position: 'relative',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
    backgroundColor: PROGRESS_COLORS.PROGRESS_BG,
  },
  shimmerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
  },
  shimmerGradient: {
    height: '100%',
    width: '100%',
  },
  percentageText: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
    marginLeft: 12,
    minWidth: 30,
  },
  progressItemsContainer: {
    gap: 10,
  },
  progressItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressItemContent: {
    flex: 1,
  },
  progressItemTextContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    marginRight: 12,
    width: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  webLoadingContainer: {
    width: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  outerCircle: {
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: PROGRESS_COLORS.FILL_BG,
    alignItems: 'center',
    justifyContent: 'center',
  },
  innerCircle: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: PROGRESS_COLORS.PROGRESS_BG,
  },
  loadingSpinnerContainer: {
    width: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingSpinner: {
    width: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  spinnerRing: {
    width: 14,
    height: 14,
    borderRadius: 7,
    borderWidth: 2,
    borderTopColor: PROGRESS_COLORS.FILL_BG,
    borderRightColor: PROGRESS_COLORS.PROGRESS_BG,
    borderBottomColor: PROGRESS_COLORS.PROGRESS_BG,
    borderLeftColor: PROGRESS_COLORS.PROGRESS_BG,
  },
  progressItemText: {
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    flex: 1,
    lineHeight: 20,
  },
  timeText: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.GREY_VAR_1,
    marginLeft: 8,
  },
  completedText: {
    color: COLORS.BLACK,
  },
  pendingText: {
    color: COLORS.GREY_VAR_1,
  },
  fallbackContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fallbackText: {
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.BLACK,
    textAlign: 'center',
  },
});

export default ItineraryStatusCard;
