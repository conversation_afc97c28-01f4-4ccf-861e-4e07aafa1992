import React, { FC, useMemo } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import { TouchableFeedback } from '../../base';
import { ShadowedView } from 'react-native-fast-shadow';
import GradientText from '../../base/GradientText';
import { CARDS_WIDTH_MAP } from '../../../const';
import { defaultDarkColorPalette, MarkdownText } from '../MarkdownText';

type ItineraryCardSource = 'listing' | 'preview';

interface ItineraryOptionCardProps extends ItineraryOptionCardType {
  onCardClick: (data: ItineraryOptionCardType) => void;
  source?: ItineraryCardSource;
  msg: Message;
}

// Constants
const CARD_HEIGHT = {
  listing: 190,
  preview: 142,
} as const;

const GRADIENT_COLORS = ['#C86DD7', '#3023AE'];
const GRADIENT_START = { x: 0, y: 0.5 } as const;
const GRADIENT_END = { x: 0.4, y: 1 } as const;

const CARD_CONFIG = {
  borderRadius: 8,
  previewWidthOffset: 2,
  backgroundTint: '#E6FFF9',
} as const;

const VARIANT_STYLES = {
  listing: {
    padding: { paddingVertical: 16, paddingHorizontal: 12 },
    headerTextContainer: { minHeight: 38 },
    headerText: { lineHeight: 18, fontSize: 16 },
    badge: { fontSize: 12, lineHeight: 14, height: 18 },
    journeyMap: { fontSize: 12, lineHeight: 14, marginTop: 4 },
    inclusion: { marginTop: 16 },
    inclusionTitle: {
      fontSize: 12,
      lineHeight: 14,
      fontFamily: FONTS.FONT_FAMILY_900,
      color: COLORS.BLACK,
    },
    inclusionText: { fontSize: 12, lineHeight: 14, marginTop: 4 },
    callout: { fontSize: 12, lineHeight: 14, paddingVertical: 8 },
  },
  preview: {
    padding: { paddingVertical: 12, paddingHorizontal: 8 },
    headerTextContainer: {},
    headerText: { lineHeight: 16, fontSize: 14 },
    badge: { fontSize: 10, lineHeight: 12, height: 16 },
    journeyMap: { fontSize: 12, lineHeight: 14 },
    inclusion: { marginTop: 8 },
    inclusionTitle: {
      fontSize: 10,
      lineHeight: 12,
      fontFamily: FONTS.FONT_FAMILY_900,
      color: COLORS.BLACK,
    },
    inclusionText: { fontSize: 10, lineHeight: 12, marginTop: 2 },
    callout: { fontSize: 10, lineHeight: 12, paddingVertical: 5 },
  },
} as const;

// Helper
const getInclusionText = (activities?: { title?: string; items?: string[] }) => {
  if (!activities?.title || !activities?.items) return '';
  const count = activities.items.length;
  return activities.title.replace(
    '(##NO_OF_ITEMS## items)',
    `(${count} item${count !== 1 ? 's' : ''})`,
  );
};

const ItineraryOptionCardComponent: FC<ItineraryOptionCardProps> = ({
  data,
  onCardClick,
  source = 'listing',
  msg,
}) => {
  const {
    title,
    subTitle,
    callout,
    activities,
    itineraryDays,
    id,
    card_name,
    cta,
    quote_metadata,
  } = data;

  const isPreview = source === 'preview';
  const variant = VARIANT_STYLES[source];
  const inclusionText = useMemo(() => getInclusionText(activities), [activities]);
  const hasActivities = activities?.items?.length;

  const cardStyle = useMemo(
    () => ({
      width:
        CARDS_WIDTH_MAP['itinerary-card'] -
        (isPreview ? CARD_CONFIG.previewWidthOffset : 0),
      ...(!isPreview && { height: CARD_HEIGHT[source] }),
      borderRadius: isPreview ? CARD_CONFIG.borderRadius : 16,
    }),
    [isPreview, source],
  );

  const parentBoxStyle = useMemo(
    () => [styles.parentBox, isPreview && { height: undefined }],
    [isPreview],
  );

  return (
    <ShadowedView style={!isPreview ? styles.shadow : undefined}>
      <TouchableFeedback
        onPress={() =>
          onCardClick({
            data: {
              ...data,
              msgId: msg.id,
            },
          })
        }
        style={[styles.root, cardStyle]}
      >
        <View style={parentBoxStyle}>
          <View style={[styles.childBox, variant.padding]}>
            <View style={styles.header}>
              <View style={[styles.headerText, variant.headerTextContainer]}>
                <MarkdownText
                  text={title}
                  size="default"
                  color={defaultDarkColorPalette}
                  lineHeight={variant.headerText.lineHeight}
                  adjustHeight
                  fontSize={variant.headerText.fontSize}
                  marginTop={0}
                  marginBottom={0}
                  numberOfLines={2}
                />
              </View>
              <Text style={[styles.headerBadge, variant.badge]}>
                {itineraryDays}
              </Text>
            </View>

            <Text numberOfLines={1} style={[styles.journeyMap, variant.journeyMap]}>
              {subTitle}
            </Text>

            {hasActivities && (
              <View style={[styles.inclusion, variant.inclusion]}>
                <GradientText
                  gradientColors={GRADIENT_COLORS}
                  style={variant.inclusionTitle}
                  start={GRADIENT_START}
                  end={GRADIENT_END}
                >
                  {inclusionText}
                </GradientText>
                <Text
                  numberOfLines={1}
                  style={[styles.inclusionText, variant.inclusionText]}
                >
                  {activities.items.join(', ')}
                </Text>
              </View>
            )}
          </View>

          {callout?.text && source === 'listing' && (
            <Text style={[styles.matchingText, variant.callout]}>
              {callout?.text}
            </Text>
          )}
        </View>
      </TouchableFeedback>
    </ShadowedView>
  );
};

export const ItineraryOptionCard = React.memo(ItineraryOptionCardComponent);
ItineraryOptionCard.displayName = 'ItineraryOptionCard';

const styles = StyleSheet.create({
  shadow: {
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    marginBottom: 16,
    overflow: 'hidden',
    padding: 2,
    borderRadius: 16,
  },
  root: {
    overflow: 'hidden',
    alignContent: 'center',
  },
  parentBox: {
    backgroundColor: CARD_CONFIG.backgroundTint,
    height: '100%',
  },
  childBox: {
    width: '100%',
    backgroundColor: COLORS.WHITE,
    borderBottomEndRadius: 16,
    borderBottomStartRadius: 16,
  },
  header: {
    flexDirection: 'row',
    flexWrap: 'nowrap',
  },
  headerText: {
    flexDirection: 'row',
    paddingRight: 16,
    flexWrap: 'wrap',
    width: 184,
  },

  headerBadge: {
    paddingVertical: 2,
    paddingHorizontal: 4,
    backgroundColor: '#0C58B4',
    borderRadius: 4,
    color: COLORS.WHITE,
    marginLeft: 'auto',
  },
  journeyMap: {
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
  },
  inclusion: {
    borderColor: COLORS.GREY_VAR_5,
    padding: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  inclusionText: {
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_LOW_EMPHASIS,
  },
  matchingText: {
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.GREEN_VAR_2,
    textAlign: 'center',
    borderBottomEndRadius: 16,
    borderBottomStartRadius: 16,
  },
});
