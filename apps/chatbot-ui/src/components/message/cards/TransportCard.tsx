import React, { FC, useCallback } from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import { COLORS, FONTS, Z_INDEX } from '../../../constants/globalStyles';
import {
  BookmarkIconNonImageCards,
  BookmarkIconNonImageCardsFilled,
} from '../../../assets/BookMarkIconNonImageCards';
import { useBookmarkCard } from '../../../store/messages';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { TouchableFeedback } from '../../base/TouchableFeeback';
import { ShadowedView } from 'react-native-fast-shadow';
import { CARDS_WIDTH_MAP } from '../../../const';

type TransportCardProps = TransportCardType & {
  msg: Message;
  onCardClick: (data: TransportCardType) => void;
};

export const TransportCard: FC<TransportCardProps> = (props) => {
  const {
    data: {
      image_url,
      title,
      sub_title,
      info_text,
      description,
      fare_info_text,
      fare_amount_text,
      cta_link,
      card_cta_link,
      lob = 'transport',
    },
    onCardClick: onCardClickProp,
  } = props;
  const { onBookmark, isBookmarked } = useBookmarkCard(props, lob);
  const onCardClick = useCallback(() => {
    onCardClickProp(props);
  }, [onCardClickProp, props]);
  return (
    <View style={{ flex: 1, padding: 3, overflow: 'hidden' }}>
      <ShadowedView style={styles.transportCard}>
        <TouchableFeedback
          onPress={onCardClick}
          containerStyle={styles.transportCardTouchable}
        >
          <TouchableOpacity
            activeOpacity={1}
            onPress={onBookmark}
            containerStyle={styles.bookmarkContainer}
          >
            {isBookmarked && (
              <BookmarkIconNonImageCardsFilled height={16} width={16} />
            )}
            {!isBookmarked && <BookmarkIconNonImageCards height={16} width={16} />}
          </TouchableOpacity>
          <View style={styles.topContainer}>
            <Image style={styles?.image} source={{ uri: image_url }} />
            <View style={{ flex: 1, alignItems: 'stretch' }}>
              <Text style={styles.primartText} numberOfLines={2}>
                {title}
              </Text>
              <Text style={styles.secondaryText} numberOfLines={2}>
                {sub_title?.trim?.()}
              </Text>
              <Text style={styles.tertiaryText}>{description || info_text}</Text>
            </View>
          </View>
          <View style={styles.footerContainer}>
            <Text style={styles.footerText}>
              {fare_info_text}
              <Text
                style={styles.price}
              >{`  ${fare_amount_text?.replace(',,', ',')}`}</Text>
            </Text>
          </View>
        </TouchableFeedback>
      </ShadowedView>
    </View>
  );
};

const styles = StyleSheet.create({
  transportCard: {
    backgroundColor: COLORS.GREY_VAR_8,
    width: CARDS_WIDTH_MAP['transport-card'],
    borderRadius: 20,
    shadowColor: 'rgba(0, 0, 0, 0.60)',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.15,
    shadowRadius: 5,
    flex: 1,
  },
  transportCardTouchable: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  bookmarkContainer: {
    position: 'absolute',
    height: 28,
    width: 28,
    borderRadius: 14,
    borderColor: 'rgba(0, 0, 0, 0.07)',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.GREY_VAR_9,
    right: 10,
    top: 10,
    zIndex: Z_INDEX.OVERLAP_1,
  },
  topContainer: {
    flex: 1,
    alignSelf: 'stretch',
    display: 'flex',
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    alignItems: 'flex-start',
  },
  image: {
    width: 30,
    height: 30,
    borderRadius: 4,
  },
  primartText: {
    fontSize: 16,
    lineHeight: 18,
    marginRight: 32,
    color: COLORS.BLACK,
    fontFamily: FONTS.FONT_FAMILY_900,
  },
  secondaryText: {
    fontSize: 14,
    lineHeight: 16,
    marginTop: 4,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.BLACK,
  },
  tertiaryText: {
    fontSize: 12,
    lineHeight: 16,
    marginTop: 8,
    color: COLORS.TEXT_LOW_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_400,
  },
  footerContainer: {
    alignItems: 'center',
    borderColor: COLORS.BLACK_VAR_3,
    borderTopWidth: 1,
    padding: 16,
  },
  footerText: {
    color: COLORS.BLACK,
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 14,
    lineHeight: 16,
  },
  price: {
    color: COLORS.BLUE_VAR_1,
    fontFamily: FONTS.FONT_FAMILY_700,
    fontSize: 14,
    lineHeight: 16,
    marginLeft: 8,
  },
});
