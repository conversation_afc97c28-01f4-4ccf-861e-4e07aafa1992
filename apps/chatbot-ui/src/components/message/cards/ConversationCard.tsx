import React, { FC, useState, useCallback, useRef } from 'react';
import { StyleSheet, Text, TouchableOpacity, View, Dimensions } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import FlightIcon from '../../../assets/FlightIcon';
import HotelIcon from '../../../assets/HotelIcon';
import { SvgProps } from 'react-native-svg';
import { ImageView } from '../../image/ImageView';
import LocationIcon from '../../../assets/location';
import { PopoverMenu, getElementPosition } from '../../PopoverMenu';
import { Toast } from '../../toast';
import { DeleteConversationModal } from '../../DeleteConversationModal';
import { invalidateConversationsHistoryQuery } from '../../../network/hooks/use-conversations-history-query';
import { getPlatform } from '../../../utils/getPlatform';
import { deletePopupConfig } from '../../../network/api/conversationApi';
import {
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../../native/tracking/pdt/pdtTypes';
import { trackPDTEvent } from '../../../native/tracking/pdt';
import { refetchChatViewData } from '../../../store/messages/newChatView';

type ConversationCardProps = {
  compact: boolean;
  conversation: ConversationHistoryItem;
  popupConfigs?: { delete: deletePopupConfig } | null | undefined;
  handleConversationClick: (conversationId: string) => void;
  contextMenuDataMap?: Record<string, contextMenu[]>;
};

type LobCards = 'Flights' | 'Hotels' | 'Itinerary';

const lobIconMap: Record<LobCards, React.FC<SvgProps>> = {
  Flights: FlightIcon,
  Hotels: HotelIcon,
  Itinerary: LocationIcon,
};

const containerWidth = Dimensions.get('window').width;
const ConversationCard: FC<ConversationCardProps> = ({
  compact,
  conversation,
  handleConversationClick,
  popupConfigs,
  contextMenuDataMap,
}) => {
  const rootRef = useRef<View>(null);
  const [showMenuOverlayData, setShowMenuOverlayData] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
  } | null>(null);
  const [isDeleteChatModalOpen, setIsDeleteChatModalOpen] = useState(false);
  const platform = getPlatform();
  const contextMenuData = contextMenuDataMap?.[conversation.contextMenuId || ''];
  const handleConversationLongPress = useCallback(() => {
    if (!contextMenuData) {
      return;
    }
    if (platform !== 'android' && platform !== 'ios' && containerWidth > 860) {
      Toast.show('Conversation menu is not available on full screen view');
      return;
    }
    getElementPosition(rootRef)
      .then((data) => {
        if (!data || !data.width || !data.height || !data.x || !data.y) {
          Toast.show('Something went wrong');
          return;
        }
        trackPDTEvent({
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: eventValueSchema.CONVERSATION_LONG_PRESS,
          conversationId: conversation.conversationId,
        });
        setShowMenuOverlayData(data);
        return;
      })
      .catch((error) => {
        console.error('Error getting element position', error);
        Toast.show('Something went wrong');
      });
  }, [platform, contextMenuData, conversation.conversationId]);
  const handleDeleteChat = () => {
    invalidateConversationsHistoryQuery();
    refetchChatViewData({ body: { requestReason: 'deleteConversation' } }, false);
  };
  const handleOpenChatMenuPressed = () => {
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: 'conversation_context_menu_open_clicked',
      conversationId: conversation.conversationId,
    });
    // setShowMenuOverlayData(null);
    setTimeout(() => {
      handleConversationClick(conversation.conversationId);
    }, 0);
  };
  const handleDeleteChatMenuPressed = () => {
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: 'conversation_context_menu_delete_clicked',
      conversationId: conversation.conversationId,
    });
    setShowMenuOverlayData(null);
    setIsDeleteChatModalOpen(true);
  };
  const handleContextMenuAction = (action: string) => {
    switch (action) {
      case 'delete':
        handleDeleteChatMenuPressed();
        break;
      case 'open':
        handleOpenChatMenuPressed();
        break;
      default:
        Toast.show('Something went wrong');
        break;
    }
  };
  const contextMenuDataWithHandler = contextMenuData?.map((item) => ({
    ...item,
    onPress: handleContextMenuAction,
  }));
  return (
    <>
      <View>
        <TouchableOpacity
          activeOpacity={0.5}
          onPress={() => {
            handleConversationClick(conversation.conversationId);
          }}
          onLongPress={handleConversationLongPress}
          style={styles.conversationContainer}
          key={conversation.conversationId}
          ref={rootRef as React.Ref<TouchableOpacity>}
        >
          <View style={{ flexDirection: 'row' }}>
            <View style={styles.cardLeftSection}>
              {conversation.icon && (
                <View style={styles.imgContainer}>
                  <ImageView
                    source={{
                      uri: conversation.icon,
                    }}
                    style={{ height: 32, width: 32 }}
                    containerStyle={{ width: 32, height: 32, borderRadius: 50 }}
                    showMmtPlaceholder={false}
                  />
                  {conversation.subIcon &&
                    lobIconMap?.[conversation.subIcon as LobCards] && (
                      <View style={styles.lobImgContainer}>
                        {React.createElement(
                          lobIconMap[conversation.subIcon as LobCards],
                          {
                            height: 13,
                            width: 13,
                          },
                        )}
                      </View>
                    )}
                  {conversation.unRead && (
                    <View style={styles.unreadCountBadge}></View>
                  )}
                </View>
              )}
              <View style={styles.textSection}>
                <Text
                  numberOfLines={1}
                  ellipsizeMode={'tail'}
                  style={styles.cardTitle}
                >
                  {conversation.title}
                </Text>
                <Text
                  numberOfLines={1}
                  ellipsizeMode={'tail'}
                  style={styles.cardMsg}
                >
                  {conversation.message}
                </Text>
              </View>
            </View>
            <View>
              <Text style={styles.cardTimestamp}>{conversation.timestamp}</Text>
              {conversation?.unReadCount && conversation.unReadCount > 0 && (
                <View style={styles.unreadCountContainer}>
                  <Text style={styles.unreadCountText}>
                    {conversation.unReadCount}
                  </Text>
                </View>
              )}
            </View>
          </View>
          {conversation.tags && conversation.tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {conversation.tags.map((tag, index) => (
                <TouchableOpacity key={index} style={styles.tag}>
                  <Text key={index} style={styles.tagText}>
                    {tag}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
          {conversation.enrichedTags && (
            <View style={styles.tagsContainer}>
              {conversation.enrichedTags.map((tag, index) => (
                <TouchableOpacity key={index} style={styles.tag}>
                  {tag.gradientColors ? (
                    <LinearGradient
                      colors={tag.gradientColors}
                      start={{ x: 1, y: 0.5 }}
                      end={{ x: 0, y: 0.5 }}
                      style={styles.tagBackground}
                    >
                      <Text style={styles.tagText}>{tag.text}</Text>
                    </LinearGradient>
                  ) : (
                    <View
                      style={[styles.tagBackground, { backgroundColor: tag.color }]}
                    >
                      <Text style={styles.tagText}>{tag.text}</Text>
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          )}
        </TouchableOpacity>
      </View>
      {showMenuOverlayData &&
        Array.isArray(contextMenuDataWithHandler) &&
        contextMenuDataWithHandler.length > 0 && (
          <PopoverMenu
            targetElementData={showMenuOverlayData}
            TargetElementComponent={ConversationCard}
            targetElementProps={{
              compact,
              conversation,
              handleConversationClick: () => {
                return;
              },
            }}
            onClose={() => setShowMenuOverlayData(null)}
            MenuItemList={
              contextMenuDataWithHandler as (contextMenu & {
                onPress: (action: contextMenu['action']) => void;
              })[]
            }
          />
        )}
      {isDeleteChatModalOpen && (
        <DeleteConversationModal
          visible={true}
          conversationId={conversation.conversationId}
          onClose={() => setIsDeleteChatModalOpen(false)}
          onDeleteConversation={() => {
            setIsDeleteChatModalOpen(false);
            handleDeleteChat();
          }}
          strings={popupConfigs?.delete}
          source="history"
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  conversationContainer: {
    padding: 12,
    flexDirection: 'column',
    borderRadius: 16,
    overflow: 'hidden',
    marginVertical: 6,
    gap: 4,
    backgroundColor: COLORS.GREY_VAR_8,

    //TODO: fix shadow
    // shadowColor: '#000000',
    // shadowOffset: {
    //   width: 0,
    //   height: 1,
    // },
    // shadowOpacity: 0.45,
    // shadowRadius: 7,

    // elevation: 2,

    borderColor: COLORS.GREY_VAR_3,
    borderWidth: 1,
  },
  cardLeftSection: { flexDirection: 'row', flex: 1 },
  textSection: { flexDirection: 'column', flex: 1, gap: 4, paddingRight: 40 },
  cardTitle: {
    fontSize: 14,
    lineHeight: 16,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
  },
  cardMsg: {
    fontSize: 12,
    lineHeight: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_LOW_EMPHASIS,
  },
  cardTimestamp: {
    fontSize: 12,
    lineHeight: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_LOW_EMPHASIS,
  },
  imgContainer: {
    width: 25,
    height: 25,
    marginRight: 15,
    alignSelf: 'center',
    justifyContent: 'center',
  },
  lobImgContainer: {
    width: 18,
    height: 18,
    borderRadius: 22,
    position: 'absolute',
    bottom: -7,
    right: -12,
    backgroundColor: COLORS.PURPLE_LIGHT,
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: COLORS.WHITE,
    borderWidth: 2,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    marginTop: 10,
  },
  tagText: {
    fontSize: 12,
    lineHeight: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.GREY_VAR_1,
  },
  tag: {
    borderRadius: 4,
    overflow: 'hidden',
  },
  tagBackground: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    backgroundColor: COLORS.CEMENT_WHITE,
  },
  unreadCountContainer: {
    width: 18,
    height: 18,
    borderRadius: 22,
    backgroundColor: COLORS.GREEN_VAR_1,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-end',
    marginTop: 4,
  },
  unreadCountText: {
    fontSize: 12,
    lineHeight: 14,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.WHITE,
  },
  unreadCountBadge: {
    width: 8,
    height: 8,
    borderRadius: 8,
    backgroundColor: COLORS.GREEN_VAR_1,
    position: 'absolute',
    right: -6,
  },
  conversationOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ConversationCard;
