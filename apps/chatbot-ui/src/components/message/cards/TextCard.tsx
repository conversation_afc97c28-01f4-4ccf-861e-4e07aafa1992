import React, { FC, useCallback, useState } from 'react';
import {
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import { COLORS, FONTS, Z_INDEX } from '../../../constants/globalStyles';
import { openDeepLink } from '../../../native/deepLinks';
import { trackOmnitureClickEvent } from '../../../native/omniture';
import { MarkdownText } from '../MarkdownText';
import Animated, {
  FadeIn,
  FadeOut,
  SlideInDown,
  SlideOutDown,
} from 'react-native-reanimated';
import { LinearGradient } from 'react-native-linear-gradient';
import { CloseIcon } from '../../../assets/CloseIcon';
import { ScrollView } from 'react-native-gesture-handler';
import { ModalSlot } from '../../../screens/ModalProvider';
import { CARDS_WIDTH_MAP } from '../../../const';

type TextCardProps = TextCardType & { msg: Message };
const COLLAPSE_BAR_HEIGHT = 36;

export const TextCard: FC<TextCardProps> = (props) => {
  const {
    data: {
      title,
      sub_title,
      sub_title_icon,
      description: description,
      cta_title,
      card_link,
      lob = 'destination',
    },
  } = props;

  const [showReadMore, setShowReadMore] = useState<boolean>(false);
  const [showModal, setShowModal] = useState(false);
  const onReadMoreClicked = useCallback(() => {
    setShowModal(true);
  }, []);

  const onModalClosed = useCallback(() => {
    setShowModal(false);
  }, []);

  const DESCRIPTION_MAX_HEIGHT = 72;
  const onLayout = useCallback(
    (event: { nativeEvent: { layout: { height: number } } }) => {
      const thresholdReached =
        event.nativeEvent.layout.height - DESCRIPTION_MAX_HEIGHT > 5;
      setShowReadMore(thresholdReached);
    },

    [],
  );
  const handleClick = useCallback(() => {
    trackOmnitureClickEvent('CARD_CLICKED', {
      CONTENT_TYPE: `media_${lob}`,
    });

    if (card_link) {
      openDeepLink(card_link);
    }
  }, [card_link, lob]);

  return (
    <View style={styles.destinationCard}>
      <View style={styles.borderView} />
      <View style={styles.cardBody}>
        <View style={styles.titleSection}>
          <Text numberOfLines={1} style={styles.title}>
            {title}
          </Text>
        </View>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          {sub_title_icon && (
            <Image style={styles.subtitleIcon} source={{ uri: sub_title_icon }} />
          )}
          <Text numberOfLines={1} style={styles.subtitle}>
            {sub_title}
          </Text>
        </View>
        <View
          style={{
            flex: 1,
            overflow: 'scroll',
            height: DESCRIPTION_MAX_HEIGHT,
            maxHeight: DESCRIPTION_MAX_HEIGHT,
          }}
          nativeID="text-card-description"
        >
          <View onLayout={onLayout}>
            {!!description && <MarkdownText text={description} size={'small'} />}
          </View>
        </View>
        <TouchableOpacity
          activeOpacity={0.5}
          onPress={onReadMoreClicked}
          style={{
            opacity: showReadMore ? 1 : 0,
            alignSelf: 'stretch',
            // backgroundColor: 'red',
            backgroundColor: 'transparent',
            // overflow: 'hidden',
            marginTop: -16,
            marginBottom: -8,
            marginHorizontal: -12,
            maxHeight: COLLAPSE_BAR_HEIGHT,
            height: COLLAPSE_BAR_HEIGHT,
          }}
        >
          <View
            style={{
              position: 'relative',
              backgroundColor: 'transparent',
              maxHeight: COLLAPSE_BAR_HEIGHT,
            }}
            nativeID="text-card-collapse-bar"
          >
            <LinearGradient
              colors={['#ffffffff', '#ffffff00']}
              useAngle
              angle={0}
              angleCenter={{ x: 0.5, y: 0.1 }}
              style={{
                ...StyleSheet.absoluteFillObject,
                height: COLLAPSE_BAR_HEIGHT,
              }}
            />

            <Text
              style={{
                color: COLORS.BLUE_VAR_1,
                textAlignVertical: 'bottom',
                paddingHorizontal: 12,
                paddingTop: 8,
                fontFamily: FONTS.FONT_FAMILY_700,
                ...StyleSheet.absoluteFillObject,
                height: COLLAPSE_BAR_HEIGHT,
                lineHeight: COLLAPSE_BAR_HEIGHT,
              }}
            >
              {cta_title}
            </Text>
          </View>
        </TouchableOpacity>
        {showModal && (
          <ModalSlot>
            <TextCardExpanded {...props} onClose={onModalClosed} />
          </ModalSlot>
        )}
      </View>
    </View>
  );
};

export function TextCardExpanded(
  cardProps: TextCardProps & { onClose: () => void },
) {
  const { title, sub_title, sub_title_icon, description } = cardProps.data;
  const { width, height } = useWindowDimensions();
  return (
    <Animated.View
      entering={FadeIn}
      exiting={FadeOut}
      style={{
        flexDirection: 'column',
        zIndex: Z_INDEX.MODAL,
        ...StyleSheet.absoluteFillObject,
        backgroundColor: '#0002',
        alignItems: 'flex-end',
        justifyContent: 'flex-end',
      }}
    >
      <Animated.View
        entering={SlideInDown}
        exiting={SlideOutDown}
        style={[
          styles.cardBody,
          {
            flex: 0,
            zIndex: Z_INDEX.MODAL,
            minHeight: 200,
            borderRadius: 0,
            width,
            maxHeight: Math.round(height * 0.6),
            position: 'relative',
            backgroundColor: '#fff',
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
          },
        ]}
      >
        <View style={{ flexDirection: 'row', gap: 8 }}>
          <View style={{ flexDirection: 'column', flex: 1 }}>
            <View style={styles.titleSection}>
              <Text numberOfLines={1} style={styles.title}>
                {title}
              </Text>
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              {sub_title_icon && (
                <Image
                  style={styles.subtitleIcon}
                  source={{ uri: sub_title_icon }}
                />
              )}
              <Text numberOfLines={1} style={styles.subtitle}>
                {sub_title}
              </Text>
            </View>
          </View>

          <View>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => {
                cardProps.onClose();
              }}
            >
              <CloseIcon />
            </TouchableOpacity>
          </View>
        </View>

        <ScrollView>
          <View
            style={{
              flexDirection: 'column',
            }}
          >
            {!!description && <MarkdownText text={description} size={'small'} />}
          </View>
        </ScrollView>
      </Animated.View>
    </Animated.View>
  );
}

const CARD_WIDTH = CARDS_WIDTH_MAP['text-card'];
const styles = StyleSheet.create({
  borderView: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 20,
    borderColor: 'rgb(243,229,238)',
    backgroundColor: COLORS.WHITE,
    // backgroundColor: 'red',
    overflow: 'hidden',
    ...StyleSheet.absoluteFillObject,
  },
  destinationCard: {
    flex: 1,
    overflow: 'hidden',
    borderRadius: 20,
    // backgroundColor: 'red',
    width: CARD_WIDTH,
  },
  cardBody: {
    flex: 1,
    overflow: 'hidden',
    borderRadius: 22,
    // backgroundColor: 'red',
    padding: 13, // 12(padding) + 1(border inset)
  },
  titleSection: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 0,
  },
  title: {
    flex: 1,
    paddingRight: 8,
    fontSize: 14,
    lineHeight: 16,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
  },
  subtitleIcon: {
    height: 12,
    width: 12,
    marginRight: 4,
  },
  subtitle: {
    fontSize: 12,
    lineHeight: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.GREY_VAR_1,
    marginTop: 2,
  },
  toggleButton: {
    position: 'relative',
    // backgroundColor: 'red',
    height: COLLAPSE_BAR_HEIGHT,
    alignSelf: 'stretch',
    justifyContent: 'flex-end',
  },
  toggleButtonText: {
    backgroundColor: 'transparent',
    color: COLORS.BLUE_VAR_1,
    fontFamily: FONTS.FONT_FAMILY_700,
  },
});
