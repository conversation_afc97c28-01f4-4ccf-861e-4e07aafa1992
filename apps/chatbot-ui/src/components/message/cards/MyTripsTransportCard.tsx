import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import IconBus from '../../../assets/IconBus';
import IconRails from '../../../assets/iconRails';
import IconFlights from '../../../assets/IconFlights';
import RightArrow from '../../../assets/RightArrow';
import IconCabs from '../../../assets/IconCabs';
import BaseTripsCard, { IconName, IconMap } from './BaseTripsCard';

type MyTripsCardProps = MyTripsTransportCardType & {
  msg: Message;
  onCardClick: (data: MyTripsTransportCardType) => void;
};

const MyTripsTransportCard = (props: MyTripsCardProps) => {
  const renderIcon = (iconType: string) => {
    return IconMap[iconType as IconName] || null;
  };

  const renderMainContent = (main: any) => {
    return (
      <>
        {(main.left_section || main.right_section) && (
          <View style={styles.tripDetailsRowContainer}>
            {main.left_section && (
              <View style={styles.tripDetailColumn}>
                {main.left_section.title && (
                  <Text style={styles.tripDetailColumnTitle}>
                    {main.left_section.title}
                  </Text>
                )}

                {main.left_section.sub_title && (
                  <Text style={styles.tripDetailColumnSubtitle}>
                    {main.left_section.sub_title}
                  </Text>
                )}

                {main.left_section.text && (
                  <Text style={styles.tripDetailColumnDescription}>
                    {main.left_section.text}
                  </Text>
                )}
              </View>
            )}

            {(main.left_section || main.right_section) && (
              <View style={styles.arrowContainer}>
                <RightArrow />
              </View>
            )}

            {main.right_section && (
              <View style={styles.tripDetailColumn}>
                {main.right_section.title && (
                  <Text style={styles.tripDetailColumnTitle}>
                    {main.right_section.title}
                  </Text>
                )}

                {main.right_section.sub_title && (
                  <Text style={styles.tripDetailColumnSubtitle}>
                    {main.right_section.sub_title}
                  </Text>
                )}

                {main.right_section.text && (
                  <Text style={styles.tripDetailColumnDescription}>
                    {main.right_section.text}
                  </Text>
                )}
              </View>
            )}
          </View>
        )}
      </>
    );
  };

  return (
    <BaseTripsCard
      {...props}
      renderIcon={renderIcon}
      renderMainContent={renderMainContent}
    />
  );
};

const styles = StyleSheet.create({
  tripDetailsRowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginTop: 8,
  },
  tripDetailColumn: {},
  arrowContainer: {
    paddingHorizontal: 8,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  tripDetailColumnTitle: {
    color: COLORS.BLACK,
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_900,
  },
  tripDetailColumnSubtitle: {
    color: COLORS.GREY_VAR_2,
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_700,
    marginTop: 2,
  },
  tripDetailColumnDescription: {
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 12,
    color: COLORS.GREY_VAR_2,
    marginTop: 2,
  },
});

export default MyTripsTransportCard;
