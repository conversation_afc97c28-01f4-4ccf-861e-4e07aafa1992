import React, { FC, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { COLORS, FONTS, Z_INDEX } from '../../../constants/globalStyles';
import { useBookmarkCard } from '../../../store/messages/messageStore';
import { TravelRoute } from '../../travel-route';
import IconDuration from '../../../assets/IconDuration';
import IconUpArrow from '../../../assets/IconUpArrow';
import { HowToGoDetailsPopup } from '../../modals/how-to-go-details';
import IconBus from '../../../assets/IconBus';
import IconRails from '../../../assets/iconRails';
import IconFlights from '../../../assets/IconFlights';
import { trackOmnitureClickEvent } from '../../../native/omniture';
import { ShadowedView } from 'react-native-fast-shadow';
import {
  BookmarkIconNonImageCards,
  BookmarkIconNonImageCardsFilled,
} from '../../../assets/BookMarkIconNonImageCards';
import { useResponsiveWidth } from '../../../hooks/useResponsiveWidth';

/* Section: Card container for the overall layout */
const Card: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const {containerWidth} = useResponsiveWidth();
  //TODO
  const width = '100%';
  return <ShadowedView style={[styles.card, { width: containerWidth - 55 }]}>{children}</ShadowedView>;
};

/* Section: Mapping transport icons */
export const IconMap = {
  bus: <IconBus />,
  train: <IconRails />,
  flight: <IconFlights />,
};

export type TravelDetailDataOnCardClick = { data: Segment };

type TravelDetailCardProps = TravelDetailCardType & {
  msg: Message;
  onCardClick: (data: TravelDetailDataOnCardClick) => void;
};

/* Section: Main TravelDetailCard component */
const TravelDetailCard: FC<TravelDetailCardProps> = (props) => {
  const [showModal, setShowModal] = useState(false);

  const onClose = () => {
    setShowModal(false);
  };

  /* Section: Event handlers and state */
  const {
    bottom_tag,
    title = '',
    tag,
    cta,
    fare_amount_text = '',
    fare_info_text = '',
    segments,
    lob = 'transport',
  } = props.data;

  const { onBookmark, isBookmarked } = useBookmarkCard(props, lob);

  const onCTAClick = (data: Segment | undefined) => {
    if (data) {
      // Analytics
      trackOmnitureClickEvent('H2G_CARD_CLICKED', {
        CONTENT_TYPE: `link`,
      });
      props.onCardClick({ data: data });
    } else {
      // Analytics
      trackOmnitureClickEvent('H2G_CARD_CLICKED', {
        CONTENT_TYPE: `overlay`,
      });
      setShowModal(true);
    }
  };

  return (
    <>
      {/* Section: Card view with header, content, route, and footer */}
      <Card>
        <View style={styles.cardHeader}>
          <View style={styles.tagContainer}>
            {tag?.map((item, index) => (
              <View
                key={index}
                style={[styles.tagBadge, { borderColor: item.color }]}
              >
                <Text style={[styles.tagText, { color: item.color }]}>
                  {item?.text?.toUpperCase() || ''}
                </Text>
              </View>
            ))}
          </View>
          <TouchableOpacity
            activeOpacity={1}
            onPress={onBookmark}
            style={styles.bookmarkContainer}
          >
            {isBookmarked && (
              <BookmarkIconNonImageCardsFilled
                stroke={COLORS.BLUE_VAR_1}
                fill={COLORS.BLUE_VAR_1}
                height={16}
                width={16}
              />
            )}
            {!isBookmarked && (
              <BookmarkIconNonImageCards
                stroke={COLORS.TEXT_MEDIUM_EMPHASIS}
                height={16}
                width={16}
              />
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.content}>
          <Text style={styles.routeTitle}>{title}</Text>
          <View style={styles.pricingContainer}>
            <Text style={styles.fareInfo}>{fare_info_text}</Text>
            <Text style={styles.fareAmount}>{fare_amount_text}</Text>
          </View>
        </View>

        <View style={styles.routeContainer}>
          <TravelRoute segments={segments} />
        </View>

        <View style={styles.footer}>
          <View style={styles.bottomTagContainer}>
            {bottom_tag?.map((tag, index) => (
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  backgroundColor: COLORS.GREY_VAR_3,
                  borderRadius: 10,
                  padding: 2,
                  paddingHorizontal: 5,
                }}
                key={index}
              >
                <IconDuration />
                <Text style={styles.duration} key={index}>
                  {tag.text || ''}
                </Text>
              </View>
            ))}
          </View>
          <TouchableOpacity
            onPress={() =>
              onCTAClick(segments?.length === 1 ? segments[0] : undefined)
            }
            style={styles.detailsButton}
          >
            <Text style={styles.detailsButtonText}>{cta?.text || ''}</Text>
            {cta?.link ? null : <IconUpArrow />}
          </TouchableOpacity>
        </View>
      </Card>
      <HowToGoDetailsPopup
        /* Section: Detailed info popup */
        item={props.data}
        showModal={showModal}
        onClose={onClose}
        onCTAClick={onCTAClick}
      />
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 24,
    padding: 10,
    shadowColor: 'rgba(0, 0, 0, 0.70)',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.15,
    shadowRadius: 5,
    backgroundColor: COLORS.GREY_VAR_8,
    flex: 1,
    margin: 5
  },
  tagBadge: {
    borderWidth: 1,
    borderRadius: 10,
    paddingHorizontal: 10,
    paddingVertical: 2,
    marginRight: 5,
  },
  tagText: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_900,
  },
  recommendedText: {
    fontSize: 12,
    color: '#D35400',
    fontWeight: 'bold',
  },
  content: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  routeTitle: {
    fontSize: 16,
    color: '#333',
    fontFamily: FONTS.FONT_FAMILY_900,
  },
  pricingContainer: {
    alignItems: 'center',
    flexDirection: 'row',
  },

  fareInfo: {
    fontSize: 12,
    color: COLORS.GREY_VAR_1,
    marginRight: 3,
    fontFamily: FONTS.FONT_FAMILY_400,
  },
  fareAmount: {
    fontSize: 18,
    color: COLORS.BLACK,
    fontFamily: FONTS.FONT_FAMILY_900,
  },
  routeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 10,
  },
  stopContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    marginHorizontal: 5,
  },
  stopText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#555',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10,
  },
  duration: {
    fontSize: 12,
    color: COLORS.BLACK,
    marginLeft: 2,
    fontFamily: FONTS.FONT_FAMILY_500,
  },
  detailsButton: {
    borderColor: COLORS.BLUE_VAR_1,
    borderWidth: 1,
    flexDirection: 'row',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  detailsButtonText: {
    color: COLORS.BLUE_VAR_1,
    fontWeight: 'bold',
    fontSize: 13,
  },
  bookmarkContainer: {
    height: 28,
    width: 28,
    borderRadius: 14,
    borderColor: 'rgb(243,229,238)',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(247,247,247,1)',
    zIndex: Z_INDEX.OVERLAP_1,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  bottomTagContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tagContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '85%',
  },
});

export default TravelDetailCard;
