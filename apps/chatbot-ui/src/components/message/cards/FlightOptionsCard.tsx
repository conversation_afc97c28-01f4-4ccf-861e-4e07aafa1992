import React, { FC, useCallback } from 'react';
import { View, StyleSheet, Text, Image, TouchableOpacity } from 'react-native';
import { COLORS, FONTS, Z_INDEX } from '../../../constants/globalStyles';
import { TouchableFeedback } from '../../base/TouchableFeeback';
import { useBookmarkCard } from '../../../store/messages';
import { ShadowedView } from 'react-native-fast-shadow';
import { CARDS_WIDTH_MAP } from '../../../const';
import {
  BookmarkIconNonImageCards,
  BookmarkIconNonImageCardsFilled,
} from '../../../assets/BookMarkIconNonImageCards';
import FastImageWrapper from '../../image/FastImageWrapper';
import { MarkdownText } from '../MarkdownText';

interface FlightOptionsCardProps extends FlightOptionsCardType  {
  onCardClick: (data: FlightOptionsCardProps) => void;
}

export const FlightOptionsCard: FC<FlightOptionsCardProps> = (props) => {
  const {
    data: {
      header,
      sections,
      fare_info_text,
      fare_amount_text,
      cta_link,
      lob = 'flights',
    },
    onCardClick: onCardClickProp,
  } = props;

  const onCardClick = useCallback(() => {
    onCardClickProp(props);
  }, [onCardClickProp, props]);

  const { onBookmark, isBookmarked } = useBookmarkCard(props, lob);

  return (
    <TouchableFeedback
      onPress={onCardClick}
      style={{ flex: 1 }}
      containerStyle={styles.flightCard}
    >
      <ShadowedView
        style={{
          shadowColor: COLORS.GREY_VAR_1,
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.18,
          shadowRadius: 5,
          borderRadius: 24,
          width: CARD_WIDTH,
          position: 'relative',
        }}
      >
        <View style={styles.cardBody}>
          <BookMarkButton isBookmarked={isBookmarked} onBookmark={onBookmark} />
          <View style={styles.headerSection}>
            <View style={styles.headerIcons}>
              {header.icons.map((icon, index) => (
                <FastImageWrapper
                  key={index}
                  style={[
                    styles.airlineIcon,
                    index % 2 === 0 ? { marginRight: 12 } : { marginLeft: 12 },
                  ]}
                  source={{ uri: icon }}
                />
              ))}
            </View>
            <Text numberOfLines={1} style={styles.headerTitle}>
              {header.title}
            </Text>
          </View>

          {/* Flight Sections */}
          <View style={styles.sectionsContainer}>
            {sections.map((section, index) => (
              <View key={index} style={styles.sectionItem}>
                <View style={styles.sectionHeader}>
                  {section?.icon && (
                    <FastImageWrapper
                      style={styles.sectionIcon}
                      source={{ uri: section.icon }}
                    />
                  )}
                  <View style={styles.sectionContent}>
                    <Text numberOfLines={1} style={styles.sectionTitle}>
                      {section.title}
                    </Text>
                    <Text numberOfLines={1} style={styles.sectionSubtitle}>
                      {section.sub_title}
                    </Text>
                    <View style={styles.sectionDescription}>
                      <MarkdownText
                        text={section.description}
                        adjustHeight={false}
                        lineHeight={16}
                      />
                    </View>
                  </View>
                </View>
              </View>
            ))}
          </View>

          {/* Fare Section */}
          <View style={styles.fareContainer}>
            <Text style={styles.fareInfo}>{fare_info_text}</Text>
            <Text style={styles.fareAmount}>{fare_amount_text}</Text>
          </View>
        </View>
      </ShadowedView>
    </TouchableFeedback>
  );
};


function BookMarkButton({
  isBookmarked,
  onBookmark,
}: {
  isBookmarked: boolean;
  onBookmark: () => void;
}) {
  return (
    <TouchableFeedback
      onPress={onBookmark}
      containerStyle={styles.bookmarkContainer}
    >
      {isBookmarked ? (
        <BookmarkIconNonImageCardsFilled height={16} width={16} />
      ) : (
        <BookmarkIconNonImageCards height={16} width={16} />
      )}
    </TouchableFeedback>
  );
}

const CARD_WIDTH = CARDS_WIDTH_MAP['rt-transport-card'];

const styles = StyleSheet.create({
  flightCard: {
    flex: 1,
    overflow: 'hidden',
    width: CARD_WIDTH,
    borderRadius: 24,
    paddingBottom:4,
  },
  cardBody: {
    flex: 1,
    paddingVertical: 14,
    paddingHorizontal: 12,
    backgroundColor: COLORS.WHITE,
    borderRadius: 24,
    width: '100%',
  },
  bookmarkContainer: {
    position: 'absolute',
    zIndex: Z_INDEX.OVERLAP_1,
    height: 28,
    width: 28,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F7F7F7',
    right: 10,
    top: 8,
  },
  headerSection: {
    marginBottom: 16,
    flexDirection: 'row',
    alignContent: 'center',
    alignItems: 'center',
  },
  headerIcons: {
    flexDirection: 'column',
    marginRight: 8,
  },
  airlineIcon: {
    width: 12,
    height: 12,
    borderRadius: 2,
  },
  headerTitle: {
    fontSize: 12,
    lineHeight: 14,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
    height: 14,
  },
  sectionsContainer: {
    flex: 1,
    gap: 12,
  },
  sectionItem: {
    borderWidth: 1,
    borderRadius: 16,
    borderColor: COLORS.GREY_VAR_5,
    padding: 16,
    paddingBottom: 0,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  sectionContent: {
    flex: 1,
  },
  sectionIcon: {
    width: 24,
    height: 24,
    marginRight: 8,
    borderRadius: 4.5,
  },
  sectionTitle: {
    fontSize: 16,
    lineHeight: 18,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: COLORS.BLACK,
    flex: 1,
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 12,
    lineHeight: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_LOW_EMPHASIS,
  },
  sectionDescription: {
    overflow: 'hidden',
    position: 'relative',
  },
  fareContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent:'center',
    paddingTop:16,
    borderTopColor: COLORS.GREY_VAR_3,
  },
  fareInfo: {
    fontSize: 14,
    lineHeight: 16,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.BLACK,
  },
  fareAmount: {
    fontSize: 18,
    lineHeight: 20,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: COLORS.BLUE_VAR_1,
    marginLeft: 4,
  },
});
