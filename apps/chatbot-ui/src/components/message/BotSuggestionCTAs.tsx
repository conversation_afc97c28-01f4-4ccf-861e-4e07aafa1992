import React from 'react';
import { StyleSheet, View } from 'react-native';
import { TouchableFeedback } from '../base/TouchableFeeback';
import { MSG_OUTER_PADDING_VERT } from './messageStyles';
import { trackOmnitureClickEvent } from '../../native/omniture';
import { COLORS, FONTS } from '../../constants/globalStyles';
import Animated, { FadeIn, SlideInRight } from 'react-native-reanimated';
import { ScrollView } from 'react-native-gesture-handler';
import { Analytics, TrackingEvent } from '../../analytics';
import LinearGradient from 'react-native-linear-gradient';
// import { Text } from 'react-native-svg';

const textEnterAnim = FadeIn.delay(450).duration(300);

export const BotSuggestionCTAs: React.FC<{
  ctas: Ctas[];
  onCTAClick: (cta: Ctas) => void;
}> = ({ ctas, onCTAClick }) => {
  if (!ctas?.length) {
    return null;
  }
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      nativeID="bot-suggestions-scrollview"
    >
      <View style={styles.suggestions}>
        {ctas?.map((cta, index) => {
          if (!cta?.cta_text) {
            return null;
          }
          return (
            <Animated.View
              entering={SlideInRight.duration(600).delay(index * 100)}
              style={styles.suggestionContainer}
            >
              <LinearGradient
                colors={['#66B0F0', '#718FF5']}
                style={styles.gradientColor}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              />
              <View style={styles.suggestionItem}>
                <TouchableFeedback
                  containerStyle={styles.suggestionItemButtonContainer}
                  activeOpacity={0.5}
                  style={styles.suggestionItemButton}
                  onPress={() => {
                    trackOmnitureClickEvent('SUGGESTION_CTAS_CLICKED');
                    onCTAClick(cta);
                    Analytics.trackClickEvent(TrackingEvent.payload_PromptClicked());
                  }}
                >
                  <Animated.Text
                    entering={textEnterAnim}
                    style={styles.suggestionText}
                  >
                    {cta.cta_text}
                  </Animated.Text>
                </TouchableFeedback>
              </View>
            </Animated.View>
          );
        })}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  suggestionContainer: {
    borderRadius: 15,
    overflow: 'hidden',
    position: 'relative',
    justifyContent: 'center',
    // alignItems: 'center',
  },
  suggestions: {
    // display: keyboardState === 'SHOWN' ? 'flex' : 'none',
    flexDirection: 'row',
    gap: 12,
    flex: 1,
    paddingHorizontal: 15,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    paddingVertical: MSG_OUTER_PADDING_VERT,
    marginTop: -8,
  },
  suggestionText: {
    paddingVertical: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 14,
    lineHeight: 16,
    color: COLORS.GREY_VAR_2,
    textAlign: 'center',
  },
  gradientColor: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: 16,
    // alignSelf: 'center',
  },
  suggestionItem: {
    padding: 1.2,
    paddingHorizontal: 1.2,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 0.15,
    marginLeft: 0.16,
    borderRadius: 16,
  },
  suggestionItemButtonContainer: {
    backgroundColor: COLORS.GREY_VAR_8,
    borderRadius: 15,
    flex: 1,
    paddingHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  suggestionItemButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
