import { StyleSheet, Text, View, LayoutAnimation } from 'react-native';
import {
  ThumbsDownFilledIcon,
  ThumbsDownIcon,
  ThumbsUpFilledIcon,
  ThumbsUpIcon,
} from '../../assets/ThumbUpIcon';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { BookmarkIcon, BookmarkIconFilled } from '../../assets/BookmarkIcon';
import { TouchableScale } from '../base/TouchableScale';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { MESSAGES } from '../../constants/messages';
import { useBookmarkMessage, useFeedbackAction } from '../../store/messages';
import { IssueReporter } from '../IssueReporter';
import Animated, { LinearTransition } from 'react-native-reanimated';
import { trackOmnitureClickEvent } from '../../native/omniture';
import useGetFeatureFlags from '../../hooks/useGetFeatureFlags';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import { useTalkBackStore } from '../../store/audio-talkback/talkbackStore';
import { SpeakerIcon } from '../../assets/SpeakerIcon';
import { SpeakerCoachmark } from '../coachmark/SpeakerCoachmark';
import { useDebounceCallback } from '../../utils/useDebounceCallback';
import StorageUtils from '../../utils/storageUtils';
import { scrollEvents } from '../../utils/scrollEvents';
import { CopyIcon } from '../../assets/CopyIcon';
import { TickIcon } from '../../assets/TickIcon';
import clipboardUtils, { extractMessageText } from '../../utils/clipboardUtils';
import { Toast } from '../toast';

interface MessageFeedbackViewProps {
  msg: Message;
  isLastMessage?: boolean;
}

export function MessageFeedbackView({
  msg,
  isLastMessage = false,
}: MessageFeedbackViewProps) {
  const { feedback, sendFeedback, responseText } = useFeedbackAction(msg);
  const { onBookmark, isBookmarked } = useBookmarkMessage(msg);
  const { showCopyIcon, showSpeakerCoachmark } = useGetFeatureFlags();
  const currFeedbackValRef = useRef(feedback);
  const [showTick, setShowTick] = useState(false);
  let { audioTalkBack } = msg;
  const { id: msgId } = msg;
  const { speak, isInitRunning, isPlaying, messagesAudioData } = useTalkBackStore();
  if (!audioTalkBack && msgId in messagesAudioData && messagesAudioData[msgId]) {
    audioTalkBack = messagesAudioData[msgId];
  }
  const showTalkback = !!audioTalkBack && !!audioTalkBack.summary;
  const [showCoachmark, setShowCoachmark] = useState(false);
  const [hasShownCoachmark, setHasShownCoachmark] = useState<boolean | null>(null);
  // Load coachmark shown flag from storage on mount
  useEffect(() => {
    const loadCoachmarkFlag = async () => {
      const hasShown = await StorageUtils.getSpeakerCoachmarkShown();
      setHasShownCoachmark(hasShown);
    };
    loadCoachmarkFlag();
  }, []);

  // Show coachmark only once in lifetime for ASSISTANT messages AND only on the last message
  useEffect(() => {
    if (hasShownCoachmark === false && msg.role === 'ASSISTANT' && isLastMessage) {
      const timer = setTimeout(async () => {
        // Configure layout animation for smooth coachmark appearance
        LayoutAnimation.configureNext({
          duration: 400,
          create: {
            type: LayoutAnimation.Types.easeInEaseOut,
            property: LayoutAnimation.Properties.opacity,
          },
          update: {
            type: LayoutAnimation.Types.easeInEaseOut,
          },
        });

        setShowCoachmark(true);
        setHasShownCoachmark(true);
        await StorageUtils.setSpeakerCoachmarkShown();
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [msg.role, msg.id, isLastMessage, hasShownCoachmark]);

  const [debouncedSpeak] = useDebounceCallback(
    (summary: string, locale: string | null) => {
      speak(summary, locale);
    },
    1000,
    { leading: true, trailing: false },
  );

  const onSpeakerPressed = useCallback(() => {
    setShowCoachmark(false);
    if (audioTalkBack?.summary) {
      debouncedSpeak(audioTalkBack.summary, audioTalkBack.locale || null);
    }
  }, [audioTalkBack, debouncedSpeak]);

  const onCopyPressed = useCallback(async () => {
    try {
      const plainText = extractMessageText(msg);

      if (!plainText.trim()) {
        Toast.show('No text content to copy');
        return;
      }

      await clipboardUtils.copyTextWithFeedback(
        plainText,
        () => {
          setShowTick(true);
          setTimeout(() => setShowTick(false), 2000);

          const shouldShowToast = clipboardUtils.shouldShowCustomToast();
          if (shouldShowToast) {
            Toast.show(MESSAGES.COPIED_TO_CLIPBOARD);
          }
        },
        (error) => {
          Toast.show(MESSAGES.UNABLE_TO_COPY_CONTENT);
        }
      );
    } catch (error) {
      console.error('Copy button error:', error);
      Toast.show(MESSAGES.UNABLE_TO_COPY_CONTENT);
    }
  }, [msg]);

  const showPlayerRunning = isInitRunning || isPlaying;
  const onLikePressed = useCallback(() => {
    if (!currFeedbackValRef.current) {
      trackOmnitureClickEvent('FEEDBACK_LIKE_CLICKED');
      trackPDTEvent({
        eventName: eventNameSchema.CHAT_INTERACTED,
        eventType: eventTypeSchema.ACTION,
        eventValue: eventValueSchema.LIKE,
      });
    }
    sendFeedback('LIKE');
  }, []);

  const onDislikePressed = useCallback(() => {
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.DISLIKE,
    });
    if (!currFeedbackValRef.current) {
      trackOmnitureClickEvent('FEEDBACK_DISLIKE_CLICKED');
    }
    sendFeedback('DISLIKE');
  }, []);

  const handleCloseCoachmark = useCallback(() => {
    setShowCoachmark(false);
  }, []);

  // Handle spacer layout to trigger scroll adjustment
  const handleSpacerLayout = useCallback(() => {
    // Wait for layout animation to complete before scrolling for smoothest experience
    setTimeout(() => {
      scrollEvents.triggerScrollToBottom();
    }, 450); // Slightly longer than the 400ms layout animation
  }, []);

  useEffect(() => {
    if (
      currFeedbackValRef.current !== null &&
      currFeedbackValRef.current !== feedback
    ) {
      trackOmnitureClickEvent(
        feedback === 'LIKE'
          ? 'FEEDBACK_CHANGED_TO_LIKE'
          : 'FEEDBACK_CHANGED_TO_DISLIKE',
      );
    }
    currFeedbackValRef.current = feedback;
  }, [feedback]);

  return (
    <View style={styles.root}>
      <View style={styles.iconRow}>
        <TouchableScale style={styles.iconTouchable} onPress={onLikePressed}>
          <View style={styles.iconContainer}>
            {feedback === 'LIKE' && <ThumbsUpFilledIcon />}
            {feedback !== 'LIKE' && <ThumbsUpIcon />}
          </View>
        </TouchableScale>
        <TouchableScale style={styles.iconTouchable} onPress={onDislikePressed}>
          <View style={styles.iconContainer}>
            <View style={{ marginLeft: 2, marginTop: 2 }}>
              {feedback === 'DISLIKE' && <ThumbsDownFilledIcon />}
              {feedback !== 'DISLIKE' && <ThumbsDownIcon />}
            </View>
          </View>
        </TouchableScale>
        {showTalkback && (
          <TouchableScale
            disabled={showPlayerRunning}
            style={styles.iconTouchable}
            onPress={onSpeakerPressed}
          >
            <View style={styles.iconContainer}>
              <View style={{ marginLeft: 2, marginTop: 2 }}>
                <SpeakerIcon isPlaying={showPlayerRunning} />
              </View>
            </View>
          </TouchableScale>
        )}
        <TouchableScale style={styles.iconTouchable} onPress={onBookmark}>
          <View style={styles.iconContainer}>
            {isBookmarked && <BookmarkIconFilled height={16} width={16} />}
            {!isBookmarked && <BookmarkIcon height={16} width={16} />}
          </View>
        </TouchableScale>
        {(showCopyIcon ?? true) && (
          <TouchableScale style={styles.iconTouchable} onPress={onCopyPressed}>
            <View style={styles.iconContainer}>
              {showTick ? (
                <TickIcon width={16} height={16} />
              ) : (
                <CopyIcon width={16} height={16} />
              )}
            </View>
          </TouchableScale>
        )}
      </View>

      {/* Spacer to prevent coachmark overlap - placed after icons */}
      {showTalkback && showCoachmark && (
        <View style={styles.coachmarkSpacer} onLayout={handleSpacerLayout} />
      )}

      {feedback && feedback === 'LIKE' && responseText && (
        <Animated.View layout={LinearTransition}>
          <Text style={styles.feedbackText}>{responseText || ''}</Text>
        </Animated.View>
      )}
      {feedback === 'DISLIKE' && <IssueReporter msg={msg} />}

      {showTalkback && showCoachmark && (showSpeakerCoachmark ?? true) && (
        <SpeakerCoachmark
          onClose={handleCloseCoachmark}
          visible={showCoachmark}
          text={MESSAGES.SPEAKER_COACHMARK}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  root: {
    flexDirection: 'column',
    // marginTop: -MSG_OUTER_PADDING_VERT, // there's padding=16 in the message bubble, we want it to be 8
    marginLeft: 16,
    position: 'relative',
  },
  iconRow: {
    flexDirection: 'row',
  },
  iconTouchable: {
    paddingHorizontal: 8,
  },
  iconContainer: {
    borderColor: COLORS.GREY_VAR_6,
    backgroundColor: COLORS.WHITE,
    elevation: 1,
    shadowColor: 'rgba(8, 8, 8, 0.08)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 1,
    shadowRadius: 2,
    borderWidth: 1,
    borderRadius: 8,
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  feedbackText: {
    fontSize: 12,
    height: 16,
    textAlign: 'left',
    paddingHorizontal: 8,
    marginTop: 8,
    color: COLORS.BLUE_VAR_7,
    fontFamily: FONTS.FONT_FAMILY_400,
  },
  coachmarkSpacer: {
    height: 67, // Space for coachmark (59px bubble + 8px arrow)
  },
});
