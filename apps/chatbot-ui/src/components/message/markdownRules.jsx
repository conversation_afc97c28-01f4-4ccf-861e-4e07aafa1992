import React from 'react';
import { Linking, Text, TouchableWithoutFeedback, View } from 'react-native';

export const rules = {
  heading1: (node, children) => <Text key={node.key}>{children}</Text>,
  heading2: (node, children) => <Text key={node.key}>{children}</Text>,
  heading3: (node, children) => <Text key={node.key}>{children}</Text>,
  heading4: (node, children) => <Text key={node.key}>{children}</Text>,
  heading5: (node, children) => <Text key={node.key}>{children}</Text>,
  heading6: (node, children) => <Text key={node.key}>{children}</Text>,
  paragraph: (node, children, parent, styles) => (
    <View key={node.key} style={styles._VIEW_SAFE_paragraph}>
      {children}
    </View>
  ),
  blocklink: (node, children, parent, styles, onLinkPress) => (
    <TouchableWithoutFeedback
      key={node.key}
      onPress={() => openUrl(node.attributes.href, onLinkPress)}
      style={styles.blocklink}
    >
      <Text style={styles.image}>{children}</Text>
    </TouchableWithoutFeedback>
  ),
  /*  list_item: (node, children, parent, styles, inheritedStyles = {}) => {
    // we need to grab any text specific stuff here that is applied on the list_item style
    // and apply it onto bullet_list_icon. the AST renderer has some workaround code to make
    // the content classes apply correctly to the child AST tree items as well
    // as code that forces the creation of the inheritedStyles object for list_items
    const refStyle = {
      ...inheritedStyles,
      ...StyleSheet.flatten(styles.list_item),
    };

    const arr = Object.keys(refStyle);

    const modifiedInheritedStylesObj = {};

    for (let b = 0; b < arr.length; b++) {
      if (textStyleProps.includes(arr[b])) {
        modifiedInheritedStylesObj[arr[b]] = refStyle[arr[b]];
      }
    }

    if (hasParents(parent, 'bullet_list')) {
      return (
        <View
          key={node.key}
          style={[
            styles._VIEW_SAFE_list_item,
            {
              flexDirection: 'column',
              justifyContent: 'flex-start',
              // backgroundColor: 'green',
            },
          ]}
        >
          <Text
            style={[modifiedInheritedStylesObj, styles.bullet_list_icon]}
            accessible={false}
          >
            {Platform.select({
              android: '\u2022',
              ios: '\u00B7',
              default: '\u2022',
            })}
          </Text>
          <Text style={styles._VIEW_SAFE_bullet_list_content}>{children}</Text>
        </View>
      );
    }

    if (hasParents(parent, 'ordered_list')) {
      const orderedListIndex = parent.findIndex((el) => el.type === 'ordered_list');

      const orderedList = parent[orderedListIndex];
      let listItemNumber;

      if (orderedList.attributes && orderedList.attributes.start) {
        listItemNumber = orderedList.attributes.start + node.index;
      } else {
        listItemNumber = node.index + 1;
      }

      return (
        <View key={node.key} style={styles._VIEW_SAFE_list_item}>
          <Text style={[modifiedInheritedStylesObj, styles.ordered_list_icon]}>
            {listItemNumber}
            {node.markup}
          </Text>
          <Text style={styles._VIEW_SAFE_ordered_list_content}>{children}</Text>
        </View>
      );
    }

    // we should not need this, but just in case
    return (
      <View key={node.key} style={styles._VIEW_SAFE_list_item}>
        {children}
      </View>
    );
  },*/
};

// data from 'react-native-markdown-display'
const textStyleProps = [
  'textShadowOffset',
  'color',
  'fontSize',
  'fontStyle',
  'fontWeight',
  'lineHeight',
  'textAlign',
  'textDecorationLine',
  'textShadowColor',
  'fontFamily',
  'textShadowRadius',
  'includeFontPadding',
  'textAlignVertical',
  'fontVariant',
  'letterSpacing',
  'textDecorationColor',
  'textDecorationStyle',
  'textTransform',
  'writingDirection',
];

// utils from 'react-native-markdown-display'
function openUrl(url, customCallback) {
  if (customCallback) {
    const result = customCallback(url);
    if (url && result && typeof result === 'boolean') {
      Linking.openURL(url);
    }
  } else if (url) {
    Linking.openURL(url);
  }
}

function hasParents(parents, type) {
  return parents.findIndex((el) => el.type === type) > -1;
}
