import { useEffect, useMemo } from 'react';
import { Animated, useWindowDimensions } from 'react-native';

export function useLoaderAnimation() {
  const opacityAnimatedValue = useMemo(() => new Animated.Value(0), []);
  const opacityTransform = useMemo(() => {
    return opacityAnimatedValue.interpolate({
      inputRange: [0, 0.5, 1],
      outputRange: [0.1, 1, 0.1],
    });
  }, []);
  useEffect(() => {
    Animated.loop(
      Animated.timing(opacityAnimatedValue, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
    ).start();
  }, [opacityAnimatedValue]);
  return { opacityTransform };
}

export function useTranslateAnimation() {
  const { width } = useWindowDimensions();
  const maxTranslateX = Math.round(width * 0.66);
  const translateXValue = useMemo(() => new Animated.Value(0), []);
  useEffect(() => {
    Animated.loop(
      Animated.timing(translateXValue, {
        toValue: maxTranslateX,
        duration: 1000,
        useNativeDriver: true,
      }),
    ).start();
  }, [translateXValue]);
  return { translateXValue };
}
