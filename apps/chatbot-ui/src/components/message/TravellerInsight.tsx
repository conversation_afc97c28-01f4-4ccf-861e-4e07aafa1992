import React from 'react';
import { Dimensions, StyleSheet, View } from 'react-native';
import Markdown from 'react-native-markdown-display';
import { AiStarComp } from '../base/AiStarComp';
import LinearGradient from 'react-native-linear-gradient';
import { FONTS } from '../../constants/globalStyles';
import { useResponsiveWidth } from '../../hooks/useResponsiveWidth';
import useGetFeatureFlags from '../../hooks/useGetFeatureFlags';

interface TravellerInsightProps {
  text: string;
  listKey: string;
}

const screenWidth = Dimensions.get('window').width;

export const TravellerInsight: React.FC<TravellerInsightProps> = ({
  text,
  listKey,
}) => {
  const { containerWidth } = useResponsiveWidth();
  const { shouldIncreaseFontSize } = useGetFeatureFlags();
  return (
    <View style={[styles.container, { width: containerWidth - 24 }]} key={listKey}>
      <LinearGradient
        colors={['rgba(51, 102, 241, 0.2)', 'rgba(255, 255, 255, 0)']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        locations={[0, 0.25]}
        style={styles.borderGradient}
      >
        <View style={styles.card}>
          <LinearGradient
            colors={['rgba(51, 102, 241, 0.1)', 'rgba(255, 255, 255, 0.0)']}
            locations={[0.0207, 0.3277]}
            start={{ x: 0.25, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.innerGradient}
          >
            <View style={styles.header}>
              <AiStarComp
                containerStyle={{
                  height: 24,
                  width: 24,
                  alignSelf: 'flex-start',
                }}
                primaryStarSize={16}
                secondaryStarSize={8}
                primaryStarStyle={{ top: 0, left: 0 }}
                secondaryStarStyle={{ top: 10, left: 10 }}
              />
            </View>
            <View style={styles.content}>
              <Markdown
                style={{
                  ...markdownStyles,
                  ...(shouldIncreaseFontSize && {
                    text: {
                      ...markdownStyles.text,
                      fontSize: 15,
                    },
                  }),
                }}
              >
                {text}
              </Markdown>
            </View>
          </LinearGradient>
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginLeft: 12,
    marginVertical: 12,
  },
  borderGradient: {
    padding: 1.5,
    borderRadius: 12,
  },
  card: {
    borderRadius: 11,
    backgroundColor: '#FFFFFF',
    overflow: 'hidden',
  },
  innerGradient: {
    borderRadius: 10,
    paddingVertical: 8,
    paddingRight: 19,
    paddingLeft: 2.5,
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
  header: {
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    transform: [{ scale: 1.2 }, { translateY: 4 }],
  },
  content: {
    flex: 1,
  },
});

const markdownStyles = StyleSheet.create({
  body: {
    marginTop: 0,
    marginBottom: 0,
  },
  paragraph: {
    marginTop: 0,
    marginBottom: 0,
  },
  text: {
    color: '#11287A',
    fontSize: 14,
    lineHeight: 20,
    flexShrink: 1,
    flexWrap: 'wrap',
    width: '100%',
    fontFamily: FONTS.FONT_FAMILY_400,
    // fontStyle: 'italic',
  },
  strong: {
    fontFamily: FONTS.FONT_FAMILY_700,
  },
  em: {
    fontStyle: 'italic',
    color: '#3366F1',
    fontFamily: FONTS.FONT_FAMILY_700,
  },
});
