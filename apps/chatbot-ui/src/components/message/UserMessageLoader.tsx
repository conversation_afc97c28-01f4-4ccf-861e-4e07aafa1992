import React from 'react';
import { Animated, View } from 'react-native';
import { messageBubbleStyles } from './messageStyles';
import { LinearGradient } from 'react-native-linear-gradient';
import { COLORS } from '../../constants/globalStyles';
import { useLoaderAnimation } from './useLoaderAnimation';

export const UserMessageLoader: React.FC<unknown> = () => {
  const { opacityTransform } = useLoaderAnimation();
  return (
    <View style={messageBubbleStyles.userMessageContainer}>
      <View
        style={[
          messageBubbleStyles.userMessage,
          {
            height: 40,
            width: 100,
            paddingVertical: 0,
            paddingHorizontal: 0,
          },
        ]}
      >
        <Animated.View
          style={{
            flex: 1,
            opacity: opacityTransform,
            height: 40,
            width: 100,
          }}
        >
          <LinearGradient
            colors={[COLORS.WHITE, COLORS.CEMENT_WHITE, COLORS.WHITE]}
            style={{ flex: 1, height: 40, width: 100 }}
            start={{
              x: -1,
              y: 0.5,
            }}
            end={{
              x: 2,
              y: 0.5,
            }}
          />
        </Animated.View>
      </View>
    </View>
  );
};
