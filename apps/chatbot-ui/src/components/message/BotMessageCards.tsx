import { ScrollView } from 'react-native-gesture-handler';
import {
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  Pressable,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { CARDS_WIDTH_MAP } from '../../const';
import { DestinationCard, ItineraryOptionCard, TransportCard } from './cards';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { InfoCard } from './cards/InfoCard';
import { trackOmnitureClickEvent } from '../../native/omniture';
import { TextCard } from './cards/TextCard';
import { openDeepLink } from '../../native/deepLinks';
import { ModalSlot } from '../../screens/ModalProvider';
import {
  LobCards,
  shouldShowTransition,
  TransitionOverlay,
} from './transition-modal/TransitionOverlay';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { ViewAllArrowIcon } from '../../assets/ViewAllArrowIcon';
import { TouchableFeedback } from '../base/TouchableFeeback';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
  trackMediaClicked,
} from '../../native/tracking/pdt';
import { ImageView } from '../image/ImageView';
import { ShadowedView } from 'react-native-fast-shadow';
import TravelDetailCard, {
  TravelDetailDataOnCardClick,
} from './cards/TravelDetailCard';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import { useMessageStore } from '../../store/messages';
import { canWriteToConversation } from '../../utils/storeUtils';
import MyTripsCard from './cards/MyTripsCard';
import MyTripsTransportCard from './cards/MyTripsTransportCard';
import { DocumentCard } from './cards/DocumentCard';
import { useAppStateStore } from '../../store/app';
import { FlightOptionsCard } from './cards/FlightOptionsCard';
import { getPlatform } from '../../utils/getPlatform';
import { ChevronRight } from '../../assets/ChevronRight';
import { VideoCard } from './PackageCard/VideoCard';
import { useBackAction } from '../../utils/useBackHandler';
import { Analytics, TrackingEvent } from '../../analytics';

const isValidUrl = (string: string): boolean => {
  try {
    // Check if the string starts with http or https
    const hasValidProtocol = /^https?:\/\//.test(string);
    if (!hasValidProtocol) {
      return false;
    }

    // Try to create URL to validate format
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
};
function Header({
  title,
  subTitle,
  icon,
}: {
  title: string;
  subTitle?: string | null;
  icon: string;
}) {
  if (!title || !icon) {
    return null;
  }
  return (
    <View style={styles.headerContainer}>
      {icon && isValidUrl(icon) && (
        <ImageView
          source={{ uri: icon }}
          style={styles.headerIcon}
          containerStyle={styles.headerIcon}
          showMmtPlaceholder={false}
        />
      )}
      <View style={styles.headerTextContainer}>
        <Text style={styles.headerTitle}>{title}</Text>
        {subTitle && <Text style={styles.headerSubTitle}>{subTitle}</Text>}
      </View>
    </View>
  );
}

function ViewAllButton({
  onPress,
  cardType,
  isScrollVertical,
}: {
  onPress: () => void;
  cardType?: string;
  isScrollVertical?: boolean;
}) {
  return (
    <TouchableFeedback
      onPress={onPress}
      containerStyle={styles.viewAllTouchable}
      style={[
        styles.viewAllContainer,
        cardType === 'itinerary-card' && {
          marginBottom: 12,
        },
      ]}
    >
      <ShadowedView
        style={[
          styles.viewAllWrapper,
          { flexDirection: isScrollVertical ? 'row' : 'column' },
        ]}
      >
        <Text style={styles.viewAllText}>{'View All'}</Text>
        <ViewAllArrowIcon width={24} height={24} />
      </ShadowedView>
    </TouchableFeedback>
  );
}
const GAP = 12;

const VERTICAL_CARDS = [
  'travel-detail-card',
  'mytrips-card',
  'mytrips-transport-card',
  'video-card',
];

export function BotMessageCards({
  card,
  msg,
  setVideoModalData,
}: {
  card: CardWidget;
  msg: Message;
  setVideoModalData?: SetVideoModalDataType;
}) {
  const { activeConversationId, conversationById } = useMessageStore();
  const { setCurrentView, setItineraryData } = useAppStateStore();
  const canOpenLink = canWriteToConversation();
  const conversation = conversationById[activeConversationId || 'draft'];
  const [activeIndex, setActiveIndex] = useState(0);
  const triggeredScrollTrackEvent = useRef<boolean>(false);
  const triggeredCardsShownEvent = useRef<boolean>(false);
  const scrollViewRef = useRef<ScrollView>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const _platform = getPlatform();
  const onScroll = useCallback(() => {
    if (!triggeredScrollTrackEvent.current) {
      trackOmnitureClickEvent('CARDS_SCROLLED');
      triggeredScrollTrackEvent.current = true;
    }
  }, []);
  const [pendingTransitionToLob, setPendingTransitionToLob] =
    useState<LobCards | null>(null);
  const timer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const cancelNavigation = useCallback(() => {
    if (timer.current) {
      clearTimeout(timer.current);
      setPendingTransitionToLob(null);
      return true;
    }
    return false;
  }, []);
  useBackAction(cancelNavigation, { enabled: timer.current !== null });

  // Track cards shown event only once for itinerary cards
  useEffect(() => {
    const hasItineraryCards = card?.value?.templateInfo?.payload?.some(
      (cardItem) => cardItem.type === 'itinerary-card',
    );

    if (hasItineraryCards && !triggeredCardsShownEvent.current) {
      trackOmnitureClickEvent('CARDS_SHOWN');
      trackPDTEvent({
        eventName: eventNameSchema.CHAT_INTERACTED,
        eventType: eventTypeSchema.ACTION,
        eventValue: eventValueSchema.MEDIA_SHOWN_SYNC_ITINERARY,
      });
      triggeredCardsShownEvent.current = true;
    }
  }, [card?.value?.templateInfo?.payload]);

  useEffect(() => {
    return () => setPendingTransitionToLob(null);
  }, []);

  const scrollLeft = useCallback(() => {
    if (scrollViewRef.current && canScrollLeft) {
      const cardType = card?.value?.templateInfo?.payload[0]?.type;
      const width =
        cardType in CARDS_WIDTH_MAP
          ? CARDS_WIDTH_MAP[cardType as keyof typeof CARDS_WIDTH_MAP]
          : 0;
      const cardWidth = width - GAP;
      const scrollDistance = cardWidth + GAP;
      scrollViewRef.current.scrollTo({
        x: Math.max(0, (activeIndex - 1) * scrollDistance),
        animated: true,
      });
    }
  }, [
    scrollViewRef,
    canScrollLeft,
    activeIndex,
    card?.value?.templateInfo?.payload,
  ]);

  const scrollRight = useCallback(() => {
    if (scrollViewRef.current && canScrollRight) {
      const cardType = card?.value?.templateInfo?.payload[0]?.type;
      const width =
        cardType in CARDS_WIDTH_MAP
          ? CARDS_WIDTH_MAP[cardType as keyof typeof CARDS_WIDTH_MAP]
          : 0;
      const cardWidth = activeIndex === 0 ? width + 1.7 * GAP : width + GAP;
      const scrollDistance = cardWidth + GAP;

      // Account for View All CTA in max index calculation
      const totalCards = card.value.templateInfo.payload.length;
      const viewAll = card?.value?.templateInfo?.others?.view_all;
      const hasViewAll =
        viewAll &&
        typeof viewAll === 'object' &&
        viewAll !== null &&
        Object.keys(viewAll).length > 0;
      const maxIndex = hasViewAll ? totalCards : totalCards - 1; // Include View All index if it exists

      const nextIndex = Math.min(activeIndex + 1, maxIndex);
      scrollViewRef.current.scrollTo({
        x: nextIndex * scrollDistance,
        animated: true,
      });
    }
  }, [
    scrollViewRef,
    canScrollRight,
    activeIndex,
    card.value.templateInfo.payload,
    card?.value?.templateInfo?.others?.view_all,
  ]);

  if (!card?.value?.templateInfo?.payload?.length) {
    return null;
  }

  const openPageOnClick = (cardData: ItineraryOptionCardType) => {
    if (setItineraryData) {
      setItineraryData(cardData);
      setCurrentView?.('itinerary');
    }
    trackOmnitureClickEvent('CARD_CLICKED', {
      CONTENT_TYPE: `media_sync_itinerary`,
    });
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.MEDIA_CLICKED_SYNC_ITINERARY,
    });
  };

  const onCardClick = useCallback(
    (
      cardData:
        | DetailedCardType
        | TransportCardType
        | TravelDetailDataOnCardClick
        | MyTripsCardType
        | MyTripsTransportCardType
        | FlightOptionsCardType,
    ) => {
      if (!canOpenLink) {
        return;
      }

      const {
        data: { lob, cta_link },
      } = cardData;
      let link = cta_link;
      if (
        !link &&
        'type' in cardData &&
        (cardData.type === 'transport-card' ||
          cardData.type === 'mytrips-card' ||
          cardData.type === 'mytrips-transport-card') &&
        cardData.data.card_cta_link
      ) {
        link = cardData.data.card_cta_link;
      }
      if (!link) {
        return;
      }

      // Analytics
      trackOmnitureClickEvent('CARD_CLICKED', {
        CONTENT_TYPE: `media_${lob}`,
      });
      trackMediaClicked();
      Analytics.trackClickEvent(TrackingEvent.payload_CardClicked(lob));
      // If the lob is not known, open the deep link without any transition
      if (!shouldShowTransition(lob)) {
        openDeepLink(link as string);
        return;
      }

      setPendingTransitionToLob(lob);
      const setPendingTransitionToLobTimeout = () => {
        if (Platform.OS === 'web') {
          setTimeout(() => {
            setPendingTransitionToLob(null);
          }, 2000);
        } else {
          setPendingTransitionToLob(null);
        }
      };
      timer.current = setTimeout(() => {
        timer.current = null;
        openDeepLink(link as string);
        setPendingTransitionToLobTimeout();
      }, 2000);
    },
    [canOpenLink],
  );

  const { view_all = null, cards_header = null } =
    card?.value?.templateInfo?.others || {};
  const moveViewAllToBottom = view_all?.moveViewAllToBottom || true;
  const viewAllClick = useCallback(() => {
    if (!canOpenLink) {
      return;
    }
    trackOmnitureClickEvent('VIEW_MORE_CLICKED');
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.VIEW_MORE,
    });
    if (view_all?.link) {
      openDeepLink(view_all.link);
    }
  }, [view_all]);

  const cardType = card?.value?.templateInfo?.payload[0]?.type;
  const width =
    cardType in CARDS_WIDTH_MAP
      ? CARDS_WIDTH_MAP[cardType as keyof typeof CARDS_WIDTH_MAP]
      : 0;
  const cardWidth = width - GAP;
  const uiLayout = card?.value?.templateInfo?.uiLayout;
  const enableShadowWrapperException =
    card?.value?.templateInfo?.payload?.length > 0 &&
    (card.value.templateInfo.payload[0].type === 'mytrips-card' ||
      card.value.templateInfo.payload[0].type === 'mytrips-transport-card');

  const isScrollVertical =
    card?.value?.templateInfo?.payload?.length > 0 &&
    VERTICAL_CARDS.includes(card.value.templateInfo.payload[0].type);

  // Arrow visibility logic
  const totalCards = card.value.templateInfo.payload.length;
  const viewAllForArrows = card?.value?.templateInfo?.others?.view_all;
  const hasViewAll =
    viewAllForArrows &&
    typeof viewAllForArrows === 'object' &&
    viewAllForArrows !== null &&
    Object.keys(viewAllForArrows).length > 0;
  const totalItems = hasViewAll ? totalCards + 1 : totalCards;

  const shouldShowLeftArrow =
    !isScrollVertical &&
    _platform === 'desktop' &&
    totalItems > 1 &&
    canScrollLeft &&
    activeIndex > 0;

  const shouldShowRightArrow =
    !isScrollVertical &&
    _platform === 'desktop' &&
    totalItems > 1 &&
    canScrollRight &&
    (hasViewAll ? activeIndex < totalCards : activeIndex < totalCards - 1); // Show until View All is in focus, or until last card if no View All

  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const contentOffsetX = event.nativeEvent.contentOffset.x; // Horizontal scroll position
      const visibleIndex = card.value.templateInfo.payload.findIndex((_, index) => {
        const cardOffsetX = index * cardWidth + index * GAP; // Position of the card in the ScrollView (including the gap)
        const cardCenter = cardOffsetX + cardWidth / 2; // The center of the card

        // Check if the center of the card is within the visible threshold range (half of the card should be visible)
        return (
          contentOffsetX >= cardOffsetX - width / 2 && contentOffsetX <= cardCenter
        );
      });

      setActiveIndex(visibleIndex); // Update the active index based on the most visible card

      // Update scroll button states for horizontal scrolling
      if (!isScrollVertical) {
        const { contentSize, layoutMeasurement, contentOffset } = event.nativeEvent;
        setCanScrollLeft(contentOffset.x > 0);
        setCanScrollRight(
          contentOffset.x < contentSize.width - layoutMeasurement.width,
        );
      }
    },
    [card.value.templateInfo.payload, cardWidth, width, isScrollVertical],
  );

  const containerStyle = (() => {
    const type = card.value.templateInfo.payload?.[0]?.type;
    // if (type === 'itinerary-card') {
    //   return {};
    // }
    if (!enableShadowWrapperException && isScrollVertical) {
      return styles.verticalContainer;
    } else {
      return styles.container;
    }
  })();
  const rootStyleByType = (() => {
    const type = card.value.templateInfo.payload?.[0]?.type;
    // if (type === 'itinerary-card') {
    //   return {};
    // }
    return styles.paddingBox;
  })();
  return (
    <View style={rootStyleByType}>
      <ShadowedView style={containerStyle}>
        {cards_header && (enableShadowWrapperException || !isScrollVertical) && (
          <Header
            title={cards_header?.title}
            icon={cards_header?.icon}
            subTitle={cards_header?.subTitle}
          />
        )}
        <View style={styles.scrollContainer}>
          {/* Left Arrow */}
          {shouldShowLeftArrow && (
            <Pressable
              onPress={scrollLeft}
              style={styles.leftArrow}
              android_disableSound={true}
              {...(Platform.OS === 'web' && {
                focusable: false,
                'data-focusable': false,
              })}
            >
              <View style={styles.arrowContainer}>
                <View style={{ transform: [{ rotate: '180deg' }] }}>
                  <ChevronRight width={20} height={20} color={COLORS.BLUE_VAR_1} />
                </View>
              </View>
            </Pressable>
          )}

          <ScrollView
            ref={scrollViewRef}
            horizontal={!isScrollVertical}
            onScrollBeginDrag={onScroll}
            scrollEventThrottle={
              100 /* this is not used for anything critical, lesser throttle is overkill */
            }
            showsHorizontalScrollIndicator={false}
            overScrollMode={'always'}
            bounces={false}
            onScroll={handleScroll}
            nativeID="bot-message-cards-scrollview"
            style={[
              styles.scrollView,
              _platform === 'desktop' &&
                Platform.OS === 'web' &&
                ({
                  scrollbarWidth: 'none', // Firefox
                  msOverflowStyle: 'none', // Internet Explorer 10+
                } as any),
            ]}
            // pagingEnabled
          >
            <View
              style={{
                flexDirection: !isScrollVertical ? 'row' : 'column',
                gap: GAP,
                alignItems: 'stretch',
                // paddingRight: BOT_MSG_START_X * 2,
                marginHorizontal: 16,
                marginTop: 2,
                ...(Platform.OS === 'web' && { overflow: 'scroll' }),
              }}
            >
              {card.value.templateInfo.payload.map((cardItem, index) => {
                if (
                  cardItem.type === 'detailed-card' ||
                  cardItem.type === 'simple-card'
                ) {
                  return (
                    <DestinationCard
                      onCardClick={onCardClick}
                      key={index}
                      msg={msg}
                      shouldApplyGradient={uiLayout?.shouldApplyGradient}
                      {...cardItem}
                    />
                  );
                }
                if (cardItem.type === 'travel-detail-card') {
                  return (
                    <TravelDetailCard
                      onCardClick={onCardClick}
                      key={index}
                      msg={msg}
                      {...cardItem}
                    />
                  );
                }
                if (cardItem.type === 'transport-card') {
                  return (
                    <TransportCard
                      onCardClick={onCardClick}
                      key={index}
                      msg={msg}
                      {...cardItem}
                    />
                  );
                }
                if (cardItem.type === 'mytrips-card') {
                  return (
                    <MyTripsCard
                      onCardClick={onCardClick}
                      key={index}
                      msg={msg}
                      {...cardItem}
                    />
                  );
                }

                if (cardItem.type === 'mytrips-transport-card') {
                  return (
                    <MyTripsTransportCard
                      onCardClick={onCardClick}
                      key={index}
                      msg={msg}
                      {...cardItem}
                    />
                  );
                }

                if (cardItem.type === 'text-card') {
                  return <TextCard key={index} msg={msg} {...cardItem} />;
                }
                if (
                  cardItem.type === 'template_3' ||
                  cardItem.type === 'info-card'
                ) {
                  return <InfoCard key={index} msg={msg} {...cardItem} />;
                }
                if (cardItem.type === 'documents-card') {
                  return (
                    <DocumentCard
                      key={index}
                      msg={msg}
                      onCardClick={onCardClick}
                      {...cardItem}
                    />
                  );
                }
                if (cardItem.type === 'itinerary-card') {
                  return (
                    <ItineraryOptionCard
                      onCardClick={openPageOnClick}
                      key={index}
                      msg={msg}
                      {...cardItem}
                    />
                  );
                }
                if (cardItem.type === 'rt-transport-card') {
                  return (
                    <FlightOptionsCard
                      onCardClick={onCardClick}
                      key={index}
                      msg={msg}
                      {...cardItem}
                    />
                  );
                }
                if (cardItem.type === 'video-card') {
                  return (
                    <VideoCard
                      setVideoModalData={setVideoModalData}
                      key={index}
                      msg={msg}
                      media={cardItem.data}
                      containerStyle={{
                        shadowOffset: { width: 0, height: 0 },
                        shadowOpacity: 0,
                        shadowRadius: 0,
                        marginBottom: 0,
                      }}
                      {...cardItem}
                    />
                  );
                }
                return null;
              })}
              {hasViewAll && !moveViewAllToBottom && !isScrollVertical && (
                <ViewAllButton
                  onPress={viewAllClick}
                  cardType={cardType}
                  isScrollVertical={isScrollVertical}
                />
              )}
            </View>
          </ScrollView>

          {/* Right Arrow */}
          {shouldShowRightArrow && (
            <Pressable
              onPress={scrollRight}
              style={styles.rightArrow}
              android_disableSound={true}
              {...(Platform.OS === 'web' && {
                focusable: false,
                'data-focusable': false,
              })}
            >
              <View style={styles.arrowContainer}>
                <ChevronRight width={20} height={20} color={COLORS.BLUE_VAR_1} />
              </View>
            </Pressable>
          )}
        </View>
        {width && !isScrollVertical && card.value.templateInfo.payload.length > 1 ? (
          <DotIndicator
            payload={card.value.templateInfo.payload}
            activeIndex={activeIndex}
          />
        ) : null}
        {hasViewAll && (isScrollVertical || moveViewAllToBottom) && (
          <View
            style={{
              paddingTop: 8,
              paddingHorizontal: !isScrollVertical ? 8 : 16,
            }}
          >
            <ViewAllButton
              onPress={viewAllClick}
              cardType={cardType}
              isScrollVertical={true}
            />
          </View>
        )}
      </ShadowedView>
      {pendingTransitionToLob && (
        <ModalSlot>
          <TransitionOverlay lob={pendingTransitionToLob} />
        </ModalSlot>
      )}
    </View>
  );
}

const AnimatedDot = React.memo(
  ({
    index,
    sharedActiveIndex,
    isActive,
  }: {
    index: number;
    sharedActiveIndex: Animated.SharedValue<number>;
    isActive: boolean;
  }) => {
    const animatedStyle = useAnimatedStyle(() => {
      const active = sharedActiveIndex.value === index;
      return {
        transform: [{ scale: withSpring(active ? 1.4 : 1) }],
      };
    }, [sharedActiveIndex]);

    return (
      <Animated.View
        style={[styles.dot, isActive ? styles.activeDot : null, animatedStyle]}
      />
    );
  },
);

const DotIndicator = React.memo(
  ({
    payload,
    activeIndex,
  }: {
    payload: CardWidget['value']['templateInfo']['payload'];
    activeIndex: number;
  }) => {
    const sharedActiveIndex = useSharedValue(activeIndex);

    useEffect(() => {
      sharedActiveIndex.value = activeIndex;
    }, [activeIndex, sharedActiveIndex]);

    const containerStyle = useMemo(() => {
      const type = payload?.[0]?.type;
      if (type === 'itinerary-card') {
        return { ...styles.dotContainer, paddingTop: 0 };
      }
      return styles.dotContainer;
    }, [payload]);

    return (
      <View style={containerStyle}>
        {payload.map((_, index) => (
          <AnimatedDot
            key={index}
            index={index}
            sharedActiveIndex={sharedActiveIndex}
            isActive={index === activeIndex}
          />
        ))}
      </View>
    );
  },
);

const styles = StyleSheet.create({
  paddingBox: {
    padding: 5,
    display: 'flex',
    justifyContent: 'center',
    width: '100%',
  },
  verticalContainer: {
    width: '100%',
  },
  container: {
    marginHorizontal: 10,
    paddingVertical: 16,
    borderWidth: 1,
    borderColor: COLORS.WHITE,
    borderRadius: 24,
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    // elevation: 2,
    backgroundColor: COLORS.GREY_VAR_8,
    // overflow: 'hidden',
    // width: 'auto',
    // marginVertical: 2,
  },
  viewAllWrapper: {
    flex: 1,
    padding: 8,
    paddingBottom: 10,
    gap: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.GREY_VAR_8,
    borderRadius: 16,
    shadowColor: 'rgba(0, 0, 0, 0.70)',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.15,
    shadowRadius: 5,
    // elevation: 1,
  },
  viewAllContainer: {
    flex: 1,
    borderRadius: 16,
    padding: 1,
  },
  headerContainer: {
    gap: 8,
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 16,
    marginBottom: 12,
  },
  headerTextContainer: {
    gap: 2,
  },
  headerTitle: {
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_900,
    lineHeight: 16,
    color: COLORS.BLACK,
  },
  headerSubTitle: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_700,
    lineHeight: 14,
  },
  headerIcon: {
    height: 32,
    width: 32,
    borderRadius: 50,
  },

  dotContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 16,
  },
  dot: {
    width: 6,
    height: 6,
    borderRadius: 50,
    backgroundColor: COLORS.GREY_VAR_10,
    margin: 5,
  },
  activeDot: {
    backgroundColor: COLORS.BLUE_VAR_1,
  },
  scrollContainer: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    overflow: 'visible',
  },
  scrollView: {
    flex: 1,
  },
  leftArrow: {
    position: 'absolute',
    left: -10,
    top: '50%',
    zIndex: 999,
    backgroundColor: COLORS.WHITE,
    borderRadius: 24,
    padding: 12,
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
    transform: [{ translateY: -24 }], // Half of the button height to center vertically
    // Remove focus/hover outlines and borders
    ...(Platform.OS === 'web' &&
      ({
        outline: 'none',
        border: 'none',
        ':focus': {
          outline: 'none',
          border: 'none',
        },
        ':hover': {
          outline: 'none',
          border: 'none',
        },
      } as any)),
  },
  rightArrow: {
    position: 'absolute',
    right: -10,
    top: '50%',
    zIndex: 999,
    backgroundColor: COLORS.WHITE,
    borderRadius: 24,
    padding: 12,
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
    transform: [{ translateY: -24 }], // Half of the button height to center vertically
    // Remove focus/hover outlines and borders
    ...(Platform.OS === 'web' &&
      ({
        outline: 'none',
        border: 'none',
        ':focus': {
          outline: 'none',
          border: 'none',
        },
        ':hover': {
          outline: 'none',
          border: 'none',
        },
      } as any)),
  },
  arrowContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 20,
    height: 20,
    outline: 'none',
    border: 'none',
  },
  viewAllTouchable: {
    borderRadius: 16,
    ...(Platform.OS !== 'web' && { flex: 1 }),
    backgroundColor: COLORS.GREY_VAR_8,
    // padding: 2,
    paddingVertical: 2,
  },
  viewAllText: {
    color: COLORS.BLUE_VAR_1,
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_700,
  },
});
