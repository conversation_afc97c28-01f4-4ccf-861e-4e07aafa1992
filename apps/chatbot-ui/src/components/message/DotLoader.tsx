import React from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withRepeat,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import { COLORS } from '../../constants/globalStyles';
import LinearGradient from 'react-native-linear-gradient';

const Dot = ({ index, total }: { index: number; total: number }) => {
  const SCALE_START = 1;
  const SCALE_END = 0;
  const scaleY = useSharedValue(SCALE_START);

  React.useEffect(() => {
    const CASCADE_FACTOR = 1;
    const ANIMATION_DURATION = 300;
    const startDelay = Math.round(index * CASCADE_FACTOR * ANIMATION_DURATION);
    scaleY.value = withDelay(
      startDelay,
      withRepeat(
        withSequence(
          withTiming(SCALE_END, {
            duration: ANIMATION_DURATION,
            easing: Easing.inOut(Easing.ease),
          }),
          withTiming(SCALE_START, {
            duration: ANIMATION_DURATION,
            easing: Easing.inOut(Easing.ease),
          }),
          withTiming(SCALE_START, {
            duration: ANIMATION_DURATION * CASCADE_FACTOR * (total - 1), // end delay
            easing: Easing.inOut(Easing.ease),
          }),
        ),
        -1,
      ),
    );
  }, [scaleY, index]);

  const animatedStyle = useAnimatedStyle(
    (): ViewStyle => ({
      transform: [{ translateY: 4 }, { scaleY: scaleY.value }, { translateY: -4 }],
    }),
		[scaleY]
  );

  return (
    <Animated.View style={[styles.dot, animatedStyle]}>
      <View style={StyleSheet.absoluteFill}>
        <LinearGradient
          colors={['#51AFE6', '#355FF2', '#11287A']}
          style={[StyleSheet.absoluteFill, { borderRadius: 50 }]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </View>
    </Animated.View>
  );
};

export const LoadingDots = () => {
  return (
    <View style={styles.container}>
      <Dot index={0} total={3} />
      <Dot index={1} total={3} />
      <Dot index={2} total={3} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-start',
    backgroundColor: COLORS.GREY_VAR_8,
    borderRadius: 8,
    marginTop: -12,
    padding: 12,
    shadowColor: COLORS.GREY_VAR_1,
    shadowOpacity: 0.1,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: COLORS.BLUE_VAR_3,
    marginHorizontal: 5,
  },
});
