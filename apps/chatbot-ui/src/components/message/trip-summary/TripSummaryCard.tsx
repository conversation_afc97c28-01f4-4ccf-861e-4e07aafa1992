import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableHighlight,
  useWindowDimensions,
  View,
} from 'react-native';
import { COLORS, FONTS, Z_INDEX } from '../../../constants/globalStyles';
import { ItineraryIcon } from '../../../assets/ItineraryIcon';
import { LinearGradient } from 'react-native-linear-gradient';
import { RightIcon } from '../../../assets/RightIcon';
import { TouchableFeedback } from '../../base/TouchableFeeback';
import { ModalSlot } from '../../../screens/ModalProvider';
import { ScrollView } from 'react-native-gesture-handler';
import { CloseIcon } from '../../../assets/CloseIcon';
import {
  isCardWidgetType,
  isTextWidgetType,
  useMessageStore,
} from '../../../store/messages';
import { defaultDarkColorPalette, MarkdownText } from '../MarkdownText';
import { BotMessageCards } from '../BotMessageCards';
import { useQuery } from 'react-query';
import {
  fetchTripSummary,
  TripSummarySuccessResponse,
} from '../../../network/api/conversationApi';
import Animated, { FadeIn, FadeOut, SlideInDown } from 'react-native-reanimated';
import { useBackAction } from '../../../utils/useBackHandler';
import { Toast } from '../../toast';
import { trackOmnitureClickEvent } from '../../../native/omniture';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../../native/tracking/pdt';

export const TripSummaryCard = memo(
  ({ ctaText }: { ctaText?: string | undefined | null }) => {
    const [showTripSummary, setShowTripSummary] = useState<boolean>(false);
    const { activeConversationId } = useMessageStore();
    const { isLoading, isError, data } = useQuery({
      queryKey: ['tripSummary', activeConversationId],
      enabled: showTripSummary,
      cacheTime: 0,
      queryFn: () =>
        fetchTripSummary({
          conversationId: activeConversationId as string,
        }),
    });

    const showModal = useMemo(
      () => data?.success && showTripSummary,
      [data, showTripSummary],
    );
    useEffect(() => {
      const responseErrpr = typeof data !== 'undefined' && !data.success;
      if (showTripSummary && (isError || responseErrpr)) {
        setShowTripSummary(false);
        Toast.show("Couldn't fetch trip summary");
        return;
      }
    }, [showTripSummary, isLoading, isError, data]);

    useEffect(() => {
      trackOmnitureClickEvent('ITINERARY_SHOWN');
      trackPDTEvent({
        eventName: eventNameSchema.CHAT_INTERACTED,
        eventType: eventTypeSchema.ACTION,
        eventValue: eventValueSchema.ITINERARY_SHOWN,
      });
    }, []);

    const onCardClick = useCallback(() => {
      setShowTripSummary(true);
      trackOmnitureClickEvent('ITINERARY_CLICKED');
    }, []);

    return (
      <View
        style={{
          flexDirection: 'row',
          marginVertical: 24,
        }}
      >
        <TouchableFeedback
          disabled={showTripSummary}
          onPress={onCardClick}
          style={{
            padding: 8,
            height: 36,
            margin: 0,
            alignItems: 'center',
            flexDirection: 'row',
          }}
          activeOpacity={0.8}
          containerStyle={styles.container}
        >
          <LinearGradient
            useAngle
            colors={['#51AFE6', '#355FF2', '#11287A']}
            style={styles.outerBorder}
          />
          <View style={styles.innerBorder} />
          {isLoading ? (
            <View style={styles.loaderContainer}>
              <ActivityIndicator size={20} color={COLORS.PURPLE_GRAD_START} />
            </View>
          ) : (
            <View style={styles.iconContainer}>
              <ItineraryIcon height={22} width={22} />
            </View>
          )}
          <Text style={styles.text}>{ctaText || 'Your trip itinerary'}</Text>
          <RightIcon fill={'#3944A3'} />
        </TouchableFeedback>
        {showModal && (
          <ModalSlot>
            <TripSummaryModal
              onClose={() => setShowTripSummary(false)}
              data={data as TripSummarySuccessResponse}
            />
          </ModalSlot>
        )}
      </View>
    );
  },
);

type TripSummaryModalProps = {
  data: TripSummarySuccessResponse;
  onClose: () => void;
};

function TripSummaryModal({ data, onClose }: TripSummaryModalProps) {
  useBackAction(function () {
    onClose();
    return true;
  });
  const { height } = useWindowDimensions();
  const {
    displayData: { overlay_title, overlay_subtitle },
    content,
  } = data.data;
  return (
    <Animated.View
      entering={FadeIn}
      exiting={FadeOut}
      style={{
        ...StyleSheet.absoluteFillObject,
        flexDirection: 'column',
        zIndex: Z_INDEX.MODAL,
        backgroundColor: '#0004',
        alignItems: 'flex-end',
        justifyContent: 'flex-end',
      }}
    >
      <Animated.View
        entering={SlideInDown.duration(300)}
        exiting={SlideInDown.duration(300)}
        style={[styles.modalInnerContainer, { maxHeight: height * 0.6 }]}
      >
        {/*Header*/}

        <View style={styles.modalHeader}>
          <View style={styles.modalIconContainer}>
            <ItineraryIcon height={32} width={32} />
          </View>
          <View style={styles.modalHeaderTextsContainer}>
            <View
              style={{
                flexDirection: 'row',
                gap: 16,
                flex: 1,
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Text style={styles.modalHeaderTitle}>{overlay_title}</Text>
              <TouchableHighlight
                underlayColor={'transparent'}
                activeOpacity={1}
                onPress={onClose}
              >
                <CloseIcon />
              </TouchableHighlight>
            </View>
            {overlay_subtitle && (
              <Text style={styles.modalHeaderSubtitle}>{overlay_subtitle}</Text>
            )}
          </View>
        </View>
        {/*Content*/}
        <ScrollView>
          <View style={{ flex: 1, flexDirection: 'column', paddingBottom: 16 }}>
            {(content || []).map((msgItem: MessageWidget, index: number) => {
              if (isTextWidgetType(msgItem)) {
                return (
                  <View
                    style={{
                      marginHorizontal: 16,
                      marginVertical: 8,
                      paddingHorizontal: 8,
                      borderRadius: 8,
                      borderWidth: 1,
                      borderColor: COLORS.BLACK_VAR_4,
                    }}
                  >
                    <MarkdownText
                      color={defaultDarkColorPalette}
                      text={msgItem.value}
                    />
                  </View>
                );
              }
              if (isCardWidgetType(msgItem)) {
                return (
                  <BotMessageCards key={index} card={msgItem} msg={data.data} />
                );
              }
              return null;
            })}
          </View>
        </ScrollView>
      </Animated.View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: 36,
    position: 'relative',
    backgroundColor: COLORS.WHITE,
    borderRadius: 18,
    alignItems: 'center',
    overflow: 'hidden',
    marginHorizontal: 16,
  },
  outerBorder: {
    ...StyleSheet.absoluteFillObject,
    borderRadius: 18,
    overflow: 'hidden',
  },
  innerBorder: {
    position: 'absolute',
    top: 2,
    left: 2,
    right: 2,
    bottom: 2,
    borderRadius: 16,
    overflow: 'hidden',
    alignItems: 'center',
    flexDirection: 'row',

    backgroundColor: COLORS.WHITE,
  },
  loaderContainer: {
    width: 26,
    height: 26,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    width: 26,
    height: 26,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#ECE9FF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    color: '#2B379D',
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_700,
    marginLeft: 8,
  },
  modalInnerContainer: {
    flex: 1,
    width: '100%',
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  modalIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    overflow: 'hidden',
    backgroundColor: '#ECE9FF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalHeaderTextsContainer: {
    alignItems: 'center',
    flexDirection: 'column',
    flex: 1,
  },
  modalHeaderTitle: {
    fontSize: 16,
    flex: 1,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.TEXT_HIGH_EMPHASIS,
    marginBottom: 2,
  },
  modalHeaderSubtitle: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_LOW_EMPHASIS,
  },
});
