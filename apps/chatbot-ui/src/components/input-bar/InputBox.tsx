import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  Keyboard,
  NativeSyntheticEvent,
  Platform,
  Pressable,
  StyleSheet,
  TextInputContentSizeChangeEventData,
  View,
} from 'react-native';
import { BottomSheetTextInput } from '@gorhom/bottom-sheet';
import {
  useInputStateStore,
  useMessageAction,
  useMessageStore,
} from '../../store/messages';
import { useAppStateStore } from '../../store/app';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { MESSAGES } from '../../constants/messages';
import {
  updateChatViewData,
  useConfigSettings,
} from '../../store/messages/newChatView';
import Animated, {
  SlideInDown,
  SlideOutDown,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import GradientText from '../base/GradientText';
import { config } from '../../config';
import { Analytics, TrackingEvent } from '../../analytics';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ShadowedView } from 'react-native-fast-shadow';
import { canWriteToConversation } from '../../utils/storeUtils';
import ModeCTA from './modeCTA';
import { useModeState } from '../../hooks/useModeState';
import { ReplyPreviewCard } from '../message/cards/ReplyPreviewCard';
import { ContextMenuBar } from './ContextMenuBar';
import { getPlatform } from '../../utils/getPlatform';
import {
  CONTEXT_MENU_ADDITIONAL_HEIGHT,
  INPUT_BOX_BOTTOM_PADDING,
  INPUT_BOX_HEIGHT,
  INPUT_BOX_HEIGHT_WITHOUT_MODES,
  INPUT_BOX_PLACEHOLDER,
  INPUT_VIEW_HEIGHT,
  REPLY_PREVIEW_ADDITIONAL_HEIGHT,
} from '../../const';
import { TextInput as GestureHandlerTextInput } from 'react-native-gesture-handler';
import { MicButton, SendButton } from './InputIcons';
import { hasAudioStreamModule } from '../../native/audio/audio-native-module';
import { trackOmnitureClickEvent } from '../../native/omniture';
import { audioStreamLogger } from '../../native/audio/audio-stream-logger';
import { VoiceOverlay } from './VoiceOverlay';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import { useMicCoachmark } from '../../hooks/useMicCoachmark';
import { SpeakerCoachmark } from '../coachmark/SpeakerCoachmark';
import { useDebounceCallback } from '../../utils/useDebounceCallback';
import useGetFeatureFlags from '../../hooks/useGetFeatureFlags';

type InputBoxProps = {
  trackingKey: string;
};

// Centralized height calculation function
const calculateTotalInputAreaHeight = (
  inputFieldHeight: number,
  sessionContextType?: SessionContextType,
  canShowModes?: boolean,
): number => {
  let totalHeight =
    (canShowModes ? INPUT_BOX_HEIGHT : INPUT_BOX_HEIGHT_WITHOUT_MODES) +
    inputFieldHeight -
    INPUT_VIEW_HEIGHT;

  // Calculate max height based on actual input field constraints
  // Since input field can grow to INPUT_VIEW_HEIGHT * 3.5, we need to account for that
  const maxInputFieldHeight = INPUT_VIEW_HEIGHT * 3.5;
  const maxPossibleHeight = canShowModes
    ? INPUT_BOX_HEIGHT + (maxInputFieldHeight - INPUT_VIEW_HEIGHT) + 30
    : INPUT_BOX_HEIGHT_WITHOUT_MODES +
    (maxInputFieldHeight - INPUT_VIEW_HEIGHT) +
    20;

  totalHeight = Math.min(totalHeight, maxPossibleHeight);

  // Add context menu height based on type
  switch (sessionContextType) {
    case 'REPLY':
      totalHeight += REPLY_PREVIEW_ADDITIONAL_HEIGHT;
      break;
    case 'CONTEXT':
      totalHeight += CONTEXT_MENU_ADDITIONAL_HEIGHT;
      break;
    default:
      // No additional height
      break;
  }

  return totalHeight;
};

export const Modes = ({ modes }: { modes: Mode[] }) => {
  return (
    <View style={styles.modesContainer}>
      {modes.map((mode) => {
        if (mode.visible) {
          return <ModeCTA mode={mode} key={mode.id} />;
        }
        return null;
      })}
    </View>
  );
};

const ContextMenu = ({
  sessionContext,
  onClose,
  inputBoxHeight,
  hide,
}: {
  sessionContext: SessionContext | undefined;
  onClose: (type: SessionContextType) => void;
  inputBoxHeight: number;
  hide: boolean;
}) => {
  const { setCurrentView } = useAppStateStore();

  useEffect(() => {
    if (sessionContext && !hide && sessionContext?.type === 'CONTEXT') {
      trackOmnitureClickEvent('CONTEXT_ITINERARY', {
        CONTENT_TYPE: `media_${sessionContext?.data?.id}`,
      });
      trackPDTEvent({
        eventName: eventNameSchema.CHAT_INTERACTED,
        eventType: eventTypeSchema.ACTION,
        eventValue: eventValueSchema.CONTEXT_ITINERARY,
      });
    }
  }, [sessionContext?.type, hide, sessionContext?.data?.id, sessionContext]);

  function handleContextMenuClose(type: SessionContextType) {
    onClose(type);
    if (type === 'CONTEXT') {
      trackOmnitureClickEvent('ITINERARY_CONTEXT_REMOVED');
      trackPDTEvent({
        eventName: eventNameSchema.CHAT_INTERACTED,
        eventType: eventTypeSchema.ACTION,
        eventValue: eventValueSchema.ITINERARY_CONTEXT_REMOVED,
      });
    }
  }
  if (!sessionContext || hide) {
    return null;
  }

  switch (sessionContext?.type) {
    case 'REPLY':
      return (
        <ReplyPreviewCard
          title={sessionContext.data.title}
          subtitle={
            'subTitle' in sessionContext.data
              ? sessionContext.data.subTitle
              : sessionContext.data.sub_title || ''
          }
          inputBoxHeight={inputBoxHeight}
          onClose={() => onClose(sessionContext.type)}
        />
      );
    case 'CONTEXT':
      return (
        <ContextMenuBar
          title={sessionContext.data.title}
          onClose={() => handleContextMenuClose(sessionContext.type)}
          inputBoxHeight={inputBoxHeight}
          onPress={() => {
            setCurrentView?.('trips');
            trackOmnitureClickEvent('ITINERARY_PLACEHOLDER_CLICKED');
            trackPDTEvent({
              eventName: eventNameSchema.CHAT_INTERACTED,
              eventType: eventTypeSchema.ACTION,
              eventValue: eventValueSchema.ITINERARY_PLACEHOLDER_CLICKED,
            });
          }}
        />
      );
    default:
      return null;
  }
};

export const InputBox: React.FC<InputBoxProps> = ({ trackingKey }) => {
  const {
    inputStateByConversationId,
    updateInputStateByConversationId,
    disableInput,
    setTotalInputAreaHeight,
    totalInputAreaHeight,
  } = useInputStateStore();
  const {
    activeConversationId,
    conversationById,
    newChatRequested,
    setNewChatRequested,
    updateConversationById,
  } = useMessageStore();
  const { setCurrentView } = useAppStateStore();
  const id = activeConversationId || 'draft';

  // Local state for input field height only
  const [inputFieldHeight, setInputFieldHeight] = useState(INPUT_VIEW_HEIGHT);

  const { data, isFetching: isConfigApiFetching } = useConfigSettings();

  //  disable inputbar
  const isLoading = conversationById[id]?.isLoading || false;
  const disableInputBar =
    !newChatRequested && (disableInput || isLoading || isConfigApiFetching);

  const conversation = useMessageStore(
    (state) => state.conversationById[activeConversationId || 'draft'],
  );

  const hideInput =
    conversation && 'hideInput' in conversation ? conversation.hideInput : false;
  const hideInputBar = hideInput;

  const [showVoiceOverlay, setShowVoiceOverlay] = useState(false);

  const enableMicInput =
    !isConfigApiFetching &&
    (data?.featureConfig?.speechToText ?? true) &&
    hasAudioStreamModule();

  const inputRef = React.useRef('');
  const { modes, canShowModes } = useModeState();

  // Mic coachmark functionality
  const {
    showFirstCoachmark,
    showSecondCoachmark,
    onCloseFirstCoachmark,
    onCloseSecondCoachmark,
    onMicUsed
  } = useMicCoachmark();

  const { showMicCoachmark } = useGetFeatureFlags();

  const inputViewRef = useRef<GestureHandlerTextInput>(null);
  const text = inputStateByConversationId[id] || '';

  const { sendMessage, publishEventOnly } = useMessageAction();
  const { currentView } = useAppStateStore();
  const onDefaultMicClicked = useCallback(() => {
    inputViewRef.current?.blur();
    trackOmnitureClickEvent('VOICE_INPUT_CLICKED');
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.VOICE_INPUT_CLICKED,
    });

    // Close any open coachmarks when mic is clicked
    if (showFirstCoachmark) {
      onCloseFirstCoachmark();
    }
    if (showSecondCoachmark) {
      onCloseSecondCoachmark();
    }

    onMicUsed();

    setShowVoiceOverlay(true);
  }, [onMicUsed, showFirstCoachmark, showSecondCoachmark, onCloseFirstCoachmark, onCloseSecondCoachmark]);
  const [debouncedOnDefaultMicClicked] = useDebounceCallback(
    onDefaultMicClicked,
    1000,
  );
  const onContentSizeChanged = useCallback(
    (event: NativeSyntheticEvent<TextInputContentSizeChangeEventData>) => {
      let contentHeight = event.nativeEvent.contentSize.height;
      if (Platform.OS === 'ios') {
        contentHeight += 16;
      }
      const newHeight = Math.min(
        INPUT_VIEW_HEIGHT * 3.5,
        Math.max(INPUT_VIEW_HEIGHT, contentHeight),
      );
      setInputFieldHeight(newHeight);
    },
    [],
  );
  const isDesktopOrPwa = getPlatform() === 'pwa' || getPlatform() === 'desktop';

  // Handle web input height changes
  const handleWebInputChange = useCallback((e: any) => {
    if (Platform.OS === 'web') {
      const target = e.target;
      if (target) {
        // Reset height to auto to get proper scrollHeight
        target.style.height = 'auto';
        const newHeight = Math.min(
          INPUT_VIEW_HEIGHT * 3.5,
          Math.max(INPUT_VIEW_HEIGHT, target.scrollHeight),
        );
        setInputFieldHeight?.(newHeight);
      }
    }
  }, []);

  const onTextChange = (text: string) => {
    inputRef.current = text;
    updateInputStateByConversationId(id, text);

    // Reset height if text is empty
    if (!text) {
      if (isDesktopOrPwa) {
        setInputFieldHeight?.(INPUT_VIEW_HEIGHT);
      } else {
        updateInputStateByConversationId(id, '');
      }
    }
  };

  const onSendClick = useCallback(() => {
    //close keyboard when msg is sent
    Keyboard.dismiss();
    setInputFieldHeight?.(INPUT_VIEW_HEIGHT);
    sendMessage();
    Analytics.trackClickEvent(TrackingEvent.payload_SendMessageClicked());
  }, [sendMessage]);

  // Handle keyboard events for web
  const handleKeyPress = useCallback(
    (e: any) => {
      if (Platform.OS === 'web' && e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        onSendClick();
      }
    },
    [onSendClick],
  );

  const {
    messages = [],
    awaitedUserMessage,
    isNewChatInputCtaEnabled,
  } = conversationById[id] || {};
  let shouldShowInputBar = canWriteToConversation() && !hideInputBar;

  // conditions for showing disclaimer
  const msgIsEmpty = messages.length === 0;
  const showDisclaimer = newChatRequested || !msgIsEmpty;
  // const shouldAutoPlayPlaceholder =
  //   id === 'draft' && !awaitedUserMessage && !newChatRequested;
  const disclaimerOpacity = useSharedValue(showDisclaimer ? 1 : 0);

  useEffect(() => {
    disclaimerOpacity.value = withTiming(showDisclaimer ? 1 : 0, { duration: 600 });
  }, [disclaimerOpacity, showDisclaimer]);

  // TODO: [RNW] Check and find an alternative solution
  const { bottom: bottomInset } = useSafeAreaInsets();

  // Current View as History
  const isHistoryView = currentView === 'history';
  const isMessageScreen = currentView === 'chat';

  if (!shouldShowInputBar && !isMessageScreen) {
    shouldShowInputBar = true;
  }

  const onStartNewChatClick = () => {
    updateChatViewData({
      content: { hideInput: data?.content?.hideInput },
    });
    updateConversationById(id, {
      isNewChatInputCtaEnabled: false,
    });
    // start new chat
    setCurrentView?.('chat');
    setNewChatRequested(true);
    useAppStateStore.setState({ modes: [], enabledMode: null });
  };

  const enableStartNewChatEntry = useMemo(
    () => isHistoryView || isNewChatInputCtaEnabled,
    [isHistoryView, isNewChatInputCtaEnabled],
  );

  // reply preview content from sessioncontext
  const sessionContext = conversationById[id]?.sessionContext;
  // Update total input area height whenever input field height or session context changes
  useEffect(() => {
    const totalHeight = calculateTotalInputAreaHeight(
      inputFieldHeight,
      sessionContext?.type,
      canShowModes,
    );
    setTotalInputAreaHeight(totalHeight);
  }, [
    inputFieldHeight,
    sessionContext?.type,
    setTotalInputAreaHeight,
    canShowModes,
  ]);

  const isReplyPreview = sessionContext?.type === 'REPLY';

  useEffect(() => {
    if (isReplyPreview && inputViewRef?.current) {
      setTimeout(() => {
        inputViewRef.current?.focus?.();
      }, 100);
    }
  }, [isReplyPreview]);

  const onCloseReplyPreview = (type: SessionContextType) => {
    if (type === 'REPLY' || type === 'CONTEXT') {
      // TODO: [RNW] Implement reply preview close
      updateConversationById(id, {
        sessionContext: undefined,
      });
      publishEventOnly('CONTEXT_PREVIEW_CLOSED', id);
    }
  };

  const btnMode =
    enableMicInput && (!text || !text.trim().length) ? 'default' : 'text';

  const onVoiceOverlayClose = useCallback(() => {
    audioStreamLogger.info('onVoiceOverlayClose');
    setShowVoiceOverlay(false);
  }, []);

  return shouldShowInputBar ? (
    <View style={styles.bottomContainer}>
      <Animated.View
        style={[
          styles.animatedContainer,
          {
            // TODO: [RNW] Check and find an alternative solution
            paddingBottom: Platform.OS === 'web' ? bottomInset : bottomInset + 24,
            paddingTop: 30,
          },
        ]}
        entering={SlideInDown.duration(300)}
        exiting={SlideOutDown.duration(300)}
      >
        <ContextMenu
          sessionContext={sessionContext || undefined}
          onClose={onCloseReplyPreview}
          inputBoxHeight={totalInputAreaHeight}
          hide={!isMessageScreen}
        />
        <ShadowedView
          style={[
            styles.outerBox,
            sessionContext?.type !== 'CONTEXT' && styles.shadowEffect,
            !canShowModes && {
              paddingTop: 30,
            },
          ]}
        >
          {enableStartNewChatEntry ? (
            <StartNewChatEntry onStartNewChatClick={onStartNewChatClick} />
          ) : (
            <View
              style={[
                styles.textInputContainer,
                canShowModes
                  ? { flexDirection: 'column', width: '100%' }
                  : { marginBottom: 14 },
              ]}
            >
              <BottomSheetTextInput
                value={text}
                onContentSizeChange={
                  Platform.OS === 'web' ? undefined : onContentSizeChanged
                }
                onChange={Platform.OS === 'web' ? handleWebInputChange : undefined}
                onKeyPress={Platform.OS === 'web' ? handleKeyPress : undefined}
                multiline
                ref={inputViewRef}
                maxLength={500}
                // key={shouldAutoPlayPlaceholder ? 'key1' : 'key2'}
                onSubmitEditing={onSendClick}
                returnKeyType={'send'}
                onChangeText={disableInputBar ? undefined : onTextChange}
                editable={!disableInputBar}
                style={[
                  styles.innerBox,
                  {
                    textAlignVertical: 'center',
                    alignSelf: canShowModes ? 'stretch' : 'center',
                    width: canShowModes ? '100%' : undefined,
                  },
                  isDesktopOrPwa && {
                    minHeight: Math.max(inputFieldHeight, INPUT_VIEW_HEIGHT),
                  },
                ]}
                placeholderTextColor={COLORS.TEXT_LOW_EMPHASIS}
                cursorColor={COLORS.BLUE_VAR_8}
                placeholder={INPUT_BOX_PLACEHOLDER}
                scrollEnabled={true}
                hitSlop={{
                  top: 30,
                  bottom: 30,
                  left: 30,
                  right: 30,
                }}
              />
              <View
                style={[
                  styles.textInputBottomContainer,
                  canShowModes && styles.showModesContainer,
                ]}
              >
                {canShowModes && <Modes modes={modes} />}
                {btnMode === 'text' && (
                  <SendButton onPress={onSendClick} disabled={disableInputBar} />
                )}
                {btnMode === 'default' && (
                  <MicButton
                    trackingKey={trackingKey}
                    onPress={debouncedOnDefaultMicClicked}
                    disabled={disableInputBar}
                  />
                )}
              </View>
            </View>
          )}
        </ShadowedView>
      </Animated.View>

      {/* Voice Recording Overlay */}
      {showVoiceOverlay && <VoiceOverlay onClose={onVoiceOverlayClose} />}

      {!enableStartNewChatEntry && (showMicCoachmark ?? true) && showFirstCoachmark && enableMicInput && (
        <SpeakerCoachmark
          visible={showFirstCoachmark}
          onClose={onCloseFirstCoachmark}
          text={MESSAGES.MIC_FIRST_COACHMARK}
          arrowDirection="down"
          position={{ bottom: 75, right: 15 }}
          width={140}
          arrowOffset={110}
        />
      )}

      {!enableStartNewChatEntry && (showMicCoachmark ?? true) && showSecondCoachmark && enableMicInput && (
        <SpeakerCoachmark
          visible={showSecondCoachmark}
          onClose={onCloseSecondCoachmark}
          text={MESSAGES.MIC_SECOND_COACHMARK}
          arrowDirection="down"
          position={{ bottom: 75, right: 15 }}
          width={180}
          arrowOffset={150}
        />
      )}
    </View>
  ) : null;
};

function StartNewChatEntry({
  onStartNewChatClick,
}: {
  onStartNewChatClick: () => void;
}) {
  return (
    <Pressable style={styles.container} onPress={onStartNewChatClick}>
      <GradientText
        style={styles.gradientTextWelcome}
        gradientColors={['#51AFE6', '#355FF2', '#11287A']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        {config.startNewChatText}
      </GradientText>
      <SendButton onPress={onStartNewChatClick} />
    </Pressable>
  );
}

const styles = StyleSheet.create({
  bottomContainer: {
    flexDirection: 'column',
    position: 'relative',
    width: '100%',
    borderRadius: 25,
  },
  shadowEffect: {
    shadowColor: 'rgba(53, 95, 242, 0.8)',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.18,
    shadowRadius: 20,
    elevation: 1,
  },
  animatedContainer: {
    width: '100%',
    backgroundColor: COLORS.TRANSPARENT,
    borderRadius: 25,
  },
  outerBox: {
    marginHorizontal: 10,
    alignItems: 'flex-end',
    backgroundColor: COLORS.WHITE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    blurRadius: 9,
    flexDirection: 'row',
    minHeight: INPUT_VIEW_HEIGHT,
    paddingHorizontal: 16,
    paddingVertical: 20,
    position: Platform.OS === 'web' ? 'relative' : 'absolute',
    bottom: 0,
  },
  innerBox: {
    backgroundColor: COLORS.WHITE,
    flex: 1,
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 16,
    maxHeight: INPUT_VIEW_HEIGHT * 3 - (Platform.OS === 'ios' ? 1 : 8), // 3 lines
    lineHeight: 18,
    overflow: 'hidden',
    minWidth: 0, // Prevent text overflow issues
    margin: 0,
    padding: 0,
    textAlign: 'left',
  },
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    flex: 1,
    alignSelf: 'center',
    height: INPUT_VIEW_HEIGHT + INPUT_BOX_BOTTOM_PADDING,
  },
  gradientTextWelcome: {
    fontSize: 18,
    fontFamily: FONTS.FONT_FAMILY_700,
    marginRight: 6,
    color: COLORS.BLACK,
  },
  textInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    width: '100%',
    margin: 0,
    padding: 0,
  },
  textInputBottomContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.TRANSPARENT,
  },
  showModesContainer: {
    flex: 1,
    width: '100%',
    marginTop: 20,
    marginBottom: 10,
  },
  modesContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  // Voice Overlay Styles
});
