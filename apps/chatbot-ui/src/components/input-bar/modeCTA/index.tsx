import React, { useEffect, useMemo } from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import GradientText from '../../base/GradientText';
import {
  TripModeIconDisabled,
  TripModeIconEnabled,
} from '../../../assets/IconTripMode';
import { useAppStateStore } from '../../../store/app';
import { useMessageStore } from '../../../store/messages/messageStore';
import { trackOmnitureClickEvent } from '../../../native/omniture';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../../native/tracking/pdt';
import { triggerSuccessHaptic } from '../../../utils/hapticUtils';

interface ModeCTAProps {
  mode: Mode;
}

const iconMap = {
  tripMode: {
    enabled: TripModeIconEnabled,
    disabled: TripModeIconDisabled,
  },
};

const textGradientColors = {
  enabled: ['#51AFE6', '#355FF2', '#11287A'],
  disabled: ['#757575', '#757575', '#757575'],
};

const backgroundGradientColors = {
  enabled: '#E6F5FF',
  disabled: '#F2F2F2',
};

const borderGradientColors = {
  enabled: ['#51AFE6', '#355FF2', '#11287A'],
  disabled: ['#D8D8D8', '#D8D8D8', '#D8D8D8'],
};

const ModeCTA = ({ mode }: ModeCTAProps) => {
  const activeConversationId = useMessageStore(
    (state) => state.activeConversationId,
  );
  const conversation = useMessageStore(
    (state) => state.conversationById[activeConversationId || 'draft'],
  );
  const { text, id, enabled } = mode;
  const { updateMode } = useAppStateStore();
  useEffect(() => {
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.TRIPMODE_CTA_SHOWN,
    });
    trackOmnitureClickEvent('TRIPMODE_CTA_SHOWN');
  }, []);
  const IconToShow = useMemo(() => {
    return iconMap.tripMode[enabled ? 'enabled' : 'disabled'];
  }, [enabled]);
  const textGradientColorsValue = useMemo(() => {
    return textGradientColors[enabled ? 'enabled' : 'disabled'];
  }, [enabled]);
  const backgroundGradientColorsValue = useMemo(() => {
    return backgroundGradientColors[enabled ? 'enabled' : 'disabled'];
  }, [enabled]);
  const borderGradientColorsValue = useMemo(() => {
    return borderGradientColors[enabled ? 'enabled' : 'disabled'];
  }, [enabled]);

  const handleClick = () => {
    trackOmnitureClickEvent('TRIPMODE_CTA_CLICKED');
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.TRIPMODE_CTA_CLICKED,
    });
    // if it's disabled, change it to enabled state and show trip mode activated screen
    if (!conversation?.messages?.length) {
      triggerSuccessHaptic();
      if (enabled) {
        trackOmnitureClickEvent('TRIPMODE_CTA_DISABLED');
        trackPDTEvent({
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: eventValueSchema.TRIPMODE_CTA_DISABLED,
        });
        updateMode?.(mode.id, false);
      } else {
        updateMode?.(mode.id, true);
      }
    }
  };
  return (
    <Pressable onPress={handleClick}>
      <LinearGradient
        colors={borderGradientColorsValue}
        style={styles.gradientColor}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <View
          style={[
            styles.modeContainer,
            { backgroundColor: backgroundGradientColorsValue },
          ]}
        >
          <IconToShow height={15} width={15} />
          {!enabled ? (
            <Text style={styles.text}>{text}</Text>
          ) : (
            <GradientText
              style={styles.text}
              gradientColors={textGradientColorsValue}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
            >
              {text}
            </GradientText>
          )}
        </View>
      </LinearGradient>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  gradientColor: {
    borderRadius: 29,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-start', // This makes LinearGradient wrap to content width
  },
  modeContainer: {
    margin: 1,
    height: 28,
    borderRadius: 29,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: 7,
  },
  text: {
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.GREY_VAR_1,
    textAlignVertical: 'center',
    includeFontPadding: false,
    lineHeight: 16,
  },
});

export default ModeCTA;
