import React, { useCallback, useEffect, useRef, useState } from 'react';
import Animated, {
  FadeIn,
  FadeOut,
  SlideInDown,
  SlideOutDown,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';
import { ShadowedView } from 'react-native-fast-shadow';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import { CloseButton, MicButton } from './InputIcons';
import { COLORS, FONTS } from '../../constants/globalStyles';
import LinearGradient from 'react-native-linear-gradient';
import GradientText from '../base/GradientText';
import { useVoiceTranscription } from './useVoiceTranscription';
import { trackOmnitureClickEvent } from '../../native/omniture';
import { ButtonBackground } from '../base/Button';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';

type VoiceOverlayProps = {
  onClose: () => void;
  disabled?: boolean;
};

function MicIconWithAnimation({ isActive }: { isActive: boolean }) {
  const micScale = useSharedValue(1);

  useEffect(() => {
    if (isActive) {
      micScale.value = withRepeat(withTiming(1.2, { duration: 800 }), -1, true);
    } else {
      micScale.value = withTiming(1, { duration: 200 });
    }
    return () => {
      micScale.value = withTiming(1, { duration: 200 });
    };
  }, [micScale, isActive]);

  const micScaleAnimStyle = useAnimatedStyle(
    () => ({
      transform: [{ scale: micScale.value }],
    }),
    [micScale],
  );
  return (
    <View
      style={[
        styles.micBottomContainer,
        micScale
          ? styles.micBottomContainerEnabled
          : styles.micBottomContainerDisabled,
      ]}
    >
      <Animated.View style={[styles.micExpandedButton, micScaleAnimStyle]}>
        <MicButton />
      </Animated.View>
    </View>
  );
}

const SPEAK_NOW_HINT_DISPLAY_TIME = 1_500;

function useSpeakNowHint({
  isActive,
  recognizedText,
}: {
  recognizedText?: string | null;
  isActive: boolean;
}) {
  const [showHint, setShowHint] = useState(isActive);
  const isActivePrevValRef = useRef(isActive);
  // from false -> true
  if (isActive && !isActivePrevValRef.current) {
    setShowHint(true);
    setTimeout(() => {
      setShowHint(false);
    }, SPEAK_NOW_HINT_DISPLAY_TIME);
  }
  isActivePrevValRef.current = isActive;
  // Speak now should be shown only if recognizedText is blank
  if (recognizedText && recognizedText.trim()) {
    return false;
  }
  return showHint;
}

function getHeadEllipsizedText(text: string, maxChars: number) {
  if (!text) {
    return '';
  }
  if (text.length <= maxChars) {
    return text;
  }
  return '…' + text.slice(text.length - maxChars);
}

export function VoiceOverlay({ onClose, disabled }: VoiceOverlayProps) {
  const { height } = useWindowDimensions();
  const {
    startVoiceTranscription,
    stopVoiceTranscription,
    displayStatus,
    recognizedText,
    status,
    cta,
  } = useVoiceTranscription({ onClose });

  useEffect(() => {
    startVoiceTranscription();
    return () => {
      stopVoiceTranscription();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [startVoiceTranscription, stopVoiceTranscription]);

  const onCancel = useCallback(() => {
    trackOmnitureClickEvent('VOICE_INPUT_CANCELLED');
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.VOICE_INPUT_CANCELLED,
    });
    onClose();
  }, [onClose]);

  const showSpeakNowHint = useSpeakNowHint({
    recognizedText,
    isActive: status === 'listening',
  });
  const isListening = status === 'listening';

  return (
    <Animated.View style={[styles.overlayContainer, { height }]}>
      <Backdrop onPress={onClose} />
      <Animated.View
        style={styles.overlayCardContainer}
        entering={SlideInDown}
        exiting={SlideOutDown}
      >
        <LinearGradient
          colors={['#E1DEFF00', '#E1DEFFAA', '#E1DEFF', '#79E8F9']}
          locations={[0, 0.1, 0.3, 1]}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          style={styles.gradientBg}
        />
        <ShadowedView style={styles.overlayCard}>
          {isListening && !showSpeakNowHint && (
            <Animated.View
              entering={FadeIn}
              exiting={FadeOut}
              style={styles.statusContainer}
            >
              <GradientText style={styles.statusText}>{displayStatus}</GradientText>
            </Animated.View>
          )}

          {(!isListening || showSpeakNowHint) && (
            <Animated.View
              key={showSpeakNowHint ? 'speak' : 'wait'}
              entering={FadeIn.duration(300)}
              exiting={FadeOut.duration(300)}
              style={[styles.statusContainer, styles.statusContainerCentered]}
            >
              <GradientText style={styles.statusTextCentered}>
                {showSpeakNowHint ? 'Speak now' : displayStatus}
              </GradientText>

              {cta !== null && (
                <TouchableOpacity
                  style={{ marginTop: 16 }}
                  onPress={cta?.onCtaClick}
                >
                  <ButtonBackground>
                    <Text
                      style={{
                        color: COLORS.WHITE,
                        fontFamily: FONTS.FONT_FAMILY_700,
                      }}
                    >
                      {cta?.ctaText || 'Retry'}
                    </Text>
                  </ButtonBackground>
                </TouchableOpacity>
              )}
            </Animated.View>
          )}
          {isListening && !showSpeakNowHint && (
            <>
              <View style={styles.textContainer}>
                <Text
                  style={styles.recognizerText}
                  ellipsizeMode={undefined}
                  numberOfLines={3}
                >
                  {getHeadEllipsizedText(recognizedText, 80)}
                </Text>
              </View>
              <View style={styles.buttonRow}>
                <MicIconWithAnimation isActive={true} />
                <View style={styles.closeButtonWrapper}>
                  <CloseButton onPress={onCancel} disabled={disabled} />
                </View>
              </View>
            </>
          )}
        </ShadowedView>
      </Animated.View>
    </Animated.View>
  );
}

function Backdrop(props: { onPress: () => void }) {
  return (
    <Animated.View entering={FadeIn} exiting={FadeOut} style={styles.backdrop}>
      <TouchableOpacity
        onPress={props.onPress}
        style={{ flex: 1 }}
        activeOpacity={1}
      >
        <View />
      </TouchableOpacity>
    </Animated.View>
  );
}

const VOICE_OVERLAY_CARD_HEIGHT = 250;
const MIC_BTN_PADDING_BOTTOM = 12;
const styles = StyleSheet.create({
  overlayContainer: {
    flexDirection: 'column',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.BLACK_VAR_2,
  },
  backdrop: {
    backgroundColor: COLORS.BLACK_VAR_2,
    bottom: 0,
    left: 0,
    position: 'absolute',
    right: 0,
    top: 0,
  },
  gradientBg: {
    opacity: 0.3,
    position: 'absolute',
    width: '120%',
    height: VOICE_OVERLAY_CARD_HEIGHT + 50,
  },
  overlayCardContainer: {
    flex: 1,
    // backgroundColor: 'red',
    height: VOICE_OVERLAY_CARD_HEIGHT + 50,
    maxHeight: VOICE_OVERLAY_CARD_HEIGHT + 50,
    width: '100%',
    flexDirection: 'column',
    justifyContent: 'flex-end',
  },
  overlayCard: {
    flex: 1,
    overflow: 'hidden',
    marginHorizontal: 8,
    maxHeight: VOICE_OVERLAY_CARD_HEIGHT,
    height: VOICE_OVERLAY_CARD_HEIGHT,
    backgroundColor: COLORS.WHITE,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: 'rgba(53, 95, 242, 0.8)',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.18,
    shadowRadius: 12,
    elevation: 1,
  },
  statusContainer: {
    height: 24,
    width: '100%',
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
    justifySelf: 'flex-start',
  },
  statusContainerCentered: {
    width: '100%',
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    height: VOICE_OVERLAY_CARD_HEIGHT - 32,
  },
  statusText: {
    textAlign: 'center',
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
    fontSize: 18,
    lineHeight: 20,
  },
  statusTextCentered: {
    textAlign: 'center',
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
    fontSize: 24,
    lineHeight: 28,
  },
  textContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingRight: 20,
    width: '100%',
  },
  recognizerText: {
    width: '100%',
    fontSize: 20,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
  },
  speakNowHint: {
    fontSize: 22,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
  },
  buttonRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    justifyItems: 'center',
    width: '100%',
    paddingBottom: MIC_BTN_PADDING_BOTTOM,
  },
  closeButtonWrapper: {
    flexDirection: 'column',
    marginLeft: 24,
    paddingBottom: MIC_BTN_PADDING_BOTTOM,
  },
  micBottomContainerEnabled: {
    display: 'flex',
  },
  micBottomContainerDisabled: {
    display: 'none',
  },
  micBottomContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: MIC_BTN_PADDING_BOTTOM,
  },
  micExpandedButton: {
    width: 62,
    height: 62,
    marginLeft: 62,
    borderRadius: 20,
    backgroundColor: '#F2F1F5',
    borderWidth: 1,
    borderColor: COLORS.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
});
