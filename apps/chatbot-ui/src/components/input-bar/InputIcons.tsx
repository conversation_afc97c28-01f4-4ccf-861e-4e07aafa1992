import { TouchableScale } from '../base/TouchableScale';
import { MicIcon } from '../../assets/MicIcon';
import React, { useEffect, useRef, useState } from 'react';
import { ShadowedView } from 'react-native-fast-shadow';
import { StyleSheet, View } from 'react-native';
import { SendIconGray } from '../../assets/SendIconGray';
import { SendIcon } from '../../assets/SendIcon';
import { COLORS } from '../../constants/globalStyles';
import { trackOmnitureClickEvent } from '../../native/omniture';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withRepeat,
  withSequence,
  withTiming,
} from 'react-native-reanimated';

import { useTalkBackStore } from '../../store/audio-talkback/talkbackStore';

const prevParentKeys = new Set<string>();

let bubbleAnimShown = false;
export function MicButton({
  disabled,
  onPress,
  trackingKey,
}: {
  trackingKey?: string;
  disabled?: boolean;
  onPress?: () => void;
}) {
  const scale = useSharedValue(1);
  const [showBubble, setShowBubble] = React.useState(false);
  const [animTrigger, setAnimTrigger] = useState(0);
  const { state: talkbackState } = useTalkBackStore();
  const talkbackStateRef = useRef(talkbackState);
  if (talkbackStateRef.current !== talkbackState) {
    talkbackStateRef.current = talkbackState;
    if (talkbackStateRef.current === 'playing' && talkbackState === 'stopped') {
      setAnimTrigger((prev) => prev + 1);
    }
  }

  useEffect(() => {
    if (!trackingKey) {
      return;
    }
    if (prevParentKeys.has(trackingKey)) {
      return;
    }
    prevParentKeys.add(trackingKey);
    trackOmnitureClickEvent('VOICE_OPTION_SHOWN');
  }, [trackingKey]);

  useEffect(() => {
    if (disabled) {
      scale.value = 1;
      setShowBubble(false);
      return;
    }
    bubbleAnimShown = true;
    setShowBubble(true);
    const scaleAnimDuration = 500;
    const numberOfReps = 10;
    scale.value = withDelay(
      1000,
      withRepeat(
        withSequence(
          withTiming(1.5, { duration: scaleAnimDuration }),
          withTiming(1, { duration: scaleAnimDuration }),
        ),
        numberOfReps,
        true,
      ),
    );
    const timeout = setTimeout(
      () => setShowBubble(false),
      numberOfReps * 2 * scaleAnimDuration,
    ); // 3 cycles, up+down
    return () => clearTimeout(timeout);
  }, [disabled, scale, animTrigger]);

  const animatedStyle = useAnimatedStyle(
    () => ({
      transform: [{ scale: scale.value }],
    }),
    [scale],
  );

  return (
    <Animated.View style={animatedStyle}>
      {showBubble && !disabled && (
        <View
          style={{
            position: 'absolute',
            alignSelf: 'center',
            width: 36,
            height: 36,
            borderRadius: 18,
            opacity: 0.2,
            zIndex: 0,
          }}
        />
      )}
      <TouchableScale
        disabled={disabled}
        style={{
          alignSelf: 'center',
        }}
        onPress={onPress}
        hitSlop={{ top: 10, bottom: 10, left: 15, right: 15 }}
      >
        <MicIcon disabled={disabled} />
      </TouchableScale>
    </Animated.View>
  );
}

export function CloseButton({
  disabled,
  onPress,
}: {
  disabled?: boolean;
  onPress: () => void;
}) {
  return (
    <TouchableScale onPress={onPress} disabled={disabled}>
      <ShadowedView style={styles.closeButton}>
        <View style={styles.closeButtonContainer}>
          <View style={styles.closeIcon} />
          <View style={[styles.closeIcon, styles.closeIconRotated]} />
        </View>
        <View
          style={StyleSheet.absoluteFill}
          pointerEvents={disabled ? 'none' : 'auto'}
        >
          <View style={{ flex: 1 }} onTouchEnd={onPress} />
        </View>
      </ShadowedView>
    </TouchableScale>
  );
}

export type SendButtonProps = {
  disabled?: boolean;
  onPress: () => void;
};

export function SendButton(props: SendButtonProps) {
  return (
    <TouchableScale
      disabled={props.disabled}
      style={{
        alignSelf: 'center',
      }}
      onPress={() => {
        props.onPress();
      }}
      hitSlop={{ top: 10, bottom: 10, left: 15, right: 15 }}
    >
      {props.disabled ? <SendIconGray /> : <SendIcon />}
    </TouchableScale>
  );
}

const styles = StyleSheet.create({
  closeButton: {
    width: 32,
    height: 32,
    padding: 4,
    borderRadius: 16,
    backgroundColor: COLORS.WHITE,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 20,
    shadowColor: 'rgba(0, 0, 0, 0.5)',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 1,
  },
  closeButtonContainer: {
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeIcon: {
    position: 'absolute',
    width: 14,
    height: 2,
    backgroundColor: '#000',
    borderRadius: 1,
    transform: [{ rotate: '45deg' }],
  },
  closeIconRotated: {
    transform: [{ rotate: '-45deg' }],
  },
});
