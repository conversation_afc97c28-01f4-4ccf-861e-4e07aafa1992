import React from 'react';
import { View, StyleSheet, Text, Pressable, Platform } from 'react-native';
import { COLORS, FONTS, Z_INDEX } from '../../constants/globalStyles';
import { ShadowedView } from 'react-native-fast-shadow';
import DocumentNotes from '../../assets/DocumentNotes';
import { CloseIcon } from '../../assets/CloseIcon';
import { CONTEXT_MENU_ADDITIONAL_HEIGHT, INPUT_BOX_HEIGHT } from '../../const';

type ContextMenuBarProps = {
  titlePrefix?: string;
  title?: string;
  onPress?: () => void;
  isVisible?: boolean;
  onClose?: (type: SessionContextType) => void;
  inputBoxHeight: number;
};

export const ContextMenuBar: React.FC<ContextMenuBarProps> = ({
  titlePrefix = 'Asking about ',
  title = ' ',
  onPress,
  isVisible = true,
  onClose,
  inputBoxHeight,
}) => {
  if (!isVisible) return null;

  return (
    <ShadowedView
      style={[
        styles.container,
        styles.shadowEffect,
        { height: Platform.OS === 'ios' ? inputBoxHeight - 10 : inputBoxHeight + 2 },
      ]}
    >
      <Pressable
        style={styles.barContainer}
        onPress={onPress}
        hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
      >
        <View style={styles.innerBarContainer}>
          {/* Checkmark Icon */}
          <View style={styles.leftIconContainer}>
            <DocumentNotes />
          </View>

          {/* Title Text */}
          <Text style={styles.prefixText} numberOfLines={1}>
            {titlePrefix}
            <Text style={styles.innerTitleText}>{title}</Text>
          </Text>
          <Pressable
            style={styles.rightIconContainer}
            onPress={() => onClose?.('CONTEXT')}
            hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
          >
            <CloseIcon width={16} height={16} color={COLORS.BLUE_VAR_1} />
          </Pressable>
        </View>
      </Pressable>
    </ShadowedView>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 10,
    borderTopStartRadius: 20,
    borderTopEndRadius: 20,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.BLUE_VAR_2,
    borderColor: COLORS.WHITE,
    borderWidth: 2,
    overflow: 'hidden',
    borderBottomWidth: 0,
  },
  shadowEffect: {
    shadowColor: '#355FF2',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.18,
    shadowRadius: 12,
    elevation: 1,
  },
  barContainer: {
    flexDirection: 'row',
    backgroundColor: COLORS.BLUE_VAR_2,
    height: 50,
    width: '100%',
    paddingVertical: 10,
    paddingHorizontal: 7,
    borderRadius: 12,
    alignItems: 'flex-start',
  },
  leftIconContainer: {
    width: 20,
    height: 20,
    marginRight: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  rightIconContainer: {
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  prefixText: {
    flex: 1,
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.BLACK,
    marginRight: 8,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  innerTitleText: {
    color: COLORS.BLUE_VAR_1,
  },
  innerBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
});
