/**
 * useVoiceTranscription
 *
 * Handles the lifecycle and state of voice transcription, including starting/stopping audio stream,
 * updating UI state, handling transcription results, errors, and timeouts, and updating input state.
 */
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  AudioStreamCallbacks,
  AudioStreamController,
  startAudioStream,
} from '../../native/audio/audio-stream';
import {
  useInputStateStore,
  useMessageAction,
  useMessageStore,
} from '../../store/messages';
import { triggerSuccessHaptic } from '../../utils/hapticUtils';
import {
  trackOmnitureClickEvent,
  trackOmnitureGenericEvent,
} from '../../native/omniture';
import { useConfigSettings } from '../../store/messages/newChatView';
import { audioStreamLogger } from '../../native/audio/audio-stream-logger';

import { AudioError, getErrorMessage } from '../../native/audio/audio-errors';
import { Linking } from 'react-native';
import { stopTtsIfPlaying } from '../../store/audio-talkback/talkbackStore';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';

const MIC_VIEW_AUTO_CLOSE_DELAY = 600;
const TRANSCRIPTION_CLIENT_TIMEOUT = 20_000;

type UseVoiceTranscriptionResult = {
  startVoiceTranscription: () => Promise<void>;
  stopVoiceTranscription: () => void;
  displayStatus: string;
  recognizedText: string;
  cta?: {
    ctaText: string;
    onCtaClick: () => void;
  } | null;
  status: 'initializing' | 'listening' | 'error';
};

const lastMessageSentDetail: {
  time: number;
  message: string;
} = {
  time: 0,
  message: '',
};
const EXPECTED_TIME_DIFF_BETWEEN_MESSAGES = 1000;
/**
 * useVoiceTranscription hook manages voice session, result handling, and error/timeout logic.
 * @param onClose - callback to close the overlay and cleanup
 */
export function useVoiceTranscription({
  onClose,
}: {
  onClose: () => void;
}): UseVoiceTranscriptionResult {
  const { data: configSettingsData } = useConfigSettings();
  const configSettingsDataRef = useRef(configSettingsData);
  // throw invariant errors in dev mode
  if (__DEV__) {
    if (!configSettingsData) {
      throw 'fetch ConfigSettings is not finished yet';
    }
    if (configSettingsDataRef.current !== configSettingsData) {
      throw 'ConfigSettings data changed after initial mount';
    }
  }

  const uiCloseDelay =
    configSettingsData?.voiceConfig?.closeDelay ?? MIC_VIEW_AUTO_CLOSE_DELAY;
  const clientTranscribeTimeout =
    configSettingsData?.voiceConfig?.transcribeTimeout ??
    TRANSCRIPTION_CLIENT_TIMEOUT;

  // Holds the current status message and recognized text
  const [displayStatus, setDisplayStatus] = useState('Getting ready...');
  const [recognizedText, setRecognizedText] = useState('');
  const [status, setStatus] = useState<'initializing' | 'listening' | 'error'>(
    'initializing',
  );
  const [cta, setCta] = useState<UseVoiceTranscriptionResult['cta']>(null);

  // Access input state and conversation id from store
  const { updateInputStateByConversationId } = useInputStateStore();
  const { activeConversationId } = useMessageStore();
  const audioCallbackRef = useRef<AudioStreamController | null>(null);
  const transcriptionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handles transcription timeout and triggers error state/cleanup
  const statusRef = useRef(status);
  statusRef.current = status;

  const resetTranscriptionTimeout = useCallback(() => {
    if (clientTranscribeTimeout <= 0) {
      return; // no-op
    }
    audioStreamLogger.info('resetTranscriptionTimeout()');
    if (statusRef.current !== 'listening') {
      return;
    }
    if (transcriptionTimeoutRef.current) {
      clearTimeout(transcriptionTimeoutRef.current);
    }
    transcriptionTimeoutRef.current = setTimeout(() => {
      if (statusRef.current !== 'listening') {
        return;
      }
      if (audioCallbackRef.current) {
        audioStreamLogger.info('resetTranscriptionTimeout() stopping');
        audioCallbackRef.current.stop(false, 'transcription timeout');
        setStatus('error');
        setDisplayStatus('No speech detected.');
        setRecognizedText('');
        trackOmnitureGenericEvent({
          CLICK_EVENT: 'voice_error',
          ERROR_EVENT: 'timeout_no_voice_input',
        });
        if (uiCloseDelay > 0) {
          closeTimeoutRef.current = setTimeout(() => {
            onClose();
          }, uiCloseDelay);
        } else {
          onClose();
        }
      }
    }, clientTranscribeTimeout);
  }, [onClose, uiCloseDelay, clientTranscribeTimeout]);
  useEffect(() => {
    if (status === 'listening') {
      resetTranscriptionTimeout();
    }
  }, [resetTranscriptionTimeout, status]);

  const { sendMessage } = useMessageAction();
  // Handles all audio stream events: result, start, stop, error
  const audioStreamCallbacks: AudioStreamCallbacks = useMemo<AudioStreamCallbacks>(
    () =>
      ({
        onSpeechResultReceived(speechResultPayload: unknown): void {
          audioStreamLogger.info('onSpeechResultReceived():: ', speechResultPayload);
          if (typeof speechResultPayload !== 'string') {
            return;
          }
          const {
            type,
            text: transcribedText,
            status: serverStatus,
            final: isFinal,
            locale,
            autoSubmit,
          } = JSON.parse(speechResultPayload) as any;
          if (type === 'init_result') {
            if (serverStatus === 'READY') {
              triggerSuccessHaptic();
              setStatus('listening');
              setDisplayStatus('Myra is listening...');
              setRecognizedText('');
            }
          } else if (type === 'transcription_result') {
            setStatus('listening');
            setDisplayStatus('Myra is listening...');
            setRecognizedText(transcribedText?.trim() || '');
            let delayClose = true;
            if (transcribedText) {
              if (isFinal && activeConversationId) {
                const currentTime = Date.now();
                const lastMessageSentTime = lastMessageSentDetail.time;
                const timeDiff = currentTime - lastMessageSentTime;

                // Check if we should send the message based on conditions:
                // 1. If time diff is less than expected AND message is same as last message, don't send (avoid duplicates)
                // 2. If time diff is more than expected, send message (new session)
                // 3. If time diff is less than expected BUT message is different, send message (user corrected/updated)
                const shouldSendMessage =
                  autoSubmit &&
                  (timeDiff >= EXPECTED_TIME_DIFF_BETWEEN_MESSAGES ||
                    (timeDiff < EXPECTED_TIME_DIFF_BETWEEN_MESSAGES &&
                      lastMessageSentDetail.message !== transcribedText));

                if (shouldSendMessage) {
                  lastMessageSentDetail.message = transcribedText;
                  lastMessageSentDetail.time = currentTime;
                  trackPDTEvent({
                    eventName: eventNameSchema.CHAT_INTERACTED,
                    eventType: eventTypeSchema.ACTION,
                    eventValue: eventValueSchema.VOICE_TO_TEXT_SUCCESS,
                  });
                  trackPDTEvent({
                    eventName: eventNameSchema.CHAT_INTERACTED,
                    eventType: eventTypeSchema.ACTION,
                    eventValue: eventValueSchema.PROMPT_SRC_VOICE,
                  });
                  sendMessage(transcribedText, {
                    dataProps: {
                      inputMode: 'VOICE',
                      voiceLocale: locale,
                    },
                  });
                  delayClose = false;
                } else {
                  updateInputStateByConversationId(
                    activeConversationId,
                    transcribedText,
                  );
                }
              }
              resetTranscriptionTimeout();
              if (isFinal && audioCallbackRef.current) {
                audioCallbackRef.current.stop(true, 'transcription finished');
                if (delayClose && uiCloseDelay > 0) {
                  setTimeout(() => {
                    trackOmnitureClickEvent('VOICE_TO_TEXT_SUCCESS');
                    onClose();
                  }, uiCloseDelay);
                } else {
                  onClose();
                }
              }
            }
          }
        },
        onStartRecording(): void {
          // Start listening and set timeout
          setCta(null);
          audioStreamLogger.info('onStartRecording');
          setStatus('initializing');
          setDisplayStatus('Getting ready...');
          setRecognizedText('');
          resetTranscriptionTimeout();
        },
        onStopRecording(finished, reason): void {
          audioStreamLogger.info(
            `onStopRecording, finished=${finished}, reason=${reason}`,
          );
          setCta(null);
          // Cleanup on stop
          if (transcriptionTimeoutRef.current) {
            clearTimeout(transcriptionTimeoutRef.current);
          }
          if (closeTimeoutRef.current) {
            clearTimeout(closeTimeoutRef.current);
          }
          // onClose will be called be caller when finished=true
          if (finished) {
            return;
          }
          onClose();
        },
        onError(err: AudioError): void {
          audioStreamLogger.error('onError', err);
          // Handle error and cleanup
          setStatus('error');
          setRecognizedText('');
          setDisplayStatus(getErrorMessage(err));
          const isPermissionError =
            err.code === 'MICROPHONE_PERMISSION_ERROR' ||
            err.code === 'MICROPHONE_PERMISSION_DENIED';
          if (isPermissionError) {
            setCta({
              ctaText: 'GO TO SETTINGS',
              onCtaClick: () => {
                Linking.openSettings();
              },
            });
            // don't close, just return in case of PermissionError
            return;
          } else {
            setCta(null);
          }
          setTimeout(() => {
            onClose();
          }, uiCloseDelay);
        },
      }) satisfies AudioStreamCallbacks,
    [
      activeConversationId,
      onClose,
      uiCloseDelay,
      resetTranscriptionTimeout,
      updateInputStateByConversationId,
      sendMessage,
    ],
  );

  // Starts the voice transcription session
  const startVoiceTranscription = useCallback(async () => {
    stopTtsIfPlaying('voice_transcription_started');
    audioStreamLogger.info('startVoiceTranscription()');
    setStatus('initializing');
    setDisplayStatus('Getting ready...');
    setRecognizedText('');
    audioCallbackRef.current = await startAudioStream(
      audioStreamCallbacks,
      configSettingsData,
    );
  }, [audioStreamCallbacks]);

  // Stops the voice transcription session and cleans up
  const stopVoiceTranscription = useCallback(() => {
    audioStreamLogger.info('stopVoiceTranscription()');
    if (audioCallbackRef.current?.stop) {
      audioCallbackRef.current.stop(false, 'stopVoiceTranscription');
    }
    onClose();
  }, [onClose]);

  return {
    startVoiceTranscription, // Start voice session
    stopVoiceTranscription, // Stop voice session
    displayStatus, // Status message (e.g. 'Getting ready...', error, etc.)
    recognizedText, // Current recognized speech
    status, // Current status: 'initializing' | 'listening' | 'error'
    cta, // CTA button text and click handler (e.g. 'Try again')
  };
}
