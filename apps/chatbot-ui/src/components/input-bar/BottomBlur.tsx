import React from 'react';
import { COLORS } from '../../constants/globalStyles';
import { LinearGradient } from 'react-native-linear-gradient';
import { View } from 'react-native';

export const INPUT_BAR_HEIGHT = 120;

export function BottomBlur() {
  return (
    <View
      pointerEvents={'none'}
      style={{
        height: INPUT_BAR_HEIGHT,
        width: '100%',
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
      }}
    >
      <LinearGradient
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        angle={90}
        angleCenter={{ x: 0, y: 0.5 }}
        locations={[0, 0.3, 0.9, 1]}
        colors={[
          'rgba(245,242,252,0)',
          'rgba(245,242,252,0.5)',
          COLORS.PURPLE_VAR_2,
          COLORS.PURPLE_VAR_1,
        ]}
        style={{
          // backgroundColor: 'red',
          height: INPUT_BAR_HEIGHT,
          width: '100%',
        }}
      />
    </View>
  );
}
