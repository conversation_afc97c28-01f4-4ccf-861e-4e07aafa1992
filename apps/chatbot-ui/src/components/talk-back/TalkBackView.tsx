import React, { useCallback } from 'react';
import { StyleSheet, View } from 'react-native';
import { ShadowedView } from 'react-native-fast-shadow';
import { COLORS } from '../../constants/globalStyles';
import { CloseIconLine } from '../../assets/CloseIconLine';
import FastImage from 'react-native-fast-image';
import {
  useAudioEventListeners,
  useTalkBackStore,
} from '../../store/audio-talkback/talkbackStore';
import { hasTtsNativeSupport } from '../../native/audio/audio-native-module';
import { PlayIcon } from '../../assets/PlayIcon';
import { PauseIcon } from '../../assets/PauseIcon';
import { useInputStateStore } from '../../store/messages';
import { INPUT_BOX_HEIGHT } from '../../const';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { useBackAction } from '../../utils/useBackHandler';

const ICON_SIZE = 24;
const BUTTON_SIZE = 44;
const BUTTON_RADIUS = BUTTON_SIZE / 2;
const FLUX_IMAGE_WIDTH = 100;
const FLUX_IMAGE_HEIGHT = 42;

export function TalkBackView() {
  if (hasTtsNativeSupport()) {
    return <TalkBackViewInternal />;
  }
  return null;
}

const MYRA_FLUX_BG =
  'https://jsak.mmtcdn.com/pwa/platform-myra-ui/static/sub_icons/myra_flux_bg.webp';

function TalkBackViewInternal() {
  const { currentTaskId, pause, resume, stop, state: ttsState } = useTalkBackStore();
  useAudioEventListeners();
  const showPlay = ttsState === 'paused';
  const showPause = ttsState === 'playing';
  const { totalInputAreaHeight } = useInputStateStore();
  const cancel = useCallback(() => {
    stop();
    return true;
  }, [stop]);
  const bottomPos = (totalInputAreaHeight || INPUT_BOX_HEIGHT) + 16;
  const shouldShow = currentTaskId != null && (showPlay || showPause);
  useBackAction(cancel, { enabled: shouldShow });
  if (!shouldShow) {
    return null;
  }

  return (
    <View style={[styles.container, { bottom: bottomPos }]}>
      <ShadowedView style={styles.shadowContainer}>
        <View style={styles.contentContainer}>
          {showPause && (
            <TouchableOpacity
              onPress={pause}
              containerStyle={styles.buttonContainer}
              style={styles.button}
              activeOpacity={0.8}
            >
              <PauseIcon height={ICON_SIZE} width={ICON_SIZE} />
            </TouchableOpacity>
          )}
          {showPlay && (
            <TouchableOpacity
              containerStyle={styles.buttonContainer}
              style={styles.button}
              onPress={resume}
              activeOpacity={0.8}
            >
              <PlayIcon height={ICON_SIZE} width={ICON_SIZE} />
            </TouchableOpacity>
          )}
          <View style={styles.fluxContainer}>
            <FastImage
              style={styles.fluxImage}
              source={{
                uri: MYRA_FLUX_BG,
              }}
            />
          </View>
          <TouchableOpacity
            containerStyle={styles.buttonContainer}
            style={styles.button}
            onPress={stop}
            activeOpacity={0.8}
          >
            <CloseIconLine />
          </TouchableOpacity>
        </View>
      </ShadowedView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  shadowContainer: {
    overflow: 'hidden',
    shadowColor: 'rgba(53, 95, 242, 0.99)',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.18,
    shadowRadius: 12,
    elevation: 4,
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 66,
    gap: 4,
    paddingHorizontal: 4,
    borderRadius: 16,
    backgroundColor: COLORS.GREY_VAR_9,
    borderWidth: 2,
    borderColor: COLORS.WHITE,
  },
  buttonContainer: {
    height: BUTTON_SIZE,
    width: BUTTON_SIZE,
    borderRadius: BUTTON_RADIUS,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  button: {
    height: BUTTON_SIZE,
    paddingHorizontal: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fluxContainer: {
    width: FLUX_IMAGE_WIDTH,
    overflow: 'hidden',
    height: FLUX_IMAGE_HEIGHT,
    borderRadius: 16,
  },
  fluxImage: {
    width: FLUX_IMAGE_WIDTH,
    height: FLUX_IMAGE_HEIGHT,
  },
});
