import React from 'react';
import { CreateNewChatIcon } from '../assets/CreateNewChatIcon';
import { ChatsIcon } from '../assets/ChatsIcon';
import TalkToAgentIcon from '../assets/TalkToAgentIcon';
import { BookmarkIcon } from '../assets/BookmarkIcon';
import TalkToAgentIconSmall from '../assets/TalkToAgentIconSmall';
import { BackIcon } from '../assets/BackIcon';
import ItinerarySuitcaseIcon from '../assets/ItinerarySuitcaseIcon';
import BookmarkIconLarge from '../assets/BookmarkIconLarge';
import SuitcaseIcon from '../assets/SuitcaseIcon';
import SuitCaseIconSmall from '../assets/SuitcaseIconSmall';
import FastImageWrapper from './image/FastImageWrapper';
import DeleteIcon from '../assets/DeleteIcon';

interface IconMapperProps {
  iconName: string;
  width?: number;
  height?: number;
  iconUrl?: string;
}

const ICON_MAP = {
  newchat: CreateNewChatIcon,
  chats: ChatsIcon,
  chat: ChatsIcon,
  talktoagentsmall: TalkToAgentIconSmall,
  bookmarks: BookmarkIconLarge,
  bookmarkssmall: BookmarkIcon,
  talktoagent: TalkToAgentIcon,
  back: BackIcon,
  itinerary: ItinerarySuitcaseIcon,
  itinerarysmall: ItinerarySuitcaseIcon,
  trips: SuitcaseIcon,
  tripssmall: SuitCaseIconSmall,
  delete: DeleteIcon,
};

export const IconMapper: React.FC<IconMapperProps> = ({
  iconName,
  width,
  height,
  iconUrl,
}) => {
  const iconMap = ICON_MAP;
  const IconComponent =
    iconMap[iconName as keyof typeof iconMap] || CreateNewChatIcon;
  const props = width && height ? { width, height } : {};
  if (iconUrl) {
    return (
      <FastImageWrapper
        source={{
          uri: iconUrl,
        }}
        style={{ width: width || 20, height: height || 20 }}
        resizeMode="contain"
      />
    );
  }

  return <IconComponent {...props} />;
};
