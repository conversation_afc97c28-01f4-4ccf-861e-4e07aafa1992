/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect } from 'react';
import { postIframeMessage } from '../../utils/webUtils';

const CLIENT_URL = 'https://aiagents.mmt.live';

interface EvaluationProps {
  children: React.ReactNode;
}

interface EvaluationEngineData {
  type: string;
  payload: object;
}

interface InitialSetupPayload {
  scrollHeight: number;
  height: number;
  scrollTop: number;
}

interface NodeMouseHoverPayload {
  tid: string;
}

interface NodeMouseOutPayload {
  tid: string;
}

interface NodeClickedPayload {
  tid: string;
}

interface ScrollToTurnPayload {
  tid: string;
  scrollTop: number;
  behavior: string;
}

const parentSelector = '[data-testid="evaluation-message-area"]';

const getParentRef = (selector: string) =>
  document.querySelector(selector) as HTMLElement;

const Evaluation: React.FC<EvaluationProps> = ({ children }) => {
  const [messages, setMessages] = React.useState<{ listKey: string }[]>([]);

  useEffect(() => {
    const style = document.createElement('style');
    style.textContent =
      '.eval-hovered, .active-turn { background-color: rgba(0, 0, 0, 0.05); }';
    document.head.append(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.origin !== CLIENT_URL) {
        return;
      }
      handleEvaluationEngine(event.data);
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  const HOVERED_CLASS = 'eval-hovered';
  const ACTIVE_TURN_CLASS = 'active-turn';

  const postMessageToClient = (message: object) =>
    postIframeMessage(message, CLIENT_URL);

  const handleMouseInNode = (tid: string) => {
    const chatWrapperRef = getParentRef(parentSelector);
    if (!chatWrapperRef) {
      return;
    }
    const allElems = document.querySelectorAll(`.${HOVERED_CLASS}`);
    allElems.forEach((element) => {
      element.classList.remove(HOVERED_CLASS);
    });

    chatWrapperRef
      ?.querySelectorAll(`[data-testid^="${tid}"]`)
      .forEach((element) => {
        element.classList.add(HOVERED_CLASS);
      });

    // For Leading Question
    const leadingQuestionElems = document.getElementById(`leading_question_${tid}`);
    leadingQuestionElems?.classList.add(HOVERED_CLASS);
  };

  const handleMouseOutNode = (tid: string) => {
    const chatWrapperRef = getParentRef(parentSelector);
    chatWrapperRef
      ?.querySelectorAll(`[data-testid^="${tid}"]`)
      .forEach((element) => {
        element.classList.remove(HOVERED_CLASS);
      });
    const leadingQuestionElems = document.getElementById(`leading_question_${tid}`);
    leadingQuestionElems?.classList.remove(HOVERED_CLASS);
  };

  const handleNodeClick = (tid: string) => {
    const chatWrapperRef = getParentRef(parentSelector);
    if (!chatWrapperRef) {
      return;
    }

    const allActiveElems = document.querySelectorAll(`.${ACTIVE_TURN_CLASS}`);
    allActiveElems.forEach((element) => {
      element.classList.remove(ACTIVE_TURN_CLASS);
    });

    chatWrapperRef
      ?.querySelectorAll(`[data-testid^="${tid}"]`)
      .forEach((element) => {
        element.classList.add(ACTIVE_TURN_CLASS);
      });
    const leadingQuestionElems = document.getElementById(`leading_question_${tid}`);
    leadingQuestionElems?.classList.add(ACTIVE_TURN_CLASS);
  };

  const handleScrollToTurn = (payload: ScrollToTurnPayload) => {
    const { tid, scrollTop } = payload;

    // Find scrollable container
    const scrollContainer =
      getParentRef(parentSelector) ||
      (document.querySelector(
        '[data-testid="evaluation-message-area"]',
      ) as HTMLElement);

    if (!scrollContainer) return;

    const maxScrollTop = scrollContainer.scrollHeight - scrollContainer.clientHeight;

    // Try to find the specific turn element
    const turnElement =
      (scrollContainer.querySelector(`[data-testid^="${tid}"]`) as HTMLElement) ||
      (document.querySelector(`[data-testid^="${tid}"]`) as HTMLElement);

    let targetScrollTop: number;

    if (turnElement) {
      // Calculate element position for precise scrolling
      const elementRect = turnElement.getBoundingClientRect();
      const containerRect = scrollContainer.getBoundingClientRect();
      const elementPosition =
        elementRect.top - containerRect.top + scrollContainer.scrollTop;
      const containerHeight = scrollContainer.clientHeight;

      targetScrollTop = elementPosition - 100; // Position turn 100px from top
    } else {
      // Fallback to provided scroll position
      targetScrollTop = scrollTop;
    }

    // Ensure scroll position is within bounds and apply
    scrollContainer.scrollTop = Math.max(0, Math.min(targetScrollTop, maxScrollTop));

    // Highlight turn after scroll completes
    setTimeout(() => handleNodeClick(tid), 100);
  };

  const handleEvaluationEngine = (data: EvaluationEngineData) => {
    const chatWrapperRef = getParentRef(parentSelector);
    const { type, payload } = data;
    switch (type) {
      case 'INITIAL_SETUP': {
        if (chatWrapperRef) {
          postMessageToClient({
            type: 'INITIAL_SETUP',
            payload: {
              scrollHeight: chatWrapperRef.scrollHeight,
              height: chatWrapperRef.offsetHeight,
              scrollTop: chatWrapperRef.scrollTop,
            } as InitialSetupPayload,
          });
        }
        break;
      }
      case 'NODE_MOUSE_HOVER': {
        const { tid } = payload as NodeMouseHoverPayload;
        handleMouseInNode(tid);
        break;
      }
      case 'NODE_MOUSE_OUT': {
        const { tid } = payload as NodeMouseOutPayload;
        handleMouseOutNode(tid);
        break;
      }
      case 'NODE_CLICKED': {
        const { tid } = payload as NodeClickedPayload;
        handleNodeClick(tid);
        break;
      }
      case 'SCROLL_TO_TURN': {
        const scrollPayload = payload as ScrollToTurnPayload;
        handleScrollToTurn(scrollPayload);
        break;
      }
    }
  };

  const handleScroll = () => {
    const chatWrapperRef = getParentRef(parentSelector);
    if (chatWrapperRef) {
      postMessageToClient({
        type: 'SCROLLING',
        payload: {
          scrollTop: chatWrapperRef.scrollTop,
        },
      });
    }
  };

  const resizeObserver = new ResizeObserver((entries) => {
    const chatWrapperRef = getParentRef(parentSelector);
    const results: object[] = [];

    if (chatWrapperRef) {
      postMessageToClient({
        type: 'INITIAL_SETUP',
        payload: {
          scrollHeight: chatWrapperRef.scrollHeight,
          height: chatWrapperRef.offsetHeight,
          scrollTop: chatWrapperRef.scrollTop,
        } as InitialSetupPayload,
      });

      const container = chatWrapperRef.getBoundingClientRect();
      messages.forEach((msg, index) => {
        const element = document.getElementById(msg.listKey) as HTMLElement;
        if (element) {
          const elementRect = element.getBoundingClientRect();
          const offsetTop =
            elementRect.top - container.top + chatWrapperRef.scrollTop;
          const testId = element.dataset.testid || '';
          const [tid, timestamp] = testId.split('_');
          results.push({
            index,
            tid: tid !== 'undefined' ? tid : msg.listKey,
            timestamp: timestamp !== 'undefined' ? timestamp : null,
            topPosition: offsetTop,
          });
        }
      });
      requestAnimationFrame(() => {
        postMessageToClient({
          type: 'TIMELINE_ENTRY',
          payload: { data: results },
        });
      });
    }
  });

  useEffect(() => {
    if (messages.length > 0) {
      const chatWrapperRef = getParentRef(parentSelector);
      if (chatWrapperRef) {
        chatWrapperRef.addEventListener('scroll', handleScroll);
        resizeObserver.observe(chatWrapperRef);
        messages.forEach((msg) => {
          const element = document.getElementById(msg.listKey) as HTMLElement;
          if (element) {
            resizeObserver.observe(element);
          }
        });
      }

      return () => {
        if (chatWrapperRef) {
          chatWrapperRef?.removeEventListener('scroll', handleScroll);
          messages.forEach((msg) => {
            const element = document.getElementById(msg.listKey) as HTMLElement;
            resizeObserver.unobserve(element);
          });
          resizeObserver.unobserve(chatWrapperRef);
        }
      };
    }
  }, [messages]);

  return React.cloneElement(children as React.ReactElement, {
    setEvaluationData: setMessages,
    isEvaluationMode: true,
  });
};

export default Evaluation;
