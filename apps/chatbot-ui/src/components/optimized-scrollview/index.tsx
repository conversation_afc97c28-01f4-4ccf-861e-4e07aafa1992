import React, {
  useState,
  useEffect,
  useCallback,
  useTransition,
  useMemo,
} from 'react';
import { ActivityIndicator, View } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';

interface OptimizedScrollViewProps<T> {
  data: T[];
  renderItem: ({ item }: { item: T }) => JSX.Element;
  initialRenderCount?: number; // Number of items to load initially
  loadInterval?: number; // Interval in ms (default: 2000ms)
}

export function OptimizedScrollView<T>({
  data,
  renderItem,
  initialRenderCount = 10,
  loadInterval = 1000,
}: OptimizedScrollViewProps<T>) {
  const [visibleData, setVisibleData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [isPending, startTransition] = useTransition();

  useEffect(() => {
    if (initialRenderCount && data.length > initialRenderCount) {
      setVisibleData(data.slice(0, initialRenderCount));
    } else {
      setVisibleData(data);
    }
  }, [data, initialRenderCount]);

  const loadMore = useCallback(() => {
    if (loading || visibleData.length >= data.length) return;

    setLoading(true);
    setTimeout(() => {
      startTransition(() => {
        const nextBatch = data.slice(visibleData.length, visibleData.length + 10);
        setVisibleData((prev) => [...prev, ...nextBatch]);
      });
      setLoading(false);
    }, 500);
  }, [data, visibleData.length, loading]);

  useEffect(() => {
    const interval = setInterval(() => {
      loadMore();
    }, loadInterval);

    return () => clearInterval(interval); // Cleanup when unmounted
  }, [loadMore, loadInterval]);

  const showLoading = useMemo(() => {
    return visibleData.length < data.length || loading || isPending;
  }, [loading, isPending, visibleData.length, data.length]);

  return (
    <ScrollView>
      {visibleData.map((item, index) => (
        <View key={index}>{renderItem({ item })}</View>
      ))}
      {showLoading && (
        <ActivityIndicator
          size="small"
          color="blue"
          style={{ marginVertical: 10 }}
        />
      )}
    </ScrollView>
  );
}
