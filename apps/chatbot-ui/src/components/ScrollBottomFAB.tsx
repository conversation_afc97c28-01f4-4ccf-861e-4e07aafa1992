import React, { useEffect } from 'react';
import { useWindowDimensions, View } from 'react-native';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withTiming,
} from 'react-native-reanimated';
import { COLORS, Z_INDEX } from '../constants/globalStyles';
import { DownArrow } from '../assets/DownArrow';
import { TouchableOpacityProps } from 'react-native-gesture-handler';
import { TouchableScale } from './base/TouchableScale';
import { isPlatformWeb } from '../utils/getPlatform';

type ScrollBottomFABProps = {
  show: boolean;
  hasUnreadMsg: boolean;
  onPress: TouchableOpacityProps['onPress'];
  marginBottom: number;
  compactHeightMode: boolean;
};
const SIZE = 36;
export function ScrollBottomFAB({
  onPress,
  show,
  marginBottom,
  compactHeightMode,
}: ScrollBottomFABProps) {
  const scale = useSharedValue(0);
  const { width } = useWindowDimensions();
  useEffect(() => {
    scale.value = withDelay(
      show ? 600 : 0,
      withTiming(show ? 1 : 0, {
        easing: Easing.elastic(),
        duration: 300,
      }),
    );
  }, [show, scale]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  }, [scale]);

  const isWeb = isPlatformWeb();
  const TOUCH_SIZE = isWeb ? SIZE + 20 : SIZE;
  const bottomMargin = compactHeightMode ? marginBottom + 10 : marginBottom + 15;
  // Common styles
  const containerStyle = {
    position: 'absolute' as const,
    bottom: bottomMargin,
    width: TOUCH_SIZE,
    height: TOUCH_SIZE,
    left: Math.round((width - TOUCH_SIZE) / 2),
    right: Math.round((width + TOUCH_SIZE) / 2),
    zIndex: Z_INDEX.FAB,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  };

  const buttonStyle = {
    backgroundColor: COLORS.WHITE,
    elevation: 8,
    height: SIZE,
    zIndex: Z_INDEX.FAB,
    width: SIZE,
    borderRadius: SIZE / 2,
    borderWidth: 1,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    borderColor: COLORS.GREY_VAR_5,
  };

  const touchWrapperStyle = {
    width: TOUCH_SIZE,
    height: TOUCH_SIZE,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  };

  const renderButton = () => (
    <Animated.View style={[animatedStyle, buttonStyle]}>
      <DownArrow />
    </Animated.View>
  );

  return (
    <View style={containerStyle}>
      <TouchableScale scaleFactor={0.95} onPress={onPress} activeOpacity={1}>
        {isWeb ? (
          <View style={touchWrapperStyle}>{renderButton()}</View>
        ) : (
          renderButton()
        )}
      </TouchableScale>
    </View>
  );
}
