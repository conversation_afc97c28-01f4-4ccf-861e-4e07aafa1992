import React from 'react';
import {
  Text,
  StyleSheet,
  View,
  TouchableOpacity,
  Modal,
  TouchableWithoutFeedback,
} from 'react-native';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { Button, ButtonType } from '../button';
import { Toast } from '../toast';
import { CloseIcon } from '../../assets/CloseIcon';
import { api_deleteConversation } from '../../network/api';
import { useResponsiveWidth } from '../../hooks/useResponsiveWidth';
import { useMessageStore } from '../../store/messages/messageStore';
import { useDebounceCallback } from '../../utils/useDebounceCallback';
import {
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt/pdtTypes';
import { trackPDTEvent } from '../../native/tracking/pdt';

interface DeleteConversationModalProps {
  onClose: () => void;
  strings?: {
    title?: string;
    description?: string;
    primaryButtonText?: string;
    secondaryButtonText?: string;
  };
  conversationId: string;
  onDeleteConversation: () => void;
  visible: boolean;
  source: 'menu' | 'history';
}
const defaultStrings = {
  title: 'Delete Conversation?',
  description:
    'Are you sure you want to delete this conversation? This cant be undone.',
  primaryButtonText: 'Delete Chat',
  secondaryButtonText: 'Cancel',
};
export const DeleteConversationModal = ({
  strings,
  onClose,
  conversationId,
  onDeleteConversation,
  visible,
  source,
}: DeleteConversationModalProps) => {
  const { removeConversationById, setActiveConversationId } = useMessageStore();
  const { containerWidth } = useResponsiveWidth();
  const handleDeleteConversationFailed = () => {
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: `delete_conversation_failed_${source}`,
      conversationId,
    });
    Toast.show('Something went wrong');
  };
  const handleDeleteConversation = async () => {
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: `delete_modal_delete_clicked_${source}`,
      conversationId,
    });
    try {
      onClose();
      const result = await api_deleteConversation(conversationId);
      if (result.success) {
        trackPDTEvent({
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: `delete_conversation_success_${source}`,
          conversationId,
        });
        removeConversationById(conversationId);
        setActiveConversationId('draft');
        Toast.show('Chat deleted successfully');
        onDeleteConversation();
      } else {
        handleDeleteConversationFailed();
      }
    } catch (err) {
      handleDeleteConversationFailed();
    }
  };
  const [debouncedHandleDeleteConversation] = useDebounceCallback(
    handleDeleteConversation,
    600,
    { leading: true, trailing: false },
  );
  if (!conversationId) {
    Toast.show('Something went wrong');
    return null;
  }
  const handleCancel = () => {
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: `delete_modal_cancel_clicked_${source}`,
      conversationId,
    });
    onClose();
  };
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={StyleSheet.absoluteFill} />
        </TouchableWithoutFeedback>
        <View style={[styles.modalContainer, { width: containerWidth }]}>
          <Text style={styles.title}>{strings?.title || defaultStrings.title}</Text>
          <TouchableOpacity
            activeOpacity={0.8}
            onPress={onClose}
            style={styles.closeIcon}
          >
            <CloseIcon />
          </TouchableOpacity>
          <Text style={styles.description}>
            {strings?.description || defaultStrings?.description}
          </Text>
          <Button
            buttonType={ButtonType.Primary}
            title={strings?.primaryButtonText || defaultStrings?.primaryButtonText}
            onPress={debouncedHandleDeleteConversation}
            customStyle={{ marginBottom: 10 }}
          />
          <Button
            buttonType={ButtonType.Secondary}
            title={
              strings?.secondaryButtonText || defaultStrings?.secondaryButtonText
            }
            onPress={handleCancel}
            customStyle={{ marginBottom: 10 }}
          />
        </View>
      </View>
    </Modal>
  );
};
const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: COLORS.BLACK_VAR_7,
    justifyContent: 'flex-end',
    alignItems: 'center',
    width: '100%',
    height: '100%',
  },
  modalContainer: {
    backgroundColor: COLORS.WHITE,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 20,
    width: '100%',
    position: 'relative',
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  closeIcon: {
    position: 'absolute',
    right: 20,
    top: 10,
  },
  title: {
    fontSize: 24,
    fontFamily: FONTS.FONT_FAMILY_900,
    marginBottom: 24,
    color: COLORS.BLACK,
  },
  description: {
    fontSize: 16,
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
    lineHeight: 18,
    marginBottom: 48,
  },
});
