import { INPUT_BOX_HEIGHT } from '../../const';
import { StyleSheet, View } from 'react-native';
import React, { useEffect } from 'react';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
} from 'react-native-reanimated';
import FastImage from '../image/FastImageWrapper';

const BotAnimatedLoader = () => {
  const scale = useSharedValue(1);

  useEffect(() => {
    scale.value = withTiming(
      1,
      {
        duration: 500,
        easing: Easing.inOut(Easing.ease),
      },
      () => {
        scale.value = withTiming(1, {
          duration: 500,
          easing: Easing.inOut(Easing.ease),
        });
      },
    );

    const interval = setInterval(() => {
      scale.value = withTiming(
        1.1,
        {
          duration: 500,
          easing: Easing.inOut(Easing.ease),
        },
        () => {
          scale.value = withTiming(1, {
            duration: 500,
            easing: Easing.inOut(Easing.ease),
          });
        },
      );
    }, 1000);

    return () => clearInterval(interval);
  }, [scale]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  }, [scale]);

  return (
    <Animated.View style={animatedStyle}>
      <FastImage
        style={styles.image}
        source={require('../../assets/MyraIcon.webp')}
        resizeMode="cover"
      />
    </Animated.View>
  );
};

export function MessageListLoader() {
  return (
    <View
      style={{
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingBottom: INPUT_BOX_HEIGHT,
      }}
    >
      <BotAnimatedLoader />
    </View>
  );
}

export const styles = StyleSheet.create({
  image: {
    width: 60,
    height: 60,
  },
});
