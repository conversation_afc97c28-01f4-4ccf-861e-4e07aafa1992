import Animated, {
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withSequence,
  useSharedValue,
  withDelay,
  runOnJS,
} from 'react-native-reanimated';
import { LoaderStar } from '../../assets/loaderStars';
import React, { useCallback } from 'react';
import { Platform, View } from 'react-native';

export const AnimatedStreamLoadingIndicator = () => {
  const opacity = useSharedValue(0);

  // Memoize the animation setup to prevent unnecessary recalculations
  const startAnimation = useCallback(() => {
    'worklet';
    opacity.value = withRepeat(
      withSequence(
        withDelay(
          0,
          withTiming(1, {
            duration: 500,
          }),
        ),
        withTiming(0, {
          duration: 500,
        }),
      ),
      -1,
      true,
    );
  }, [opacity]);

  React.useEffect(() => {
    // Run animation setup in the JS thread with lower priority
    requestAnimationFrame(() => {
      runOnJS(startAnimation)();
    });

    return () => {
      requestAnimationFrame(() => {
        opacity.value = 0;
      });
    };
  }, [startAnimation]);

  const animatedStyle = useAnimatedStyle(
    () => ({
      opacity: opacity.value,
    }),
    [opacity],
  );

  const loaderUI = () => {
    if (Platform.OS === 'web') {
      return (
        <Animated.View
          style={[
            {
              marginTop: 2,
              position: 'absolute',
            },
            animatedStyle,
          ]}
        >
          <LoaderStar height={20} width={20} />
        </Animated.View>
      );
    }

    return (
      <View>
        <Animated.View
          style={[
            {
              marginBottom: -5,
              marginLeft: -3,
            },
            animatedStyle,
          ]}
        >
          <LoaderStar height={20} width={20} />
        </Animated.View>
      </View>
    );
  };

  return <>{loaderUI()}</>;
};
