// implement image with default view while it loads

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import { Z_INDEX } from '../../constants/globalStyles';
import { MmtPlaceholder } from '../../assets/MmtPlaceholder';
import Animated, { FadeOut } from 'react-native-reanimated';
import type { FastImageProps, OnLoadEvent } from 'react-native-fast-image';
import FastImage from './FastImageWrapper';

type PlaceholderOptions =
  | {
      showMmtPlaceholder?: boolean;
      placeholderComponent?: never;
    }
  | {
      showMmtPlaceholder?: never;
      placeholderComponent?: React.ComponentType<{ style?: ViewStyle }>;
    };
type ImageViewProps = {
  // TODO: This should be fixed
  containerStyle?: any;
} & PlaceholderOptions &
  FastImageProps;

const PLACEHOLDER_DELAY = 300;

export function ImageView(props: ImageViewProps) {
  const {
    containerStyle,
    showMmtPlaceholder: _showMmtPlaceholder,
    placeholderComponent,
    ...imageProps
  } = props;
  const showMmtPlaceholder =
    typeof placeholderComponent !== 'undefined'
      ? false
      : typeof _showMmtPlaceholder === 'boolean'
        ? _showMmtPlaceholder
        : true;
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const isLoadedRef = useRef<boolean | null>(null);
  const onLoad = useCallback(
    (event: OnLoadEvent) => {
      isLoadedRef.current = true;
      setIsLoading(false);
      imageProps.onLoad?.(event);
    },
    [imageProps.onLoad],
  );
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!isLoadedRef.current) {
        setIsLoading(true);
      }
    }, PLACEHOLDER_DELAY);
    return () => {
      clearTimeout(timer);
    };
  }, []);
  return (
    <View style={[styles.container, containerStyle]}>
      <FastImage {...imageProps} onLoad={onLoad} />
      {isLoading && (
        <Animated.View
          exiting={FadeOut.duration(200)}
          style={[imageProps.style, containerStyle, styles.placeholderContainer]}
        >
          {showMmtPlaceholder && <MmtPlaceholder />}
          {placeholderComponent &&
            React.createElement(placeholderComponent, { style: containerStyle })}
        </Animated.View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  placeholderContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#E1E1E1',
    zIndex: Z_INDEX.OVERLAP_1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
