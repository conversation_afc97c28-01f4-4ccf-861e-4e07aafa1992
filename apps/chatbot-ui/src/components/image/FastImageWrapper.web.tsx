import { FastImageProps, OnLoadEvent } from 'react-native-fast-image';
import React, { useCallback } from 'react';
import {
  Image,
  ImageLoadEventData,
  ImageSourcePropType,
  ImageStyle,
  NativeSyntheticEvent,
} from 'react-native';

type Props = FastImageProps;
const FastImageWrapper = (props: Props) => {
  const { tintColor, source, onLoad, style, ...otherProps } = props;
  const _onLoad = useCallback(
    (e: NativeSyntheticEvent<ImageLoadEventData>) => {
      if (onLoad) {
        // Create a proper OnLoadEvent object that matches FastImage's expectations
        const fastImageEvent: OnLoadEvent = {
          nativeEvent: {
            width: e.nativeEvent.source?.width,
            height: e.nativeEvent.source?.height,
          },
        };
        onLoad(fastImageEvent);
      }
    },
    [onLoad],
  );
  return (
    <Image
      {...otherProps}
      source={source as ImageSourcePropType}
      onLoad={_onLoad}
      style={style as ImageStyle}
    />
  );
};

FastImageWrapper.resizeMode = {
  contain: 'contain',
  cover: 'cover',
  stretch: 'stretch',
  repeat: 'repeat',
};
FastImageWrapper.priority = {
  low: 'low',
  normal: 'normal',
  high: 'high',
};

export default FastImageWrapper;
