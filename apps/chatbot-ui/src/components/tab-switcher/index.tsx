import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import { COLORS, FONTS } from '../../constants/globalStyles';

interface TabSwitcherProps {
  activeIndex: number;
  setActiveIndex: (index: number) => void;
  tabs: string[];
}

const TabSwitcher: React.FC<TabSwitcherProps> = ({
  activeIndex,
  setActiveIndex,
  tabs,
}) => {
  const [containerWidth, setContainerWidth] = useState(0);
  const tabWidth = containerWidth / tabs.length;
  const indicatorPosition = useSharedValue(0);

  const handleTabPress = (index: number) => {
    setActiveIndex(index);
    indicatorPosition.value = withTiming(index * tabWidth, {
      duration: 300,
    });
  };

  const animatedIndicatorStyle = useAnimatedStyle(
    () => ({
      transform: [{ translateX: indicatorPosition.value }],
    }),
    [indicatorPosition],
  );

  return (
    <View
      style={[
        styles.container,
        { borderColor: tabWidth > 0 ? COLORS.GREY_VAR_5 : 'transparent' },
      ]}
    >
      {tabWidth > 0 && (
        <Animated.View
          style={[styles.indicator, animatedIndicatorStyle, { width: tabWidth + 2 }]}
        />
      )}
      <View
        style={[styles.tabContainer]}
        onLayout={(event) => {
          const { width } = event.nativeEvent.layout;
          setContainerWidth(width);
        }}
      >
        {tabs.map((tab, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.tab, { width: tabWidth }]}
            onPress={() => handleTabPress(index)}
          >
            <Text
              style={[styles.tabText, activeIndex === index && styles.activeText]}
            >
              {tab}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    borderRadius: 8,
    backgroundColor: COLORS.WHITE,
    borderWidth: 1,
    width: '100%',
    height: 32,
  },
  tabContainer: {
    flexDirection: 'row',
    flex: 1,
    overflow: 'hidden',
    position: 'relative',
    width: '100%',
  },
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabText: {
    color: COLORS.GREY_VAR_2,
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 12,
    zIndex: 99999, // Ensure text is above the indicator
  },
  activeText: {
    color: COLORS.BLUE_VAR_1,
    fontFamily: FONTS.FONT_FAMILY_900,
    fontSize: 12,
  },
  indicator: {
    position: 'absolute',
    bottom: -1,
    left: -1,
    height: '105%', // Match the height of the tabs
    borderRadius: 8,
    backgroundColor: COLORS.BLUE_VAR_2,
    borderWidth: 1,
    borderColor: COLORS.BLUE_VAR_1,
    zIndex: -1, // Ensure indicator is behind the text
  },
});

export default TabSwitcher;
