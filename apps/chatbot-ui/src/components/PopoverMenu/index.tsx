import React, { useRef, useState, useMemo } from 'react';
import {
  StyleSheet,
  TouchableWithoutFeedback,
  Text,
  View,
  TouchableOpacity,
} from 'react-native';
import { ModalSlot } from '../../screens/ModalProvider';
import { COLORS } from '../../constants/globalStyles';
import { useResponsiveWidth } from '../../hooks/useResponsiveWidth';
import DeleteIcon from '../../assets/DeleteIcon';
import OpenChatIcon from '../../assets/OpenChatIcon';
import { CopyIcon } from '../../assets/CopyIcon';

const IconMap = {
  delete: DeleteIcon,
  open: OpenChatIcon,
  copy: CopyIcon,
};

/**
 * Get the position of the target element
 * @param targetElementRef - The ref object of the target element
 * @returns {Promise<{ x: number; y: number; width: number; height: number }>}
 * The position of the target element { x, y, width, height } in the window
 * Rejects if the target element is not found
 */
export const getElementPosition = async (
  targetElementRef: React.RefObject<View>,
): Promise<{ x: number; y: number; width: number; height: number }> => {
  return new Promise<{ x: number; y: number; width: number; height: number }>(
    (resolve, reject) => {
      if (!targetElementRef.current) {
        reject(new Error('Target element not found'));
        return;
      }
      targetElementRef.current.measureInWindow((x, y, width, height) => {
        resolve({ x, y, width, height });
      });
    },
  );
};
const spacing = 8;

/**
 * PopoverMenu component
 * @param {Object} props - The props for the PopoverMenu component
 * @param {Object} props.targetElementData - The data for the target element
 * @param {React.FC<any>} props.TargetElementComponent - The component for the target element
 * @param {Object} props.targetElementProps - The props for the target element
 * @param {() => void} props.onClose - The function to call when the menu is closed
 * @param {Array} props.MenuItemList - The list of menu items
 * @returns {React.ReactNode} The PopoverMenu component
 */
export const PopoverMenu = ({
  targetElementData,
  TargetElementComponent,
  targetElementProps,
  onClose,
  MenuItemList,
}: {
  targetElementData: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  TargetElementComponent: React.FC<any>;
  targetElementProps?: Record<string, unknown>;
  onClose: () => void;
  MenuItemList: (contextMenu & {
    onPress: (action: contextMenu['action']) => void;
  })[];
}) => {
  const { containerWidth: screenWidth } = useResponsiveWidth();
  const [containerOffset, setContainerOffset] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const rootRef = useRef<View>(null);
  const { snapX, snapY, snapW, snapH, ctaWidth } = useMemo(() => {
    const rectX = targetElementData.x;
    const rectY = targetElementData.y;
    const rectW = targetElementData.width;
    const rectH = targetElementData.height;

    const _snapX = Math.round(rectX);
    const _snapY = Math.round(rectY);
    const _snapW = Math.round(rectW);
    const _snapH = Math.round(rectH);

    const width = Math.max(120, Math.min(220, Math.round(screenWidth * 0.25)));
    return {
      snapX: _snapX,
      snapY: _snapY,
      snapW: _snapW,
      snapH: _snapH,
      ctaWidth: width,
    };
  }, [targetElementData, screenWidth]);
  const canShowElement = useMemo(() => {
    return (
      containerOffset &&
      containerOffset.y !== 0 &&
      containerOffset.y !== undefined &&
      !isNaN(snapH) &&
      !isNaN(snapW) &&
      !isNaN(snapX) &&
      !isNaN(snapY) &&
      !isNaN(containerOffset.y) &&
      !isNaN(ctaWidth)
    );
  }, [containerOffset, snapH, snapW, snapX, snapY, ctaWidth]);
  if (!targetElementData) {
    return null;
  }
  if (MenuItemList.length === 0) {
    return null;
  }
  return (
    <ModalSlot>
      <View
        style={styles.overlay}
        nativeID="overlay"
        ref={rootRef}
        onLayout={() => {
          if (containerOffset === null) {
            getElementPosition(rootRef)
              .then(({ x, y }) => setContainerOffset({ x, y }))
              .catch(() => setContainerOffset({ x: 0, y: 0 }));
          }
        }}
      >
        <TouchableWithoutFeedback onPress={onClose}>
          <View style={styles.fullDim} />
        </TouchableWithoutFeedback>
        {canShowElement && containerOffset && (
          <View
            style={[
              styles.targetElementContainer,
              {
                left: snapX,
                top: snapY - containerOffset.y - spacing,
                width: snapW,
                // height: snapH + 20,
              },
            ]}
          >
            <TargetElementComponent
              {...targetElementProps}
              style={{ width: '100%', minHeight: snapH }}
            />
            <View style={styles.menuContainer}>
              {MenuItemList.map((item, index) => {
                const Icon = IconMap[item.icon as keyof typeof IconMap];
                return (
                  <TouchableOpacity
                    key={`${item.text}-${index}`}
                    style={[
                      styles.menuItem,
                      index !== MenuItemList.length - 1 && styles.menuItemBorder,
                    ]}
                    onPress={() => item.onPress(item.action)}
                  >
                    {!!Icon && <Icon height={16} width={16} />}
                    <Text style={styles.text}>{item.text}</Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        )}
      </View>
    </ModalSlot>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 100000,
  },
  fullDim: {
    flex: 1,
    backgroundColor: COLORS.BLACK_VAR_7,
  },
  menuContainer: {
    position: 'relative',
    alignSelf: 'flex-end',
    backgroundColor: COLORS.GREY_VAR_8,
    borderRadius: 16,
    paddingHorizontal: 14,
  },
  menuItem: {
    paddingVertical: 12,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  menuItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BLUE_VAR_17,
  },
  text: {
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
    fontSize: 14,
    fontWeight: '400',
    marginRight: 8,
    width: 105,
    overflow: 'hidden',
  },
  targetElementContainer: {
    position: 'absolute',
    zIndex: 999999,
    display: 'flex',
    gap: 8,
  },
});
