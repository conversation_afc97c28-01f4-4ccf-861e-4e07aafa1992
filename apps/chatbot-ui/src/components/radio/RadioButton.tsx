import Animated, {
  Easing,
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import React, { useEffect } from 'react';
import { COLORS } from '../../constants/globalStyles';

type RadioButtonProps = {
  isSelected: boolean;
  variant?: 'default' | 'small';
};
const duration = 100;

export function RadioButton(props: RadioButtonProps) {
  const { variant = 'default' } = props;
  const outerCircleSize = variant === 'small' ? 16 : 18;
  const innerCircleFinalSize = variant === 'small' ? 8 : 10;
  const borderWidth = variant === 'small' ? 1 : 2;
  const isSelected = props.isSelected;
  const innerCircleSize = useSharedValue(isSelected ? innerCircleFinalSize : 0);
  const color = useSharedValue(isSelected ? 1 : 0);

  useEffect(() => {
    innerCircleSize.value = withTiming(isSelected ? innerCircleFinalSize : 0, {
      duration: duration,
      easing: Easing.inOut(Easing.ease),
    });
    color.value = withTiming(isSelected ? 1 : 0, {
      duration: duration,
      easing: Easing.inOut(Easing.ease),
    });
  }, [isSelected, innerCircleSize]);

  const innerStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      color.value,
      [0, 1],
      [COLORS.GREY_VAR_1, COLORS.BLUE_VAR_1],
    );
    return {
      height: innerCircleSize.value,
      width: innerCircleSize.value,
      borderRadius: innerCircleSize.value / 2,
      backgroundColor,
    };
  }, [innerCircleSize]);
  const outerStyle = useAnimatedStyle(() => {
    const borderColor = interpolateColor(
      color.value,
      [0, 1],
      [COLORS.GREY, COLORS.BLUE_VAR_1],
    );
    return {
      height: outerCircleSize,
      width: outerCircleSize,
      borderRadius: outerCircleSize / 2,
      borderWidth,
      alignItems: 'center',
      justifyContent: 'center',
      borderColor,
    };
  }, [innerCircleSize]);

  return (
    <Animated.View style={outerStyle}>
      <Animated.View style={innerStyle} />
    </Animated.View>
  );
}
