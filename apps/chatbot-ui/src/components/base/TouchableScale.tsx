import React, { memo, useState } from 'react';
import { Animated, TouchableOpacity, TouchableOpacityProps } from 'react-native';

export const TouchableScale = memo(
  (props: TouchableOpacityProps & { scaleFactor?: number }) => {
    const { children, scaleFactor, ...otherProps } = props;
    const [btnScale, setBtnScale] = useState(1);
    return (
      <TouchableOpacity
        {...otherProps}
        onPressIn={() => {
          setBtnScale(scaleFactor || 0.9);
        }}
        onPressOut={() => {
          setBtnScale(1);
        }}
        activeOpacity={0.9}
        onPress={(e) => {
          props.onPress?.(e);
        }}
      >
        <Animated.View
          style={{
            transform: [
              {
                scale: btnScale,
              },
            ],
          }}
        >
          {children}
        </Animated.View>
      </TouchableOpacity>
    );
  },
);
