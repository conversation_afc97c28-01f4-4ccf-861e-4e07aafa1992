import { Platform } from 'react-native';
import React from 'react';
import {
  TouchableNativeFeedback,
  TouchableOpacity,
  TouchableOpacityProps,
} from 'react-native-gesture-handler';

const DEFAULT_ACTIVE_OPACITY = 0.7;
export function TouchableFeedback(props: TouchableOpacityProps) {
  return Platform.OS === 'android' ? (
    <TouchableNativeFeedback {...props} />
  ) : (
    <TouchableOpacity activeOpacity={DEFAULT_ACTIVE_OPACITY} {...props} />
  );
}
