// @ts-nocheck
import React from 'react';
import LinearGradient, { LinearGradientProps } from 'react-native-linear-gradient';
import { COLORS } from '../../constants/globalStyles';

type BtnBgProps = {
  disabled?: boolean;
  style?: LinearGradientProps['style'];
  children: LinearGradientProps['children'];
};

export function ButtonBackground({ style, children, disabled }: BtnBgProps) {
  const colors = disabled
    ? [COLORS.GREY_VAR_4, COLORS.GREY_VAR_4]
    : ['rgb(83,178, 254)', 'rgb(6,90, 243)'];

  return (
    <LinearGradient
      colors={colors}
      useAngle
      angle={90}
      style={[
        {
          alignItems: 'center',
          height: 48,
          padding: 16,
          borderRadius: 8,
        },
        style,
      ]}
    >
      {children}
    </LinearGradient>
  );
}
