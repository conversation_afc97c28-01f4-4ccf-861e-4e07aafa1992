import React, { memo } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import { MarkdownText } from '../../message/MarkdownText';

// Minimum width constraint for frontend validation
const MIN_COLUMN_WIDTH = 60;

// Basic table interfaces
export interface BasicTableColumn {
  key: string;
  header: string;
  width: number; // Backend-calculated width (will be validated)
  align?: 'left' | 'center' | 'right';
  isMarkdown?: boolean;
  headerStyle?: {
    backgroundColor?: string;
    color?: string;
    fontSize?: number;
  };
}

export interface BasicTableRow {
  [key: string]: string | number | boolean;
}

export interface BasicTableData {
  title: string;
  columns: BasicTableColumn[];
  rows: BasicTableRow[];
  hideColumnHeader?: boolean;
  hideTitle?: boolean;
  titleStyle?: {
    backgroundColor?: string;
    color?: string;
    fontSize?: number;
  };
}

export interface BasicTableProps {
  data: BasicTableData;
  onCellPress?: (rowIndex: number, columnKey: string, value: any) => void;
  onHeaderPress?: (columnKey: string) => void;
}

// Utility functions
const validateColumnWidth = (width: number): number => {
  if (!width || isNaN(width) || width < 0) {
    return MIN_COLUMN_WIDTH;
  }
  return Math.max(width, MIN_COLUMN_WIDTH);
};

const getColumnWidth = (
  column: BasicTableColumn,
  columnIndex: number,
  totalColumns: number,
) => {
  const isLastColumn = columnIndex === totalColumns - 1;
  return isLastColumn ? 'flex' : validateColumnWidth(column.width);
};

const getColumnStyle = (
  column: BasicTableColumn,
  columnIndex: number,
  totalColumns: number,
) => {
  const width = getColumnWidth(column, columnIndex, totalColumns);
  const isLastColumn = columnIndex === totalColumns - 1;

  return {
    ...(typeof width === 'number' ? { width } : { flex: 1 }),
    ...(isLastColumn
      ? {}
      : { borderRightWidth: 1, borderRightColor: COLORS.GREY_VAR_5 }),
  };
};

const getTextStyle = (column: BasicTableColumn, baseStyle: any) => [
  baseStyle,
  { textAlign: column.align || 'left' },
  ...(column.headerStyle?.color ? [{ color: column.headerStyle.color }] : []),
  ...(column.headerStyle?.fontSize
    ? [{ fontSize: column.headerStyle.fontSize }]
    : []),
];

const getHeaderStyle = (
  column: BasicTableColumn,
  columnIndex: number,
  totalColumns: number,
) => [
  styles.header,
  getColumnStyle(column, columnIndex, totalColumns),
  ...(column.headerStyle?.backgroundColor
    ? [{ backgroundColor: column.headerStyle.backgroundColor }]
    : []),
];

// Basic cell component
const BasicCell = memo<{
  content: string | number | boolean;
  column: BasicTableColumn;
  columnIndex: number;
  totalColumns: number;
  onPress?: () => void;
}>(({ content, column, columnIndex, totalColumns, onPress }) => {
  const renderContent = () => {
    const text = String(content);
    return column.isMarkdown ? (
      <MarkdownText text={text} size="small" />
    ) : (
      <Text style={getTextStyle(column, styles.cellText)} numberOfLines={0}>
        {text}
      </Text>
    );
  };

  return (
    <View
      style={[styles.cell, getColumnStyle(column, columnIndex, totalColumns)]}
      onTouchEnd={onPress}
    >
      {renderContent()}
    </View>
  );
});

BasicCell.displayName = 'BasicCell';

// Basic header component
const BasicHeader = memo<{
  column: BasicTableColumn;
  columnIndex: number;
  totalColumns: number;
  onPress?: () => void;
}>(({ column, columnIndex, totalColumns, onPress }) => (
  <View
    style={getHeaderStyle(column, columnIndex, totalColumns)}
    onTouchEnd={onPress}
  >
    <Text style={getTextStyle(column, styles.headerText)} numberOfLines={0}>
      {column.header}
    </Text>
  </View>
));

BasicHeader.displayName = 'BasicHeader';

// Utility function for title styling
const getTitleStyle = (data: BasicTableData) => [
  styles.title,
  ...(data.titleStyle?.backgroundColor
    ? [{ backgroundColor: data.titleStyle.backgroundColor }]
    : []),
  ...(data.titleStyle?.color ? [{ color: data.titleStyle.color }] : []),
  ...(data.titleStyle?.fontSize ? [{ fontSize: data.titleStyle.fontSize }] : []),
];

// Row rendering component
const TableRow = memo<{
  columns: BasicTableColumn[];
  row?: BasicTableRow;
  rowIndex?: number;
  isHeader?: boolean;
  onCellPress?: (rowIndex: number, columnKey: string, value: any) => void;
  onHeaderPress?: (columnKey: string) => void;
}>(
  ({ columns, row, rowIndex = 0, isHeader = false, onCellPress, onHeaderPress }) => {
    const handleCellPress = (columnKey: string, value: any) => {
      if (isHeader) {
        onHeaderPress?.(columnKey);
      } else {
        onCellPress?.(rowIndex, columnKey, value);
      }
    };

    const rowStyle = isHeader ? styles.headerRow : styles.dataRow;

    return (
      <View style={rowStyle}>
        {columns.map((column, columnIndex) => {
          if (isHeader) {
            return (
              <BasicHeader
                key={column.key}
                column={column}
                columnIndex={columnIndex}
                totalColumns={columns.length}
                onPress={
                  onHeaderPress
                    ? () => handleCellPress(column.key, undefined)
                    : undefined
                }
              />
            );
          }

          return (
            <BasicCell
              key={`${rowIndex}-${column.key}`}
              content={row?.[column.key] ?? ''}
              column={column}
              columnIndex={columnIndex}
              totalColumns={columns.length}
              onPress={
                onCellPress
                  ? () => handleCellPress(column.key, row?.[column.key])
                  : undefined
              }
            />
          );
        })}
      </View>
    );
  },
);

TableRow.displayName = 'TableRow';

// Basic table component - Simple layout
export const BasicTable = memo<BasicTableProps>(
  ({ data, onCellPress, onHeaderPress }) => (
    <View style={styles.container}>
      {!data.hideTitle && <Text style={getTitleStyle(data)}>{data.title}</Text>}

      <View style={styles.table}>
        {!data.hideColumnHeader && (
          <TableRow
            columns={data.columns}
            isHeader={true}
            onHeaderPress={onHeaderPress}
          />
        )}

        {data.rows.map((row, rowIndex) => (
          <TableRow
            key={rowIndex}
            columns={data.columns}
            row={row}
            rowIndex={rowIndex}
            onCellPress={onCellPress}
          />
        ))}
      </View>
    </View>
  ),
);

BasicTable.displayName = 'BasicTable';

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
    borderRadius: 12,
    backgroundColor: COLORS.WHITE,
    elevation: 2,
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  title: {
    fontSize: 18,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
    backgroundColor: COLORS.GREY_VAR_11,
    paddingVertical: 12,
    paddingHorizontal: 16,
    textAlign: 'center',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  table: {
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: COLORS.GREY_VAR_5,
  },
  headerRow: {
    flexDirection: 'row',
    backgroundColor: COLORS.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.GREY_VAR_5,
    minHeight: 45,
  },
  header: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    backgroundColor: COLORS.WHITE,
  },
  dataRow: {
    flexDirection: 'row',
    minHeight: 50,
    alignItems: 'stretch',
    borderBottomWidth: 1,
    borderBottomColor: COLORS.GREY_VAR_5,
  },
  cell: {
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    alignSelf: 'stretch',
  },
  headerText: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.GREY_VAR_1,
    textAlign: 'center',
    flexWrap: 'wrap', // Allow text wrapping for narrow columns
  },
  cellText: {
    fontSize: 11,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_DEFAULT,
    lineHeight: 14,
    flexWrap: 'wrap', // Allow text wrapping for narrow columns
  },
});

export default BasicTable;
