import React from 'react';
import { BasicTable, BasicTableData } from './BasicTable';
import { AdvancedTable, AdvancedTableData } from './AdvancedTable';

// Table layout types - backend flag values
export type TableLayout = 'basic' | 'advanced';

// Backend table configuration - all widths calculated by backend
export interface BackendTableConfig {
  layout: TableLayout;
  tableData: BasicTableData | AdvancedTableData;
}

// Basic layout configuration
export interface BasicTableConfig {
  layout: 'basic';
  tableData: BasicTableData;
}

// Advanced layout configuration
export interface AdvancedTableConfig {
  layout: 'advanced';
  tableData: AdvancedTableData;
}

// Union type for all possible configurations
export type TableRendererData = BasicTableConfig | AdvancedTableConfig;

// Props for the table renderer
export interface TableRendererProps {
  data: TableRendererData;
  onCellPress?: (rowIndex: number, columnKey: string, value: any) => void;
  onHeaderPress?: (columnKey: string) => void;
}

export const TableRenderer: React.FC<TableRendererProps> = ({
  data,
  onCellPress,
  onHeaderPress,
}) => {
  if (data.layout === 'basic') {
    return (
      <BasicTable
        data={data.tableData}
        onCellPress={onCellPress}
        onHeaderPress={onHeaderPress}
      />
    );
  }

  if (data.layout === 'advanced') {
    return (
      <AdvancedTable
        data={data.tableData}
        onCellPress={onCellPress}
        onHeaderPress={onHeaderPress}
      />
    );
  }

  // Fallback to basic table for unknown layout types
  return (
    <BasicTable
      data={(data as BasicTableConfig).tableData}
      onCellPress={onCellPress}
      onHeaderPress={onHeaderPress}
    />
  );
};

// Sample backend responses with calculated widths
export const SAMPLE_TABLE_RESPONSES = {
  // Basic layout response with backend-calculated widths
  BASIC_LAYOUT: {
    layout: 'basic' as const,
    tableData: {
      title: 'Day-by-Day Highlights',
      columns: [
        {
          key: 'dayRange',
          header: 'Day Range',
          width: 100, // Backend calculated this width
          align: 'center' as const,
        },
        {
          key: 'activities',
          header: 'Activities',
          width: 280, // Backend calculated this width
          align: 'left' as const,
          isMarkdown: true,
        },
      ],
      rows: [
        {
          dayRange: 'Day 1-4',
          activities:
            '**Tokyo Cultural Immersion**\n\n• Sensoji Temple visit, tea ceremony\n• Tsukiji food tour, traditional Tokyo neighborhoods\n• Cherry blossom viewing at Ueno Park',
        },
        {
          dayRange: 'Day 5-8',
          activities:
            '**Kyoto Traditional Japan**\n\n• Fushimi Inari hike, traditional ryokan stay\n• Kaiseki dining experience\n• Arashiyama bamboo grove exploration',
        },
        {
          dayRange: 'Day 9-11',
          activities:
            '**Osaka Food Capital**\n\n• Local food experiences and cooking class\n• Osaka Castle visit\n• Famous street food adventure',
        },
      ],
    },
  },

  // Advanced layout response with backend-calculated widths
  ADVANCED_LAYOUT: {
    layout: 'advanced' as const,
    tableData: {
      title: 'Day 5-8: Culture & History Capital Kyoto',
      columns: [
        {
          key: 'day',
          header: 'Day',
          width: 70, // Backend calculated this width
          align: 'center' as const,
        },
        {
          key: 'baseCity',
          header: 'Base City',
          width: 90, // Backend calculated this width
          align: 'center' as const,
        },
        {
          key: 'destination',
          header: 'Destination/Activity',
          width: 220, // Backend calculated this width
          align: 'left' as const,
          isMarkdown: true,
        },
        {
          key: 'travelTime',
          header: 'Travel Time',
          width: 110, // Backend calculated this width
          align: 'center' as const,
        },
        {
          key: 'notes',
          header: 'Notes',
          width: 180, // Backend calculated this width
          align: 'left' as const,
          isMarkdown: true,
        },
      ],
      rows: [
        {
          day: 'Day 5',
          baseCity: 'Kyoto',
          destination:
            '**Arrive in Kyoto**\n\nCheck-in and explore:\n• Shibuya Crossing\n• Hachiko statue\n• Omotesando shopping',
          travelTime: 'Local Subway',
          notes:
            '*Relaxing day after flight*\n\n**Tips:**\n• Get IC card\n• Try local food',
        },
        {
          day: 'Day 6',
          baseCity: 'Kyoto',
          destination:
            '**Arashiyama District**\n\n• Bamboo Grove walk\n• Tenryu-ji Temple\n• Monkey Park visit',
          travelTime: 'JR Train + Walk',
          notes: '**Best time:** Early morning\n\n*Avoid crowds*',
        },
        {
          day: 'Day 7',
          baseCity: 'Kyoto',
          destination:
            '**Traditional Experience**\n\n• Fushimi Inari hike\n• Traditional ryokan stay\n• Kaiseki dinner',
          travelTime: 'Local Train',
          notes: '**Book in advance**\n\n• Dress code applies\n• No shoes inside',
        },
        {
          day: 'Day 8',
          baseCity: 'Kyoto',
          destination:
            '**Cultural Sites**\n\n• Kiyomizu-dera Temple\n• Gion district walk\n• Tea ceremony experience',
          travelTime: 'Walking + Bus',
          notes: '**Photography:**\n\n• Golden hour best\n• Respect local customs',
        },
      ],
    },
  },
};

// Backend JSON structure documentation
export const BACKEND_TABLE_STRUCTURE = {
  description: 'Complete backend JSON structure with calculated widths',

  structure: {
    layout: 'basic | advanced', // Flag to determine table type
    tableData: {
      title: 'string',
      columns: 'array of column objects with calculated width',
      rows: 'array of row objects',
      // All width calculations done by backend
    },
  },

  layoutTypes: {
    basic: {
      description: 'Simple table with backend-calculated widths',
      features: [
        'Clean and simple design',
        'Any number of columns',
        'Always bordered',
        'Markdown support',
        'Backend-calculated widths only',
        'No horizontal scrolling',
      ],
      useCase: 'Simple data tables with moderate content',
    },
    advanced: {
      description: 'Enhanced table with backend-calculated widths',
      features: [
        'Unlimited columns',
        'Always horizontal scrolling',
        'Always bordered',
        'Enhanced styling',
        'Backend-calculated widths only',
        'Markdown support',
        'Height limits for large datasets',
      ],
      useCase: 'Complex tables with many columns or large datasets',
    },
  },

  examples: {
    basicRequest: `{
      "layout": "basic",
      "tableData": {
        "title": "Day-by-Day Highlights",
        "columns": [
          { "key": "days", "header": "Days", "width": 100, "align": "center" },
          { "key": "activities", "header": "Activities", "width": 280, "align": "left", "isMarkdown": true }
        ],
        "rows": [
          {
            "days": "Day 1-3",
            "activities": "**Tokyo Adventure**\\n\\n• Senso-ji Temple\\n• Shibuya Crossing"
          }
        ]
      }
    }`,

    advancedRequest: `{
      "layout": "advanced",
      "tableData": {
        "title": "Detailed Itinerary",
        "columns": [
          { "key": "day", "header": "Day", "width": 70, "align": "center" },
          { "key": "city", "header": "City", "width": 90, "align": "center" },
          { "key": "activities", "header": "Activities", "width": 220, "isMarkdown": true },
          { "key": "transport", "header": "Transport", "width": 110, "align": "center" },
          { "key": "notes", "header": "Notes", "width": 180, "isMarkdown": true }
        ],
        "rows": [...]
      }
    }`,
  },
};

export default TableRenderer;
