import React, { memo } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { ScrollView as GestureHandlerScrollView } from 'react-native-gesture-handler';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import { MarkdownText } from '../../message/MarkdownText';

// Minimum width constraint for frontend validation
const MIN_COLUMN_WIDTH = 60;

// Advanced table interfaces
export interface AdvancedTableColumn {
  key: string;
  header: string;
  width: number; // Backend-calculated width (will be validated)
  align?: 'left' | 'center' | 'right';
  isMarkdown?: boolean;
  headerStyle?: {
    backgroundColor?: string;
    color?: string;
    fontSize?: number;
  };
}

export interface AdvancedTableRow {
  [key: string]: string | number | boolean;
}

export interface AdvancedTableData {
  title: string;
  columns: AdvancedTableColumn[];
  rows: AdvancedTableRow[];
  hideColumnHeader?: boolean;
  hideTitle?: boolean;
  titleStyle?: {
    backgroundColor?: string;
    color?: string;
    fontSize?: number;
  };
}

export interface AdvancedTableProps {
  data: AdvancedTableData;
  onCellPress?: (rowIndex: number, columnKey: string, value: any) => void;
  onHeaderPress?: (columnKey: string) => void;
}

// Width validation utility
const validateColumnWidth = (width: number): number => {
  // Handle invalid values (NaN, null, undefined, negative numbers)
  if (!width || isNaN(width) || width < 0) {
    return MIN_COLUMN_WIDTH;
  }
  // Ensure minimum width
  return Math.max(width, MIN_COLUMN_WIDTH);
};

// Advanced cell component
const AdvancedCell = memo<{
  content: string | number | boolean;
  column: AdvancedTableColumn;
  onPress?: () => void;
}>(({ content, column, onPress }) => {
  const getTextAlign = () => column.align || 'left';
  const validatedWidth = validateColumnWidth(column.width);

  const renderContent = () => {
    const text = String(content);

    if (column.isMarkdown) {
      return <MarkdownText text={text} size="small" />;
    }

    return (
      <Text
        style={[styles.cellText, { textAlign: getTextAlign() }]}
        numberOfLines={0}
      >
        {text}
      </Text>
    );
  };

  return (
    <View style={[styles.cell, { width: validatedWidth }]} onTouchEnd={onPress}>
      {renderContent()}
    </View>
  );
});

AdvancedCell.displayName = 'AdvancedCell';

// Advanced header component
const AdvancedHeader = memo<{
  column: AdvancedTableColumn;
  onPress?: () => void;
}>(({ column, onPress }) => {
  const getTextAlign = () => column.align || 'left';
  const validatedWidth = validateColumnWidth(column.width);
  // Apply backend header styling if provided
  const headerCellStyle = [
    styles.header,
    { width: validatedWidth },
    ...(column.headerStyle?.backgroundColor
      ? [{ backgroundColor: column.headerStyle.backgroundColor }]
      : []),
  ];
  const headerTextStyle = [
    styles.headerText,
    { textAlign: getTextAlign() },
    ...(column.headerStyle?.color ? [{ color: column.headerStyle.color }] : []),
    ...(column.headerStyle?.fontSize
      ? [{ fontSize: column.headerStyle.fontSize }]
      : []),
  ];

  return (
    <View style={headerCellStyle} onTouchEnd={onPress}>
      <Text style={headerTextStyle} numberOfLines={0}>
        {column.header}
      </Text>
    </View>
  );
});

AdvancedHeader.displayName = 'AdvancedHeader';

// Advanced table component - Enhanced layout with scrolling
export const AdvancedTable = memo<AdvancedTableProps>(
  ({ data, onCellPress, onHeaderPress }) => {
    const handleCellPress = (rowIndex: number, columnKey: string, value: any) => {
      onCellPress?.(rowIndex, columnKey, value);
    };

    const handleHeaderPress = (columnKey: string) => {
      onHeaderPress?.(columnKey);
    };

    // Apply backend title styling if provided
    const titleStyle = [
      styles.title,
      ...(data.titleStyle?.backgroundColor
        ? [{ backgroundColor: data.titleStyle.backgroundColor }]
        : []),
      ...(data.titleStyle?.color ? [{ color: data.titleStyle.color }] : []),
      ...(data.titleStyle?.fontSize ? [{ fontSize: data.titleStyle.fontSize }] : []),
    ];

    const renderTable = () => (
      <View style={styles.table}>
        {/* Header row */}
        {!data.hideColumnHeader && (
          <View style={styles.headerRow}>
            {data.columns.map((column) => (
              <AdvancedHeader
                key={column.key}
                column={column}
                onPress={
                  onHeaderPress ? () => handleHeaderPress(column.key) : undefined
                }
              />
            ))}
          </View>
        )}

        {/* Data rows */}
        {data.rows.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.dataRow}>
            {data.columns.map((column) => (
              <AdvancedCell
                key={`${rowIndex}-${column.key}`}
                content={row[column.key]}
                column={column}
                onPress={
                  onCellPress
                    ? () => handleCellPress(rowIndex, column.key, row[column.key])
                    : undefined
                }
              />
            ))}
          </View>
        ))}
      </View>
    );

    return (
      <View style={styles.container}>
        {!data.hideTitle && <Text style={titleStyle}>{data.title}</Text>}

        <GestureHandlerScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.scrollContainer}
          contentContainerStyle={styles.scrollContent}
        >
          {renderTable()}
        </GestureHandlerScrollView>
      </View>
    );
  },
);

AdvancedTable.displayName = 'AdvancedTable';

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
    borderRadius: 12,
    backgroundColor: COLORS.WHITE,
    elevation: 3,
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  title: {
    fontSize: 18,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
    backgroundColor: COLORS.GREY_VAR_11,
    paddingVertical: 14,
    paddingHorizontal: 16,
    textAlign: 'center',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  scrollContainer: {
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  scrollContent: {
    paddingBottom: 2,
  },
  table: {
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: COLORS.GREY_VAR_5,
    backgroundColor: COLORS.WHITE,
  },
  headerRow: {
    flexDirection: 'row',
    backgroundColor: COLORS.GREY_VAR_3,
    borderBottomWidth: 2,
    borderBottomColor: COLORS.GREY_VAR_5,
    minHeight: 48,
  },
  header: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRightWidth: 1,
    borderRightColor: COLORS.GREY_VAR_5,
    backgroundColor: COLORS.WHITE,
    // borderBottomWidth: 0.2,
  },
  dataRow: {
    flexDirection: 'row',
    minHeight: 60,
    alignItems: 'stretch',
    borderBottomWidth: 1,
    borderBottomColor: COLORS.GREY_VAR_5,
  },
  cell: {
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    alignSelf: 'stretch',
    borderRightWidth: 1,
    borderRightColor: COLORS.GREY_VAR_5,
  },
  headerText: {
    fontSize: 13,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.GREY_VAR_1,
    textAlign: 'center',
    lineHeight: 16,
    flexWrap: 'wrap', // Allow text wrapping for narrow columns
  },
  cellText: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_400,
    color: COLORS.TEXT_DEFAULT,
    lineHeight: 16,
    flexWrap: 'wrap', // Allow text wrapping for narrow columns
  },
});

export default AdvancedTable;
