import React from 'react';
import { View, StyleSheet, StyleProp, ViewStyle } from 'react-native';
import AiStar from '../../assets/AiStar';

interface AiStarCompProps {
  containerStyle?: StyleProp<ViewStyle>;
  primaryStarSize?: number;
  secondaryStarSize?: number;
  primaryStarStyle?: StyleProp<ViewStyle>;
  secondaryStarStyle?: StyleProp<ViewStyle>;
}

export const AiStarComp = ({
  containerStyle,
  primaryStarSize = 12,
  secondaryStarSize = 6,
  primaryStarStyle,
  secondaryStarStyle,
}: AiStarCompProps) => {
  return (
    <View style={[styles.starContainer, containerStyle && containerStyle]}>
      <View
        style={[styles.primaryStarContainer, primaryStarStyle && primaryStarStyle]}
      >
        <AiStar height={primaryStarSize} width={primaryStarSize} />
      </View>
      <View
        style={[
          styles.secondaryStarContainer,
          secondaryStarStyle && secondaryStarStyle,
        ]}
      >
        <AiStar height={secondaryStarSize} width={secondaryStarSize} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  starContainer: {
    position: 'relative',
    width: 10,
    height: 10,
    marginLeft: 2,
  },
  primaryStarContainer: {
    position: 'absolute',
    top: -7,
    left: -3,
  },
  secondaryStarContainer: {
    position: 'absolute',
    top: 0,
    left: 5,
  },
});
