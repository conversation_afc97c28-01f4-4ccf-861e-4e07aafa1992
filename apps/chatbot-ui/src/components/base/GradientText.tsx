/* eslint-disable @typescript-eslint/ban-ts-comment */
import React from 'react';
import { Platform, Text, TextProps } from 'react-native';
import MaskedView from '@react-native-community/masked-view';
import LinearGradient from 'react-native-linear-gradient';
import { COLORS } from '../../constants/globalStyles';

type GradientProps = {
  style?: TextProps['style'];
  gradientColors?: string[];
  children: string;
  start?: { x: number; y: number };
  end?: { x: number; y: number };
};

const DEFAULT_GRADIENT_COLORS = ['#51AFE6', '#355FF2', '#11287A'];
const DEFAULT_START = { x: 0, y: 0 };
const DEFAULT_END = { x: 2, y: 2 };

const GradientText = (props: GradientProps) => {
  const {
    start = DEFAULT_START,
    end = DEFAULT_END,
    gradientColors = DEFAULT_GRADIENT_COLORS,
  } = props || {};
  if (Platform.OS === 'web') {
    const gradinentColorsToAdd = gradientColors.join(', ');
    return (
      <Text
        style={[
          {
            //@ts-ignore
            background: `linear-gradient(90deg, ${gradinentColorsToAdd})`,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: COLORS.TRANSPARENT,
          },
          props.style,
        ]}
      >
        {props.children}
      </Text>
    );
  }
  return (
    <MaskedView maskElement={<Text {...props} />}>
      <LinearGradient colors={gradientColors} start={start} end={end}>
        <Text {...props} style={[props.style, { opacity: 0 }]} />
      </LinearGradient>
    </MaskedView>
  );
};

export default GradientText;
