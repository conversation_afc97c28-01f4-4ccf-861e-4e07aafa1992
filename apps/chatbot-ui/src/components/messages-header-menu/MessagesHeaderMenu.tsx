import React, { useMemo } from 'react';
import { Menu, MenuOption, MenuOptions, MenuTrigger } from 'react-native-popup-menu';
import { Platform, Pressable, StyleSheet, Text, View } from 'react-native';
import { MenuIcon } from '../../assets/MenuIcon';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { IconMapper } from '../IconMapper';
import { useMessageStore } from '../../store/messages/messageStore';
import { useMessageAction } from '../../store/messages/useMessageAction';
import { TrackingEvent } from '../../analytics/events';
import { Analytics } from '../../analytics/analytics';

export function MessagesHeaderMenu({
  onHistoryClicked,
  onNewChat,
  onMenuClicked,
  onTalkToAgentClicked,
  onBookmarkClicked,
  onChatClicked,
  onItineraryClicked,
  menuContext,
  type = 'left',
  onDeleteClicked,
}: {
  onHistoryClicked: () => void;
  onNewChat?: () => void; // undefined if new chat is not allowed
  onMenuClicked?: () => void;
  onTalkToAgentClicked?: () => void;
  onBookmarkClicked?: () => void;
  onChatClicked?: () => void;
  onItineraryClicked?: () => void;
  menuContext: ChatHeaderConfig['header'];
  onDeleteClicked?: () => void;
  type?: 'left' | 'right';
}) {
  // Feature flag checks
  const { conversationById, activeConversationId } = useMessageStore();
  const conversationData = conversationById[activeConversationId || 'draft'];
  const { sendMessage } = useMessageAction();
  // handle enabling for menus

  const showTalkToAgentSmall = useMemo(
    () =>
      conversationData && 'hideT2aCta' in conversationData
        ? !conversationData?.hideT2aCta
        : true,
    [conversationData],
  );

  // Helper function to check if menu item should be shown
  const shouldShowMenuItem = (actionValue: string) => {
    switch (actionValue) {
      case 'talktoagentsmall':
        return showTalkToAgentSmall;
      default:
        return true;
    }
  };

  const { menuItems, leftItems, rightItems } = menuContext;
  const menuClickHandlerMap: Record<string, (() => void) | undefined> = {
    chat: onChatClicked,
    chats: onHistoryClicked,
    back: onChatClicked,
    itinerary: onItineraryClicked,
    trips: onItineraryClicked,
    tripssmall: onItineraryClicked,
    itinerarysmall: onItineraryClicked,
    newchat: onNewChat,
    home: onNewChat,
    history: onHistoryClicked,
    talktoagent: onTalkToAgentClicked,
    bookmarks: onBookmarkClicked,
    talktoagentsmall: onTalkToAgentClicked,
    delete: onDeleteClicked,
  };
  // Filter menu items based on availability of handlers and conditions
  const availableMenuItems =
    menuItems?.filter((item: MenuItem) => {
      return (
        shouldShowMenuItem(item.icon) &&
        ((item?.action?.type === 'navigate' &&
          menuClickHandlerMap[
            item?.action?.value as keyof typeof menuClickHandlerMap
          ] !== undefined) ||
          item?.action?.type === 'message')
      );
    }) || [];
  const hasMenuItems = type === 'left' ? availableMenuItems.length > 0 : false;

  const availableLeftItems =
    leftItems?.filter((item: MenuItem) => {
      return shouldShowMenuItem(item.icon);
    }) || [];

  const availableRightItems =
    rightItems?.filter((item: MenuItem) => {
      return shouldShowMenuItem(item.icon);
    }) || [];
  const handleMenuClicked = (menuItem: MenuItem) => {
    const action = menuItem.action;
    if (action.type === 'navigate') {
      menuClickHandlerMap[action.value as keyof typeof menuClickHandlerMap]?.();
    } else if (action.type === 'message') {
      sendMessage(action.value);
    }
    if (
      action.type === 'message' &&
      (menuItem.icon === 'talktoagent' || menuItem.icon === 'talktoagentsmall')
    ) {
      Analytics.trackClickEvent(TrackingEvent.payload_TalkToAgentClicked());
    }
  };
  const displayItems = type === 'left' ? availableLeftItems : availableRightItems;

  return (
    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
      {/* Render Menu popover if menuItems exist */}
      {hasMenuItems && (
        <Menu>
          <MenuTrigger
            onPress={onMenuClicked}
            customStyles={{
              ...(Platform.OS !== 'web' &&
                {
                  // TriggerTouchableComponent: TouchableFeedback,
                }),
            }}
          >
            <View style={{ padding: 8, margin: 4 }}>
              <MenuIcon />
            </View>
          </MenuTrigger>

          <MenuOptions
            customStyles={{}}
            optionsContainerStyle={optionStyles.optionListContainer}
          >
            {availableMenuItems.map((item: MenuItem, index: number) => {
              return (
                <MenuOption
                  key={index}
                  style={optionStyles.menuOption}
                  onSelect={() => handleMenuClicked(item)}
                >
                  <View style={optionStyles.optionContainer}>
                    <IconMapper
                      iconName={item.icon}
                      width={item.iconUrlWidth || 24}
                      height={item.iconUrlHeight || 24}
                      iconUrl={item.iconUrl}
                    />
                    <Text style={optionStyles.optionText}>{item.text}</Text>
                  </View>
                </MenuOption>
              );
            })}
          </MenuOptions>
        </Menu>
      )}

      {/* Render items as separate pressable icons */}
      {displayItems?.length > 0 &&
        displayItems.map((item: MenuItem, index: number) => {
          return (
            <Pressable
              key={index}
              onPress={() => handleMenuClicked(item)}
              style={{ marginLeft: hasMenuItems ? 8 : 0 }}
            >
              {type === 'right' && item.text ? (
                // Right side items: render with both icon and text
                <View style={rightItemStyles.rightItemContainer}>
                  <IconMapper
                    iconName={item.icon}
                    width={item.iconUrlWidth || 20}
                    height={item.iconUrlHeight || 20}
                    iconUrl={item.iconUrl}
                  />
                  <Text style={rightItemStyles.rightItemText}>{item.text}</Text>
                </View>
              ) : (
                // Left side items: render icon only
                <View style={{ padding: 8, margin: 4 }}>
                  <IconMapper
                    iconName={item.icon}
                    width={item.iconUrlWidth}
                    height={item.iconUrlHeight}
                    iconUrl={item.iconUrl}
                  />
                </View>
              )}
            </Pressable>
          );
        })}
    </View>
  );
}

const optionStyles = StyleSheet.create({
  optionListContainer: {
    padding: 0,
    marginTop: 36,
    marginLeft: 12,
    borderRadius: 8,
    zIndex: 9999,
  },
  optionContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
    marginVertical: 4,
    marginHorizontal: 4,
  },
  menuOption: {
    padding: 8,
    maxHeight: 48,
  },
  optionText: {
    fontSize: 16,
    lineHeight: 20,
    color: COLORS.TEXT_HIGH_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_400,
  },
});

const rightItemStyles = StyleSheet.create({
  rightItemContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 6,
    marginHorizontal: 12,
    borderRadius: 100,
    backgroundColor: COLORS.GREY_VAR_11,
  },
  rightItemText: {
    fontSize: 12,
    lineHeight: 16,
    color: COLORS.TEXT_HIGH_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_400,
  },
});
