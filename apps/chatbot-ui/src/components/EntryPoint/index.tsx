import React, { useEffect, useRef, useState } from 'react';
import { StyleSheet, TouchableOpacity, View, Dimensions } from 'react-native';
import MyraIcon from './MyraIcon';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import { COLORS } from '../../constants/globalStyles';
import GradientText from '../base/GradientText';
import AiStar from '../../assets/AiStar';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';

export interface EntryPointProps {
  onPress: () => void;
  onLoadCB?: () => void;
  textScrollProps?: {
    texts?: Array<string>;
    textScrollingInterval?: number;
  };
  shouldAnimate?: boolean;
  showNewMessageIcon?: boolean;
  isCentreAligned?: boolean;
  textStyles?: {
    fontSize?: number;
    fontWeight?: number;
  };
  showStar?: boolean;
}

const textScrollContainerHeight = 52;
const defaultTextScrollingInterval = 1500;
const containerWidth = 225;
export const TravelplexFloatingCta = ({
  textScrollProps,
  onPress,
  onLoadCB,
  shouldAnimate = true,
  showNewMessageIcon = false,
  isCentreAligned = false,
  textStyles = {
    fontSize: 16,
    fontWeight: 700,
  },
  showStar = true,
}: EntryPointProps) => {
  const [showExpanded, setShowExpanded] = useState(false);
  const { texts, textScrollingInterval } = textScrollProps || {};
  const width = useSharedValue(0);
  const paddingLeft = useSharedValue(0);
  const translateY = useSharedValue(textScrollContainerHeight);

  const screenWidth = Dimensions.get('window').width;
  const expandedWidth = containerWidth + 50;
  const centerMarginRight = isCentreAligned ? (screenWidth - expandedWidth) / 2 : 20;

  const animatedMarginRight = useSharedValue(20);
  const loadCbCalledRef = useRef<boolean>(false);

  useEffect(() => {
    if (loadCbCalledRef.current) {
      return;
    }
    loadCbCalledRef.current = true;
    onLoadCB?.();
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.TRVLPLX_SHOWN,
    });
  }, [onLoadCB]);

  useEffect(() => {
    if (!shouldAnimate) {
      return;
    }
    if (width.value > 0) {
      return;
    }
    const timer = setTimeout(() => {
      // Start both animations in parallel after 2 seconds
      animatedMarginRight.value = withTiming(centerMarginRight, { duration: 1000 });
      width.value = withTiming(
        width.value + containerWidth,
        { duration: 1000 },
        () => {
          if (!texts) {
            return;
          }
          translateY.value = withSequence(
            ...texts.map((_: string, index: number) =>
              withDelay(
                index === 0
                  ? 0
                  : textScrollingInterval || defaultTextScrollingInterval,
                // 1500,
                withTiming(
                  -index * textScrollContainerHeight,
                  { duration: 600 },
                  () => {
                    if (index === texts.length - 1) {
                      translateY.value = withDelay(
                        textScrollingInterval || defaultTextScrollingInterval,
                        withTiming(
                          textScrollContainerHeight,
                          { duration: 10 },
                          () => {
                            width.value = withDelay(
                              30,
                              withTiming(0, { duration: 1000 }),
                            );
                            paddingLeft.value = withDelay(
                              30,
                              withTiming(0, { duration: 1000 }),
                            );
                            animatedMarginRight.value = withDelay(
                              30,
                              withTiming(20, { duration: 1000 }),
                            );
                          },
                        ),
                      );
                    }
                  },
                ),
              ),
            ),
          );
        },
      );
      paddingLeft.value = withTiming(paddingLeft.value + 24, { duration: 1000 });
      setShowExpanded(true);
    }, 2000);
    return () => clearTimeout(timer);
  }, [
    shouldAnimate,
    width,
    paddingLeft,
    translateY,
    texts,
    centerMarginRight,
    animatedMarginRight,
  ]);

  const handlePress = () => {
    onPress?.();
    width.value = withTiming(0, { duration: 700 });
    paddingLeft.value = withTiming(0, { duration: 700 });
    animatedMarginRight.value = withTiming(20, { duration: 700 });
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.TRVLPLX_CLICKED,
    });
  };
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  const wrapperAnimatedStyle = useAnimatedStyle(() => ({
    marginRight: animatedMarginRight.value,
  }));

  return (
    <Animated.View style={[styles.myraIconAnimatedWrapper, wrapperAnimatedStyle]}>
      <TouchableOpacity
        activeOpacity={0.9}
        onPress={handlePress}
        hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
      >
        <View style={styles.myraIconView}>
          <View style={styles.myraIconWrapper}>
            <MyraIcon showNewMessage={showNewMessageIcon} />
          </View>
          <Animated.View
            style={[styles.textScrollContainer, { width, paddingLeft }]}
          >
            {showExpanded && (
              <View
                style={{
                  height: textScrollContainerHeight,
                  paddingRight: 5,
                  maxWidth: '95%',
                  overflow: 'hidden',
                }}
              >
                {texts?.map((text: string, index: number) => (
                  <Animated.View
                    key={`text|${index}`}
                    style={[
                      {
                        width: '100%',
                        overflow: 'hidden',
                        flexDirection: 'row',
                        height: textScrollContainerHeight,
                        paddingRight: 3,
                        paddingVertical: Math.floor(
                          (textScrollContainerHeight - 20) / 2,
                        ),
                        justifyContent: 'center',
                      },
                      animatedStyle,
                    ]}
                  >
                    <GradientText
                      gradientColors={['#355FF2', '#355FF2']}
                      style={[
                        styles.text,
                        {
                          fontSize: textStyles.fontSize,
                          fontWeight: textStyles.fontWeight?.toString() as any,
                        },
                      ]}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 0.45, y: 0 }}
                    >
                      {text}
                    </GradientText>
                    {!!showStar && (
                      <View style={styles.starContainer}>
                        <View style={styles.bigStar}>
                          <AiStar height={14} width={14} />
                        </View>
                        <View style={styles.smallStar}>
                          <AiStar height={8} width={8} />
                        </View>
                      </View>
                    )}
                  </Animated.View>
                ))}
              </View>
            )}
          </Animated.View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};
const styles = StyleSheet.create({
  textScrollContainer: {
    backgroundColor: COLORS.WHITE,
    opacity: 1,
    marginLeft: -20,
    zIndex: 1,
    borderTopEndRadius: 25,
    borderBottomEndRadius: 25,
    // overflow: 'hidden',
    height: textScrollContainerHeight,
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 2,
    elevation: 5,
    flexDirection: 'row',
    alignItems: 'center',
  },
  myraIconAnimatedWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    justifyContent: 'flex-end',
  },
  myraIconView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 30,
  },
  myraIconContainer: {
    flexDirection: 'row',
    zIndex: 2,
    shadowColor: COLORS.BLACK,
    shadowOpacity: 0.1,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: 2 },
    elevation: 10,
    margin: 10,
  },
  myraIconWrapper: {
    backgroundColor: COLORS.GREY_VAR_6,
    shadowColor: COLORS.BLACK,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 99,
    borderRadius: 50,
  },
  text: {
    fontSize: 16,
    height: 20,
    fontWeight: '700',
    color: COLORS.BLACK,
    // overflow: 'hidden',
  },
  starContainer: {
    position: 'relative',
    flexDirection: 'row',
    bottom: 5,
  },
  bigStar: { position: 'relative', bottom: 4 },
  smallStar: { position: 'relative', top: 4, left: -3 },
});
