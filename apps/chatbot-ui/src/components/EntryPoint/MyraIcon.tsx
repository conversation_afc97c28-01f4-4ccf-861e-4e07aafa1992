import React from 'react';
import {
  Image,
  ImageStyle,
  StyleProp,
  StyleSheet,
  View,
  ViewStyle,
} from 'react-native';
import { ImageView } from '../image/ImageView';
import { COLORS } from '../../constants/globalStyles';

const iconUrl =
  'https://jsak.mmtcdn.com/pwa/platform-myra-ui/static/sub_icons/tp-new-animated-without-con.webp';
const iconUrlNonAnimated =
  'https://jsak.mmtcdn.com/pwa/platform-myra-ui/static/sub_icons/<EMAIL>';
interface MyraIconProps {
  showNewMessage: boolean;
  overrideIconStyle?: any;
  overrideIconContainerStyle?: StyleProp<ViewStyle>;
  shouldAnimate?: boolean;
}

const MyraIcon = ({
  showNewMessage,
  overrideIconContainerStyle,
  overrideIconStyle,
  shouldAnimate = true,
}: MyraIconProps) => {
  let iconToUse = iconUrl;
  if (!shouldAnimate) {
    iconToUse = iconUrlNonAnimated;
  }
  // /* @ts-ignore */
  return (
    <View
      style={[
        styles.iconContainer,
        ...(overrideIconContainerStyle ? [overrideIconContainerStyle] : []),
      ]}
    >
      <ImageView
        style={[styles.icon, ...(overrideIconStyle ? [overrideIconStyle] : [])]}
        containerStyle={[
          styles.icon,
          ...(overrideIconStyle ? [overrideIconStyle] : []),
        ]}
        source={{
          uri: iconToUse,
        }}
        resizeMode="cover"
        placeholderComponent={StaticPlaceholder}
      />
      {showNewMessage ? <View style={styles.newMessageIcon} /> : <View />}
    </View>
  );
};

function StaticPlaceholder({ style }: { style?: ViewStyle }) {
  return (
    <Image
      source={{ uri: iconUrlNonAnimated }}
      style={[styles.icon, style as ImageStyle]}
    />
  );
}

const styles = StyleSheet.create({
  iconContainer: {
    backgroundColor: COLORS.WHITE,
    borderColor: COLORS.WHITE,
    padding: 4,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 99,
    alignSelf: 'flex-start',
    position: 'relative',
  },
  icon: {
    height: 50,
    width: 50,
    borderRadius: 25,
  },
  newMessageIcon: {
    height: 10,
    width: 10,
    borderRadius: 100 / 2,
    position: 'absolute',
    zIndex: 2,
    elevation: 2,
    top: 5,
    left: 5,
    backgroundColor: COLORS.RED,
  },
});
export default MyraIcon;
