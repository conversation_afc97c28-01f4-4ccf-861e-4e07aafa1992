import React from 'react';
import { StyleSheet, View } from 'react-native';
import GradientText from '../../base/GradientText';
import { MarkdownText } from '../../message/MarkdownText';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import AiStar from '../../../assets/AiStar';

interface TripModeTitleDescProps {
  title: string;
  description: string;
}

const TripModeTitleDesc = ({ title, description }: TripModeTitleDescProps) => {
  return (
    <View style={styles.container}>
      {title && (
        <View style={styles.titleContainer}>
          <GradientText
            gradientColors={['#51AFE6', '#355FF2', '#11287A']}
            start={{ x: 0, y: 0.6 }}
            end={{ x: 0.6, y: 1 }}
            style={styles.title}
          >
            {title}
          </GradientText>
          <View style={styles.starContainer}>
            <View style={styles.primaryStarContainer}>
              <AiStar height={12} width={12} />
            </View>
            <View style={styles.secondaryStarContainer}>
              <AiStar height={6} width={6} />
            </View>
          </View>
        </View>
      )}
      {description && (
        <MarkdownText text={description} size="default" lineHeight={20} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // paddingHorizontal: 16,
    paddingVertical: 12,
  },
  title: {
    fontSize: 16,
    fontFamily: FONTS.FONT_FAMILY_900,
    lineHeight: 18,
    color: COLORS.BLACK_VAR_1,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  starContainer: {
    position: 'relative',
    width: 10,
    height: 10,
    marginLeft: 2,
  },
  primaryStarContainer: {
    position: 'absolute',
    top: -7,
    left: -3,
  },
  secondaryStarContainer: {
    position: 'absolute',
    top: 0,
    left: 5,
  },
});
export default TripModeTitleDesc;
