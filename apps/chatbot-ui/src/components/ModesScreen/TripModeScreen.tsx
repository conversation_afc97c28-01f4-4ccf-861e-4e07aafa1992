import React, { useEffect } from 'react';
import { StyleSheet, View, TouchableWithoutFeedback, Keyboard } from 'react-native';
import TripModeTitleDesc from './TripMode/TripModeTitleDesc';
import { ImageView } from '../image/ImageView';
import { trackOmnitureClickEvent } from '../../native/omniture';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import { useResponsiveWidth } from '../../hooks/useResponsiveWidth';
import { useInputStateStore } from '../../store/messages';

interface TripModeScreenProps {
  title: string;
  description: string;
  image: string;
}

const TripModeScreen = (props: TripModeScreenProps) => {
  const { totalInputAreaHeight } = useInputStateStore();

  useEffect(() => {
    // Only track if we have valid props
    if (props.image) {
      trackPDTEvent({
        eventName: eventNameSchema.CHAT_INTERACTED,
        eventType: eventTypeSchema.ACTION,
        eventValue: eventValueSchema.TRIPMODE_ACTIVE,
      });
      trackOmnitureClickEvent('TRIPMODE_ACTIVE');
    }
  }, [props.image]);

  if (!props.image) {
    return null;
  }

  const { title, description } = props;
  const { containerWidth, screenWidth } = useResponsiveWidth();
  const imageWidth = screenWidth * 0.73;

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View style={styles.container}>
        <View style={styles.imageContainer}>
          <ImageView
            style={[
              styles.tripImage,
              {
                width: imageWidth,
              },
            ]}
            source={{ uri: props.image }}
            resizeMode="contain"
          />
        </View>

        {/* Title and description with fixed space */}
        <View style={styles.titleDescContainer}>
          <TripModeTitleDesc title={title} description={description} />
        </View>

        {/* Bottom spacer to account for input box area */}
        <View style={[styles.bottomSpacer, { height: totalInputAreaHeight }]} />
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  container: {
    flex: 1,
    flexDirection: 'column',
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 20,
  },
  tripImage: {
    flex: 1,
    width: '100%',
    maxHeight: '100%',
    marginLeft: 65,
    marginRight: 32,
  },
  titleDescContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 10,
    marginBottom: 10,
  },
  bottomSpacer: {
    paddingBottom: 10,
  },
});
export default TripModeScreen;
