import React from 'react';
import TripModeScreen from './TripModeScreen';
import { TRIP_PLAN_MODE_ID } from '../../hooks/useModeState';
import { useAppStateStore } from '../../store/app';

const modeMap: Record<string, React.ComponentType<any>> = {
  [TRIP_PLAN_MODE_ID]: TripModeScreen,
};
const ModeScreen = ({ mode }: { mode: string }) => {
  if (!mode) {
    return null;
  }
  const modes = useAppStateStore.getState().modes;
  const ScreenToShow = modeMap[mode];
  const enabledModeData = modes?.find((m) => m.id === mode);
  if (!ScreenToShow || !enabledModeData) {
    return null;
  }
  return <ScreenToShow {...enabledModeData} />;
};

export default ModeScreen;
