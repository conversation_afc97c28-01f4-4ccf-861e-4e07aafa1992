import React from 'react';
import { Pressable, Text, StyleSheet, ViewStyle } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { FONTS, COLORS } from '../../constants/globalStyles';

export enum ButtonType {
  Primary = 'primary',
  Secondary = 'secondary',
}

export interface ButtonProps {
  title: string;
  onPress: () => void;
  buttonType: ButtonType;
  disabled?: boolean;
  customStyle?: ViewStyle;
}

export const Button = React.memo(({
  title,
  onPress,
  buttonType,
  disabled = false,
  customStyle,
}: ButtonProps) => {
  const isSecondary = buttonType === ButtonType.Secondary;

  return (
    <Pressable
      onPress={onPress}
      disabled={disabled}
      style={[
        styles.button,
        isSecondary && styles.secondaryButton,
        disabled && styles.disabledButton,
        customStyle,
      ]}
      accessibilityRole="button"
      accessibilityLabel={title}
      accessibilityState={{ disabled }}
    >
      {buttonType === ButtonType.Primary ? (
        <LinearGradient
          colors={['#53B2FE', '#065AF3']}
          start={{ x: 0, y: 0.5 }}
          end={{ x: 1, y: 0.5 }}
          style={styles.primaryGradient}
        >
          <Text
            style={[styles.commonButtonText, styles.primaryButtonText]}
            numberOfLines={1}
          >
            {title}
          </Text>
        </LinearGradient>
      ) : (
        <Text
          style={[styles.commonButtonText, styles.secondaryButtonText]}
          numberOfLines={1}
        >
          {title}
        </Text>
      )}
    </Pressable>
  );
});

Button.displayName = 'Button';


const styles = StyleSheet.create({
  button: {
    width: '100%',
    height: 44,
    borderRadius: 8,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  secondaryButton: {
    borderWidth: 1,
    borderColor: '#008CFF',
  },
  disabledButton: {
    opacity: 0.6,
  },
  primaryButtonText: {
    color: COLORS.WHITE,
  },
  secondaryButtonText: {
    color: COLORS.BLUE_VAR_1,
  },
  commonButtonText: {
    fontFamily: FONTS.FONT_FAMILY_900,
    fontSize: 16,
    lineHeight: 18,
    textAlign: 'center',
  },
  primaryGradient: {
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
  