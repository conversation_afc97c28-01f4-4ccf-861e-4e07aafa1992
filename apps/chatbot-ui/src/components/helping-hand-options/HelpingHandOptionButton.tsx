import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { COLORS, FONTS } from '../../constants/globalStyles';

interface HelpingHandOptionButtonProps {
  header: string;
  subHeader: string;
  Icon: React.FC;
  onClick: () => void;
}

export const HelpingHandOptionButton: React.FC<HelpingHandOptionButtonProps> = (
  props,
) => {
  const { header, subHeader, onClick, Icon } = props;
  return (
    <TouchableOpacity style={styles.buttonContainer} onPress={onClick}>
      <View style={styles.iconPlaceholder}>
        <Icon />
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.title}>{header}</Text>
        <Text style={styles.description}>{subHeader}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 15,
    alignItems: 'center',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
  },
  iconPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.BLUE_VAR_2,
    marginRight: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: COLORS.BLUE_VAR_1,
  },
  description: {
    fontSize: 12,
    color: COLORS.GREY_VAR_2,
    fontFamily: FONTS.FONT_FAMILY_400,
  },
});
