import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { COLORS, FONTS } from '../../constants/globalStyles';

const CallbackSlotGroupsList = ({
  callbackSlots,
  selectedSlot,
  onSelectSlot,
}: any) => {
  return (
    <View style={styles.container}>
      {callbackSlots.map((item: any) => (
        <View key={item.headerText}>
          <Text style={styles.sectionHeader}>{item?.headerText?.toUpperCase()}</Text>
          <View style={styles.slotContainer}>
            {item.slotDetails.map((slot: any) => (
              <TouchableOpacity
                key={slot.timestamp}
                style={[
                  styles.slot,
                  selectedSlot?.timestamp === slot.timestamp && styles.selectedSlot,
                ]}
                onPress={() => onSelectSlot(slot)}
              >
                <Text
                  style={
                    selectedSlot?.timestamp === slot.timestamp
                      ? styles.selectedSlotText
                      : styles.slotText
                  }
                >
                  {trimTime(slot.slotText)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      ))}
    </View>
  );
};

//FIXME this is just to test UI, this should be fixed from the server
function trimTime(time: string) {
  const separator = ' - ';
  return time
    .split(separator)
    .map((str: string) => {
      if (str.startsWith('0')) {
        str = str.slice(1);
      }
      return str.replace(':00', '');
    })
    .join(separator);
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  sectionHeader: {
    fontSize: 11.3,
    fontFamily: FONTS.FONT_FAMILY_700,
    marginTop: 15,
    marginBottom: 5,
    color: COLORS.GREY_VAR_2,
  },
  slotContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  slot: {
    paddingVertical: 13,
    paddingHorizontal: 24,
    borderRadius: 16,
    margin: 5,
    borderColor: '#0000001A',
    borderWidth: 1,
  },
  selectedSlot: {
    backgroundColor: '#007bff',
  },
  slotText: {
    color: '#000',
    fontSize: 13.2,
  },
  selectedSlotText: {
    color: '#fff',
  },
});

export default CallbackSlotGroupsList;
