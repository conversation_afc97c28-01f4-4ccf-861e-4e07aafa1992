import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { COLORS, FONTS } from '../../constants/globalStyles';
import IconPin from '../../assets/IconPin';
import IconPinGray from '../../assets/IconPinGray';
import IconMidStop from '../../assets/IconMidStop';
import { IconMap } from '../message/cards/TravelDetailCard';

const Pin = ({ isEnd = false }) => {
  return isEnd ? <IconPin /> : <IconPinGray />;
};

/* Section: TravelRoute component definition */
export const TravelRoute = ({
  segments,
}: {
  segments: TravelDetailCardProps['segments'];
}) => {
  const segmentWidth = 100 / segments.length;
  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      horizontal
      showsHorizontalScrollIndicator={false}
      nativeID="travel-route-scrollview"
    >
      {segments.map((segment, index) => (
        <View
          key={index}
          style={[
            styles.segment,
            { width: `${segmentWidth}%` },
          ]} /* Section: Dynamic style function for segment width */
        >
          <View style={styles.pinContainer}>
            {/* First pin or mid stop icon */}
            {index === 0 ? <Pin /> : <IconMidStop />}
          </View>
          <View style={styles.pinOverlay}>
            {/* Line and arrow box */}
            <View style={styles.line} />
            {index === segments.length - 1 && <View style={styles.box} />}
          </View>

          {segment?.type &&
            IconMap[segment.type.toLowerCase() as keyof typeof IconMap] && (
              <View style={styles.iconContainer}>
                {IconMap[segment.type.toLowerCase() as keyof typeof IconMap]}
              </View>
            )}

          {index === segments.length - 1 ? (
            <View style={styles.pinContainer}>
              <Pin isEnd />
            </View>
          ) : (
            <View />
          )}

          <Text style={[styles.textSource, index === 0 ? null : styles.textOffset]}>
            {segment.source}
          </Text>
          {index === segments.length - 1 && (
            <Text style={[styles.textSource, styles.textDestination]}>
              {segment.destination}
            </Text>
          )}
        </View>
      ))}
    </ScrollView>
  );
};

/* Section: Style definitions */
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: 50,
  },
  contentContainer: {
    flexGrow: 1,
  },
  segment: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  pinContainer: {
    padding: 5,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  pinOverlay: {
    flex: 1,
    position: 'absolute',
    width: '100%',
    flexDirection: 'row',
  },
  iconContainer: {
    padding: 5,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
  },
  textSource: {
    position: 'absolute',
    zIndex: 1,
    bottom: 0,
    fontSize: 12,
    color: COLORS.BLACK_VAR_1,
    fontFamily: FONTS.FONT_FAMILY_400,
  },
  textOffset: {
    top: 0,
    left: -20,
  },
  textDestination: {
    position: 'absolute',
    zIndex: 1,
    bottom: 0,
    right: 0,
  },
  circle: {
    width: 4,
    height: 4,
    borderRadius: 0,
    borderWidth: 1,
    padding: 1,
    borderColor: COLORS.BLUE_VAR_1,
  },
  line: {
    height: 1,
    backgroundColor: COLORS.GREY_VAR_5,
    width: '100%',
  },
  box: {
    width: 6,
    height: 6,
    borderRightWidth: 2,
    borderTopWidth: 2,
    borderColor: COLORS.GREY_VAR_5,
    transform: [{ rotate: '45deg' }],
    marginLeft: -2,
    right: 25,
    bottom: -2.5,
    position: 'absolute',
  },
});
