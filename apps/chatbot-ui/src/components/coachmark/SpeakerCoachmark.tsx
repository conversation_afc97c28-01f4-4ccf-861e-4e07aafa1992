import React from 'react';
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import { FONTS } from '../../constants/globalStyles';
import { CrossIcon } from '../../assets/CrossIcon';

type ArrowDirection = 'up' | 'down' | 'left' | 'right';

interface CoachmarkProps {
  onClose: () => void;
  visible: boolean;
  text: string;
  arrowDirection?: ArrowDirection;
  position?: {
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  };
  width?: number;
  arrowOffset?: number;
}

export const SpeakerCoachmark: React.FC<CoachmarkProps> = ({
  onClose,
  visible,
  text,
  arrowDirection = 'up',
  position = { top: 40, left: 2 },
  width = 254,
  arrowOffset = 110,
}) => {
  if (!visible) {
    return null;
  }

  const containerStyle = {
    ...styles.container,
    ...position,
    width,
  };

  const arrowStyle = {
    ...getArrowStyles(arrowDirection, arrowOffset),
  };

  const bubbleStyle = {
    ...styles.bubble,
    width,
  };

  return (
    <View style={containerStyle}>
      {arrowDirection === 'up' && <View style={arrowStyle} />}

      {/* Main coachmark bubble */}
      <View style={bubbleStyle}>
        <Text style={styles.text}>{text}</Text>

        {/* Close button */}
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <CrossIcon width={14} height={14} fill="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {arrowDirection === 'down' && <View style={arrowStyle} />}
    </View>
  );
};

// Helper function to get arrow styles based on direction
const getArrowStyles = (direction: ArrowDirection, offset: number) => {
  const baseArrow = {
    width: 0,
    height: 0,
    borderLeftWidth: 8,
    borderRightWidth: 8,
  };

  switch (direction) {
    case 'down':
      return {
        ...baseArrow,
        borderTopWidth: 8,
        borderLeftColor: 'transparent',
        borderRightColor: 'transparent',
        borderTopColor: '#007E7D',
        alignSelf: 'flex-start' as const,
        marginLeft: offset,
        marginTop: -1, // Connect to bottom of bubble
      };
    case 'up':
      return {
        ...baseArrow,
        borderBottomWidth: 8,
        borderLeftColor: 'transparent',
        borderRightColor: 'transparent',
        borderBottomColor: '#007E7D',
        alignSelf: 'flex-start' as const,
        marginLeft: offset,
        marginBottom: -1, // Connect to top of bubble
      };
    case 'left':
      return {
        width: 0,
        height: 0,
        borderTopWidth: 8,
        borderBottomWidth: 8,
        borderRightWidth: 8,
        borderTopColor: 'transparent',
        borderBottomColor: 'transparent',
        borderRightColor: '#007E7D',
        alignSelf: 'flex-start' as const,
        marginTop: offset,
        marginRight: -1,
      };
    case 'right':
      return {
        width: 0,
        height: 0,
        borderTopWidth: 8,
        borderBottomWidth: 8,
        borderLeftWidth: 8,
        borderTopColor: 'transparent',
        borderBottomColor: 'transparent',
        borderLeftColor: '#007E7D',
        alignSelf: 'flex-start' as const,
        marginTop: offset,
        marginLeft: -1,
      };
    default:
      return baseArrow;
  }
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    zIndex: 10000,
    elevation: 20,
  },
  bubble: {
    backgroundColor: '#007E7D',
    borderRadius: 8,
    minHeight: 48,
    paddingHorizontal: 12,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    opacity: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 12,
  },
  text: {
    color: '#FFFFFF',
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    fontWeight: '400',
    fontStyle: 'normal',
    lineHeight: 14,
    letterSpacing: 0,
    textAlignVertical: 'center',
    flex: 1,
    includeFontPadding: false,
  },
  closeButton: {
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 12,
    borderRadius: 24,
  },
}); 