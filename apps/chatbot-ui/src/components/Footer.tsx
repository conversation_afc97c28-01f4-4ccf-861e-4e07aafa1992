import React, { useCallback, useEffect, useState } from 'react';
import BottomMessage from './message/BottomMessage';
import { InputBox } from './input-bar/InputBox';
import { SocketClient } from '../store/socket';
import { WebSocketState } from '../store/socket/SocketClient';
import { useConfigSettings } from '../store/messages/newChatView';
import { useMessageStore } from '../store/messages';
import { useConversationMessagesWithMutation } from '../store/messages/useConversationMessages';

const Footer = () => {
  // trackingKey is a unique value for the footer component of current chat session.
  // Used to ensure certain analytics events (e.g., InputBox with Mic shown) are tracked only once per session,
  // even if the InputBox is hidden/shown multiple times during the session.
  const [trackingKey] = useState(() => `${Date.now()}`);

  const [isConnecting, setIsConnecting] = useState(false);

  const activeConversationId = useMessageStore(
    (state) => state.activeConversationId,
  );

  const { mutate: fetchMessages } = useConversationMessagesWithMutation();

  const { isFetching: isConfigApiFetching, isError: isConfigApiError } =
    useConfigSettings();

  const reconnectWebSocket = useCallback(async () => {
    // to be in sync with the latest messages
    if (activeConversationId !== 'draft' && activeConversationId) {
      fetchMessages(activeConversationId);
    }
    SocketClient.reconnectManually();
    setIsConnecting(true);
  }, [fetchMessages, activeConversationId]);

  const [isSocketActive, setIsSocketActive] = useState(true);
  useEffect(() => {
    const socketReadyState = SocketClient.getSocketReadyState();
    if (
      socketReadyState === WebSocketState.CLOSED ||
      socketReadyState === WebSocketState.ERROR
    ) {
      setIsSocketActive(false);
      setIsConnecting(false);
    }

    const unsubscribe = SocketClient.onSocketStateChange((state, isActive) => {
      setIsSocketActive(isActive);
      setIsConnecting(!isActive && state === WebSocketState.CONNECTING);
    });
    return () => {
      unsubscribe();
    };
  }, []);

  // hide the input bar when socket is not connected
  // hide the input bar when config api is loading or error
  if (isConfigApiFetching || isConfigApiError) return null;

  if (isSocketActive) {
    return <InputBox trackingKey={trackingKey} />;
  }

  return (
    <BottomMessage isConnecting={isConnecting} handlePress={reconnectWebSocket} />
  );
};

export default Footer;
