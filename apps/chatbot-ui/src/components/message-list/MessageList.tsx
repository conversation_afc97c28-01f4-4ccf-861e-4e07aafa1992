import { Platform, StyleSheet, View, ViewToken } from 'react-native';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useInputStateStore, useMessageAction } from '../../store/messages';
import { BotMessageLoader } from '../message/BotMessageLoader';
import { BotSuggestions, UserMessageBubble } from '../message';
import { FlatList } from 'react-native-gesture-handler';
import { ScrollBottomFAB } from '../ScrollBottomFAB';
import { TripSummaryCard } from '../message/trip-summary/TripSummaryCard';
import { useKeyboardState } from '../../utils/screenUtils';
import {
  AgentMessage,
  BotCardMessage,
  BotTextMessage,
  LeadingQuestion,
  NewChatPrompt,
  TableMessage,
  UserQuotedMessageBubble,
} from '../message/ChatMessageBubble';
import { MessageListItems } from '../../store/messages/listItemTypes';
import { Logger } from '../../utils/logger';
import { findLastIndex } from '../../utils/objectUtils';
import { SystemMessage } from '../message/SystemMessages';
import HelpingHandCards from '../message/helping-hands/HelpingHandCards';
import { HelpingHandSlotsModal } from '../message/helping-hands/HelpingHandSlotsModal';
import { BotSuggestionCTAs } from '../message/BotSuggestionCTAs';
import { PackageCardMessage } from '../message/PackageCard/PackageCardMessage';
import { INPUT_BOX_HEIGHT } from '../../const';
import { TravellerInsight } from '../message/TravellerInsight';
import ItineraryStatusCard from '../message/cards/ItineraryStatusCard';
import { useTalkBackStore } from '../../store/audio-talkback/talkbackStore';
import { TalkBackView } from '../talk-back/TalkBackView';
import VideoPlayerModal from '../message/PackageCard/VideoPlayerModal';
import { scrollEvents } from '../../utils/scrollEvents';

interface MessagesScreenProps {
  listData: MessageListItems[];
  setEvaluationData?: (data: MessageListItems[]) => void;
  isEvaluationMode?: boolean;
  compactHeightMode: boolean;
}

type AutoScrollOptions = {
  animated: boolean;
  target: 'end' | 'anchor';
  ignoreUserInput?: string;
  retryAttempt?: number;
};
const logger = Logger.createLogger({
  tag: 'MessageList',
  level: 'NONE',
});
export const MessageList = ({
  listData,
  setEvaluationData,
  isEvaluationMode,
  compactHeightMode,
}: MessagesScreenProps) => {
  const viewabilityConfig = useMemo(
    () => ({
      itemVisiblePercentThreshold: 50, // Item must be 50% visible
      minimumViewTime: 100, // Must be visible for 100ms
      waitForInteraction: false,
    }),
    [],
  );
  const flatListRef = useRef<FlatList<MessageListItems>>();
  const { selectSuggestion, selectSuggestionCTA } = useMessageAction();
  const { totalInputAreaHeight } = useInputStateStore();
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [videoModalData, setVideoModalData] = useState<{
    show: boolean;
    media: MediaInfo;
  } | null>(null);
  const isKeyboardShown = useKeyboardState() === 'SHOWN';
  const timersRef = useRef<Set<ReturnType<typeof setTimeout>>>();
  if (!timersRef.current) {
    timersRef.current = new Set();
  }
  const prevListCountRef = useRef(-1);
  const prevInputAreaHeight = useRef(totalInputAreaHeight);
  const currentScrollOffset = useRef(0);

  useEffect(() => {
    return () => {
      // clear all timers
      timersRef.current?.forEach((timer) => {
        clearTimeout(timer);
      });
      timersRef.current?.clear();
    };
  }, []);
  const scrollToPositionWithRetry = useCallback(
    (options: AutoScrollOptions) => {
      logger.debug(
        `---\n\nbegin scrollToPosition listSize=${listDataRef.current?.length}, viewPort=${JSON.stringify(viewportItems.current)} \n\n---`,
      );
      const { retryAttempt = 0, target, animated } = options;
      if (target === 'end') {
        logger.debug('scrolling to end');
        flatListRef.current?.scrollToOffset({
          offset: 999999, // Large offset ensures we reach absolute bottom
          animated,
        });
        prevListCountRef.current = listData.length || -1;
      } else if (target === 'anchor') {
        const anchorPos = findLastIndex(
          listDataRef.current || [],
          (item) =>
            item.type === 'USER_MSG' ||
            item.type === 'USER_QUOTED_MSG' ||
            item.type === 'AGENT_MSG_TEXT' ||
            item.type === 'SYSTEM_MSG_TEXT',
        );
        if (anchorPos > -1) {
          logger.debug(`scrolling to anchor. pos = ${anchorPos}`);
          flatListRef.current?.scrollToIndex({
            index: anchorPos,
            animated: true,
            viewOffset: 0,
          });
          prevListCountRef.current = anchorPos;
        }
      }
      const timer = setTimeout(() => {
        if (!options.ignoreUserInput && !shouldAutoScroll.current) {
          logger.debug('canceling retry. user scrolled');
          return;
        }
        const lastItemIndex = (listDataRef.current?.length || 0) - 1;
        const visibleItems = viewportItems.current;
        let shouldRetry = false;
        if (!visibleItems.length) {
          logger.debug(`no visible items. attempt=${retryAttempt}`);
          shouldRetry = true;
        } else if (target === 'anchor') {
          const firstVisibleItem = visibleItems[0];
          const latestAnchorPos = findLastIndex(
            listDataRef.current || [],
            (item) =>
              item.type === 'USER_MSG' ||
              item.type === 'USER_QUOTED_MSG' ||
              item.type === 'AGENT_MSG_TEXT' ||
              item.type === 'SYSTEM_MSG_TEXT',
          );
          // if anchor is not at the top, try again.
          shouldRetry = latestAnchorPos !== firstVisibleItem;
          if (shouldRetry) {
            logger.debug(
              `anchor is not at top. firstItem=${firstVisibleItem}, anchor=${latestAnchorPos} attempt=${retryAttempt}`,
            );
          }
        } else if (target === 'end') {
          shouldRetry = !visibleItems.includes(lastItemIndex);
          if (shouldRetry) {
            logger.debug(`scroll to end failed. attempt=${retryAttempt}`);
          }
        }
        if (shouldRetry) {
          retryAttempt < 3 &&
            scrollToPositionWithRetry({
              ...options,
              retryAttempt: retryAttempt + 1,
            });
        }
        timersRef.current?.delete(timer);
      }, 300);
      timersRef.current?.add(timer);
    },
    [listData],
  );

  useEffect(() => {
    if (isEvaluationMode && setEvaluationData) {
      setEvaluationData(listData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [listData, isEvaluationMode, setEvaluationData]);

  const scrollToEnd = useCallback(() => {
    scrollToPositionWithRetry({ animated: true, target: 'end' });
  }, [scrollToPositionWithRetry]);

  // Listen for coachmark scroll events
  useEffect(() => {
    const unsubscribe = scrollEvents.subscribe(() => {
      scrollToEnd();
    });

    return unsubscribe;
  }, [scrollToEnd]);

  useEffect(() => {
    if (isKeyboardShown) {
      scrollToEnd();
    }
  }, [isKeyboardShown, scrollToEnd]);

  // Handle input area height changes - scroll up when input grows, down when it shrinks
  // useEffect(() => {
  //   const heightDifference = totalInputAreaHeight - prevInputAreaHeight.current;

  //   // Handle both height increases and decreases
  //   if (heightDifference !== 0) {
  //     // Use setTimeout to ensure the layout has updated
  //     setTimeout(() => {
  //       // Try to get current offset from FlatList directly as fallback
  //       let currentOffset = currentScrollOffset.current;
  //       try {
  //         const scrollMetrics = (flatListRef.current as any)?._scrollMetrics;
  //         if (scrollMetrics?.offset !== undefined) {
  //           currentOffset = scrollMetrics.offset;
  //         }
  //       } catch (e) {
  //         // Fallback to tracked offset
  //       }

  //       const newOffset = Math.max(0, currentOffset + heightDifference);
  //       console.log('scrolling to offset', newOffset);
  //       flatListRef.current?.scrollToOffset({
  //         offset: newOffset,
  //         animated: true,
  //       });
  //       currentScrollOffset.current = newOffset;
  //     }, 100);
  //   }

  //   prevInputAreaHeight.current = totalInputAreaHeight;
  // }, [totalInputAreaHeight]);

  // Track scroll position
  const onScroll = useCallback((event: any) => {
    const newOffset = event.nativeEvent.contentOffset.y;
    currentScrollOffset.current = newOffset;
  }, []);

  // using ref instead of useState to avoid re-create of handleViewableItemsChanged.
  // FlatList doesn't allow dynamic change of onViewableItemsChanged
  const listDataRef = useRef<typeof listData>();
  listDataRef.current = listData;
  const viewportItems = useRef<number[]>([]);
  const onViewableItemsChanged: (info: {
    viewableItems: ViewToken[];
    changed: ViewToken[];
  }) => void = useCallback(({ viewableItems }: { viewableItems: ViewToken[] }) => {
    viewportItems.current = viewableItems.map((item) => item.index) as number[];
    const isAtBottom = viewableItems.some(
      (item) => item.index === (listDataRef.current?.length || 0) - 1,
    );
    setShowScrollToBottom(!isAtBottom);
  }, []);

  const isTalkBackViewShown = useTalkBackStore(
    (state) => state.currentTaskId !== null,
  );
  // Auto scroll to bottom initially
  const shouldAutoScroll = useRef<boolean>(true);
  const onScrollBeginDrag = useCallback(() => {
    shouldAutoScroll.current = false;
  }, []);

  const onScrollToIndexFailed = useCallback(() => {
    logger.error('scroll to index failed');
  }, []);

  // initially scroll to bottom

  useEffect(() => {
    if (showScrollToBottom && prevListCountRef.current === -1) {
      logger.debug('initial scroll to end');
      shouldAutoScroll.current = true;
      scrollToPositionWithRetry({ target: 'end', animated: false });
    }
  }, [showScrollToBottom, scrollToPositionWithRetry]);

  const prevLoaderIndex = useRef<number>(-1);
  // on new message
  useEffect(() => {
    if (!listData.length) {
      return;
    }
    const lastIdx = listData.length - 1;
    if (prevLoaderIndex.current === lastIdx) {
      // loader is still at the end and no new messages added
      return;
    }

    // else if loader is at the end and a new message added
    if (listData[lastIdx].type === 'MSG_LOADER') {
      // transitioning from no-loader to showing loader
      if (prevLoaderIndex.current === -1) {
        shouldAutoScroll.current = true;
        prevLoaderIndex.current = lastIdx;
      }

      logger.debug(
        `scroll to new message. shouldAutoScroll=${shouldAutoScroll.current}`,
      );

      // if the user has scrolled manually, don't auto scroll
      if (shouldAutoScroll.current) {
        scrollToPositionWithRetry({ target: 'anchor', animated: true });
      }
    } else if (listData[lastIdx].type === 'AGENT_MSG_TEXT') {
      scrollToPositionWithRetry({ target: 'end', animated: true });
    } else {
      prevLoaderIndex.current = -1;
    }
  }, [listData, scrollToPositionWithRetry]);

  const keyExtractor = useCallback(
    (item: MessageListItems, index: number) =>
      'listKey' in item
        ? item.listKey
        : item.type === 'NO_OP_MESSAGE'
          ? 'NO_OP_MESSAGE_' + index
          : item.type,
    [],
  );
  const renderItem = useCallback(
    ({ item, index }: { item: MessageListItems; index: number }) => {
      switch (item.type) {
        case 'USER_MSG': {
          return <UserMessageBubble key={item.listKey} {...item} />;
        }
        case 'USER_QUOTED_MSG': {
          return <UserQuotedMessageBubble key={item.listKey} {...item} />;
        }
        case 'BOT_MSG_CARDS': {
          return (
            <BotCardMessage
              key={item.listKey}
              {...item}
              setVideoModalData={setVideoModalData}
            />
          );
        }
        case 'BOT_MSG_PACKAGE_CARD': {
          return (
            <PackageCardMessage
              key={item.listKey}
              {...item}
              setVideoModalData={setVideoModalData}
            />
          );
        }
        case 'TABLE': {
          return <TableMessage key={item.listKey} {...item} />;
        }
        case 'BOT_MSG_ITINERARY_PROGRESS_CARD': {
          return <ItineraryStatusCard key={item.listKey} {...item} />;
        }
        case 'TRAVELLER_INSIGHT': {
          return (
            <TravellerInsight
              key={item.listKey}
              {...item.data}
              listKey={item.listKey}
            />
          );
        }
        case 'AGENT_MSG_TEXT': {
          return <AgentMessage key={item.listKey} {...item} />;
        }
        case 'BOT_MSG_TEXT': {
          return <BotTextMessage key={item.listKey} {...item} />;
        }
        case 'SYSTEM_MSG_TEXT':
          return <SystemMessage key={item.listKey} {...item} />;
        case 'LEADING_QUESTION':
          return <LeadingQuestion key={item.listKey} {...item} />;
        case 'TRIP_SUMMARY':
          return <TripSummaryCard ctaText={item.ctaText} />;
        case 'MSG_LOADER':
          return (
            <View style={styles.footer}>
              <BotMessageLoader />
            </View>
          );
        case 'SUGGESTION':
          return (
            <BotSuggestions
              key={index}
              showOnAction={item.showOnAction}
              suggestions={item.data}
              onSuggestionClick={selectSuggestion}
              requestScrollToBottom={scrollToEnd}
            />
          );
        case 'SUGGESTION_CTAS':
          return (
            <BotSuggestionCTAs
              key={index}
              ctas={item.data}
              onCTAClick={selectSuggestionCTA}
            />
          );
        case 'HELPING_HAND_CARDS':
          return <HelpingHandCards key={index} cardsData={item.data} />;
        case 'HELPING_HAND_SLOTS':
          return <HelpingHandSlotsModal key={index} slotsData={item.data} />;
        case 'NEW_CHAT_PROMPT':
          return <NewChatPrompt key={item.type} />;
        case 'NO_OP_MESSAGE':
          return null;
        default:
          // if the switch is not exhaustive, the below line will throw a ts-error
          return assertNever(item);
      }
    },
    [selectSuggestion, selectSuggestionCTA, scrollToEnd],
  );

  const renderWithTestId = useCallback(
    ({ item, index }: { item: MessageListItems; index: number }) => {
      const testId =
        ('msg' in item ? `${item?.msg?.id}_${item?.msg?.createdAt}` : '') ||
        ('listKey' in item ? item.listKey : '');
      return (
        <View testID={testId} nativeID={'listKey' in item ? item.listKey : ''}>
          {renderItem({ item, index })}
        </View>
      );
    },
    [renderItem],
  );
  const evaluationMode = useMemo(
    () => (isEvaluationMode ? renderWithTestId : renderItem),
    [isEvaluationMode, renderWithTestId, renderItem],
  );
  return (
    <View style={styles.container}>
      <FlatList<MessageListItems>
        {...(compactHeightMode ? { viewabilityConfig } : {})}
        ref={flatListRef as any}
        bounces={Platform.OS === 'ios'}
        initialNumToRender={listData.length}
        overScrollMode={Platform.OS === 'android' ? 'never' : 'auto'}
        onScroll={onScroll}
        scrollEventThrottle={16}
        onScrollBeginDrag={onScrollBeginDrag}
        onViewableItemsChanged={onViewableItemsChanged}
        onScrollToIndexFailed={onScrollToIndexFailed}
        style={styles.flatlist}
        contentContainerStyle={{
          paddingBottom: totalInputAreaHeight,
        }}
        data={listData}
        keyExtractor={keyExtractor}
        renderItem={evaluationMode}
        ListFooterComponent={EmptyFooterSpace}
        {...(isEvaluationMode ? { testID: 'evaluation-message-area' } : {})}
      />
      <TalkBackView />
      <ScrollBottomFAB
        hasUnreadMsg={shouldAutoScroll.current}
        show={!isKeyboardShown && showScrollToBottom}
        marginBottom={
          (totalInputAreaHeight || INPUT_BOX_HEIGHT) + (isTalkBackViewShown ? 72 : 0)
        }
        onPress={() => {
          shouldAutoScroll.current = false;
          scrollToPositionWithRetry({ animated: true, target: 'end' });
        }}
        compactHeightMode={compactHeightMode}
      />
      {videoModalData && (
        <VideoPlayerModal
          shouldShowVideoPlayer={!!videoModalData?.show}
          closeVideoPlayer={() => setVideoModalData(null)}
          media={videoModalData?.media}
        />
      )}
    </View>
  );
};

function EmptyFooterSpace() {
  return <View style={{ height: 36 }} />;
}

const assertNever = (x: never): never => {
  return null as never;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    position: 'relative',
  },
  flatlist: {
    flex: 1,
  },
  footer: {},
});
