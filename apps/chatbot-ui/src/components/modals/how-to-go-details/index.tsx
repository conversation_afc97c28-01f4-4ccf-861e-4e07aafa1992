import React from 'react';
import { BottomModalPopup } from '../../../components/BottomModalPopup';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import IconGrayDot from '../../../assets/IconGrayDot';
import IconRedDot from '../../../assets/IconRedDot';
import { IconMap } from '../../../components/message/cards/TravelDetailCard';
import { COLORS, FONTS } from '../../../constants/globalStyles';
import IconDuration from '../../../assets/IconDuration';
import IconMidStop from '../../../assets/IconMidStop';

/**
 * Renders the list of travel segments, highlighting each step along the route.
 */
const DetailsView = (props: {
  data: TravelDetailCardProps['segments'];
  onCTAClick: (data: Segment | undefined) => void;
}) => {
  const { data, onCTAClick } = props;

  /**
   * <PERSON>les deep link opening for the passed segment item.
   * @param {Segment} item - The travel segment containing the CTA link.
   */
  const onBookClick = (item: Segment) => {
    if (item?.cta_link) {
      onCTAClick(item);
    }
  };

  return (
    <View style={styles.marginBottom40}>
      {data?.map((item, index) => (
        <>
          <View style={styles.container} key={index}>
            <View style={styles.linesContainer}>
              <View
                style={[
                  styles.icon,
                  index === 0 ? { paddingVertical: 3 } : { paddingVertical: 6 },
                ]}
              >
                {index === 0 ? <IconGrayDot /> : <IconMidStop />}
              </View>
              <View style={styles.verticalLine} />
            </View>

            <View style={styles.textContainer}>
              <Text style={styles.textBase}>{item.source || ''}</Text>

              <View style={styles.rowCenter}>
                {IconMap[item.type.toLowerCase() as keyof typeof IconMap]
                  ? IconMap[item.type.toLowerCase() as keyof typeof IconMap]
                  : null}
                <Text style={[styles.textBase, styles.typeText]}>
                  {item.type || ''}
                </Text>
              </View>

              <View style={styles.rowCenterPadding}>
                <IconDuration />
                <Text style={[styles.duration, styles.textBase]}>
                  {item.text || ''}
                </Text>
              </View>
            </View>

            <View style={styles.actionContainer}>
              <View></View>
              <View style={styles.rowEnd}>
                <Text style={[styles.textBase, styles.fareInfo]}>
                  {item.fare_info_text || ''}
                </Text>
                <Text style={[styles.textBase, styles.fareAmount]}>
                  {item.fare_amount_text || ''}
                </Text>
              </View>

              <TouchableOpacity
                onPress={() => onBookClick(item)}
                style={styles.detailsButton}
              >
                <Text style={styles.detailsButtonText}>{item.cta_text}</Text>
              </TouchableOpacity>
            </View>
          </View>
          {index === data.length - 1 ? (
            <View style={[styles.container, { height: 20 }]}>
              <View style={styles.linesContainer}>
                <View style={styles.icon}>
                  {index === data.length - 1 ? <IconRedDot /> : null}
                </View>
              </View>
              <View style={styles.textContainer}>
                <Text style={styles.textBase}>{item.destination || ''}</Text>
              </View>
            </View>
          ) : null}
        </>
      ))}
    </View>
  );
};

/**
 * Displays a modal popup with detailed travel information.
 * @param {boolean} showModal - Indicates if the modal is visible.
 * @param {() => void} onClose - Closes the modal.
 * @param {TravelDetailCardProps} item - Contains the segments to display.
 */
export function HowToGoDetailsPopup({
  showModal,
  onClose,
  item,
  onCTAClick,
}: {
  showModal: boolean;
  onClose: () => void;
  item: TravelDetailCardProps;
  onCTAClick: (data: Segment | undefined) => void;
}) {
  const modalProps = {
    enabled: showModal,
    title: '',
    // icon: <RightIcon fill={'#3944A3'} />,
    onClose,
  };

  return (
    <BottomModalPopup {...modalProps}>
      <DetailsView data={item.segments} onCTAClick={onCTAClick} />
    </BottomModalPopup>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: 120,
    paddingHorizontal: 18,
  },
  textBase: {
    fontSize: 12,
    color: COLORS.BLACK,
    fontFamily: FONTS.FONT_FAMILY_400,
  },
  linesContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    width: '3%',
  },
  verticalLine: {
    height: '80%',
    width: 1,
    backgroundColor: COLORS.GREY_VAR_1,
    marginTop: 3,
  },
  textContainer: {
    width: '65%',
    justifyContent: 'space-between',
    height: '80%',
    paddingLeft: 10,
  },
  duration: {
    fontSize: 12,
    color: COLORS.BLACK,
    marginLeft: 3,
  },
  actionContainer: {
    width: '30%',
    justifyContent: 'space-between',
    height: '80%',
  },
  detailsButtonText: {
    color: COLORS.BLUE_VAR_1,
    fontWeight: 'bold',
    fontSize: 13,
  },
  detailsButton: {
    borderColor: COLORS.BLUE_VAR_1,
    borderWidth: 1,
    flexDirection: 'row',
    paddingVertical: 6,
    paddingHorizontal: 7,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fareAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.BLACK,
    fontFamily: FONTS.FONT_FAMILY_900,
  },
  fareInfo: {
    fontSize: 13,
    color: COLORS.GREY_VAR_2,
    marginRight: 3,
  },
  typeText: {
    fontFamily: FONTS.FONT_FAMILY_900,
  },
  icon: {
    zIndex: 1,
    paddingVertical: 3,
  },
  marginBottom40: {
    marginBottom: 30,
  },
  rowCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowCenterPadding: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 2,
    paddingHorizontal: 5,
  },
  rowEnd: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
});
