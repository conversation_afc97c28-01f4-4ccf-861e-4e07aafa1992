import React from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  StyleSheet,
  useWindowDimensions,
  TouchableWithoutFeedback,
} from 'react-native';
import { COLORS, FONTS, Z_INDEX } from '../../constants/globalStyles';
import { CloseIcon } from '../../assets/CloseIcon';
import { ModalSlot } from '../../screens/ModalProvider';
import Animated, {
  FadeIn,
  FadeOut,
  SlideInDown,
  SlideOutDown,
} from 'react-native-reanimated';
import { ScrollView } from 'react-native-gesture-handler';

interface BottomModalPopupProps {
  title?: string;
  icon?: React.ReactNode;
  onIconPress?: () => void;
  sub_title?: string;
  description?: string;
  onClose: () => void;
  children: React.ReactNode;
  enabled: boolean;

  /**
   * ENHANCEMENT: Configurable modal height as percentage of screen height
   *
   * Why added: Originally modal height was hardcoded to 60% (0.6) of screen height.
   * This enhancement allows different modals to have different heights based on their content needs.
   *
   * @param heightPercentage - Decimal value (0-1) representing percentage of screen height
   * @example 0.7 = 70% of screen height, 0.5 = 50% of screen height
   * @default 0.6 (60% - maintains backward compatibility)
   */
  heightPercentage?: number;

  /**
   * Control between auto-sizing vs fixed-height behavior
   *
   * Why added: Original behaviour used maxHeight which allows modal to be smaller than
   * specified height if content doesn't fill it. Some modals (like forms) need to
   * maintain consistent height regardless of content size for better UX.
   *
   * @param useFixedHeight - When true, modal takes exactly the specified height percentage
   *                        When false, modal auto-sizes up to the height percentage (original behaviour)
   * @default false (maintains backward compatibility with existing modals)
   *
   * Use cases:
   * - false: Content-driven modals (lists, variable content) - original behaviour
   * - true: Form modals, consistent layout requirements - new behaviour
   */
  useFixedHeight?: boolean;

  /**
   * Control whether to show or hide the header section
   *
   * @param hideHeader - When true, hides the header (close button and title)
   *                    When false, shows the header (default behavior)
   * @default false (header is shown by default)
   */
  hideHeader?: boolean;
}

export function BottomModalPopup({
  title,
  icon,
  onIconPress,
  sub_title,
  onClose,
  children,
  enabled,
  heightPercentage = 0.6, // Default to 60% (0.6) for backward compatibility
  useFixedHeight = false, // Default to false for backward compatibility - preserves original auto-sizing behavior
  hideHeader = false, // Default to false - header is shown by default
}: BottomModalPopupProps) {
  const { height } = useWindowDimensions();

  if (!enabled) {
    return null;
  }

  // Dynamic height calculation based on configuration
  // Configurable percentage with choice between fixed height vs max height
  const calculatedHeight = Math.round(height * heightPercentage);
  const heightStyle = useFixedHeight
    ? { height: calculatedHeight } // Fixed height: Modal is exactly the specified height
    : { maxHeight: calculatedHeight }; // Max height: Modal can be smaller (original behavior)

  return (
    <ModalSlot>
      <TouchableWithoutFeedback onPress={onClose}>
        <Animated.View
          entering={FadeIn}
          exiting={FadeOut}
          style={{
            flexDirection: 'column',
            zIndex: Z_INDEX.MODAL,
            ...StyleSheet.absoluteFillObject,
            backgroundColor: '#0002',
            alignItems: 'flex-end',
            justifyContent: 'flex-end',
          }}
        >
          <TouchableWithoutFeedback onPress={() => {}}>
            <Animated.View
              entering={SlideInDown}
              exiting={SlideOutDown}
              style={[
                styles.cardBody,
                {
                  flex: 0,
                  zIndex: Z_INDEX.MODAL,
                  minHeight: 200,
                  ...heightStyle, // ENHANCEMENT: Apply either height or maxHeight based on useFixedHeight flag
                  position: 'relative',
                  backgroundColor: '#fff',
                  borderTopLeftRadius: 16,
                  borderTopRightRadius: 16,
                },
              ]}
            >
              {/* Header */}
              {!hideHeader && (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    height: 64,
                    paddingHorizontal: 12,
                    borderBottomWidth: 1,
                    borderBottomColor: COLORS.GREY_VAR_5,
                  }}
                >
                  <TouchableOpacity
                    activeOpacity={0.8}
                    onPress={onClose}
                    style={{ marginLeft: 8 }}
                  >
                    <CloseIcon />
                  </TouchableOpacity>
                  <View>
                    <Text numberOfLines={1} style={styles.title}>
                      {title}
                    </Text>
                  </View>
                </View>
              )}

              <ScrollView
                nestedScrollEnabled
                contentContainerStyle={{ flexGrow: 1 }}
              >
                {children}
              </ScrollView>
            </Animated.View>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableWithoutFeedback>
    </ModalSlot>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    position: 'relative',
  },
  modalTitle: {
    fontSize: 16,
    color: COLORS.TEXT_HIGH_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_700,
    marginBottom: 16,
  },

  titleSection: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 0,
  },
  title: {
    fontSize: 16,
    fontFamily: FONTS.FONT_FAMILY_700,
    color: COLORS.BLACK,
    paddingLeft: 10,
  },
  cardBody: {
    width: '100%',
    flex: 1,

    overflow: 'hidden',
    flexDirection: 'column',
  },
});
