import React from 'react';
import { Image, Platform, StyleSheet, View } from 'react-native';
import chatBg from '../assets/chat_bg.webp';

interface ChatBgProps {
  currentView?: 'chat' | 'history' | 'bookmarks' | null;
}
const getImageSource = () => {
  return Platform.select({
    web: { uri: chatBg },
    default: require('../assets/chat_bg.webp'),
  });
};

export function ChatBg({ currentView }: ChatBgProps) {
  return (
    <View style={{ flex: 1, ...StyleSheet.absoluteFillObject }}>
      {/*<LinearGradient colors={['#f2e6ee', '#ffffff']} style={{ flex: 1 }} />*/}
      <Image
        source={getImageSource()}
        style={{
          width: '100%',
          height: '100%',
          opacity: currentView !== 'chat' ? 0.3 : 0.6,
        }}
      />
    </View>
  );
}
