import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useAppStateStore } from '../../store/app';
import { BackIcon } from '../../assets/BackIcon';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { LinearGradient } from 'react-native-linear-gradient';
import { TouchableFeedback } from '../base/TouchableFeeback';
import { isPlatformWeb } from '../../utils/getPlatform';
import FastImageWrapper from '../image/FastImageWrapper';

export type HeaderProps = {
  leftMenu?: 'back' | React.ReactNode;
  title?: string | React.ReactNode;
  rightMenu?: React.ReactNode;
  children?: React.ReactNode;
  isRightMenuDetailed?: boolean;
  enrichedTitle?: {
    type: 'text' | 'image';
    value: string;
  };
};

function HeaderDividerStrip() {
  return (
    <LinearGradient
      angle={90}
      useAngle
      colors={['#fff0', '#ffff', '#fff0']}
      style={{ height: 2, width: '100%' }}
    />
  );
}

export function BaseHeader({
  leftMenu,
  title,
  rightMenu,
  children,
  isRightMenuDetailed,
  enrichedTitle,
}: HeaderProps) {
  const { setCurrentView } = useAppStateStore();
  let titleContainerMaxWidth = '65%';
  if (isPlatformWeb()) {
    titleContainerMaxWidth = isRightMenuDetailed ? '50%' : '45%';
  } else {
    titleContainerMaxWidth = isRightMenuDetailed ? '50%' : '80%';
  }
  const getTitle = () => {
    if (enrichedTitle) {
      if (enrichedTitle.type === 'text') {
        return (
          <Text numberOfLines={1} style={styles.title}>
            {enrichedTitle.value}
          </Text>
        );
      }
      if (enrichedTitle.type === 'image') {
        return (
          <FastImageWrapper
            source={{ uri: enrichedTitle.value }}
            style={styles.logoImage}
            resizeMode={FastImageWrapper.resizeMode.contain}
          />
        );
      }
    }
    if (typeof title === 'string') {
      return (
        <Text numberOfLines={1} style={styles.title}>
          {title}
        </Text>
      );
    }
    if (typeof title === 'object' && title !== null) {
      return title;
    }
    return null;
  };
  return (
    <View
      style={{ flexDirection: 'column', backgroundColor: 'rgba(255,255,255,0.6)' }}
    >
      {/* Left Icon */}
      <View style={styles.container}>
        <View style={styles.sideMenuContainer}>
          {leftMenu === 'back' && (
            <TouchableFeedback
              activeOpacity={0.5}
              onPress={() => {
                if (setCurrentView) {
                  setCurrentView('chat');
                }
              }}
            >
              <View style={{ paddingVertical: 8, paddingHorizontal: 12 }}>
                <BackIcon />
              </View>
            </TouchableFeedback>
          )}
          {leftMenu !== 'back' && leftMenu}
        </View>

        {/* Title */}
        <View
          style={[
            styles.titleContainer,
            {
              maxWidth: titleContainerMaxWidth as unknown as number,
            },
          ]}
        >
          {getTitle()}
        </View>

        {/* Right Icon */}
        <View style={styles.sideMenuContainer}>{rightMenu}</View>
      </View>
      {children}
      <HeaderDividerStrip />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    paddingHorizontal: 0,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 24,
    minHeight: 80,
    position: 'relative',
  },
  sideMenuContainer: {
    minWidth: 48,
  },
  titleContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
    zIndex: 1,
    maxWidth: '80%',
  },
  iconContainer: {
    width: 40,
  },
  title: {
    fontSize: 14,
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_700,
    lineHeight: 16,
  },
  logoImage: {
    width: 85,
    aspectRatio: 3.125, // 75/24 = 3.125 to maintain the current ratio
    alignSelf: 'center',
  },
});
