/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react';
import Svg, { Defs, LinearGradient, Path, Stop, SvgProps } from 'react-native-svg';

export function MyraBotIcon(props: SvgProps) {
  return (
    <Svg width={57} height={25} viewBox="0 0 57 25" fill="none" {...props}>
      <Path
        d="M14.016 10.392V22h-2.12v-7.128c0-.15.003-.31.008-.48.01-.17.027-.344.048-.52l-3.32 6.296c-.181.347-.461.52-.84.52h-.336a.955.955 0 01-.496-.128.953.953 0 01-.344-.392l-3.336-6.32c.016.187.03.368.04.544.************.016.48V22h-2.12V10.392H3.04c.101 0 .192.003.272.*************.021.208.048a.39.39 0 *************.75 0 01.152.208l3.248 6.192c.101.187.195.379.28.576.************.256.608.08-.213.163-.421.248-.624.09-.203.187-.397.288-.584l3.224-6.168a.75.75 0 01.152-.208.463.463 0 01.168-.112.657.657 0 01.208-.048c.08-.005.173-.008.28-.008h1.824zm9.59 3.36L19.16 24.216a.852.852 0 01-.256.336c-.096.075-.25.112-.464.112h-1.64l1.56-3.328-3.312-7.584h1.936c.176 0 .312.04.408.*************.173.208.28l1.568 3.944c.058.155.11.312.152.472.048.155.093.31.136.464.096-.315.205-.63.328-.944l1.456-3.936a.58.58 0 01.232-.288.65.65 0 01.368-.112h1.768zm2.94 1.36c.262-.47.563-.84.905-1.112a1.868 1.868 0 011.2-.408c.373 0 .677.088.912.264l-.144 1.632c-.027.107-.07.181-.128.224a.387.387 0 01-.224.056c-.086 0-.209-.01-.369-.032a2.73 2.73 0 00-.448-.04c-.202 0-.384.03-.544.088a1.368 1.368 0 00-.415.248 1.878 1.878 0 00-.337.392 4.05 4.05 0 00-.271.528V22h-2.209v-8.248h1.305c.224 0 .378.04.463.12.086.08.147.219.184.416l.12.824zm8.178 3.44a9.791 9.791 0 00-1.344.144c-.357.064-.643.15-.856.256-.208.107-.357.23-.448.368a.8.8 0 00-.128.44c0 .32.088.547.264.68.181.133.432.2.752.2.368 0 .685-.064.952-.192.272-.133.541-.339.808-.616v-1.28zm-4.496-3.656a4.877 4.877 0 011.608-.976 5.408 5.408 0 011.904-.328c.49 0 .928.08 1.312.24.39.16.717.384.984.672.272.283.48.621.624 1.016.144.395.216.827.216 1.296V22h-1.008c-.208 0-.368-.03-.48-.088-.107-.059-.195-.181-.264-.368l-.176-.528c-.208.181-.41.341-.608.48a3.599 3.599 0 01-.6.352 3.29 3.29 0 01-.672.208 3.97 3.97 0 01-.792.072 3.23 3.23 0 01-.992-.144 2.23 2.23 0 01-.776-.424 1.946 1.946 0 01-.496-.712 2.53 2.53 0 01-.176-.976c0-.304.077-.608.232-.912.155-.304.419-.579.792-.824.379-.25.88-.456 1.504-.616.63-.165 1.416-.259 2.36-.28v-.424c0-.517-.11-.896-.328-1.136-.219-.245-.533-.368-.944-.368-.304 0-.557.037-.76.112-.203.07-.381.147-.536.232-.15.08-.29.157-.424.232a.992.992 0 01-.464.104.666.666 0 01-.392-.112 1.16 1.16 0 01-.256-.28l-.392-.704z"
        fill="url(#paint0_linear_2242_25333)"
      />
      <Path
        d="M14.016 10.392V22h-2.12v-7.128c0-.15.003-.31.008-.48.01-.17.027-.344.048-.52l-3.32 6.296c-.181.347-.461.52-.84.52h-.336a.955.955 0 01-.496-.128.953.953 0 01-.344-.392l-3.336-6.32c.016.187.03.368.04.544.************.016.48V22h-2.12V10.392H3.04c.101 0 .192.003.272.*************.021.208.048a.39.39 0 *************.75 0 01.152.208l3.248 6.192c.101.187.195.379.28.576.************.256.608.08-.213.163-.421.248-.624.09-.203.187-.397.288-.584l3.224-6.168a.75.75 0 01.152-.208.463.463 0 01.168-.112.657.657 0 01.208-.048c.08-.005.173-.008.28-.008h1.824zm9.59 3.36L19.16 24.216a.852.852 0 01-.256.336c-.096.075-.25.112-.464.112h-1.64l1.56-3.328-3.312-7.584h1.936c.176 0 .312.04.408.*************.173.208.28l1.568 3.944c.058.155.11.312.152.472.048.155.093.31.136.464.096-.315.205-.63.328-.944l1.456-3.936a.58.58 0 01.232-.288.65.65 0 01.368-.112h1.768zm2.94 1.36c.262-.47.563-.84.905-1.112a1.868 1.868 0 011.2-.408c.373 0 .677.088.912.264l-.144 1.632c-.027.107-.07.181-.128.224a.387.387 0 01-.224.056c-.086 0-.209-.01-.369-.032a2.73 2.73 0 00-.448-.04c-.202 0-.384.03-.544.088a1.368 1.368 0 00-.415.248 1.878 1.878 0 00-.337.392 4.05 4.05 0 00-.271.528V22h-2.209v-8.248h1.305c.224 0 .378.04.463.12.086.08.147.219.184.416l.12.824zm8.178 3.44a9.791 9.791 0 00-1.344.144c-.357.064-.643.15-.856.256-.208.107-.357.23-.448.368a.8.8 0 00-.128.44c0 .32.088.547.264.68.181.133.432.2.752.2.368 0 .685-.064.952-.192.272-.133.541-.339.808-.616v-1.28zm-4.496-3.656a4.877 4.877 0 011.608-.976 5.408 5.408 0 011.904-.328c.49 0 .928.08 1.312.24.39.16.717.384.984.672.272.283.48.621.624 1.016.144.395.216.827.216 1.296V22h-1.008c-.208 0-.368-.03-.48-.088-.107-.059-.195-.181-.264-.368l-.176-.528c-.208.181-.41.341-.608.48a3.599 3.599 0 01-.6.352 3.29 3.29 0 01-.672.208 3.97 3.97 0 01-.792.072 3.23 3.23 0 01-.992-.144 2.23 2.23 0 01-.776-.424 1.946 1.946 0 01-.496-.712 2.53 2.53 0 01-.176-.976c0-.304.077-.608.232-.912.155-.304.419-.579.792-.824.379-.25.88-.456 1.504-.616.63-.165 1.416-.259 2.36-.28v-.424c0-.517-.11-.896-.328-1.136-.219-.245-.533-.368-.944-.368-.304 0-.557.037-.76.112-.203.07-.381.147-.536.232-.15.08-.29.157-.424.232a.992.992 0 01-.464.104.666.666 0 01-.392-.112 1.16 1.16 0 01-.256-.28l-.392-.704z"
        fill="url(#paint1_linear_2242_25333)"
      />
      <Path
        d="M42.04 4.6h-.002a5.64 5.64 0 01-5.518 5.519v.002a5.64 5.64 0 015.518 5.52h.003a5.64 5.64 0 015.519-5.52v-.002A5.64 5.64 0 0142.04 4.6z"
        fill="#fff"
      />
      <Path
        d="M42.04 4.6h-.002a5.64 5.64 0 01-5.518 5.519v.002a5.64 5.64 0 015.518 5.52h.003a5.64 5.64 0 015.519-5.52v-.002A5.64 5.64 0 0142.04 4.6z"
        fill="url(#paint2_linear_2242_25333)"
      />
      <Path
        d="M47.561 11.96a2.82 2.82 0 01-2.76 2.76 2.82 2.82 0 012.76 2.76 2.82 2.82 0 012.76-2.76 2.82 2.82 0 01-2.76-2.76z"
        fill="#fff"
      />
      <Path
        d="M47.561 11.96a2.82 2.82 0 01-2.76 2.76 2.82 2.82 0 012.76 2.76 2.82 2.82 0 012.76-2.76 2.82 2.82 0 01-2.76-2.76z"
        fill="url(#paint3_linear_2242_25333)"
      />
      <Path
        d="M47.561 11.96a2.82 2.82 0 01-2.76 2.76 2.82 2.82 0 012.76 2.76 2.82 2.82 0 012.76-2.76 2.82 2.82 0 01-2.76-2.76z"
        fill="url(#paint4_linear_2242_25333)"
      />
      <Defs>
        {/*@ts-ignore*/}
        <LinearGradient
          id="paint0_linear_2242_25333"
          x1={37.5}
          y1={21.5}
          x2={1}
          y2={10}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#2493EB" />
          <Stop offset={1} stopColor="#50F" />
        </LinearGradient>
        {/*@ts-ignore*/}
        <LinearGradient
          id="paint1_linear_2242_25333"
          x1={2.56538e-7}
          y1={11}
          x2={37.5}
          y2={22}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#51AFE6" />
          <Stop offset={0.755} stopColor="#355FF2" />
          <Stop offset={1} stopColor="#11287A" />
        </LinearGradient>
        {/*@ts-ignore*/}
        <LinearGradient
          id="paint2_linear_2242_25333"
          x1={41.5795}
          y1={6.9001}
          x2={46.9462}
          y2={13.4934}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#51AFE6" />
          <Stop offset={0.461569} stopColor="#355FF2" />
          <Stop offset={0.694356} stopColor="#11287A" />
        </LinearGradient>
        {/*@ts-ignore*/}
        <LinearGradient
          id="paint3_linear_2242_25333"
          x1={47.3124}
          y1={13.616}
          x2={50.5259}
          y2={13.88}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#51AFE6" />
          <Stop offset={0.25} stopColor="#355FF2" />
          <Stop offset={1} stopColor="#11287A" />
        </LinearGradient>
        {/*@ts-ignore*/}
        <LinearGradient
          id="paint4_linear_2242_25333"
          x1={47.3308}
          y1={13.11}
          x2={49.6308}
          y2={16.1}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#51AFE6" />
          <Stop offset={0.21909} stopColor="#355FF2" />
          <Stop offset={0.694356} stopColor="#11287A" />
        </LinearGradient>
      </Defs>
    </Svg>
  );
}
