// @ts-nocheck
import * as React from 'react';
import Svg, { <PERSON>, Defs, LinearGradient, Stop, SvgProps } from 'react-native-svg';
const SuitcaseIcon = (props: SvgProps) => (
  <Svg width={24} height={24} fill="none" {...props}>
    <Path
      fill="url(#a)"
      fillRule="evenodd"
      d="M11.948 1.25h.104c.899 0 1.648 0 2.242.08.628.084 1.195.27 1.65.725.456.456.642 1.023.726 1.65.06.44.075.964.079 1.57.648.021 1.226.06 1.74.128 1.172.158 2.121.49 2.87 1.238.748.749 1.08 1.698 1.238 2.87.153 1.14.153 2.595.153 4.433v.112c0 1.838 0 3.294-.153 4.433-.158 1.172-.49 2.121-1.238 2.87-.749.748-1.698 1.08-2.87 1.238-1.14.153-2.595.153-4.433.153H9.944c-1.838 0-3.294 0-4.433-.153-1.172-.158-2.121-.49-2.87-1.238-.748-.749-1.08-1.698-1.238-2.87-.153-1.14-.153-2.595-.153-4.433v-.112c0-1.838 0-3.294.153-4.433.158-1.172.49-2.121 1.238-2.87.749-.748 1.698-1.08 2.87-1.238.514-.069 1.092-.107 1.74-.128.004-.606.02-1.13.079-1.57.084-.627.27-1.194.725-1.65.456-.455 1.023-.64 1.65-.725.595-.08 1.345-.08 2.243-.08ZM8.752 5.252c.378-.002.775-.002 1.192-.002h4.112c.417 0 .814 0 1.192.002-.004-.57-.018-1-.064-1.347-.063-.461-.17-.659-.3-.789-.13-.13-.328-.237-.79-.3-.482-.064-1.13-.066-2.094-.066s-1.612.002-2.095.067c-.461.062-.659.169-.789.3-.13.13-.237.327-.3.788-.046.346-.06.776-.064 1.347ZM5.71 6.89c-1.006.135-1.586.389-2.01.812-.422.423-.676 1.003-.811 2.009-.138 1.027-.14 2.382-.14 4.289 0 1.907.002 3.262.14 4.29.135 1.005.389 1.585.812 2.008.423.423 1.003.677 2.009.812 1.028.138 2.382.14 4.289.14h4c1.907 0 3.262-.002 4.29-.14 1.005-.135 1.585-.389 2.008-.812.423-.423.677-1.003.812-2.009.138-1.027.14-2.382.14-4.289 0-1.907-.002-3.261-.14-4.29-.135-1.005-.389-1.585-.812-2.008-.423-.423-1.003-.677-2.009-.812-1.027-.138-2.382-.14-4.289-.14h-4c-1.907 0-3.261.002-4.29.14Z"
      clipRule="evenodd"
    />
    <Path fill="url(#b)" d="M17 9a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" />
    <Path fill="url(#c)" d="M9 9a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" />
    <Defs>
      <LinearGradient
        id="a"
        x1={12}
        x2={12}
        y1={0.787}
        y2={23.213}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#1F1F1F" />
        <Stop offset={1} stopColor="#4B4B4B" />
      </LinearGradient>
      <LinearGradient
        id="b"
        x1={12}
        x2={12}
        y1={0.787}
        y2={23.213}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#1F1F1F" />
        <Stop offset={1} stopColor="#4B4B4B" />
      </LinearGradient>
      <LinearGradient
        id="c"
        x1={12}
        x2={12}
        y1={0.787}
        y2={23.213}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#1F1F1F" />
        <Stop offset={1} stopColor="#4B4B4B" />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default SuitcaseIcon;
