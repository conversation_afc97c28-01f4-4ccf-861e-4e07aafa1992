import * as React from 'react';
import Svg, {
  Defs,
  G,
  LinearGradient,
  Path,
  Stop,
  SvgProps,
} from 'react-native-svg'; /* SVGR has dropped some elements not supported by react-native-svg: filter */
/* SVGR has dropped some elements not supported by react-native-svg: filter */
export const BottombarBlur = (props: SvgProps) => (
  <Svg width={443} height={161} fill="none" {...props}>
    <Path fill="#fff" d="M20 20h360v84H20z" />
    {/*@ts-ignore*/}
    <G filter="url(#a)">
      <Path fill="url(#b)" d="M32 40h337v47H32z" />
    </G>
    <Defs>
      {/*@ts-ignore*/}
      <LinearGradient
        id="b"
        x1={200.5}
        x2={200.5}
        y1={40}
        y2={87}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#E7DEFF" />
        <Stop offset={1} stopColor="#9C79F9" />
      </LinearGradient>
    </Defs>
  </Svg>
);
