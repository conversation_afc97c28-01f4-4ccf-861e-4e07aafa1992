import * as React from 'react';
import Svg, {
  G,
  Path,
  Defs,
  LinearGradient,
  Stop,
  SvgProps,
} from 'react-native-svg';
const IconPin = (
  props: React.JSX.IntrinsicAttributes &
    React.JSX.IntrinsicClassAttributes<React.Component<SvgProps, any, any>> &
    Readonly<SvgProps>,
) => (
  <Svg width={14} height={14} fill="none" {...props}>
    <React.Fragment>
      <Path
        stroke="#757575"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="gray"
        d="M6.737 7a2.362 2.362 0 1 0 0-4.725 2.362 2.362 0 0 0 0 4.725Z"
      />
      <Path
        stroke="#757575"
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M6.737 7v4.725"
      />
    </React.Fragment>
    <Defs>
      <LinearGradient
        id="a"
        x1={9.1}
        x2={4.768}
        y1={7}
        y2={2.428}
        gradientUnits="userSpaceOnUse"
      />
      <Stop offset="0%" stopColor="#757575" />
      <Stop offset="100%" stopColor="#BDBDBD" />
    </Defs>
  </Svg>
);
export default IconPin;
