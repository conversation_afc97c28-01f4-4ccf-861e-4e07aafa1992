import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface CrossIconProps {
    width?: number;
    height?: number;
    fill?: string;
}

export const CrossIcon: React.FC<CrossIconProps> = ({
    width = 14,
    height = 14,
    fill = 'white',
}) => {
    return (
        <Svg width={width} height={height} viewBox="0 0 14 14" fill="none">
            <Path
                d="M1.4 14L0 12.6L5.6 7L0 1.4L1.4 0L7 5.6L12.6 0L14 1.4L8.4 7L14 12.6L12.6 14L7 8.4L1.4 14Z"
                fill={fill}
            />
        </Svg>
    );
};