import * as React from "react";
import Svg, { Ellip<PERSON>, Path, Circle } from "react-native-svg";

export interface NoResultProps {
  width?: number;
  height?: number;
}

export const NoResult = ({
  width=147,
  height=149,
}: NoResultProps) => {

  return (
    <Svg
      width={width}
      height={height}
      viewBox="0 0 147 149"
      fill="none"
    >
      <Ellipse cx={74.032} cy={140.446} fill="#CDE3FF" rx={54.048} ry={8.478} />
      <Path
        stroke="#0C58B4"
        strokeWidth={3.5}
        d="M37.502 111.5c0-14.142 0-21.213 4.393-25.607C46.29 81.5 53.36 81.5 67.502 81.5h15c14.142 0 21.213 0 25.607 4.393 4.393 4.394 4.393 11.465 4.393 25.607s0 21.213-4.393 25.607c-4.394 4.393-11.465 4.393-25.607 4.393h-15c-14.142 0-21.213 0-25.607-4.393-4.393-4.394-4.393-11.465-4.393-25.607ZM90.002 81.5c0-7.07 0-10.606-2.197-12.803C85.61 66.5 82.073 66.5 75.002 66.5c-7.071 0-10.607 0-12.803 2.197-2.197 2.197-2.197 5.732-2.197 12.803"
      />
      <Path
        stroke="#0C58B4"
        strokeLinejoin="round"
        strokeWidth={3.5}
        d="M67.502 115.25h-15c-1.768 0-2.652 0-3.2.549-.55.549-.55 1.433-.55 3.201v3.75c0 1.768 0 2.652.55 3.201.548.549 1.432.549 3.2.549h15c1.768 0 2.652 0 3.2-.549.55-.549.55-1.433.55-3.201V119c0-1.768 0-2.652-.55-3.201-.548-.549-1.432-.549-3.2-.549Z"
      />
      <Path
        stroke="#0C58B4"
        strokeLinecap="round"
        strokeWidth={3.5}
        d="M52.5 83.376v31.874m0 24.375v-11.25M97.5 83.376v56.249"
      />
      <Path
        stroke="#617CDE"
        strokeLinecap="round"
        strokeWidth={2.291}
        d="m130.113 32.482-2.026 10.402M123.898 36.67l10.402 2.026"
      />
      <Path
        stroke="#5601FF"
        strokeLinecap="round"
        strokeWidth={2.291}
        d="m28.795 1.617 3.235 16.605M38.713 8.302l-16.605 3.235"
      />
      <Circle
        cx={142.15}
        cy={54.368}
        r={3.284}
        stroke="#BF6DBE"
        strokeLinecap="round"
        strokeWidth={2.12}
        transform="rotate(11.024 142.15 54.368)"
      />
      <Circle
        cx={8.822}
        cy={8.822}
        r={8.822}
        stroke="#0C58B4"
        strokeLinecap="round"
        strokeWidth={2.12}
        transform="scale(-1 1) rotate(11.024 -215.163 -74.56)"
      />
    </Svg>
  );
};

export default NoResult;
