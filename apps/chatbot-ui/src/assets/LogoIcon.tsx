// @ts-nocheck
import * as React from 'react';
import Svg, {
  SvgProps,
  G,
  Rect,
  Path,
  Circle,
  Defs,
  ClipPath,
} from 'react-native-svg';
const LogoIcon = (props: SvgProps) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={26}
    height={25}
    fill="none"
    {...props}
  >
    <G clipPath="url(#a)">
      <Rect width={25} height={25} x={0.5} fill="#fff" rx={12.5} />
      <G filter="url(#b)">
        <Path
          fill="#5304FE"
          d="M23.01 14.25c0 4.418-1.68 8-3.75 8-2.072 0-3.75-3.582-3.75-8s-2.822-7.75-.75-7.75c2.07 0 8.25 3.332 8.25 7.75Z"
        />
      </G>
      <G filter="url(#c)">
        <Circle cx={16.75} cy={3.75} r={5.75} fill="#268BEC" />
      </G>
      <G filter="url(#d)">
        <Circle cx={1.25} cy={14.25} r={5.75} fill="#4600FF" />
      </G>
      <G filter="url(#e)">
        <Circle cx={16.75} cy={22.25} r={5.75} fill="#2590EB" />
      </G>
      <G filter="url(#f)">
        <Circle cx={5.25} cy={19.75} r={3.75} fill="#4922FA" />
      </G>
      <G filter="url(#g)">
        <Path
          fill="#3269F0"
          d="M20 22.25c0 2.071-6.679 4.25-8.75 4.25-2.071 0-5-2.679-5-4.75 0-2.07 1.929-1.25 4-1.25 2.071 0 9.75-.321 9.75 1.75Z"
        />
      </G>
      <G filter="url(#h)">
        <Circle cx={20.75} cy={18.75} r={4.25} fill="#fff" />
      </G>
      <G filter="url(#i)">
        <Path
          fill="#3C4BF4"
          d="M9.5 5.25c0 4.142 3.96 10.5 1.75 10.5S2.5 11.142 2.5 7 6.79-3 9-3s.5 4.108.5 8.25Z"
        />
      </G>
      <G filter="url(#j)">
        <Circle cx={3.75} cy={5.25} r={4.25} fill="#fff" />
      </G>
    </G>
    <Defs>
      <ClipPath id="a">
        <Rect width={25} height={25} x={0.5} fill="#fff" rx={12.5} />
      </ClipPath>
    </Defs>
  </Svg>
);
export default LogoIcon;
