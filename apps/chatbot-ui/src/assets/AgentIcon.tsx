// @ts-nocheck
import * as React from 'react';
import Svg, { SvgProps, Path, Mask, G, Ellipse, Defs } from 'react-native-svg';
const AgentIcon = (props: SvgProps) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={22}
    height={22}
    fill="none"
    {...props}
  >
    <Path
      fill="#392410"
      d="M19.767 8.93a.481.481 0 0 0-.481-.482h-4.33v4.81h4.33a.481.481 0 0 0 .481-.48v-3.85Zm-17.186 0c0-.267.216-.482.481-.482h4.33v4.81h-4.33a.481.481 0 0 1-.48-.48v-3.85Z"
    />
    <Mask
      id="b"
      width={6}
      height={6}
      x={2}
      y={8}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: 'alpha',
      }}
    >
      <Path
        fill="#392410"
        d="M2.581 8.928c0-.266.216-.481.481-.481h4.33v4.81h-4.33a.481.481 0 0 1-.48-.48v-3.85Z"
      />
    </Mask>
    <G filter="url(#a)" mask="url(#b)">
      <Ellipse cx={-1.929} cy={11.026} fill="#fff" rx={2.209} ry={3.954} />
    </G>
    <Path
      stroke="#392410"
      strokeWidth={1.203}
      d="M4.025 8.46c0-9.24 13.972-9.249 13.972-.01"
    />
    <Path
      fill="#D9A045"
      d="M17.013 11.12c0 4.132-2.692 9.353-6.013 9.353-3.321 0-6.013-5.22-6.013-9.354 0-4.132 2.692-7.483 6.013-7.483 3.321 0 6.013 3.35 6.013 7.483Z"
    />
    <Mask
      id="d"
      width={14}
      height={18}
      x={4}
      y={3}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: 'alpha',
      }}
    >
      <Path
        fill="#FFC466"
        d="M17.013 11.121c0 4.133-2.692 9.354-6.013 9.354-3.321 0-6.013-5.221-6.013-9.354 0-4.133 2.692-7.483 6.013-7.483 3.321 0 6.013 3.35 6.013 7.483Z"
      />
    </Mask>
    <G filter="url(#c)" mask="url(#d)">
      <Path
        fill="#EDC17A"
        d="M16.543 11.46c0 4.131-3.19 9.35-7.124 9.35-3.935 0-7.124-5.219-7.124-9.35 0-4.132 3.19-7.481 7.124-7.481 3.934 0 7.124 3.35 7.124 7.48Z"
      />
    </G>
    <Path
      fill="#392410"
      d="M11.55 16.772a.733.733 0 1 0 1.467 0 .733.733 0 0 0-1.467 0Zm5.949-.878-.135-.027.135.027Zm-5.215 1.016h4.138v-.275h-4.138v.275Zm5.35-.988.582-2.824-.27-.055-.582 2.824.27.055Zm-1.212.988c.587 0 1.093-.413 1.211-.988l-.269-.056a.962.962 0 0 1-.942.769v.275Z"
    />
    <Defs></Defs>
  </Svg>
);
export default AgentIcon;
