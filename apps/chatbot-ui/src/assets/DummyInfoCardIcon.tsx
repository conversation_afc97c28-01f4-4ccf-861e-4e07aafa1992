import * as React from 'react';
import Svg, { Circle, Path, SvgProps } from 'react-native-svg';
import { COLORS } from '../constants/globalStyles';

export const DummyInfoCardIcon = (props: SvgProps) => (
  <Svg
    viewBox="0 0 256 256"
    width={24}
    height={24}
    stroke={COLORS.TEXT_DEFAULT}
    color={COLORS.TEXT_DEFAULT}
    {...props}
  >
    <Path fill="none" d="M0 0h256v256H0z" />
    <Circle
      cx={176}
      cy={72}
      r={24}
      fill="none"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={16}
    />
    <Path
      fill="none"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={16}
      d="M40 193.61c72-59.69 104 56.47 176-3.22M40 153.61c72-59.69 104 56.47 176-3.22"
    />
    <Path
      fill="none"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={16}
      d="m189.58 165.58-57.46-57.46A96 96 0 0 0 64.24 80H40m39.96 56.04 38.95-38.95"
    />
  </Svg>
);
