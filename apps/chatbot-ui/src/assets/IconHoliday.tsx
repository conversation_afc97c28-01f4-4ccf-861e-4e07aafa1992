// @ts-nocheck
import * as React from 'react';
import Svg, { <PERSON>, Ellipse, Mask, Circle, G, SvgProps } from 'react-native-svg';
const SvgComponent = (
  props: React.JSX.IntrinsicAttributes &
    React.JSX.IntrinsicClassAttributes<React.Component<SvgProps, any, any>> &
    <PERSON><PERSON><PERSON><SvgProps>,
) => (
  <Svg width={19} height={17} viewBox="0 0 19 17" fill="none">
    <Path
      stroke="#20364B"
      strokeLinecap="round"
      strokeWidth={0.892}
      d="m10.438 4.037.238-.574"
    />
    <Path
      stroke="#20364B"
      strokeLinecap="round"
      strokeWidth={1.031}
      d="M8.902 8.416 6.11 15.195"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.722}
      d="m10.365 ************.01.004c1.387.637 3.046 1.813 4.27 3.03.613.607 1.101 1.211 1.394 1.747.302.552.352.95.248 1.201-.118.284-.393.53-.868.704-.475.174-1.114.261-1.88.247-1.532-.028-3.495-.46-5.49-1.286C6.066 9.045 4.41 7.976 3.346 6.93c-.533-.524-.905-1.028-1.105-1.48-.199-.452-.215-.82-.096-1.108.046-.11.15-.23.349-.35.198-.12.47-.225.804-.309.669-.169 1.537-.244 2.457-.238.917.005 1.87.09 2.699.23.733.125 1.345.29 1.75.468l.162.078Z"
    />
    <Path
      fill="#008CFF"
      d="M2.84 6.184c2.018-1.969 5.57-2.25 6.951-2.042-2.062 1.466-2.77 2.12-3.874 4.484-.421-.142-2.518-1.42-3.077-2.442ZM14.833 10.895c-.034-2.82-3.065-5.789-4.236-6.414.435 2.406.23 3.423-.641 5.92 1.189.427 4.234.868 4.877.494Z"
    />
    <Ellipse
      cx={1.081}
      cy={0.293}
      fill="#20364B"
      rx={1.081}
      ry={0.293}
      transform="rotate(22.517 -4.418 25.14)"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.637}
      d="M3.708 11.277a2.655 2.655 0 1 0 .001 5.31 2.655 2.655 0 0 0-.001-5.31Z"
    />
    <Mask
      id="a"
      width={6}
      height={6}
      x={1}
      y={11}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: 'alpha',
      }}
    >
      <Circle
        cx={2.52}
        cy={2.52}
        r={2.52}
        fill="#20364B"
        transform="matrix(-1 0 0 1 6.228 11.412)"
      />
    </Mask>
    <G mask="url(#a)">
      <Path
        stroke="#20364B"
        strokeWidth={0.637}
        d="M5.47 12.526c-.644.024-1.781.188-2.835.685-1.069.505-2.014 1.334-2.318 2.678.166.191.442.325.825.362.444.042 1-.053 1.584-.309 1.117-.49 2.298-1.558 2.948-3.275l-.204-.141Z"
      />
      <Ellipse
        cx={0.5}
        cy={0.674}
        fill="#20364B"
        rx={0.5}
        ry={0.674}
        transform="matrix(-.86368 .50404 .50404 .86368 5.848 11.714)"
      />
    </G>
  </Svg>
);
export default SvgComponent;
export const IconHoliday = React.memo(SvgComponent);
