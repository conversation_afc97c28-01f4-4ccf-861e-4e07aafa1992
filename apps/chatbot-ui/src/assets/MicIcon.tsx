import * as React from 'react';
import Svg, { Defs, LinearGradient, Path, Stop, SvgProps } from 'react-native-svg';

export type MicIconProps = { disabled?: boolean } & SvgProps;
const disabledColor = '#999999';
export const MicIcon = ({ disabled, ...props }: MicIconProps) => (
  <Svg width={24} height={24} fill="none" {...props}>
    <Path
      fill="url(#a)"
      fillRule="evenodd"
      d="M3.692 10.372c.383 0 .693.313.693.698v1.86c0 4.239 3.41 7.675 7.615 7.675 4.206 0 7.615-3.436 7.615-7.675v-1.86c0-.386.31-.698.693-.698.382 0 .692.313.692.698v1.86c0 5.01-4.03 9.07-9 9.07s-9-4.06-9-9.07v-1.86c0-.386.31-.698.692-.698Z"
      clipRule="evenodd"
    />
    <Path
      fill="url(#b)"
      fillRule="evenodd"
      d="M12 2C8.559 2 5.77 4.811 5.77 8.28v4.65c0 3.468 2.789 6.28 6.23 6.28 3.441 0 6.23-2.812 6.23-6.28V8.28C18.23 4.81 15.442 2 12 2ZM9.678 7.39a.701.701 0 0 1-.027-.986l.503.48-.502-.48v-.001l.002-.002.003-.003.007-.008a.9.9 0 0 1 .06-.055 1.5 1.5 0 0 1 .128-.099 2.27 2.27 0 0 1 .437-.232c.379-.154.93-.283 1.711-.283s1.332.129 1.711.283c.19.077.332.16.437.232a1.51 1.51 0 0 1 .188.154l.***************.002.002s.001.001-.492.471l.493-.47c.263.28.25.722-.027.987a.688.688 0 0 1-.951 0l-.007-.005a.907.907 0 0 0-.172-.088C13 7.22 12.63 7.116 12 7.116s-1 .104-1.192.182a.907.907 0 0 0-.172.088l-.007.005a.688.688 0 0 1-.95 0Zm0 2.791a.702.702 0 0 1-.027-.986l.503.48-.502-.48v-.002l.002-.001.003-.004.007-.007a.9.9 0 0 1 .06-.056 1.5 1.5 0 0 1 .128-.098 2.27 2.27 0 0 1 .437-.232c.379-.154.93-.283 1.711-.283s1.332.129 1.711.283c.19.077.332.16.437.232a1.51 1.51 0 0 1 .188.154l.***************.002.001v.001s.001.001-.492.47l.493-.469c.263.28.25.722-.027.986a.689.689 0 0 1-.951 0l-.007-.004a.904.904 0 0 0-.172-.088C13 10.01 12.63 9.907 12 9.907s-1 .104-1.192.182a.904.904 0 0 0-.172.088l-.007.005a.689.689 0 0 1-.95 0Z"
      clipRule="evenodd"
    />
    <Defs>
      {/* @ts-ignore */}
      <LinearGradient
        id="a"
        x1={8.452}
        x2={16.677}
        y1={6.419}
        y2={17.52}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={disabled ? disabledColor : '#51AFE6'} />
        <Stop offset={0.577} stopColor={disabled ? disabledColor : '#355FF2'} />
        <Stop offset={1} stopColor={disabled ? disabledColor : '#11287A'} />
      </LinearGradient>
      {/* @ts-ignore */}
      <LinearGradient
        id="b"
        x1={8.452}
        x2={16.677}
        y1={6.419}
        y2={17.52}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor={disabled ? disabledColor : '#51AFE6'} />
        <Stop offset={0.577} stopColor={disabled ? disabledColor : '#355FF2'} />
        <Stop offset={1} stopColor={disabled ? disabledColor : '#11287A'} />
      </LinearGradient>
    </Defs>
  </Svg>
);
