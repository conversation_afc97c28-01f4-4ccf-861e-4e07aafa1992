/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react';
import Svg, { Path, SvgProps, Defs, LinearGradient, Stop } from 'react-native-svg';

export function TripModeIconEnabled(
  props: React.JSX.IntrinsicAttributes &
    React.JSX.IntrinsicClassAttributes<React.Component<SvgProps, any, any>> &
    <PERSON><PERSON><PERSON><SvgProps>,
) {
  return (
    <Svg width={16} height={16} viewBox="0 0 16 16" fill="none" {...props}>
      <Path
        d="M13.746 5.633c-.7-3.08-3.386-4.466-5.746-4.466h-.007c-2.353 0-5.047 1.38-5.747 4.46-.78 3.44 1.327 6.353 3.234 8.186A3.624 3.624 0 008 14.833c.906 0 1.813-.34 2.513-1.02 1.907-1.833 4.013-4.74 3.233-8.18zM8 8.973a2.1 2.1 0 110-4.2 2.1 2.1 0 010 4.2z"
        fill="#292D32"
      />
      <Path
        d="M13.746 5.633c-.7-3.08-3.386-4.466-5.746-4.466h-.007c-2.353 0-5.047 1.38-5.747 4.46-.78 3.44 1.327 6.353 3.234 8.186A3.624 3.624 0 008 14.833c.906 0 1.813-.34 2.513-1.02 1.907-1.833 4.013-4.74 3.233-8.18zM8 8.973a2.1 2.1 0 110-4.2 2.1 2.1 0 010 4.2z"
        fill="url(#paint0_linear_774_35648)"
      />
      <Defs>
        {/* @ts-ignore */}
        <LinearGradient
          id="paint0_linear_774_35648"
          x1={3.73616}
          y1={2.58203}
          x2={11.2771}
          y2={13.279}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#51AFE6" />
          <Stop offset={0.5} stopColor="#355FF2" />
          <Stop offset={1} stopColor="#11287A" />
        </LinearGradient>
      </Defs>
    </Svg>
  );
}

export function TripModeIconDisabled(
  props: React.JSX.IntrinsicAttributes &
    React.JSX.IntrinsicClassAttributes<React.Component<SvgProps, any, any>> &
    Readonly<SvgProps>,
) {
  return (
    <Svg width={16} height={16} viewBox="0 0 16 16" fill="none" {...props}>
      <Path
        d="M13.746 5.633c-.7-3.08-3.386-4.466-5.746-4.466h-.007c-2.353 0-5.047 1.38-5.747 4.46-.78 3.44 1.327 6.353 3.234 8.186A3.624 3.624 0 008 14.833c.906 0 1.813-.34 2.513-1.02 1.907-1.833 4.013-4.74 3.233-8.18zM8 8.973a2.1 2.1 0 11-.001-4.2 2.1 2.1 0 010 4.2z"
        fill="#757575"
      />
    </Svg>
  );
}
