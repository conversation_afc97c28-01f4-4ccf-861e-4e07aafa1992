// @ts-nocheck
import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';
const SuitCaseIconSmall = (props: SvgProps) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    fill="none"
    {...props}
  >
    <Path
      fill="#1C274C"
      fillRule="evenodd"
      d="M9.957 1.042h.086c.75 0 1.373 0 1.87.066.522.07.995.225 1.374.605.38.38.534.852.605 1.375.05.366.062.803.065 1.308.54.018 1.022.05 1.45.107.978.131 1.768.408 2.392 1.031.624.624.9 1.415 1.032 2.392.127.95.127 2.162.127 3.694v.094c0 1.531 0 2.744-.127 3.694-.132.977-.408 1.767-1.032 2.391-.624.624-1.414.9-2.391 1.032-.95.127-2.163.127-3.694.127H8.286c-1.531 0-2.744 0-3.693-.127-.977-.132-1.768-.408-2.392-1.032-.624-.624-.9-1.414-1.032-2.391-.127-.95-.127-2.163-.127-3.694v-.094c0-1.532 0-2.745.127-3.694.132-.977.408-1.768 1.032-2.392.624-.623 1.415-.9 2.392-1.031.428-.058.91-.09 1.45-.107.003-.505.016-.942.065-1.308.07-.523.225-.995.605-1.375s.852-.534 1.375-.605c.496-.066 1.12-.066 1.869-.066ZM7.293 4.377c.315-.002.646-.002.993-.002h3.428c.347 0 .678 0 .993.002a9.392 9.392 0 0 0-.054-1.122c-.052-.385-.141-.55-.25-.658-.108-.109-.273-.198-.657-.25-.403-.054-.943-.055-1.746-.055s-1.343.001-1.745.055c-.385.052-.55.141-.658.25-.109.108-.198.273-.25.658-.039.288-.05.646-.054 1.122ZM4.76 5.742c-.838.112-1.321.324-1.674.676-.353.353-.564.836-.677 1.674-.115.857-.116 1.986-.116 3.575 0 1.589.001 2.718.116 3.574.113.838.324 1.322.677 1.674.353.353.836.564 1.674.677.857.115 1.985.116 3.574.116h3.334c1.589 0 2.718 0 3.574-.116.838-.113 1.322-.324 1.674-.677.353-.352.564-.835.677-1.674.115-.856.116-1.985.116-3.574 0-1.59 0-2.718-.116-3.575-.113-.838-.324-1.321-.677-1.674-.352-.352-.836-.564-1.674-.676-.856-.116-1.985-.117-3.574-.117H8.333c-1.589 0-2.717.001-3.574.117Z"
      clipRule="evenodd"
    />
    <Path
      fill="#1C274C"
      d="M14.167 7.5a.833.833 0 1 1-1.667 0 .833.833 0 0 1 1.667 0ZM7.5 7.5a.833.833 0 1 1-1.667 0 .833.833 0 0 1 1.667 0Z"
    />
  </Svg>
);
export default SuitCaseIconSmall;
