import * as React from 'react';
import Svg, { Defs, LinearGradient, Path, Stop, SvgProps } from 'react-native-svg';

export const PauseIcon = (props: SvgProps) => (
  <Svg viewBox={'0 0 21 20'} width={21} height={20} fill="none" {...props}>
    <Path
      fill="#4A4A4A"
      d="M2.167 5c0-1.572 0-2.357.488-2.845s1.274-.488 2.845-.488c1.571 0 2.357 0 2.845.488S8.833 3.428 8.833 5v10c0 1.571 0 2.357-.488 2.845s-1.274.488-2.845.488c-1.571 0-2.357 0-2.845-.488S2.167 16.571 2.167 15V5Z"
    />
    <Path
      fill="url(#a)"
      d="M2.167 5c0-1.572 0-2.357.488-2.845s1.274-.488 2.845-.488c1.571 0 2.357 0 2.845.488S8.833 3.428 8.833 5v10c0 1.571 0 2.357-.488 2.845s-1.274.488-2.845.488c-1.571 0-2.357 0-2.845-.488S2.167 16.571 2.167 15V5Z"
    />
    <Path
      fill="#4A4A4A"
      d="M12.167 5c0-1.572 0-2.357.488-2.845s1.274-.488 2.845-.488c1.571 0 2.357 0 2.845.488s.488 1.273.488 2.845v10c0 1.571 0 2.357-.488 2.845s-1.274.488-2.845.488c-1.571 0-2.357 0-2.845-.488s-.488-1.274-.488-2.845V5Z"
    />
    <Path
      fill="url(#b)"
      d="M12.167 5c0-1.572 0-2.357.488-2.845s1.274-.488 2.845-.488c1.571 0 2.357 0 2.845.488s.488 1.273.488 2.845v10c0 1.571 0 2.357-.488 2.845s-1.274.488-2.845.488c-1.571 0-2.357 0-2.845-.488s-.488-1.274-.488-2.845V5Z"
    />
    <Defs>
      {/* @ts-ignore*/}
      <LinearGradient
        id="a"
        x1={7.215}
        x2={13.829}
        y1={5.349}
        y2={15.268}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#51AFE6" />
        <Stop offset={0.577} stopColor="#355FF2" />
        <Stop offset={1} stopColor="#11287A" />
      </LinearGradient>
      {/* @ts-ignore*/}
      <LinearGradient
        id="b"
        x1={7.215}
        x2={13.829}
        y1={5.349}
        y2={15.268}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#51AFE6" />
        <Stop offset={0.577} stopColor="#355FF2" />
        <Stop offset={1} stopColor="#11287A" />
      </LinearGradient>
    </Defs>
  </Svg>
);
