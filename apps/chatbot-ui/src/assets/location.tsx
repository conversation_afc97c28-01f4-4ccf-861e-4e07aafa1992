// @ts-nocheck
import * as React from 'react';
import Svg, { <PERSON>, <PERSON>, De<PERSON>, <PERSON>lipPath, SvgProps } from 'react-native-svg';
const LocationIcon = (props: SvgProps) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={13}
    height={13}
    viewBox="0 0 9 10"
    fill="none"
    {...props}
  >
    <G clipPath="url(#a)">
      <Path
        fill="#fff"
        stroke="#20364B"
        strokeWidth={0.5}
        d="M3.66.752a2.548 2.548 0 0 0-1.316.176c-.093.04-.184.086-.273.138l-.036.021a2.458 2.458 0 0 0-.414.318l-.009.007c-.997.954-1.136 2.642-.34 4.006.577.988 1.14 1.987 1.673 3.002l.538 1.025c.***************.335.177l.036-.015.002-.002a.195.195 0 0 0 .062-.066h.001l.102-.169.434-.721a55.63 55.63 0 0 1 1.672-2.59c.333-.488.503-1.089.503-1.718a3.77 3.77 0 0 0-.877-2.389C5.168 1.25 4.409.845 3.66.752Zm.064 1.193c1.034.128 1.842 1.121 1.842 2.2 0 .46-.145.874-.397 1.191l-.114.13c-.35.358-.848.545-1.392.478-1.034-.13-1.842-1.122-1.843-2.202 0-.85.5-1.545 1.237-1.75.21-.059.435-.076.667-.047Z"
      />
      <Path
        fill="#20364B"
        d="M2.07 3.742c.001.968.728 1.842 1.624 1.954.466.058.886-.102 1.182-.405-.177.05-.369.065-.569.04-.896-.111-1.623-.986-1.624-1.953 0-.465.167-.866.44-1.146-.615.172-1.053.759-1.052 1.51Z"
      />
      <Path
        fill="#20364B"
        d="M1.945.85c.097-.058.198-.108.3-.152A2.797 2.797 0 0 1 3.692.505c.816.101 1.631.539 2.254 1.287a4.02 4.02 0 0 1 .935 2.549c0 .673-.182 1.325-.546 1.86a55.38 55.38 0 0 0-1.665 2.577l-.435.721-.102.17a.445.445 0 0 1-.141.147l.586-.35a.44.44 0 0 0 .168-.162l.537-.89c.53-.88 1.09-1.735 1.665-2.578.364-.535.546-1.186.546-1.86a4.02 4.02 0 0 0-.935-2.548C5.935.678 5.12.242 4.304.14c-.6-.074-1.2.033-1.722.33l-.008.006-.065.039L2.5.519l-.555.33Z"
      />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 0h9v10H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
export default LocationIcon;
