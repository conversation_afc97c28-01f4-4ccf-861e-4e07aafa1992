import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export type SpeakerIconProps = SvgProps & {
  isPlaying?: boolean;
};
export const SpeakerIcon = (props: SpeakerIconProps) => (
  <Svg width={16} height={16} fill="none" {...props}>
    <Path
      fill={props.isPlaying ? '#008CFF' : '#4A4A4A'}
      fillRule="evenodd"
      d="M8.093 3.273c-.476.231-1.08.628-1.939 1.195l-.146.096-.033.022c-.253.167-.429.283-.621.368a2.5 2.5 0 0 1-.593.178c-.208.035-.418.035-.722.034H4c-.962 0-1.287.011-1.563.14a1.647 1.647 0 0 0-.691.649c-.146.27-.172.547-.224 1.39-.014.23-.022.453-.022.655s.008.424.022.655c.052.843.078 1.12.224 1.39.14.258.424.526.69.65.277.127.602.138 1.564.138h.04c.303 0 .513 0 .72.035.205.034.404.094.594.178.192.085.368.2.621.368l.033.021.146.097c.86.567 1.463.963 1.94 1.195.474.23.712.24.878.183a1.03 1.03 0 0 0 .26-.133c.145-.101.276-.297.369-.812.093-.516.127-1.231.175-2.252.034-.746.057-1.372.057-1.713 0-.341-.023-.968-.057-1.714-.048-1.02-.082-1.735-.175-2.252-.093-.514-.224-.71-.369-.812a1.045 1.045 0 0 0-.26-.132c-.166-.057-.404-.048-.879.183Zm-.436-.9c.55-.267 1.092-.416 1.638-.23.181.062.356.15.512.261.471.33.67.854.778 1.453.106.59.143 1.371.188 2.344l.002.039c.034.743.058 1.392.058 1.76 0 .368-.024 1.017-.058 1.76l-.002.038c-.045.973-.082 1.755-.188 2.344-.108.6-.307 1.123-.778 1.454-.156.11-.33.198-.512.26-.546.186-1.088.038-1.638-.23-.543-.263-1.2-.697-2.021-1.238l-.178-.118c-.299-.197-.402-.262-.508-.31a1.5 1.5 0 0 0-.356-.106c-.114-.02-.236-.02-.594-.02h-.107c-.812 0-1.375 0-1.876-.232a2.64 2.64 0 0 1-1.151-1.083c-.262-.486-.293-.994-.337-1.716A11.915 11.915 0 0 1 .5 8a11.915 11.915 0 0 1 .03-.803c.044-.723.075-1.23.337-1.716.242-.45.687-.87 1.15-1.083.502-.232 1.065-.232 1.877-.232H4c.358 0 .48-.001.594-.02a1.5 1.5 0 0 0 .356-.107c.106-.047.209-.113.508-.31l.178-.117c.82-.541 1.478-.975 2.02-1.239Zm5.356 1.243a.5.5 0 0 1 .704.064l-.384.32.384-.32h.001l.001.002.002.002.005.006.014.018.043.058c.035.05.081.118.135.208.108.18.246.443.382.796.272.708.533 1.769.533 3.23s-.26 2.522-.533 3.23a5.122 5.122 0 0 1-.382.796 3.133 3.133 0 0 1-.187.277l-.005.006-.005.007-.002.002v.001s-.002.001-.386-.32l.384.321a.5.5 0 0 1-.77-.637m0 0s0-.002.002-.003a2.1 2.1 0 0 0 .112-.168 4.14 4.14 0 0 0 .306-.642c.227-.592.466-1.531.466-2.87 0-1.339-.239-2.278-.466-2.87a4.14 4.14 0 0 0-.306-.642 2.11 2.11 0 0 0-.112-.168l-.002-.003.002.003-.001-.002-.001-.001a.5.5 0 0 1 .066-.701m-1.256 1.947a.5.5 0 0 1 .68.194l-.419.233.42-.233v.001l.***************.006.012a1.633 1.633 0 0 1 .068.152c.**************.135.42.094.368.182.91.182 1.65 0 .74-.088 1.282-.182 1.65a3.417 3.417 0 0 1-.185.536 1.756 1.756 0 0 1-.018.036l-.006.012-.003.005v.002h-.001s0 .002-.42-.23l.42.232a.5.5 0 0 1-.878-.48l.003-.007a2.44 2.44 0 0 0 .12-.356c.072-.282.15-.74.15-1.4 0-.66-.078-1.118-.15-1.4a2.44 2.44 0 0 0-.12-.357l-.003-.006a.5.5 0 0 1 .197-.674Z"
      clipRule="evenodd"
    />
  </Svg>
);
