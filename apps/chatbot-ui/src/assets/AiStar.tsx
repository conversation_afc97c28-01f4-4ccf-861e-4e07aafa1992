/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react';
import Svg, { Path, Defs, LinearGradient, Stop, SvgProps } from 'react-native-svg';

function AiStar(props: SvgProps) {
  const { width = 14, height = 15 } = props;
  return (
    <Svg width={width} height={height} viewBox="0 0 14 15" fill="none" {...props}>
      <Path
        d="M7.002.57h-.004A7.152 7.152 0 010 7.567v.003a7.152 7.152 0 016.998 6.998h.004A7.152 7.152 0 0114 7.571v-.003A7.152 7.152 0 017.002.569z"
        fill="url(#paint0_linear_2116_103808)"
      />
      <Defs>
        {/*@ts-ignore*/}
        <LinearGradient
          id="paint0_linear_2116_103808"
          x1={0.640377}
          y1={7.56934}
          x2={14}
          y2={7.56934}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#94A1F8" />
          <Stop offset={0.62} stopColor="#355FF2" />
        </LinearGradient>
      </Defs>
    </Svg>
  );
}

export default AiStar;
