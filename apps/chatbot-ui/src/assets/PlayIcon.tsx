import * as React from 'react';
import Svg, { Defs, LinearGradient, Path, Stop, SvgProps } from 'react-native-svg';

export const PlayIcon = (props: SvgProps) => (
  <Svg viewBox={'0 0 21 20'} width={21} height={20} fill="none" {...props}>
    <Path
      fill="#4A4A4A"
      d="M5.167 4.115c0-1.605.907-2.282 2.018-1.508l9.278 6.473c1.111.774 1.111 2.04 0 2.814l-9.278 6.473c-1.111.774-2.018.097-2.018-1.508V4.115Z"
    />
    <Path
      fill="url(#a)"
      d="M5.167 4.115c0-1.605.907-2.282 2.018-1.508l9.278 6.473c1.111.774 1.111 2.04 0 2.814l-9.278 6.473c-1.111.774-2.018.097-2.018-1.508V4.115Z"
    />
    <Defs>
      {/* @ts-ignore*/}
      <LinearGradient
        id="a"
        x1={7.215}
        x2={13.829}
        y1={5.349}
        y2={15.268}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#51AFE6" />
        <Stop offset={0.577} stopColor="#355FF2" />
        <Stop offset={1} stopColor="#11287A" />
      </LinearGradient>
    </Defs>
  </Svg>
);
