/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react';
import Svg, { G, Path, SvgProps } from 'react-native-svg';

export const ThumbsUpIcon = (props: SvgProps) => (
  <Svg width={16} height={16} fill="none" {...props}>
    {/*@ts-ignore*/}
    <G stroke="#4A4A4A" strokeLinecap="round" strokeLinejoin="round">
      <Path d="M3.967 4.8h-.8a1 1 0 0 0-1 1v6.865a1 1 0 0 0 1 1h.8a1 1 0 0 0 1-1V5.8a1 1 0 0 0-1-1ZM4.967 6.35v5.314a2 2 0 0 0 2 2h4.582a1.666 1.666 0 0 0 1.608-1.228l1.305-4.792a1.667 1.667 0 0 0-1.608-2.105h-2.73V3a1.333 1.333 0 0 0-1.333-1.333h-.024a1.333 1.333 0 0 0-1.114.6L4.967 6.351Z" />
    </G>
  </Svg>
);

export const ThumbsUpFilledIcon = (props: SvgProps) => (
  <Svg width={16} height={16} fill="none" {...props}>
    {/*@ts-ignore*/}
    <G fill="#CCE8FF" stroke="#008CFF" strokeLinecap="round" strokeLinejoin="round">
      <Path d="M3.967 4.8h-.8a1 1 0 0 0-1 1v6.865a1 1 0 0 0 1 1h.8a1 1 0 0 0 1-1V5.8a1 1 0 0 0-1-1ZM4.967 6.35v5.314a2 2 0 0 0 2 2h4.582a1.666 1.666 0 0 0 1.608-1.228l1.305-4.792a1.667 1.667 0 0 0-1.608-2.105h-2.73V3a1.333 1.333 0 0 0-1.333-1.333h-.024a1.333 1.333 0 0 0-1.114.6L4.967 6.351Z" />
    </G>
  </Svg>
);

export const ThumbsDownIcon = (props: SvgProps) => (
  <Svg width={16} height={16} fill="none" {...props}>
    {/*@ts-ignore*/}
    <G stroke="#4A4A4A" strokeLinecap="round" strokeLinejoin="round">
      <Path d="M3.967 11.2h-.8a1 1 0 0 1-1-1V3.335a1 1 0 0 1 1-1h.8a1 1 0 0 1 1 1V10.2a1 1 0 0 1-1 1ZM4.967 9.65V4.335a2 2 0 0 1 2-2h4.582a1.667 1.667 0 0 1 1.608 1.228l1.305 4.792a1.667 1.667 0 0 1-1.608 2.105h-2.73V13a1.333 1.333 0 0 1-1.333 1.333h-.024a1.333 1.333 0 0 1-1.114-.6L4.967 9.648Z" />
    </G>
  </Svg>
);

export const ThumbsDownFilledIcon = (props: SvgProps) => (
  <Svg width={16} height={16} fill="none" {...props}>
    {/*@ts-ignore*/}
    <G fill="#CCE8FF" stroke="#008CFF" strokeLinecap="round" strokeLinejoin="round">
      <Path d="M3.967 11.2h-.8a1 1 0 0 1-1-1V3.335a1 1 0 0 1 1-1h.8a1 1 0 0 1 1 1V10.2a1 1 0 0 1-1 1ZM4.967 9.65V4.335a2 2 0 0 1 2-2h4.582a1.667 1.667 0 0 1 1.608 1.228l1.305 4.792a1.667 1.667 0 0 1-1.608 2.105h-2.73V13a1.333 1.333 0 0 1-1.333 1.333h-.024a1.333 1.333 0 0 1-1.114-.6L4.967 9.648Z" />
    </G>
  </Svg>
);
