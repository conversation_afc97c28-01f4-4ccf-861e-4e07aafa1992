/* eslint-disable @typescript-eslint/ban-ts-comment */
import React from 'react';
import Svg, { SvgProps, Path } from 'react-native-svg';

export const BookmarkIconNonImageCards = (props: SvgProps) => (
  <Svg width={16} height={16} viewBox="0 0 16 16" fill="none" {...props}>
    <Path
      d="M14 10.727V7.4c0-2.86 0-4.29-.879-5.177-.878-.889-2.293-.889-5.121-.889-2.828 0-4.243 0-5.121.889C2 3.11 2 4.539 2 7.399v3.328c0 2.065 0 3.097.49 3.548.233.215.527.35.841.386.658.075 1.427-.604 2.964-1.964.68-.6 1.019-.901 1.412-.98.193-.04.393-.04.586 0 .393.079.733.38 1.412.98 1.537 1.36 2.306 2.04 2.964 1.964.314-.036.608-.17.842-.386.489-.451.489-1.483.489-3.548z"
      stroke="#4A4A4A"
    />
    <Path d="M10 4H6" stroke="#4A4A4A" strokeLinecap="round" />
  </Svg>
);

export const BookmarkIconNonImageCardsFilled = (props: SvgProps) => (
  <Svg width={16} height={16} viewBox="0 0 16 16" fill="none" {...props}>
    <Path
      d="M14 10.727V7.4c0-2.86 0-4.29-.879-5.177-.878-.889-2.293-.889-5.121-.889-2.828 0-4.243 0-5.121.889C2 3.11 2 4.539 2 7.399v3.328c0 2.065 0 3.097.49 3.548.233.215.527.35.841.386.658.075 1.427-.604 2.964-1.964.68-.6 1.019-.901 1.412-.98.193-.04.393-.04.586 0 .393.079.733.38 1.412.98 1.537 1.36 2.306 2.04 2.964 1.964.314-.036.608-.17.842-.386.489-.451.489-1.483.489-3.548z"
      fill="#4A4A4A"
      stroke="#4A4A4A"
    />
    <Path d="M10 4H6" stroke="#fff" strokeLinecap="round" />
  </Svg>
);
