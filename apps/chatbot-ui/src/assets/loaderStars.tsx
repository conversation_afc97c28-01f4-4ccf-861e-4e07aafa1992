/* eslint-disable @typescript-eslint/ban-ts-comment */
import React from 'react';

import Svg, {
  Path,
  Defs,
  LinearGradient,
  Stop,
  SvgProps,
  G,
} from 'react-native-svg';

interface LoaderStarProps extends SvgProps {
  color?: string;
}

export const LoaderStar = (props: LoaderStarProps) => {
  const { color, ...svgProps } = props;

  return (
    <Svg
      width={svgProps.width || 10}
      height={svgProps.height || 10}
      viewBox="0 0 10 10"
      fill="none"
      {...svgProps}
    >
      <Path
        d="M5.18.582h-.003A4.342 4.342 0 01.928 4.83v.002a4.342 4.342 0 014.25 4.249h.001a4.342 4.342 0 014.25-4.25v-.001A4.342 4.342 0 015.178.58z"
        fill={color || '#fff'}
      />
      <Path
        d="M5.18.582h-.003A4.342 4.342 0 01.928 4.83v.002a4.342 4.342 0 014.25 4.249h.001a4.342 4.342 0 014.25-4.25v-.001A4.342 4.342 0 015.178.58z"
        fill={color || 'url(#paint0_linear_2690_51819)'}
      />
      {!color && (
        <Defs>
          {/*@ts-ignore*/}
          <LinearGradient
            id="paint0_linear_2690_51819"
            x1={4.82406}
            y1={2.35262}
            x2={8.956}
            y2={7.42901}
            gradientUnits="userSpaceOnUse"
          >
            <Stop stopColor="#51AFE6" />
            <Stop offset={0.461569} stopColor="#355FF2" />
            <Stop offset={0.694356} stopColor="#11287A" />
          </LinearGradient>
        </Defs>
      )}
    </Svg>
  );
};
