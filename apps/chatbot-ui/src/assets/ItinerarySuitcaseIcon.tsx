// @ts-nocheck
import * as React from 'react';
import Svg, { Path, Defs, LinearGradient, Stop, SvgProps } from 'react-native-svg';
const ItinerarySuitcaseIcon = (props: SvgProps) => (
  <Svg width={20} height={20} fill="none" {...props}>
    <Path
      fill="url(#a)"
      d="m4.289 14.878.442-.442-.442.442Zm4.756 0-.442-.442.442.442Zm-3.42-9.461a.625.625 0 0 0-1.25 0h1.25Zm0 10a.625.625 0 0 0-1.25 0h1.25Zm-1.25 2.5a.625.625 0 1 0 1.25 0h-1.25Zm11.25-12.5a.625.625 0 1 0-1.25 0h1.25Zm-1.25 12.5a.625.625 0 1 0 1.25 0h-1.25ZM8.334 5v.625h3.333v-1.25H8.334V5Zm3.333 13.333v-.625H8.334v1.25h3.333v-.625Zm-3.333 0v-.625c-1.59 0-2.718 0-3.575-.116-.838-.113-1.321-.324-1.674-.677l-.442.442-.442.442c.624.624 1.415.9 2.392 1.032.959.129 2.187.127 3.74.127v-.625Zm-6.667-6.666h-.625c0 1.553-.001 2.782.128 3.74.131.978.408 1.768 1.031 2.392l.442-.442.442-.442c-.352-.352-.564-.836-.677-1.674-.115-.856-.116-1.985-.116-3.574h-.625Zm16.667 0h-.625c0 1.589-.002 2.718-.117 3.574-.112.838-.324 1.322-.677 1.674l.442.442.442.442c.624-.624.9-1.414 1.032-2.391.129-.96.128-2.188.128-3.741h-.625Zm-6.667 6.666v.625c1.554 0 2.782.002 3.74-.127.978-.132 1.769-.408 2.392-1.032l-.442-.442-.442-.442c-.352.353-.835.564-1.674.677-.856.115-1.985.116-3.574.116v.625Zm0-13.333v.625c1.589 0 2.718.001 3.574.117.839.112 1.322.324 1.674.676l.442-.442.442-.442c-.623-.623-1.414-.9-2.391-1.031-.96-.13-2.187-.128-3.741-.128V5Zm6.667 6.667h.625c0-1.554.001-2.782-.128-3.741-.131-.977-.408-1.768-1.032-2.392l-.442.442-.442.442c.353.353.565.836.677 1.674.115.857.117 1.986.117 3.575h.625ZM8.334 5v-.625c-1.554 0-2.782-.001-3.741.128-.977.131-1.768.408-2.392 1.031l.442.442.442.442c.353-.352.836-.564 1.674-.676.857-.116 1.986-.117 3.575-.117V5Zm-6.667 6.667h.625c0-1.59.001-2.718.116-3.575.113-.838.325-1.321.677-1.674l-.442-.442-.442-.442c-.623.624-.9 1.415-1.031 2.392-.13.959-.128 2.187-.128 3.74h.625Zm8.333-10v.625c.804 0 1.343.001 1.746.055.384.052.549.141.658.25l.441-.442.442-.442c-.38-.38-.851-.534-1.375-.605-.505-.068-1.144-.066-1.912-.066v.625ZM13.334 5h.625c0-.768.001-1.407-.067-1.912-.07-.523-.225-.995-.605-1.375l-.442.442-.441.442c.108.108.197.273.25.658.053.402.055.942.055 1.745h.625ZM10 1.667v-.625c-.768 0-1.407-.002-1.912.066-.523.07-.995.225-1.375.605l.442.442.442.442c.109-.109.273-.198.658-.25.402-.054.942-.055 1.745-.055v-.625ZM6.667 5h.625c0-.803.001-1.343.055-1.745.052-.385.141-.55.25-.658l-.442-.442-.442-.442c-.38.38-.534.852-.604 1.375-.068.505-.067 1.144-.067 1.912h.625ZM5 12.5v.625h3.334v-1.25H5v.625Zm4.167.833h-.625v.834h1.25v-.834h-.625Zm-5 .834h.625v-.834h-1.25v.834h.625ZM8.334 15v-.625H5v1.25h3.334V15Zm-4.167-.833h-.625c0 .178-.001.375.02.54.025.183.089.416.285.613l.442-.442.442-.442a.305.305 0 0 1 .065.097c.01.023.009.031.006.008a1.287 1.287 0 0 1-.008-.13 10.265 10.265 0 0 1-.002-.244h-.625ZM5 15v-.625c-.1 0-.178 0-.244-.002a1.301 1.301 0 0 1-.13-.008c-.023-.003-.015-.004.008.006a.304.304 0 0 1 .097.065l-.442.442-.442.442c.197.197.43.26.613.284.165.022.362.021.54.021V15Zm4.167-.833h-.625c0 .1 0 .178-.002.244a1.28 1.28 0 0 1-.008.13c-.003.023-.004.015.006-.008a.305.305 0 0 1 .065-.097l.442.442.442.442c.196-.197.26-.43.284-.613.022-.165.021-.362.021-.54h-.625ZM8.334 15v.625c.178 0 .375.001.54-.02.183-.025.416-.088.613-.285l-.442-.442-.442-.442a.303.303 0 0 1 .097-.065c.023-.01.031-.009.008-.006a1.301 1.301 0 0 1-.13.008 10.66 10.66 0 0 1-.244.002V15Zm0-2.5v.625c.1 0 .178 0 .244.002s.105.005.13.008c.023.003.015.004-.008-.005a.307.307 0 0 1-.097-.066l.442-.442.442-.442a1.033 1.033 0 0 0-.613-.284c-.165-.022-.362-.02-.54-.02v.624Zm.833.833h.625c0-.178.001-.375-.02-.54a1.032 1.032 0 0 0-.285-.613l-.442.442-.442.442a.305.305 0 0 1-.065-.097c-.01-.023-.009-.031-.006-.008.004.025.007.064.008.13.002.066.002.144.002.244h.625ZM5 12.5v-.625c-.178 0-.375-.001-.54.02a1.033 1.033 0 0 0-.613.285l.442.442.442.442a.307.307 0 0 1-.097.066c-.023.01-.031.008-.008.005a1.27 1.27 0 0 1 .13-.008c.066-.002.144-.002.244-.002V12.5Zm-.833.833h.625c0-.1 0-.178.002-.244.001-.066.004-.105.008-.13.003-.023.004-.015-.006.008a.304.304 0 0 1-.065.097l-.442-.442-.442-.442c-.196.197-.26.43-.284.613-.022.165-.021.362-.021.54h.625ZM5 5.417h-.625V12.5h1.25V5.417H5Zm0 10h-.625v2.5h1.25v-2.5H5Zm10-10h-.625v12.5h1.25v-12.5H15Z"
    />
    <Defs>
      <LinearGradient
        id="a"
        x1={1.667}
        x2={18.334}
        y1={18.333}
        y2={1.667}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#4A4A4A" />
        <Stop offset={1} />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default ItinerarySuitcaseIcon;
