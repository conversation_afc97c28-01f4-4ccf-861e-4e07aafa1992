// @ts-nocheck
import * as React from 'react';
import Svg, { Path, Defs, LinearGradient, Stop, SvgProps } from 'react-native-svg';
const TalkToAgentIcon = (
  props: React.JSX.IntrinsicAttributes &
    React.JSX.IntrinsicClassAttributes<React.Component<SvgProps, any, any>> &
    <PERSON><PERSON>ly<SvgProps>,
) => (
  <Svg width={24} height={24} fill="none" {...props}>
    <Path
      stroke="url(#a)"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.5}
      d="M5.724 12V7.873A6.167 6.167 0 0 1 12 1.8a6.167 6.167 0 0 1 6.277 6.073V12m-3.138 8.238a3.138 3.138 0 0 0 3.138-3.138v-3.531m-3.138 6.669a1.961 1.961 0 0 1-1.962 1.961h-2.354a1.961 1.961 0 1 1 0-3.923h2.354a1.961 1.961 0 0 1 1.962 1.962ZM3.369 9.646h1.57a.785.785 0 0 1 .785.785v4.707a.785.785 0 0 1-.785.785H3.37a1.569 1.569 0 0 1-1.568-1.57v-3.138A1.569 1.569 0 0 1 3.37 9.646Zm17.262 6.277h-1.57a.785.785 0 0 1-.784-.785v-4.707a.785.785 0 0 1 .784-.785h1.57a1.57 1.57 0 0 1 1.569 1.57v3.138a1.57 1.57 0 0 1-1.57 1.569Z"
    />
    <Defs>
      <LinearGradient
        id="a"
        x1={1.801}
        x2={22.201}
        y1={22.199}
        y2={1.8}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#4A4A4A" />
        <Stop offset={1} />
      </LinearGradient>
    </Defs>
  </Svg>
);

export default TalkToAgentIcon;
