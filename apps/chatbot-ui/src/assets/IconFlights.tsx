import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';
const IconFlights = (
  props: React.JSX.IntrinsicAttributes &
    React.JSX.IntrinsicClassAttributes<React.Component<SvgProps, any, any>> &
    <PERSON><PERSON><PERSON><SvgProps>,
) => (
  <Svg width={28} height={28} fill="none" {...props}>
    <Path
      fill="#008CFF"
      d="m11.971 14.495-6-4.127 1.224-1.035 9.567 1.21-4.791 3.952Z"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.621}
      d="M17.456 8.668c-3.742 2.567-9.675 7.226-13.713 11.676.718.83 1.896 1.076 2.862.568 4.934-2.593 13.277-7.539 17.619-10.772.494-.368.636-.874.53-1.32-.108-.452-.48-.877-1.09-1.042-2.15-.584-4.398-.352-6.208.89Z"
    />
    <Path
      fill="#20364B"
      stroke="#20364B"
      strokeWidth={0.5}
      d="m5.642 17.8-1.656 1.608-2.713-1.789.638-.546 3.73.726Z"
    />
    <Path
      fill="#008CFF"
      stroke="#008CFF"
      strokeWidth={0.602}
      d="m13.906 16.007 1.898 7.438.979-1 1.347-9.55-4.224 3.112Zm-.006-.022-.033.008.033-.008Z"
    />
    <Path
      stroke="#20364B"
      strokeLinecap="round"
      strokeWidth={0.687}
      d="M23.45 8.127c-.092.316-.397.59-.836.89-.44.299-.93.575-1.721.894"
    />
  </Svg>
);
export default IconFlights;
