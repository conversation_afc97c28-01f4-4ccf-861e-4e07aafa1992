import * as React from 'react';
import Svg, { Circle, Defs, LinearGradient, Stop, SvgProps } from 'react-native-svg';
const IconRedDot = (
  props: React.JSX.IntrinsicAttributes &
    React.JSX.IntrinsicClassAttributes<React.Component<SvgProps, any, any>> &
    Readonly<SvgProps>,
) => (
  <Svg width={10} height={10} fill="none" {...props}>
    <Circle
      cx={5}
      cy={5}
      r={3}
      fill="red"
      stroke="#757575"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Defs>
      <LinearGradient
        id="a"
        x1={1}
        x2={9}
        y1={9}
        y2={1}
        gradientUnits="userSpaceOnUse"
      />
      <Stop stopColor="#FF7F3F" />
      <Stop offset={1} stopColor="#FF3E5E" />
    </Defs>
  </Svg>
);
export default IconRedDot;
