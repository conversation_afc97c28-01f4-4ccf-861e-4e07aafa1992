/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react';
import Svg, { Defs, LinearGradient, Path, Stop, SvgProps } from 'react-native-svg';

export const SendIconGray = (props: SvgProps) => (
  <Svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20.247 10.52l-1.706 5.15c-1.204 3.63-1.805 5.446-2.688 5.962-.839.49-1.876.49-2.715 0-.882-.516-1.484-2.331-2.687-5.962-.193-.583-.29-.874-.452-1.118a2.167 2.167 0 00-.594-.597c-.242-.163-.532-.26-1.111-.454-3.61-1.21-5.415-1.816-5.928-2.703a2.73 2.73 0 010-2.731c.513-.887 2.318-1.492 5.928-2.703l5.12-1.716c4.472-1.5 6.709-2.25 7.89-1.062 1.18 1.187.434 3.436-1.057 7.935zm-7.264.382a.753.753 0 01.005-1.06l4.187-4.165a.743.743 0 011.055.006c.29.295.286.77-.006 1.06l-4.187 4.165a.743.743 0 01-1.054-.006z"
      fill="#999999"
    />
    <Path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M20.247 10.52l-1.706 5.15c-1.204 3.63-1.805 5.446-2.688 5.962-.839.49-1.876.49-2.715 0-.882-.516-1.484-2.331-2.687-5.962-.193-.583-.29-.874-.452-1.118a2.167 2.167 0 00-.594-.597c-.242-.163-.532-.26-1.111-.454-3.61-1.21-5.415-1.816-5.928-2.703a2.73 2.73 0 010-2.731c.513-.887 2.318-1.492 5.928-2.703l5.12-1.716c4.472-1.5 6.709-2.25 7.89-1.062 1.18 1.187.434 3.436-1.057 7.935zm-7.264.382a.753.753 0 01.005-1.06l4.187-4.165a.743.743 0 011.055.006c.29.295.286.77-.006 1.06l-4.187 4.165a.743.743 0 01-1.054-.006z"
      fill="#999999"
    />
    <Defs>
      {/*@ts-ignore*/}
      <LinearGradient
        id="paint0_linear_2242_25334"
        x1={15.8624}
        y1={18.2791}
        x2={7.62381}
        y2={17.9217}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#2493EB" />
        <Stop offset={1} stopColor="#50F" />
      </LinearGradient>
      {/*@ts-ignore*/}
      <LinearGradient
        id="paint1_linear_2242_25334"
        x1={8.02336}
        y1={6.4186}
        x2={15.9776}
        y2={18.2791}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#51AFE6" />
        <Stop offset={0.576933} stopColor="#355FF2" />
        <Stop offset={1} stopColor="#11287A" />
      </LinearGradient>
    </Defs>
  </Svg>
);
