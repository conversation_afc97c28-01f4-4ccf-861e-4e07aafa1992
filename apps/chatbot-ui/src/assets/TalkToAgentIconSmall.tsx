// @ts-nocheck
import * as React from 'react';
import Svg, { Path, Defs, LinearGradient, Stop, SvgProps } from 'react-native-svg';
const TalkToAgentIconSmall = (
  props: React.JSX.IntrinsicAttributes &
    React.JSX.IntrinsicClassAttributes<React.Component<SvgProps, any, any>> &
    <PERSON><PERSON>ly<SvgProps>,
) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={19}
    height={20}
    fill="none"
    {...props}
  >
    <Path
      stroke="url(#a)"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.25}
      d="M4.02 10V6.56A5.139 5.139 0 0 1 9.25 1.5a5.14 5.14 0 0 1 5.23 5.06V10m-2.615 6.865a2.615 2.615 0 0 0 2.615-2.615v-2.942m-2.615 5.557a1.634 1.634 0 0 1-1.634 1.635H8.269a1.635 1.635 0 0 1 0-3.27h1.962a1.634 1.634 0 0 1 1.634 1.635ZM2.058 8.038h1.308a.654.654 0 0 1 .653.654v3.923a.654.654 0 0 1-.653.654H2.058A1.308 1.308 0 0 1 .75 11.961V9.346a1.308 1.308 0 0 1 1.308-1.308Zm14.384 5.231h-1.308a.654.654 0 0 1-.654-.654V8.692a.654.654 0 0 1 .654-.654h1.308a1.308 1.308 0 0 1 1.308 1.308v2.615a1.308 1.308 0 0 1-1.308 1.308Z"
    />
    <Defs>
      <LinearGradient
        id="a"
        x1={0.75}
        x2={17.75}
        y1={18.5}
        y2={1.5}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#4A4A4A" />
        <Stop offset={1} />
      </LinearGradient>
    </Defs>
  </Svg>
);

export default TalkToAgentIconSmall;
