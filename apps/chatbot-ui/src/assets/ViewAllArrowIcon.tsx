import * as React from 'react';
import Svg, { Path, Rect, SvgProps } from 'react-native-svg';

export const ViewAllArrowIcon = (props: SvgProps) => (
  <Svg width={32} height={33} viewBox={'0 0 32 33'} fill="none" {...props}>
    <Rect width={31} height={31} x={0.5} y={1} stroke="#008CFF" rx={7.5} />
    <Path
      fill="#008CFF"
      fillRule="evenodd"
      d="M21.45 15.8a.2.2 0 0 0 .144-.339l-3.321-3.458a.7.7 0 0 1 .018-.988l.023-.022a.7.7 0 0 1 .995.021l4.736 5.005a.7.7 0 0 1 0 .962l-4.736 5.005a.7.7 0 0 1-.995.023l-.022-.021a.7.7 0 0 1-.019-.988l3.322-3.462a.2.2 0 0 0-.145-.338H9.2a.7.7 0 1 1 0-1.4h12.25Z"
      clipRule="evenodd"
    />
  </Svg>
);
