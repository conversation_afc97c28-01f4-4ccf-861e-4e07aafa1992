import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';
const IconBus = (
  props: React.JSX.IntrinsicAttributes &
    React.JSX.IntrinsicClassAttributes<React.Component<SvgProps, any, any>> &
    <PERSON><PERSON><PERSON><SvgProps>,
) => (
  <Svg width={28} height={28} fill="none" {...props}>
    <Path
      fill="#008CFF"
      d="m12.028 15.454-.663-.973 11.697-7.454c-.003-.034.13-.025.685.287.554.312.793 1.167.77 1.376l-11.232 6.2-1.257.564Z"
    />
    <Path
      fill="#20364B"
      fillRule="evenodd"
      d="M15.056 22.45c.322-.47.53-1.046.53-1.564 0-.878-.598-1.258-1.335-.847-.738.411-1.336 1.456-1.336 2.335 0 .487.184.82.473.951.35.164.966.28 1.415.023.331-.189.33-.586.253-.897ZM22.952 18.445c.3-.471.49-1.05.49-1.589 0-.951-.598-1.412-1.335-1.03-.738.383-1.335 1.464-1.335 2.415 0 .952.597 1.413 1.335 1.03.147-.076.288-.18.42-.305a1.07 1.07 0 0 0 .132-.059.546.546 0 0 0 .293-.462Z"
      clipRule="evenodd"
    />
    <Path
      fill="#008CFF"
      stroke="#20364B"
      strokeWidth={0.631}
      d="m11.88 16.423.001-.012V16.4c0-.29.104-.635.288-.952.183-.318.43-.58.681-.725L24.012 8.28a.376.376 0 0 1 .148-.055c.029-.**************.***************.007.025.033a.374.374 0 0 1 .026.155v6.52c0 .291-.104.635-.287.953-.184.317-.43.58-.682.725l-10.748 6.203c-.027.015-.074.031-.184-.031a1.376 1.376 0 0 1-.388-.383 2.816 2.816 0 0 1-.332-.604 1.56 1.56 0 0 1-.125-.509l.374-4.869Z"
    />
    <Path
      fill="#fff"
      d="M13.007 16.5a.299.299 0 0 1 .135-.226l1.458-.836c.075-.042.136-.006.135.08l-.068 5.556a.298.298 0 0 1-.133.23l-1.622.96c-.077.046-.14.007-.136-.084l.23-5.68ZM15.618 15.006c0-.086.06-.19.134-.233l7.22-4.208c.074-.044.135-.01.135.077v2.587c0 .086-.06.19-.134.233l-7.22 4.209c-.074.043-.135.008-.135-.078v-2.587Z"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.631}
      d="M4.726 11.518a3.747 3.747 0 0 1-.138-.077l10.77-6.78.005-.002c.72-.467 1.198-.626 1.663-.607.481.02.996.23 1.795.635l5.041 2.992c.246.146.298.291.297.373-.001.083-.058.23-.31.374l-11.98 6.911-6.805-3.639a8.592 8.592 0 0 0-.338-.18Zm-.311.033.003-.003a.019.019 0 0 1-.003.003ZM16.152 21.233c0 .756-.493 1.617-1.063 1.945-.57.33-1.062.037-1.062-.72 0-.755.493-1.616 1.062-1.945.57-.329 1.063-.036 1.063.72ZM23.758 16.938c0 .757-.492 1.617-1.062 1.946-.57.329-1.062.037-1.062-.72 0-.756.492-1.617 1.062-1.945.57-.33 1.062-.037 1.062.72Z"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.631}
      d="m4.324 11.726.002-.017v-.017c0-.063.062-.185.256-.278.19-.091.369-.084.453-.036l6.203 3.59c.148.086.3.245.416.445.113.196.175.403.178.572l-.4 5.441-.004.055.014.052.389 1.412c-.008.14-.058.171-.062.174-.004.002-.066.034-.215-.053l-6.635-3.829c-.202-.116-.302-.183-.411-.297-.113-.12-.242-.298-.466-.645l-.29-1.164.572-5.405Z"
    />
    <Path
      fill="#20364B"
      stroke="#20364B"
      strokeWidth={0.691}
      d="M5.298 13.218c.03-.265.237-.358.483-.216l4.495 2.598c.285.165.509.575.484.889l-.181 2.347c-.021.272-.223.377-.47.243l-4.587-2.484c-.297-.161-.533-.595-.497-.916l.273-2.461Z"
    />
    <Path
      fill="#20364B"
      d="M4.606 17.354c.052-.132.185-.162.334-.076l.622.358c.176.102.32.35.32.555 0 .188-.122.275-.284.204l-.617-.274c-.264-.117-.467-.532-.375-.767ZM8.58 19.84c.044-.141.183-.186.35-.112l.484.212c.29.127.523.532.448.778-.044.141-.182.186-.35.112l-.483-.212c-.29-.127-.524-.532-.448-.777Z"
    />
  </Svg>
);
export default IconBus;
