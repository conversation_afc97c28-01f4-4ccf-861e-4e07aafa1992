// @ts-nocheck
import * as React from 'react';
import Svg, { <PERSON>, Rect, G, Path } from 'react-native-svg';
const DownloadPdf = (props) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={36}
    height={36}
    fill="none"
    {...props}
  >
    <Mask
      id="a"
      width={36}
      height={36}
      x={0}
      y={0}
      maskUnits="userSpaceOnUse"
      style={{
        maskType: 'alpha',
      }}
    >
      <Rect width={36} height={36} fill="#F4F4F4" rx={18} />
    </Mask>
    <G mask="url(#a)">
      <Path fill="#D8EDFF" d="M-2-1h40v39H-2z" />
    </G>
    <Path
      stroke="#000"
      strokeWidth={1.125}
      d="M11.25 19.75a1.365 1.365 0 1 0 0-2.73 1.365 1.365 0 0 0 0 2.73Z"
    />
    <Path
      stroke="#000"
      strokeWidth={1.125}
      d="M7.585 17.588c.862-3.788 6.474-3.784 7.332.005.503 2.222-.88 4.103-2.091 5.267-.88.849-2.27.849-3.154 0-1.208-1.164-2.59-3.05-2.087-5.272Z"
    />
    <Path
      stroke="#000"
      strokeLinecap="round"
      strokeWidth={1.125}
      d="M10.875 12.75v-1.763a3 3 0 0 1 3-3h7.875v3.637c0 .415.335.75.75.75h4.124v12a3 3 0 0 1-3 3H12.75a1.875 1.875 0 0 1-1.875-1.875v0"
    />
    <Path stroke="#000" strokeWidth={1.125} d="m21.75 7.875 4.875 4.5" />
    <Path
      stroke="#000"
      strokeLinecap="round"
      strokeWidth={1.125}
      d="M17.25 16.125h5.25M17.25 19.124h5.25"
    />
  </Svg>
);
export default DownloadPdf;
