// @ts-nocheck
import * as React from 'react';
import Svg, { Path, Defs, LinearGradient, Stop, SvgProps } from 'react-native-svg';
const DocumentNotes = (props: SvgProps) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={16}
    height={16}
    fill="none"
    {...props}
  >
    <Path
      fill="#1C274C"
      fillRule="evenodd"
      d="M7.296.833h1.408c1.225 0 2.196 0 2.955.102.782.105 1.415.327 1.913.826a.5.5 0 1 1-.707.707c-.282-.282-.668-.451-1.339-.542-.685-.092-1.588-.093-2.86-.093H7.334c-1.27 0-2.174.001-2.86.093-.67.09-1.056.26-1.338.542-.283.282-.452.668-.542 1.34-.092.684-.093 1.587-.093 2.859v2.666c0 1.271.001 2.175.093 **********.26 1.057.542 1.339.282.282.668.451 1.339.541.685.093 1.588.094 2.86.094h1.333c1.27 0 2.174-.001 2.86-.094.67-.09 1.056-.259 1.338-.541.464-.464.605-1.185.63-2.873a.5.5 0 1 1 1 .015c-.024 1.618-.129 2.772-.922 3.565-.5.5-1.132.72-1.914.826-.76.102-1.73.102-2.955.102H7.296c-1.225 0-2.196 0-2.955-.102-.782-.105-1.415-.327-1.913-.826-.5-.499-.72-1.131-.826-1.913-.102-.76-.102-1.73-.102-2.955V6.629c0-1.225 0-2.196.102-2.955.105-.782.327-1.414.826-1.913.498-.5 1.131-.72 1.913-.826C5.1.833 6.07.833 7.296.833Zm4.78 3.864a1.81 1.81 0 0 1 2.56 2.56l-3.17 3.171c-.172.173-.29.29-.42.391-.153.12-.32.223-.496.307-.15.071-.306.123-.538.2l-1.389.463a.854.854 0 0 1-1.08-1.08l.455-1.36.009-.028c.077-.232.13-.388.2-.538.084-.176.187-.342.307-.496.102-.13.219-.247.391-.42l.021-.02 3.15-3.15Zm1.853.707a.81.81 0 0 0-1.146 0l-.122.121a1.684 1.684 0 0 0 .43.717 1.702 1.702 0 0 0 .717.43l.121-.121a.81.81 0 0 0 0-1.147Zm-.885 2.032a2.721 2.721 0 0 1-1.146-1.147L9.633 8.553c-.2.201-.271.273-.33.35-.076.096-.14.2-.193.31a4.183 4.183 0 0 0-.164.452l-.267.***********-.267c.27-.09.365-.122.452-.164.11-.053.215-.117.311-.192.077-.06.148-.13.349-.33l2.264-2.265ZM4.834 6a.5.5 0 0 1 .5-.5h4.333a.5.5 0 1 1 0 1H5.333a.5.5 0 0 1-.5-.5Zm0 2.667a.5.5 0 0 1 .5-.5H7a.5.5 0 1 1 0 1H5.333a.5.5 0 0 1-.5-.5Zm0 2.666a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5Z"
      clipRule="evenodd"
    />
    <Path
      fill="url(#a)"
      fillRule="evenodd"
      d="M7.296.833h1.408c1.225 0 2.196 0 2.955.102.782.105 1.415.327 1.913.826a.5.5 0 1 1-.707.707c-.282-.282-.668-.451-1.339-.542-.685-.092-1.588-.093-2.86-.093H7.334c-1.27 0-2.174.001-2.86.093-.67.09-1.056.26-1.338.542-.283.282-.452.668-.542 1.34-.092.684-.093 1.587-.093 2.859v2.666c0 1.271.001 2.175.093 **********.26 1.057.542 1.339.282.282.668.451 1.339.541.685.093 1.588.094 2.86.094h1.333c1.27 0 2.174-.001 2.86-.094.67-.09 1.056-.259 1.338-.541.464-.464.605-1.185.63-2.873a.5.5 0 1 1 1 .015c-.024 1.618-.129 2.772-.922 3.565-.5.5-1.132.72-1.914.826-.76.102-1.73.102-2.955.102H7.296c-1.225 0-2.196 0-2.955-.102-.782-.105-1.415-.327-1.913-.826-.5-.499-.72-1.131-.826-1.913-.102-.76-.102-1.73-.102-2.955V6.629c0-1.225 0-2.196.102-2.955.105-.782.327-1.414.826-1.913.498-.5 1.131-.72 1.913-.826C5.1.833 6.07.833 7.296.833Zm4.78 3.864a1.81 1.81 0 0 1 2.56 2.56l-3.17 3.171c-.172.173-.29.29-.42.391-.153.12-.32.223-.496.307-.15.071-.306.123-.538.2l-1.389.463a.854.854 0 0 1-1.08-1.08l.455-1.36.009-.028c.077-.232.13-.388.2-.538.084-.176.187-.342.307-.496.102-.13.219-.247.391-.42l.021-.02 3.15-3.15Zm1.853.707a.81.81 0 0 0-1.146 0l-.122.121a1.684 1.684 0 0 0 .43.717 1.702 1.702 0 0 0 .717.43l.121-.121a.81.81 0 0 0 0-1.147Zm-.885 2.032a2.721 2.721 0 0 1-1.146-1.147L9.633 8.553c-.2.201-.271.273-.33.35-.076.096-.14.2-.193.31a4.183 4.183 0 0 0-.164.452l-.267.***********-.267c.27-.09.365-.122.452-.164.11-.053.215-.117.311-.192.077-.06.148-.13.349-.33l2.264-2.265ZM4.834 6a.5.5 0 0 1 .5-.5h4.333a.5.5 0 1 1 0 1H5.333a.5.5 0 0 1-.5-.5Zm0 2.667a.5.5 0 0 1 .5-.5H7a.5.5 0 1 1 0 1H5.333a.5.5 0 0 1-.5-.5Zm0 2.666a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 0 1h-1a.5.5 0 0 1-.5-.5Z"
      clipRule="evenodd"
    />
    <Defs>
      <LinearGradient
        id="a"
        x1={2}
        x2={14}
        y1={2.5}
        y2={13.5}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#51AFE6" />
        <Stop offset={0.577} stopColor="#355FF2" />
        <Stop offset={1} stopColor="#11287A" />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default DocumentNotes;
