import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';
const IconCabs = (
  props: React.JSX.IntrinsicAttributes &
    React.JSX.IntrinsicClassAttributes<React.Component<SvgProps, any, any>> &
    <PERSON><PERSON><PERSON><SvgProps>,
) => (
  <Svg width={28} height={28} fill="none" {...props}>
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.66}
      d="M8.437 18.51c0 1.007-.755 2.291-1.72 2.85-.967.557-1.721.143-1.721-.864 0-1.008.754-2.293 1.72-2.85.966-.558 1.72-.145 1.72.863Z"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.66}
      d="M9.752 8.313c-1.515 1.443-2.29 4.108-2.874 4.783-1.253.881-2.06 1.616-2.669 2.159-.61.543-.72 1.241-.7 1.522a42.318 42.318 0 0 0 0 1.729c.017.56.583 1.108.864 1.312 1.006.612 3.285 1.98 4.355 2.556 1.338.72 2.39.718 3.251.607.073-1.607.978-2.349 1.438-2.705 1.703-1.343 2.23.075 2.28.952l5.616-3.6c-.167-.618-.11-2.175 1.453-3.457 1.563-1.283 2.108.046 2.185.87.074-.063.474-.269.278-3.856-.064-1.184.01-1.485-.278-2.93-.574-2.255-3.217-2.822-4.06-3.147-1.468-.565-2.106-.72-2.981-.526-4.426.978-6.976 2.458-8.158 3.731Z"
    />
    <Path
      fill="#008CFF"
      d="M11.498 22.646c.132-1.605.077-2.288.707-3.048 1.38-1.665 2.802-1.928 3.51-3.022.478-.739.877-2.696 1.2-3.46 1.259-2.98 2.544-3.54 4.176-4.47 2.612-1.49 3.02-2.345 3.597 0 .462 1.875.275 2.808.275 5.506v.852-.008c-.005-.764-1.326-.786-1.896-.297-1.31 1.123-1.201 1.306-1.777 2.62l-5.81 3.683c-.033-.754-.49-1.466-.844-1.285-1.132.58-1.985 1.981-2.642 2.89l-.496.04Z"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.66}
      d="M7.964 13.837c-.346-.33-.787-1-.705-1.164.234-.664 1.38-2.953 1.605-3.253.179-.24.06.174 1.162.974 1.181.856 4.181 1.232 4.81 1.498.503.212 1.106.02.596 1.325-.32.816-.684 2.483-.886 2.808-.202.324-2.434-.224-2.692-.297-1.984-.318-3.321-1.346-3.89-1.89ZM15.372 21.174c0 .992-.696 2.197-1.555 2.693-.858.495-1.555.094-1.555-.898 0-.991.697-2.197 1.555-2.693.859-.495 1.555-.093 1.555.898ZM24.775 15.83c0 1.015-.712 2.248-1.59 2.756-.88.507-1.591.096-1.591-.919 0-1.014.712-2.248 1.59-2.755.879-.507 1.59-.096 1.59.919ZM13.348 4.814l3.46-2.047a.734.734 0 0 1 .35-.078c.145 0 .272.032.353.079l3.535 2.04c.044.026.056.045.056.045l.007.014V6.9a.215.215 0 0 1-.032.091.219.219 0 0 1-.063.074L17.298 9.17a1.842 1.842 0 0 1-1.83-.008l-2.72-1.57a.636.636 0 0 1-.211-.23.636.636 0 0 1-.094-.299V6.4c0-.652.344-1.254.905-1.586Z"
    />
    <Path
      stroke="#20364B"
      strokeWidth={0.66}
      d="m12.81 5.396 3.312 1.932c.185.108.414.11.6.004l4.212-2.387"
    />
    <Path stroke="#20364B" strokeWidth={0.66} d="m15.066 3.907 3.46 2.256V8.27" />
    <Path
      fill="#008CFF"
      stroke="#20364B"
      strokeLinecap="round"
      strokeWidth={0.66}
      d="M16.57 8.558v-.715c0-.198.108-.38.281-.476l3.277-1.808c.362-.2.805.062.805.476v.715c0 .198-.107.38-.28.476l-3.277 1.807a.543.543 0 0 1-.806-.475Z"
    />
  </Svg>
);
export default IconCabs;
