//TODO: Fix the ts-ignore and remove it
// @ts-nocheck
import * as React from 'react';
import Svg, { G, Path, Defs, ClipPath, Rect } from 'react-native-svg';

export default function HotelIcon(props) {
  return (
    <Svg width={13} height={13} viewBox="0 0 13 13" fill="none" {...props}>
      <G clipPath="url(#clip0_189_104216)">
        <G clipPath="url(#clip1_189_104216)">
          <Path
            d="M2.11163 3.70635C2.11163 3.69697 2.11788 3.6928 2.12622 3.69749L6.09705 5.85374C6.10538 5.85791 6.11267 5.86937 6.11267 5.87978V11.8241C6.11267 12.1412 5.88872 12.2683 5.61424 12.1074L2.47934 10.2709C2.32934 10.1824 2.20747 9.97301 2.20434 9.79801L2.11163 3.70635Z"
            fill="white"
            stroke="#20364B"
            strokeWidth={0.365104}
          />
          <Path
            d="M9.95205 2.93437C9.95205 2.80781 9.85205 2.77135 9.74476 2.85833L6.20831 5.72916C6.16758 5.76316 6.13428 5.80516 6.11044 5.85255C6.08661 5.89994 6.07277 5.95172 6.06976 6.00469V12.0953C6.06976 12.2198 6.16716 12.2578 6.27289 12.175L9.41351 9.71666C9.72549 9.47239 9.95205 9.02916 9.95205 8.66354V2.93437Z"
            fill="#20364B"
          />
          <Path
            d="M3.69074 8.79038L2.84591 8.30262C2.7505 8.24754 2.67316 8.29219 2.67316 8.40236V9.37788C2.67316 9.48805 2.7505 9.62201 2.84591 9.6771L3.69074 10.1649C3.78615 10.2199 3.8635 10.1753 3.8635 10.0651V9.0896C3.8635 8.97943 3.78615 8.84547 3.69074 8.79038Z"
            fill="#20364B"
          />
          <Path
            d="M3.69123 7.05576L2.8464 6.568C2.75099 6.51292 2.67365 6.55757 2.67365 6.66774V7.64326C2.67365 7.75343 2.75099 7.8874 2.8464 7.94248L3.69123 8.43024C3.78664 8.48533 3.86399 8.44067 3.86399 8.3305V7.35498C3.86399 7.24481 3.78664 7.11085 3.69123 7.05576Z"
            fill="#20364B"
          />
          <Path
            d="M3.69123 5.32041L2.8464 4.83265C2.75099 4.77757 2.67365 4.82222 2.67365 4.93239V5.90791C2.67365 6.01808 2.75099 6.15204 2.8464 6.20713L3.69123 6.69489C3.78664 6.74997 3.86399 6.70532 3.86399 6.59515V5.61963C3.86399 5.50946 3.78664 5.37549 3.69123 5.32041Z"
            fill="#9979D2"
          />
          <Path
            d="M5.32087 9.73862L4.47604 9.25086C4.38063 9.19578 4.30328 9.24043 4.30328 9.3506V10.3261C4.30328 10.4363 4.38063 10.5703 4.47604 10.6253L5.32087 11.1131C5.41628 11.1682 5.49362 11.1235 5.49362 11.0134V10.0378C5.49362 9.92767 5.41628 9.79371 5.32087 9.73862Z"
            fill="#20364B"
          />
          <Path
            d="M5.32087 8.00254L4.47604 7.51478C4.38063 7.45969 4.30328 7.50435 4.30328 7.61452V8.59004C4.30328 8.70021 4.38063 8.83417 4.47604 8.88926L5.32087 9.37702C5.41628 9.4321 5.49362 9.38745 5.49362 9.27728V8.30176C5.49362 8.19159 5.41628 8.05762 5.32087 8.00254Z"
            fill="#20364B"
          />
          <Path
            d="M5.32087 6.26816L4.47604 5.7804C4.38063 5.72532 4.30328 5.76997 4.30328 5.88014V6.85566C4.30328 6.96583 4.38063 7.0998 4.47604 7.15488L5.32087 7.64264C5.41628 7.69773 5.49362 7.65307 5.49362 7.5429V6.56738C5.49362 6.45721 5.41628 6.32325 5.32087 6.26816Z"
            fill="#20364B"
          />
          <Path
            d="M2.24318 3.66803C2.15828 3.62116 2.1463 3.57949 2.14526 3.57064C2.14526 3.56751 2.1411 3.53783 2.20255 3.49199L5.45828 1.05553C5.58328 0.961784 5.7812 0.900846 6.00151 0.891992C6.2213 0.883138 6.43745 0.926888 6.59214 1.01439L9.5713 2.6972C9.70776 2.77428 9.75255 2.85501 9.76037 2.90658C9.76714 2.95137 9.75359 3.02064 9.65516 3.10033L6.89057 5.34147C6.69227 5.50229 6.45033 5.6 6.19596 5.622C5.94159 5.644 5.68647 5.58928 5.46349 5.46491L2.2437 3.66803H2.24318Z"
            fill="white"
            stroke="#20364B"
            strokeWidth={0.365104}
          />
          <Path
            d="M7.94412 11.1807C7.84387 11.123 7.76136 11.0389 7.70558 10.9375C7.64605 10.8425 7.61164 10.7339 7.60558 10.6219L7.59308 6.1625L10.4498 7.6974C10.4842 7.71591 10.5224 7.7259 10.5614 7.72653C10.6004 7.72717 10.6389 7.71843 10.6738 7.70104L12.6269 6.73073L12.5868 10.751C12.5798 10.7932 12.5583 10.8315 12.5259 10.8594L12.5243 10.8609L10.9644 12.0521C10.7424 12.2217 10.4736 12.3189 10.1944 12.3305C9.91524 12.342 9.63929 12.2674 9.40402 12.1167L7.94412 11.1807Z"
            fill="white"
          />
          <Path
            d="M7.60506 6.08594L7.60402 6.08698M7.94412 11.1807C7.84387 11.123 7.76136 11.0389 7.70558 10.9375C7.64605 10.8425 7.61164 10.7339 7.60558 10.6219L7.59308 6.1625L10.4498 7.6974C10.4842 7.71591 10.5224 7.7259 10.5614 7.72653C10.6004 7.72717 10.6389 7.71843 10.6738 7.70104L12.6269 6.73073L12.5868 10.751C12.5798 10.7932 12.5583 10.8315 12.5259 10.8594L12.5243 10.8609L10.9644 12.0521C10.7424 12.2217 10.4736 12.3189 10.1944 12.3305C9.91524 12.342 9.63929 12.2674 9.40402 12.1167L7.94412 11.1807Z"
            stroke="#20364B"
            strokeWidth={0.486979}
            strokeLinejoin="round"
          />
          <Path
            d="M12.5432 7.01219C12.5447 6.90646 12.476 6.85802 12.3859 6.90073L10.2807 7.90229L10.2578 12.0835C10.3531 12.1252 10.4796 12.0981 10.6062 12.0085L12.3317 10.7825C12.3791 10.7468 12.4182 10.7012 12.4464 10.649C12.4747 10.5968 12.4913 10.5391 12.4953 10.4799L12.5432 7.01219Z"
            fill="#9979D2"
          />
          <Path
            d="M12.4871 6.24238L10.6644 5.19004C10.4014 5.03816 9.97486 5.03816 9.71179 5.19004L7.99642 6.1804C7.73336 6.33228 7.73336 6.57853 7.99642 6.7304L9.81915 7.78275C10.0822 7.93463 10.5087 7.93463 10.7718 7.78275L12.4871 6.79238C12.7502 6.6405 12.7502 6.39426 12.4871 6.24238Z"
            fill="white"
            stroke="#20364B"
            strokeWidth={0.275}
          />
        </G>
      </G>
      <Defs>
        <ClipPath id="clip0_189_104216">
          <Rect width={13} height={13} fill="white" />
        </ClipPath>
        <ClipPath id="clip1_189_104216">
          <Rect
            width={12.5}
            height={12.5}
            fill="white"
            transform="translate(1.25 0.625)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
}
