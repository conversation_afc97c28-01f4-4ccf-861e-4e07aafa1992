// @ts-nocheck
import * as React from 'react';
import Svg, { G, Path, Defs, SvgProps } from 'react-native-svg';
/* SVGR has dropped some elements not supported by react-native-svg: filter */
const PlayButton = (props: SvgProps) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={35}
    height={39}
    fill="none"
    style={{ outline: 'none' }}
    {...props}
  >
    <G>
      <Path
        fill="#fff"
        fillRule="evenodd"
        d="M7 7.956v20.236c0 1.543 1.747 2.48 3.093 1.64L26.44 19.716c1.245-.762 1.245-2.52 0-3.301L10.093 6.315C8.747 5.475 7 6.413 7 7.956Z"
        clipRule="evenodd"
      />
    </G>
    <Defs></Defs>
  </Svg>
);
export default PlayButton;
