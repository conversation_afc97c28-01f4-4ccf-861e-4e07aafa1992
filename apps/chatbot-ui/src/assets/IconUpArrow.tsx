import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';
const IconUpArrow = (
  props: React.JSX.IntrinsicAttributes &
    React.JSX.IntrinsicClassAttributes<React.Component<SvgProps, any, any>> &
    <PERSON><PERSON><PERSON><SvgProps>,
) => (
  <Svg width={20} height={20} fill="none" {...props}>
    <Path
      fill="#008CFF"
      d="M10.155 8.78a.139.139 0 0 1 .196 0l2.848 2.847a.694.694 0 1 0 .982-.982l-3.437-3.437a.694.694 0 0 0-.982 0l-3.437 3.437a.694.694 0 0 0 .982.982l2.848-2.848Z"
    />
  </Svg>
);
export default IconUpArrow;
