import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface SoundOnProps {
  width?: number;
  height?: number;
  color?: string;
}

const SoundOn: React.FC<SoundOnProps> = ({
  width = 24,
  height = 24,
  color = '#ffffff',
}) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 28 24" fill="none">
      {/* Speaker Base */}
      <Path
        d="M3 9V15H7L12 20V4L7 9H3Z"
        fill={color}
        stroke={color}
        strokeWidth="1"
        strokeLinejoin="round"
      />

      {/* Sound Wave 1 (Inner) */}
      <Path
        d="M15.54 8.46C16.47 9.39 17 10.67 17 12C17 13.33 16.47 14.61 15.54 15.54"
        fill="none"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
      />

      {/* Sound Wave 2 (Middle) */}
      <Path
        d="M18.07 5.93C19.98 7.84 21 10.35 21 12C21 13.65 19.98 16.16 18.07 18.07"
        fill="none"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
      />

      {/* Sound Wave 3 (Outer) */}
      <Path
        d="M20.61 3.39C23.48 6.26 25 10.01 25 12C25 13.99 23.48 17.74 20.61 20.61"
        fill="none"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        opacity="0.7"
      />
    </Svg>
  );
};

export default SoundOn;
