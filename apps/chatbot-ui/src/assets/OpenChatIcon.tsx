import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

function OpenChatIcon(props: SvgProps) {
    const { height, width } = props;
    return (
        <Svg width={width} height={height} viewBox="0 0 16 17" fill="none" {...props}>
            <Path
                d="M8.61 3.05c.186.002.371.03.544.1a6.856 6.856 0 012.255 1.486 6.859 6.859 0 011.474 2.225c.072.176.102.364.106.554.007.417.011.87.011 1.358 0 1.89-.063 3.231-.127 4.112a1.602 1.602 0 01-1.496 1.497c-.801.06-1.957.118-3.468.118-1.51 0-2.666-.057-3.467-.118a1.602 1.602 0 01-1.496-1.497 56.768 56.768 0 01-.128-4.112c0-1.89.063-3.232.128-4.113a1.602 1.602 0 011.496-1.496c.8-.061 1.957-.119 3.467-.119.243 0 .477.002.701.005z"
                stroke="#4A4A4A"
                strokeWidth={1.05}
                strokeLinejoin="round"
            />
            <Path
                d="M9.183 3.161a6.86 6.86 0 012.226 1.476 6.857 6.857 0 011.476 2.227 45.61 45.61 0 01-1.892-.061 1.84 1.84 0 01-1.75-1.75 45.548 45.548 0 01-.06-1.892z"
                stroke="#4A4A4A"
                strokeWidth={1.05}
                strokeLinejoin="round"
            />
        </Svg>
    );
}

export default OpenChatIcon;
