import * as React from 'react';
import Svg, { Circle, Path, SvgProps } from 'react-native-svg';

const PageErrorIcon = (
  props: React.JSX.IntrinsicAttributes &
    React.JSX.IntrinsicClassAttributes<React.Component<SvgProps, any, any>> &
    <PERSON><PERSON><PERSON><SvgProps>,
) => (
  <Svg width={143} height={168} fill="none" {...props}>
    <Path
      fill="#fff"
      fillRule="evenodd"
      d="M71 32.355s2.255-13.328 9.232-12.278c6.977 1.05-4.616 12.278-4.616 12.278"
      clipRule="evenodd"
    />
    <Path
      stroke="#0C58B4"
      strokeWidth={2}
      d="M71 32.355s2.255-13.328 9.232-12.278c6.977 1.05-4.616 12.278-4.616 12.278"
    />
    <Path
      fill="#fff"
      fillRule="evenodd"
      d="M76 31.98s7.9-9.15 11.828-4.892c3.926 4.257-8.793 6.644-8.793 6.644"
      clipRule="evenodd"
    />
    <Path
      stroke="#0C58B4"
      strokeWidth={2}
      d="M76 31.98s7.9-9.15 11.828-4.892c3.926 4.257-8.793 6.644-8.793 6.644"
    />
    <Path
      fill="#fff"
      fillRule="evenodd"
      stroke="#0C58B4"
      strokeWidth={2.9}
      d="M29.078 155.27A4.078 4.078 0 0 1 25 151.191V74.179C25 49.78 44.78 30 69.18 30c24.399 0 44.179 19.78 44.179 44.18v76.251a3.605 3.605 0 1 1-7.209 0v-3.004a6.578 6.578 0 1 0-13.156 0v11.521a3.546 3.546 0 0 1-7.093 0v-5.132a3.66 3.66 0 1 0-7.32 0v3.681a3.096 3.096 0 0 1-6.194 0v-8.85a4.585 4.585 0 0 0-9.17 0v13.238a4.275 4.275 0 0 1-8.55 0v-7.728a4.063 4.063 0 0 0-8.126 0v2.516a3.92 3.92 0 0 1-7.841 0v-7.636a2.772 2.772 0 0 0-5.544 0v2.154a4.078 4.078 0 0 1-4.078 4.079Z"
      clipRule="evenodd"
    />
    <Circle
      cx={58.5}
      cy={65.5}
      r={7.5}
      fill="#fff"
      stroke="#0C58B4"
      strokeWidth={2}
    />
    <Circle cx={58.5} cy={66.5} r={3.5} fill="#0C58B4" />
    <Circle cx={83} cy={66} r={5} fill="#fff" stroke="#0C58B4" strokeWidth={2} />
    <Circle cx={83} cy={67} r={2} fill="#0C58B4" />
    <Circle
      cx={138.5}
      cy={72.5}
      r={3.5}
      fill="#fff"
      stroke="#BF6DBE"
      strokeWidth={2}
    />
    <Circle
      cx={27.5}
      cy={3.5}
      r={2.5}
      fill="#fff"
      stroke="#5601FF"
      strokeWidth={2}
    />
    <Circle
      cx={2.5}
      cy={2.5}
      r={2.5}
      fill="#BF6DBE"
      transform="matrix(1 0 0 -1 0 99)"
    />
    <Path
      stroke="#0C58B4"
      strokeWidth={2.9}
      d="M58.523 88.538a14.542 14.542 0 0 1 25.489 0l.66 1.2c1.111 2.023-.857 4.393-3.065 3.72-3.88-1.183-8.481-2.454-10.34-2.454-1.857 0-6.46 1.271-10.34 2.454-2.207.673-4.175-1.697-3.064-3.72l.66-1.2Z"
      clipRule="evenodd"
    />
  </Svg>
);
export default PageErrorIcon;
