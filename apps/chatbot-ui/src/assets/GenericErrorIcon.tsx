import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

const GenericErrorIcon = (
  props: React.JSX.IntrinsicAttributes &
    React.JSX.IntrinsicClassAttributes<React.Component<SvgProps, any, any>> &
    <PERSON><PERSON><PERSON><SvgProps>,
) => (
  <Svg width={124} height={187} fill="none" {...props}>
    <Path
      fill="#fff"
      stroke="#0C58B4"
      strokeWidth={2.91}
      d="m92.794 141.432.003-.009a56.84 56.84 0 0 0 .112-.373c1.702-5.671.687-11.772-2.715-16.44l-.002-.003a17.703 17.703 0 0 0-6.406-5.441 17.706 17.706 0 0 0-8.192-1.877l-27.88.386c-3.28.049-6.487.962-9.301 2.647l-.004.002a19.201 19.201 0 0 0-5.745 5.253l-.001.001c-3.554 4.904-4.6 11.175-2.832 16.878l.119.384v.004l.727 2.339v.001c1.475 4.743 2.947 9.484 4.417 14.222 3.558 11.459 14.075 18.964 25.941 18.579l31.76-36.553Zm0 0-.7 2.334m.7-2.334-.7 2.334m0 0a14365.99 14365.99 0 0 1-3.924 13.047v.001m3.923-13.048-3.923 13.048m0 0c-2.564 8.53-8.694 15.279-16.42 18.711m16.42-18.711-16.42 18.711m0 0a28.653 28.653 0 0 1-10.714 2.46l10.714-2.46Z"
    />
    <Path
      fill="#ECE3FF"
      d="m83.513 93.336-42.405.305c-8.685.063-15.758-7.004-15.758-15.783v-9.284c0-3.966 3.199-7.174 7.137-7.166l59.392.13c3.795.01 6.864 3.163 6.864 7.045v9.09c0 8.588-6.802 15.602-15.23 15.663Z"
    />
    <Path
      fill="#ECE3FF"
      d="m64.712 101.217-6.042.057c-8.379.08-15.201-6.757-15.201-15.27 0-8.51 6.822-15.417 15.2-15.424h6.043c8.308 0 15.014 6.82 15.014 15.244 0 8.423-6.706 15.314-15.014 15.393Z"
    />
    <Path
      fill="#fff"
      stroke="#0C58B4"
      strokeWidth={2.91}
      d="m104.484 84.599-.002.004c-4.187 9.855-13.945 17.956-26.626 21.849a57.342 57.342 0 0 1-16.172 2.489h-.004c-20.492.25-37.816-9.917-43.947-23.923l-.003-.007a27.696 27.696 0 0 1-.672-20.82l.002-.003c4.34-11.87 16.601-21.32 32.287-24.54a58.429 58.429 0 0 1 12.335-1.183h.002c21.04.198 38.263 11.316 43.436 25.85l.001.003a27.681 27.681 0 0 1 1.621 9.317 27.921 27.921 0 0 1-2.258 10.964Z"
    />
    <Path
      fill="#fff"
      stroke="#0C58B4"
      strokeWidth={2.746}
      d="M39.459 85.051c-5.406.028-9.82-4.377-9.82-9.864v-4.12c0-3.213 2.584-5.797 5.752-5.792l53.205.079h.002c3.011 0 5.504 2.513 5.504 5.672v4.042c0 5.384-4.262 9.726-9.474 9.754l-45.17.23ZM104.346 61.55c4.657.666 8.237 4.671 8.237 9.513V79.3c0 4.84-3.58 8.846-8.237 9.512V61.55ZM19.225 61.55c-4.657.666-8.237 4.671-8.237 9.513V79.3c0 4.84 3.58 8.846 8.237 9.512V61.55Z"
    />
    <Path
      stroke="#BF6DBE"
      strokeLinecap="round"
      strokeWidth={2.12}
      d="M43.282 76.023h25.541M76.117 76.023h1.825"
    />
    <Path
      stroke="#0C58B4"
      strokeLinecap="round"
      strokeWidth={2.12}
      d="m46.172 25.294-3.837-6.618-1.67 4.634-4.652-7.494"
    />
    <Path
      stroke="#5601FF"
      strokeLinecap="round"
      strokeWidth={2.291}
      d="m58.44 24.17-.936-12.93-5.962 5.643L50.015 2"
    />
    <Path
      stroke="#BF6DBE"
      strokeLinecap="round"
      strokeWidth={2.12}
      d="M71.209 24.938s-6.13-14.214 3.344-11.372c10.215 3.065 10.588 14.214 4.458 9.665-6.13-4.548-7.626-19.268 1.672-15.92 9.474 3.413 8.917 12.509 6.13 10.234-2.786-2.274-10.032-14.215 3.901-12.51"
    />
  </Svg>
);
export default GenericErrorIcon;
