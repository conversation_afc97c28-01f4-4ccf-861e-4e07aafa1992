import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export const UnBookmark = (props: SvgProps) => (
  <Svg width={33} height={32} fill="none" {...props}>
    <Path
      fill="#000"
      stroke="#fff"
      strokeWidth={0.345}
      d="M24.597 9.023V27.14l-7.8-3.343-.068-.029-.067.03-7.8 3.342V9.023c0-.585.206-1.082.625-1.5a2.046 2.046 0 0 1 1.5-.625h11.485a2.04 2.04 0 0 1 1.501.625c.418.418.625.915.624 1.5ZM10.814 23.897v.262l.24-.104 5.675-2.44 5.675 2.44.24.104V8.85h-11.83v15.047Z"
    />
    <Path
      fill="#000"
      stroke="#fff"
      strokeWidth={1.149}
      d="m27.869 4.765 1.941 1.942L6.372 30.145 4.43 28.204z"
    />
  </Svg>
);
