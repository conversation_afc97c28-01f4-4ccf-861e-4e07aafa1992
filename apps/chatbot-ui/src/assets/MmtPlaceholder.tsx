import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

export const MmtPlaceholder = (props: SvgProps) => (
  <Svg width={42} height={42} fill="none" {...props}>
    <Path
      fill="#757575"
      d="M37.291 11.961c-.653.73-4.88 8.653-5.494 9.922-.845 1.538-1.652 2.038-2.267 2.038-1.383 0-1.114-2.27-.884-3.038 1.307-5.115 2.728-6.999 2.92-7.345 1-1.384.039-3.038-1.383-3.038-1.729 0-2.382 1.077-2.382 2.846-.5 1.384-1.499 3.807-1.652 4.037-.615 1.308-3.727 5.884-4.803 6.922-.23.231-.538.231-.538-.077.115-3.153 1.844-9.037 1.844-9.037.039-.192.077-.384.077-.538 0-.923-.423-1.538-1.383-1.615-.96-.077-1.46.385-2.344 1.423-1.921 2.384-4.227 5.999-5.34 7.652-.116.193-.347.116-.347-.115 0-.73 2.344-8.998 2.344-8.998.192-.885-.384-1.577-1.383-1.577-1.114 0-3.458 3.192-7.108 8.23-.116.153-.385.076-.308-.078.846-2.268 1.652-4.999 1.652-6.422 0-1.345-.576-2.038-1.729-2.038-2.612 0-5.033 2.923-5.033 4.307 0 .77.461 1.5 1.23 1.5 1.152 0 1.421-2.153 2.267-2.153.153 0 .153.192.153.23-.153 1.538-2.036 7.845-3.035 10.844-.5 1.539-.192 2.615 1.076 2.615.922 0 1.806-.884 2.69-2.23 1.728-2.615 4.303-6.691 4.572-6.691.076 0 .115.038.115.115-.346 1.885-1.806 7.691 1.076 7.691.538 0 1.114-.154 1.729-.769.691-.73 3.381-4.345 4.995-7.23.115-.192.23-.038.192.116-.307 1.5-1.383 5.691-1.383 6.653 0 2.23 1.613 2.23 1.69 2.23 3.113 0 6.225-5.499 6.763-6.268-.96 3.345.615 6.653 4.034 5.307-.96 2.384-2.613 5.999-2.613 9.69 0 2.27 1.268 3.384 2.536 3.384.923 0 1.768-.538 1.768-1.461-1.307-2.461-.115-8.921 3.189-16.382 1.575-1.769 5.456-7.806 5.456-9.69 0-1.077-.615-1.577-1.422-1.577-.691-.115-1.075.115-1.537.615"
    />
  </Svg>
);
