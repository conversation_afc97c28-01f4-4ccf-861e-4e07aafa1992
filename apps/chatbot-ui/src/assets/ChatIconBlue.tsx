import React from 'react';
import Svg, { Rect, Path } from 'react-native-svg';

export function ChatIconBlue() {
  return (
    <Svg width={36} height={36} viewBox="0 0 36 36">
      <Rect width={36} height={36} rx={18} fill="#EAF5FF" />
      <Path
        d="M18 28C23.5228 28 28 23.5228 28 18C28 12.4772 23.5228 8 18 8C12.4772 8 8 12.4772 8 18C8 19.5997 8.37562 21.1116 9.04346 22.4525C9.22094 22.8088 9.28001 23.2161 9.17712 23.6006L8.58151 25.8267C8.32295 26.793 9.20701 27.677 10.1733 27.4185L12.3994 26.8229C12.7839 26.72 13.1912 26.7791 13.5475 26.9565C14.8884 27.6244 16.4003 28 18 28Z"
        stroke="#008CFF"
        strokeWidth={1.5}
        fill="none"
      />
      <Path
        d="M14 16.5H22"
        stroke="#008CFF"
        strokeWidth={1.5}
        strokeLinecap="round"
      />
      <Path
        d="M14 20H19.5"
        stroke="#008CFF"
        strokeWidth={1.5}
        strokeLinecap="round"
      />
    </Svg>
  );
}
