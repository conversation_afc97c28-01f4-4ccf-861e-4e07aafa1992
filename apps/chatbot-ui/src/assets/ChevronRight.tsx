import * as React from 'react';
import Svg, { Path } from 'react-native-svg';
import { isPlatformWeb } from '../utils/getPlatform';

export interface ChevronRightProps {
  width?: number;
  height?: number;
  color?: string;
}

export const ChevronRight = ({
  width = 20,
  height = 21,
  color = '#008CFF',
}: ChevronRightProps) => {
  const isWebPlatform = isPlatformWeb();
  return (
    <Svg
      width={width}
      height={height}
      viewBox={`0 0 20 21`}
      fill="none"
      style={
        isWebPlatform
          ? ({
              outline: 'none',
              border: 'none',
              pointerEvents: 'none',
            } as any)
          : undefined
      }
      focusable={false}
      {...(isWebPlatform && {
        'data-focusable': false,
        tabIndex: -1,
      })}
    >
      <Path
        fill={color}
        d="M11.161 10.618a.167.167 0 0 0 0-.236L7.744 6.965a.833.833 0 1 1 1.179-1.179l4.124 4.125a.833.833 0 0 1 0 1.178l-4.125 4.125a.833.833 0 0 1-1.179-1.178l3.418-3.418Z"
      />
    </Svg>
  );
};

export default ChevronRight;
