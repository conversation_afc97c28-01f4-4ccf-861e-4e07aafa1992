import * as React from 'react';
import { SvgProps } from 'react-native-svg';
import { StyleSheet, View } from 'react-native';
import { ThumbsUpIcon } from './ThumbUpIcon';

const styles = StyleSheet.create({
  rotated: {
    transform: [
      {
        rotate: '180deg',
      },
    ],
  },
});


export const ThumbsDownIcon = (props: SvgProps) => (
  <View style={styles.rotated}>
    <ThumbsUpIcon {...props} />
  </View>
);
