import * as React from 'react';
import Svg, { G, Path, SvgProps } from 'react-native-svg';
const IconDuration = (
  props: React.JSX.IntrinsicAttributes &
    React.JSX.IntrinsicClassAttributes<React.Component<SvgProps, any, any>> &
    <PERSON><PERSON>ly<SvgProps>,
) => (
  <Svg width={16} height={16} fill="none" {...props}>
    {/*@ts-ignore*/}
    <G stroke="#4A4A4A" strokeLinecap="round" strokeLinejoin="round">
      <React.Fragment>
        <Path d="M1.7 8a6.3 6.3 0 1 0 12.6 0A6.3 6.3 0 0 0 1.7 8ZM8 8V5.75M8 8l2.812 2.813" />
      </React.Fragment>
    </G>
  </Svg>
);
export default IconDuration;
