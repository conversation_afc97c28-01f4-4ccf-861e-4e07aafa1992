// @ts-nocheck
import * as React from 'react';
import Svg, { Path, Defs, LinearGradient, Stop, SvgProps } from 'react-native-svg';
const WhatsAppIcon = (props: SvgProps) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={36}
    height={36}
    fill="none"
    {...props}
  >
    <Path
      fill="url(#a)"
      d="M4.599 17.82c0 2.352.619 4.649 1.797 6.674l-1.91 6.917 7.135-1.856a13.536 13.536 0 0 0 6.431 1.625h.006c7.418 0 13.455-5.99 13.459-13.35a13.215 13.215 0 0 0-3.939-9.445 13.427 13.427 0 0 0-9.52-3.915c-7.418 0-13.456 5.989-13.459 13.35"
    />
    <Path
      fill="url(#b)"
      d="M4.117 17.815c0 2.437.641 4.816 1.86 6.913L4 31.895l7.39-1.923a14.02 14.02 0 0 0 6.663 1.684h.006c7.683 0 13.938-6.204 13.941-13.83a13.692 13.692 0 0 0-4.08-9.783 13.91 13.91 0 0 0-9.861-4.055c-7.685 0-13.939 6.203-13.942 13.828Zm4.401 6.552-.276-.434a11.398 11.398 0 0 1-1.771-6.117c.002-6.338 5.2-11.494 11.592-11.494a11.559 11.559 0 0 1 8.192 3.371 11.38 11.38 0 0 1 3.391 8.132c-.003 6.338-5.2 11.494-11.587 11.494h-.005a11.644 11.644 0 0 1-5.898-1.602l-.423-.25-4.386 1.142 1.171-4.242Z"
    />
    <Path
      fill="#fff"
      d="M14.574 12.034c-.26-.576-.535-.587-.784-.597-.203-.01-.435-.008-.667-.008-.233 0-.61.086-.93.432-.319.346-1.218 1.182-1.218 2.883 0 1.7 1.248 3.344 1.422 3.574.174.23 2.41 3.832 5.95 5.218 2.943 1.151 3.542.922 4.18.864.64-.057 2.062-.836 2.352-1.643.29-.807.29-1.498.203-1.643-.087-.144-.32-.23-.668-.403-.348-.173-2.06-1.01-2.38-1.125-.32-.115-.551-.172-.784.174-.232.345-.9 1.123-1.102 1.354-.204.231-.407.26-.755.087-.349-.174-1.47-.538-2.802-1.716-1.035-.916-1.734-2.047-1.938-2.393-.203-.346-.021-.533.153-.705.157-.155.349-.404.523-.606.174-.202.232-.346.348-.576.116-.23.058-.433-.029-.605-.087-.173-.764-1.883-1.074-2.566Z"
    />
    <Defs>
      <LinearGradient
        id="a"
        x1={1356}
        x2={1356}
        y1={2698.6}
        y2={4.47}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#1FAF38" />
        <Stop offset={1} stopColor="#60D669" />
      </LinearGradient>
      <LinearGradient
        id="b"
        x1={1404}
        x2={1404}
        y1={2794.73}
        y2={3.987}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#F9F9F9" />
        <Stop offset={1} stopColor="#fff" />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default WhatsAppIcon;
