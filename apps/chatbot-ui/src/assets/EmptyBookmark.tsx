// @ts-nocheck
import * as React from 'react';
import Svg, { El<PERSON><PERSON>, Mask, Path, Circle, SvgProps } from 'react-native-svg';
export const EmptyBookmark = (props: SvgProps) => (
  <Svg width={147} height={150} fill="none" {...props}>
    <Ellipse cx={74.032} cy={141.247} fill="#CDE3FF" rx={54.048} ry={8.478} />
    <Mask id="a" fill="#fff">
      <Path d="M108.474 120.386v-18.521c0-15.907 0-23.86-4.889-28.803-4.889-4.941-12.757-4.941-28.494-4.941-15.736 0-23.604 0-28.493 4.941-4.889 4.942-4.889 12.896-4.889 28.803v18.521c0 11.486 0 17.229 2.723 19.738a8.306 8.306 0 0 0 4.684 2.149c3.661.419 7.937-3.363 16.488-10.927 3.78-3.343 5.67-5.014 7.856-5.455a8.254 8.254 0 0 1 3.262 0c2.187.441 4.077 2.112 7.857 5.455 8.551 7.564 12.827 11.346 16.488 10.927a8.307 8.307 0 0 0 4.684-2.149c2.723-2.509 2.723-8.252 2.723-19.738Z" />
    </Mask>
    <Path
      fill="#fff"
      d="M108.474 120.386v-18.521c0-15.907 0-23.86-4.889-28.803-4.889-4.941-12.757-4.941-28.494-4.941-15.736 0-23.604 0-28.493 4.941-4.889 4.942-4.889 12.896-4.889 28.803v18.521c0 11.486 0 17.229 2.723 19.738a8.306 8.306 0 0 0 4.684 2.149c3.661.419 7.937-3.363 16.488-10.927 3.78-3.343 5.67-5.014 7.856-5.455a8.254 8.254 0 0 1 3.262 0c2.187.441 4.077 2.112 7.857 5.455 8.551 7.564 12.827 11.346 16.488 10.927a8.307 8.307 0 0 0 4.684-2.149c2.723-2.509 2.723-8.252 2.723-19.738Z"
    />
    <Path
      fill="#0C58B4"
      d="m84.579 131.346-1.928 2.18 1.928-2.18Zm-7.857-5.455-.574 2.853.574-2.853Zm-11.118 5.455 1.928 2.18-1.928-2.18Zm7.856-5.455.575 2.853-.575-2.853Zm-29.028 14.233-1.972 2.14 1.972-2.14Zm4.684 2.149.33-2.891-.33 2.891Zm56.635-2.149 1.972 2.14-1.972-2.14Zm-4.684 2.149-.331-2.891.331 2.891Zm4.497-40.408v18.521h5.82v-18.521h-5.82Zm-60.945 18.521v-18.521h-5.82v18.521h5.82Zm41.888 8.781c-1.85-1.636-3.383-2.996-4.736-3.986-1.391-1.017-2.804-1.806-4.474-2.142l-1.15 5.705c.517.104 1.143.371 2.189 1.135 1.083.793 2.384 1.939 4.315 3.647l3.856-4.359Zm-18.975 4.359c1.93-1.708 3.231-2.854 4.315-3.647 1.046-.764 1.671-1.031 2.188-1.135l-1.15-5.705c-1.67.336-3.082 1.125-4.473 2.142-1.353.99-2.887 2.35-4.736 3.986l3.856 4.359Zm9.765-10.487a11.141 11.141 0 0 0-4.411 0l1.149 5.705a5.33 5.33 0 0 1 2.113 0l1.149-5.705Zm-38.498-2.653c0 5.673-.004 10.167.357 13.544.353 3.299 1.111 6.313 3.304 8.334l3.944-4.28c-.53-.488-1.133-1.601-1.461-4.672-.32-2.993-.324-7.113-.324-12.926h-5.82Zm24.877 8.781c-4.329 3.829-7.396 6.536-9.832 8.261-2.5 1.771-3.707 2.033-4.397 1.954l-.662 5.781c2.971.34 5.732-1.08 8.423-2.986 2.755-1.951 6.101-4.916 10.324-8.651l-3.856-4.359ZM42.46 142.264a11.216 11.216 0 0 0 6.325 2.899l.662-5.781a5.4 5.4 0 0 1-3.043-1.398l-3.944 4.28Zm63.104-21.878c0 5.813-.004 9.933-.324 12.926-.328 3.071-.931 4.184-1.461 4.672l3.944 4.28c2.193-2.021 2.951-5.035 3.304-8.334.361-3.377.357-7.871.357-13.544h-5.82Zm-22.913 13.14c4.223 3.735 7.569 6.7 10.324 8.651 2.69 1.906 5.452 3.326 8.423 2.986l-.662-5.781c-.69.079-1.897-.183-4.398-1.954-2.435-1.725-5.503-4.432-9.831-8.261l-3.856 4.359Zm21.128 4.458a5.4 5.4 0 0 1-3.043 1.398l.662 5.781a11.217 11.217 0 0 0 6.325-2.899l-3.944-4.28ZM75.091 71.03c7.952 0 13.62.007 17.923.592 4.217.573 6.688 1.652 8.503 3.487l4.137-4.093c-3.074-3.107-6.982-4.499-11.856-5.16-4.787-.651-10.921-.645-18.707-.645v5.82Zm36.293 30.835c0-7.872.006-14.066-.637-18.897-.653-4.912-2.024-8.85-5.093-11.952l-4.137 4.093c1.819 1.839 2.893 4.349 3.461 8.626.58 4.358.586 10.095.586 18.13h5.82ZM75.091 65.211c-7.785 0-13.92-.006-18.706.644-4.874.662-8.782 2.054-11.856 5.161l4.137 4.093c1.815-1.835 4.286-2.914 8.503-3.487 4.303-.585 9.971-.591 17.922-.591v-5.82ZM44.62 101.865c0-8.035.006-13.772.585-18.13.57-4.277 1.643-6.787 3.462-8.626l-4.137-4.093c-3.07 3.103-4.44 7.04-5.093 11.952-.643 4.831-.637 11.025-.637 18.897h5.82Z"
      mask="url(#a)"
    />
    <Path
      stroke="#0C58B4"
      strokeLinecap="round"
      strokeWidth={2.91}
      d="M86.219 82.96H63.964"
    />
    <Path
      stroke="#617CDE"
      strokeLinecap="round"
      strokeWidth={2.291}
      d="m130.113 33.283-2.026 10.402M123.898 37.47l10.402 2.027"
    />
    <Path
      stroke="#5601FF"
      strokeLinecap="round"
      strokeWidth={2.291}
      d="m28.795 2.418 3.235 16.605M38.713 9.103l-16.605 3.235"
    />
    <Circle
      cx={142.149}
      cy={55.169}
      r={3.284}
      stroke="#BF6DBE"
      strokeLinecap="round"
      strokeWidth={2.12}
      transform="rotate(11.024 142.149 55.169)"
    />
    <Circle
      cx={8.822}
      cy={8.822}
      r={8.822}
      stroke="#0C58B4"
      strokeLinecap="round"
      strokeWidth={2.12}
      transform="scale(-1 1) rotate(11.024 -219.312 -74.156)"
    />
  </Svg>
);
