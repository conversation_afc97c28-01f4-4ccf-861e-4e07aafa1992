// @ts-nocheck
import * as React from 'react';
import Svg, { <PERSON>lipP<PERSON>, Defs, G, Path, SvgProps } from 'react-native-svg';

export const ItineraryIcon = (props: SvgProps) => (
  <Svg width={22} height={22} viewBox={'0 0 22 22'} fill="none" {...props}>
    <G stroke="#000" clipPath="url(#a)">
      <Path d="M5 12.556a1.213 1.213 0 1 0 0-2.427 1.213 1.213 0 0 0 0 2.427Z" />
      <Path d="M1.743 10.634c.766-3.367 5.755-3.363 6.518.004.447 1.976-.782 3.648-1.86 4.683a2.02 2.02 0 0 1-2.803 0c-1.073-1.035-2.302-2.711-1.855-4.687Z" />
      <Path
        strokeLinecap="round"
        d="M4.668 6.333V4.766A2.667 2.667 0 0 1 7.335 2.1h7v3.233c0 .368.298.667.666.667h3.667v10.666a2.667 2.667 0 0 1-2.667 2.667H6.335c-.92 0-1.667-.746-1.667-1.667v0"
      />
      <Path d="m14.334 2 4.333 4" />
      <Path strokeLinecap="round" d="M10.334 9.333h4.667M10.334 12h4.667" />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M.334.333h21.333v21.333H.334z" />
      </ClipPath>
    </Defs>
  </Svg>
);
