/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react';
import Svg, {
  Rect,
  Path,
  Defs,
  LinearGradient,
  Stop,
  SvgProps,
} from 'react-native-svg';

function PlaceholderIcon(props: SvgProps) {
  return (
    <Svg width={32} height={32} viewBox="0 0 32 32" fill="none" {...props}>
      <Rect x={0.25} y={0.25} width={31.5} height={31.5} rx={5.75} fill="#EAF1FF" />
      <Rect
        x={0.25}
        y={0.25}
        width={31.5}
        height={31.5}
        rx={5.75}
        stroke="#fff"
        strokeWidth={0.5}
      />
      <Path
        d="M20.43 13.987c-.217.244-1.626 2.884-1.83 3.307-.283.513-.552.68-.757.68-.46 0-.37-.757-.294-1.013.435-1.705.91-2.333.973-2.448.333-.462.013-1.013-.46-1.013-.577 0-.795.359-.795.949-.166.461-.5 1.269-.55 1.345-.205.436-1.243 1.962-1.602 2.308-.076.077-.179.077-.179-.026.039-1.051.615-3.012.615-3.012.013-.064.025-.128.025-.18 0-.307-.14-.512-.46-.538-.32-.026-.487.128-.782.474-.64.795-1.409 2-1.78 2.551-.039.064-.115.039-.115-.038 0-.244.78-3 .78-3 .065-.295-.127-.525-.46-.525-.372 0-1.153 1.064-2.37 2.743-.038.051-.128.025-.102-.026.282-.756.55-1.666.55-2.14 0-.45-.191-.68-.576-.68-.87 0-1.677.974-1.677 1.436 0 .256.153.5.41.5.384 0 .473-.718.755-.718.051 0 .051.064.051.077-.051.512-.679 2.615-1.012 3.614-.166.513-.064.872.359.872.307 0 .602-.295.897-.743.576-.872 1.434-2.23 1.524-2.23.025 0 .038.012.038.038-.115.628-.602 2.563.359 2.563.179 0 .371-.05.576-.256.23-.244 1.127-1.448 1.665-2.41.039-.064.077-.013.064.039-.102.5-.461 1.897-.461 2.217 0 .744.538.744.564.744 1.037 0 2.074-1.833 2.254-2.09-.32 1.116.205 2.218 1.345 1.77-.32.794-.871 1.999-.871 3.23 0 .756.422 1.127.845 1.127.307 0 .59-.179.59-.487-.436-.82-.04-2.974 1.062-5.46.525-.59 1.819-2.602 1.819-3.23 0-.36-.205-.526-.474-.526-.23-.039-.359.038-.512.205z"
        fill="url(#paint0_linear_2368_98106)"
      />
      <Path
        d="M23.47 10.115h-.001a2.29 2.29 0 01-2.354 2.123v.002a2.29 2.29 0 012.124 2.353 2.29 2.29 0 012.354-2.123v-.001a2.29 2.29 0 01-2.123-2.354z"
        fill="url(#paint1_linear_2368_98106)"
      />
      <Path
        d="M25.767 13.645a1.092 1.092 0 01-1.122 1.012c.583.043 1.03.538 1.012 1.122a1.092 1.092 0 011.122-1.012 1.092 1.092 0 01-1.012-1.122z"
        fill="url(#paint2_linear_2368_98106)"
      />
      <Defs>
        {/*@ts-ignore*/}
        <LinearGradient
          id="paint0_linear_2368_98106"
          x1={9.17051}
          y1={18.4927}
          x2={21.4168}
          y2={18.4927}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#94A1F8" />
          <Stop offset={0.62} stopColor="#355FF2" />
        </LinearGradient>
        {/*@ts-ignore*/}
        <LinearGradient
          id="paint1_linear_2368_98106"
          x1={21.3202}
          y1={12.2495}
          x2={25.5932}
          y2={12.4692}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#94A1F8" />
          <Stop offset={0.62} stopColor="#355FF2" />
        </LinearGradient>
        {/*@ts-ignore*/}
        <LinearGradient
          id="paint2_linear_2368_98106"
          x1={24.7429}
          y1={14.6623}
          x2={26.7793}
          y2={14.767}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#94A1F8" />
          <Stop offset={0.62} stopColor="#355FF2" />
        </LinearGradient>
      </Defs>
    </Svg>
  );
}

export default PlaceholderIcon;
