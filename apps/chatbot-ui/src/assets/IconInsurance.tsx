// @ts-nocheck
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';
const IconInsurance = (props) => (
  <Svg width={20} height={20} viewBox="0 0 13 13" fill="none">
    <Path
      fill="#D9D9D9"
      d="M2.28 10.02c-.276-.16-.367-.396-.235-.608l.741-1.183c.113-.18.374-.317.696-.364l1.582-.234c.37-.055.763.016 1.013.184l1.865 1.257c.629.424.81 1.012.471 1.535l-.341.528L4.33 11a1.424 1.424 0 0 1-.648-.169l-1.403-.81Z"
    />
    <Path
      fill="#66BAFF"
      stroke="#20364B"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={0.375}
      d="M4.462 11.035c.017.482.187.835.471.979l3.644 1.255.15-.927 3.11-9.929-1.306-.997a.808.808 0 0 0-.154-.093c-.287-.132-.598.051-.897.156-.617.216-1.07-.128-1.755-.35a.238.238 0 0 0-.167-.004.548.548 0 0 0-.166.117.34.34 0 0 0-.063.091c-.728 1.47-1.224 2.222-2.54 2.91a.354.354 0 0 0-.123.1 1.04 1.04 0 0 0-.204.58v6.112Z"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={0.375}
      d="m8.62 13.329 2.197-3.043c.652-1.034 1.038-2.265 1.038-3.31V2.49c-1.079.777-1.7.443-2.718.058-.102-.038-.216-.054-.312-.002a.483.483 0 0 0-.228.238c-.517 1.073-.935 1.61-2.023 2.113a.519.519 0 0 0-.172.12c-.23.258-.39.646-.39.972v5.49c0 .452.092.73.28.933.11.118.262.184.414.238l1.913.68Z"
    />
    <Path
      fill="#20364B"
      d="m10.554 6.974-1.248-.227V5.612c0-.358-.102-.607-.23-.697-.08-.055-.21.03-.29.188-.128.257-.229.637-.229.995v1.135l-1.248 1.85c-.075.113-.1.226-.075.307l.025.114c.037.089.112.105.2.032l1.098-1.005.125 1.22-.33.643a.385.385 0 0 0-.045.17c0 .125.089.162.183.076l.372-.339a.46.46 0 0 1 .139-.09l.372-.145c.094-.037.183-.188.183-.313 0-.053-.016-.093-.045-.111l-.33-.215.125-1.383 1.099-.421c.087-.041.162-.154.2-.292l.037-.138c.012-.122-.013-.203-.088-.22Z"
    />
  </Svg>
);
export default IconInsurance;
