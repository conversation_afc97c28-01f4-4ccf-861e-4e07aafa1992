import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';
const IconRails = (
  props: React.JSX.IntrinsicAttributes &
    React.JSX.IntrinsicClassAttributes<React.Component<SvgProps, any, any>> &
    <PERSON><PERSON><PERSON><SvgProps>,
) => (
  <Svg width={28} height={28} fill="none" {...props}>
    <Path
      stroke="#20364B"
      strokeLinecap="round"
      strokeWidth={0.5}
      d="M1.978 21.332 21.567 9.709M6.471 22.693 3.16 20.805M5.134 23.554 24.723 11.93"
    />
    <Path
      fill="#20364B"
      d="M16.025 17.924c0 .638-.434 1.398-.97 1.696-.537.299-.971.023-.971-.615 0-.639.434-1.398.97-1.697.537-.299.971-.023.971.616ZM21.733 14.995c0 .69-.434 1.477-.97 1.755-.537.278-.971-.057-.971-.749 0-.691.434-1.477.97-1.755.537-.278.971.057.971.748Z"
    />
    <Path
      fill="#fff"
      stroke="#20364B"
      strokeWidth={0.562}
      d="M5.088 16.837c-1.855 5.004 3.284 5.471 6.754 3.626l12.65-7.234-.083-5.548-5.629-2.753s-8.204 3.83-9.711 5.285c-1.508 1.454-3.049 4.112-3.98 6.624Z"
    />
    <Path
      fill="#EAF5FF"
      d="M18.778 5.68V4.39l1.11-.285 6.223 2.978-1.684.924-5.65-2.326Z"
    />
    <Path
      fill="#20364B"
      stroke="#20364B"
      strokeWidth={0.617}
      d="M7.094 13.02s-1.026 1.616-1.402 2.597c-.377.98.095 2.135.946 2.503 2.036.944 5.79-.65 8.275-2.099l.052-1.655c-2.877 1.265-5.795 1.459-7.018.813-1.223-.645-.878-1.663-.853-2.159Z"
    />
    <Path
      fill="#008CFF"
      stroke="#20364B"
      strokeWidth={0.562}
      d="M12.322 17.305c1.181-2.224 3.312-4.722 4.551-5.454l7.582-4.248.125 5.658c-1.188.608-11.22 6.297-12.294 6.728-1.074.43-.635-1.42.036-2.684Z"
    />
    <Path
      stroke="#20364B"
      strokeLinecap="round"
      strokeWidth={0.5}
      d="M9.288 10.065c-.404 1.39.526 3.599 7.585 1.772"
    />
    <Path
      fill="#20364B"
      d="M19.027 4.845c0 .164-.119.378-.246.378-.128 0-.231-.133-.231-.297 0-.163.103-.296.23-.296.128 0 .247.052.247.215ZM24.918 7.357c0 .177-.095.408-.197.408-.102 0-.184-.144-.184-.32 0-.177.082-.32.184-.32s.197.055.197.232ZM25.03 13.02c0 .18-.095.417-.197.417-.101 0-.184-.147-.184-.328 0-.18.083-.328.184-.328.102 0 .198.057.198.239Z"
    />
    <Path fill="#EAF5FF" d="m24.063 8.146.97-.552.011 5.083-.982.544V8.146Z" />
  </Svg>
);
export default IconRails;
