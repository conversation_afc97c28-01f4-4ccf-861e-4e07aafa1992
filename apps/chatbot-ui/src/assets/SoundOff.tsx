import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface SoundOffProps {
  width?: number;
  height?: number;
  color?: string;
}

const SoundOff: React.FC<SoundOffProps> = ({
  width = 24,
  height = 24,
  color = '#000',
}) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 24 24" fill="none">
      {/* Speaker base */}
      <Path
        d="M11 5L6 9H2V15H6L11 19V5Z"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />
      {/* X mark for mute */}
      <Path
        d="M23 9L17 15"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M17 9L23 15"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default SoundOff;
