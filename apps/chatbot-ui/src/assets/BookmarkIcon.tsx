/* eslint-disable @typescript-eslint/ban-ts-comment */
import * as React from 'react';
import Svg, { G, Path, SvgProps } from 'react-native-svg';

export const BookmarkIcon = (props: SvgProps) => (
  <Svg width={16} height={16} fill="none" {...props}>
    {/*@ts-ignore*/}
    <G stroke={props.stroke || '#000'}>
      <Path d="M13.93 10.742v-3.33c0-2.858 0-4.288-.88-5.176-.878-.888-2.292-.888-5.12-.888-2.829 0-4.243 0-5.122.888-.878.888-.878 2.318-.878 5.177v3.329c0 2.064 0 3.096.489 3.547.233.216.528.35.842.387.658.075 1.426-.605 2.963-1.964.68-.601 1.02-.902 1.412-.98.194-.04.393-.04.587 0 .393.078.732.379 1.412.98 1.537 1.36 2.305 2.039 2.963 1.964.314-.036.609-.171.842-.387.49-.45.49-1.483.49-3.547Z" />
      <Path strokeLinecap="round" d="M9.93 4.014h-4" />
    </G>
  </Svg>
);
export const BookmarkIconFilled = (props: SvgProps) => (
  <Svg width={16} height={16} fill="none" {...props}>
    {/*@ts-ignore*/}
    <G stroke={props.stroke || '#008CFF'}>
      <Path
        fill={props.fill || '#CCE8FF'}
        d="M13.93 10.742v-3.33c0-2.858 0-4.288-.88-5.176-.878-.888-2.292-.888-5.12-.888-2.829 0-4.243 0-5.122.888-.878.888-.878 2.318-.878 5.177v3.329c0 2.064 0 3.096.489 3.547.233.215.528.35.842.386.658.076 1.426-.604 2.963-1.964.68-.6 1.02-.9 1.412-.98.194-.039.393-.039.587 0 .393.08.732.38 1.412.98 1.537 1.36 2.305 2.04 2.963 1.964.314-.036.609-.17.842-.386.49-.45.49-1.483.49-3.547Z"
      />
      <Path strokeLinecap="round" d="M9.93 4.015h-4" />
    </G>
  </Svg>
);
