// @ts-nocheck
import * as React from 'react';
import Svg, { Path, Defs, LinearGradient, Stop, SvgProps } from 'react-native-svg';
const BookmarksIconLarge = (props: SvgProps) => (
  <Svg width={22} height={22} viewBox="0 0 24 24" fill="none" {...props}>
    <Path
      stroke="url(#a)"
      strokeWidth={1.5}
      d="M21 16.09v-4.992c0-4.29 0-6.433-1.318-7.766C18.364 2 16.242 2 12 2 7.757 2 5.636 2 4.318 3.332 3 4.665 3 6.81 3 11.098v4.993c0 3.096 0 4.645.734 5.321.35.323.792.526 1.263.58.987.113 2.14-.907 4.445-2.946 1.02-.901 1.529-1.352 2.118-1.47.29-.06.59-.06.88 0 .59.118 1.099.569 2.118 1.47 2.305 2.039 3.458 3.059 4.445 2.945.47-.053.913-.256 1.263-.579.734-.676.734-2.224.734-5.321Z"
    />
    <Path stroke="url(#b)" strokeLinecap="round" strokeWidth={1.5} d="M15 6H9" />
    <Defs>
      <LinearGradient
        id="a"
        x1={3}
        x2={22.89}
        y1={22}
        y2={4.099}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#4A4A4A" />
        <Stop offset={1} />
      </LinearGradient>
      <LinearGradient
        id="b"
        x1={9}
        x2={9.324}
        y1={7}
        y2={5.054}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#4A4A4A" />
        <Stop offset={1} />
      </LinearGradient>
    </Defs>
  </Svg>
);
export default BookmarksIconLarge;
