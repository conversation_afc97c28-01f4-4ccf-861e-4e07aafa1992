/**
 * Types related to web components and query parameters
 */

/**
 * Interface for the infoAttr query parameter object
 * Contains various attributes passed via URL query parameters
 */
export interface InfoAttr {
  attr1?: string;
  attr2?: string;
  attr3?: string;
  attr4?: string;
  fromCity?: string;
  destination?: string;
  pageName?: string;
  device?: string;
  chatId?: string;
  evar83?: string;
  evar57?: string;
  proactive?: string;
  m_v108?: string;
}

/**
 * Interface for common query parameters used in web components
 */
export interface QueryParams {
  conversationId?: string;
  evaluationMode?: string;
  channel?: string;
  entityType?: string;
  pageIdentifier?: string;
  platform?: string;
  multipleCTA?: string | boolean;
  infoAttr?: string;
  [key: string]: any; // Allow additional dynamic parameters
  hideInput?: string;
}
