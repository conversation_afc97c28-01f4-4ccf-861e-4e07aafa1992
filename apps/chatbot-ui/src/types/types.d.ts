/* eslint-disable no-unused-vars */

// Common Types
type Org = 'MMT' | 'GI';
type Target = 'USER' | 'AGENT';
type Language = {
  displayTxt: string;
  speechRecognizer: Languages;
  speechReader: string;
  enable: boolean;
};
type SourceView = 'SINGLE' | 'COMBINED' | 'COMBINED_AGENT';
type TemplateView = 'EXPERT' | 'EXPERT_AGENT';
type ConversationType = 'BOT' | 'EXPERT' | null;
type ConversationContext = {
  chatType?: ConversationType;
};
type AgentType = 'HUMAN' | 'BOT';
type AgentMetaData = {
  agentType: AgentType;
  currentTicketInfo: {
    agentName: string;
    supportTicketId: string;
    supportTicketStatus: string;
    lob: Lob;
  };
};
type QuoteMetadata = {
  messageId: string;
  contentId: string;
  cardId: string;
  type?: string;
  textDescription?: string;
  agentName?: string;
  quotedContentInfo?: Record<string, unknown>;
  quoteMetadata?: Record<string, unknown>;
  templatePayload: {
    data: ItineraryDataType | BookablePackage;
  };
};

type SessionContextType = 'MESSAGE' | 'REPLY' | 'CONTEXT';
type SessionContext = {
  type: SessionContextType;
  data: ItineraryDataType | BookablePackage;
  conversationMetadata?: QuoteMetadata;
};

type Feedback = 'LIKE' | 'DISLIKE';

type UIElementData = {
  [key: UIElement]: [
    {
      modeId: string;
      visible: boolean;
      enabled: boolean;
    },
  ];
};
type UIElement = 'NEW_CHAT_CTA' | 'MODE_CTA';
type Tags = Tag[];
type IconsEnum =
  | 'FLIGHTS'
  | 'STAYS'
  | 'CABS'
  | 'BUS'
  | 'INSURANCE'
  | 'TRAINS'
  | 'HOLIDAYS'
  | 'GIFT_CARDS';

// API Helper Types ///////////////////////////////////////////
type PaginationParameters = {
  limit: number;
  direction: 'LATEST' | 'OLD';
  hasMore?: boolean;
};

type ChatConfig = {
  botConfKeys: {
    languages: BotLanguage[];
    guestEnabled: boolean;
  };
  userDetails?: UserDetails;
  botInfo: BotInfo;
  examplePrompts?: ExamplePrompts;
  greetingMessage?: GreetingMessage;
  omnitureConfig: Record<string, string>;
  uiConfigData?: UIConfigData;
  communicationMode?: CommunicationMode;
  welcomeMsg?: string;
  disclaimer?: string;
  featureConfig?: FeatureConfig;
};

type BotLanguage = {
  displayTxt: string;
  speechRecognizer: Languages;
  speechReader: string;
  enable: boolean;
};
type UserDetails = {
  name: string;
};
type BotInfo = {
  icon: string;
  title: string;
  titlePrefix?: string;
  description: string;
};
type ExamplePrompt = {
  text: string;
  icon?: string;
  clickable?: boolean;
};
type ExamplePrompts = ExamplePrompt[];
type GreetingMessage = Omit<Message, 'id'> & { titleContent: string };
type UIConfigData = {
  mimaCtaType?: 'collapsed' | 'expanded' | 'animated';
  autoHideKeyboardOnMessageSubmit?: boolean;
  connectWSOnUserInteraction?: boolean;
  'mmt.backend.common.default.default.application_property.apollo.messageBarVersion'?:
    | 1
    | 2;
};
type CommunicationMode = 'HTTP' | 'WEB_SOCKET';

type BotConfKey = 'guestEnabled' | 'languages';
type BotConfKeys = BotConfKey[];
type FeatureConfig = {
  platforms?: ParentPlatform[];
  showBookmarkOptions?: boolean;
  showMimaOptions?: boolean;
  showReadMore?: boolean;
  /**
   * @defaultValue true
   * */
  speechToText?: boolean;
  /**
   * @defaultValue false
   * */
  ttsUseStreaming?: boolean;
  /**
   * @defaultValue true
   * */
  ttsUseCache?: boolean;
  /**
   * @defaultValue 10_000
   * */
  ttsConnTimeoutMs?: number;
  /**
   * @defaultValue 30_000
   * */
  ttsReadTimeoutMs?: number;
  streamMarkdown?: boolean;
  showMimaOptionsCTA?: boolean;
  showIntroPopupModal?: boolean;
  shouldIncreaseFontSize?: boolean;
  showCopyIcon?: boolean;
  showSpeakerCoachmark?: boolean;
  showMicCoachmark?: boolean;
};
type lobGreetingMessage = {
  greetingMessageContent: MessageWidget | undefined;
  tempGreetingMessage: boolean;
};

type PromptsWithImages = {
  image: string;
  description?: string;
}[];

type PromptsWithImagesInContents = {
  examplePromptInfos: {
    image: string;
    size: string;
    clickable: boolean;
    icon: string;
    description: string;
  }[];
};
type PromptsWithoutImages = {
  title: string;
  titleIcon: string;
  examplePromptInfos: {
    size?: string;
    clickable: boolean;
    icon: string;
    description: string;
  }[];
};

type ContentTypes =
  | 'CONVERSATION_VIEW'
  | 'RECENT_CHATS'
  | 'PROMPTS_WITH_IMAGES'
  | 'PROMPTS_WITHOUT_IMAGES'
  | 'CHAT_HISTORY'
  | 'HELPING_HAND_CARDS';

type ContentsConversationResponse = {
  data: ConversationHistoryGroup;
};

type MenuItem = {
  icon: string;
  text: string;
  action: {
    type: string;
    value: string;
  };
  iconUrl?: string;
  iconUrlWidth?: number;
  iconUrlHeight?: number;
};
type ChatHeaderConfig = {
  title: string;
  header: {
    leftItems?: MenuItem[];
    rightItems?: MenuItem[];
    menuItems?: MenuItem[];
  };
};

type ContentsConfig = {
  type?: ContentTypes;
  title?: string;
  value?:
    | SessionMessagesResponse
    | PromptsWithImagesInContents
    | PromptsWithoutImages;
  disableChat?: boolean;
  hideInput?: boolean;
  hideT2aCta?: boolean;
};

type ShareWhatsappCta = {
  destination_tag_name?: string;
  link?: string;
  cta_title: string;
  icon: string;
  wa_message?: string;
};

type IntroPopupConfig = {
  forceShowIntroPopup?: boolean;
  showIntroPopupOnce: boolean;

  // url
  introPopupImage: string;
  introPopupImageAnimated?: string; // Animated version of the image

  //popup
  popupHeight?: number;
  resizeMode?: 'contain' | 'cover';

  // text content
  heading?: string;
  title?: string;
  subtitle?: string;

  // to clear the intro popup config
  clear?: boolean;

  // onyl for UI
  showPopup?: boolean;
  ctaText?: string;
  modeId?: string;
};

type ApolloSocketConfig = {
  hbInterval: number;
};

/**
 * Configuration options for voice transcription and overlay behavior.
 */
type VoiceConfig = {
  /**
   * WebSocket URL for the speech-to-text (STT) service.
   * @default Endpoints.STT_WEB_SOCKET
   */
  wsUrl?: string | null;

  /**
   * Timeout in milliseconds for establishing the WebSocket connection.
   * @default 10000 (10 seconds)
   */
  connectTimeoutMs?: number | null;

  /**
   * How long (in ms) the VoiceOverlay popup should remain visible after receiving the `isFinal` signal.
   * If set to 0 or less, the overlay closes immediately.
   */
  closeDelay?: number | null;

  /**
   * Maximum time (in ms) to wait for transcription after the connection was made.
   * If no response is received from the server for this duration, the UI will display a timeout error and close.
   * If set to 0 or less, this is a no-op.
   */
  transcribeTimeout?: number | null;
};

type ParentPlatform = 'android' | 'ios' | 'pwa' | 'desktop';
type AttachmentType = 'jpg' | 'png' | 'pdf';

type ExpertMetaData = Record<string, any> & { page: string };

type FormDataMap = { [key: string | number]: string | number | boolean | object };

// ChatContext Types //////////////////////////////////////////
type ChatContext = {
  context: {
    lob: Lob | undefined;
    lobCategory: string | null;
    view: string;
    prevPage?: string | null;
    platform?: ParentPlatform;
    bu?: string | null;
  };
  expertMetadata: Record<string, any>;
  contextMetadata: {
    pageContext: Record<string, any>;
    searchContext: Record<string, any>;
  };
  botMetadata?: Record<string, any>;
  debugger_uuid?: string | null;
  quotedContentInfo?: Record<string, unknown>;
};

type Lob = string;

// Session Types //////////////////////////////////////////////
type Suggestion = string | SuggestionText | EndAgentChatCTA | FilterRemoveCTA;
type SuggestionText = {
  type: 'SUGGESTION_TEXT';
  value: string;
};
type EndAgentChatCTA = {
  type: 'END_AGENT_CHAT_CTA';
  value: string;
};
type FilterRemoveCTA = {
  type: 'FILTER_REMOVE';
  value: {
    ctaText: string;
    messageText: string;
  };
};

// Message Widget Types ///////////////////////////////////////
type MessageWidget =
  | TextWidget
  | SuggestionWidget
  | CardWidget
  | LinkWidget
  | HolidayAgentWidget
  | UserPIICard
  | TableWidget;

type TextWidget = {
  type: 'TEXT' | 'LARGE_TEXT' | 'LOADER_TEXT';
  value: string;
  contentId?: string;
  quotedContentInfo?: QuoteMetadata;
};
type InsightWidget = {
  type: 'INSIGHTS';
  response_text: string;
  contentId?: string;
};
type SuggestionWidget = {
  type: Suggestion['type'];
  value: string;
  contentId?: string;
};
type TextCardData = {
  id: string;
  card_link?: string;
  cta_title: string;
  description?: string;
  title: string;
  sub_title?: string;
  sub_title_icon?: 'location';
  lob: string;
};
type TextCardType = {
  id: string;
  type: 'text-card';
  data: TextCardData;
} & BookmarkEntity;
type DetailedCardData = {
  id: string;
  cta_link?: string;
  cta_title: string;
  description?: string;
  image_url?: string;
  title: string;
  sub_title?: string;
  sub_title_icon?: 'location';
  lob: string;
  info: {
    text: string;
    sub_text: string;
  } | null;
  currency_rate: string;
  currency_tag: string;
  description_icon: string;
};
type DetailedCardType = {
  id: string;
  type: 'detailed-card' | 'simple-card';
  data: DetailedCardData;
} & BookmarkEntity;
type BookmarkEntity = {
  showBookmarkFeedback?: boolean | undefined;
  bookmarkId?: string | undefined;
  bookmarkFlag?: boolean | undefined;
  isMessageBookmarked?: boolean | undefined;
  highlightBookmark?: boolean | undefined;
  bookmarkedAt?: number;
  bookmarkPayload?: unknown;
  tags?: string[];
};

type BookmarkPageSource = 'CHAT_PAGE' | 'BOOKMARK_PAGE';

interface Segment {
  segment_id: string;
  source: string;
  destination: string;
  icon: string;
  icon_text?: string;
  type: string;
  text: string;
  fare_info_text: string;
  fare_amount_text: string;
  cta_text: string;
  cta_link?: string;
  lob: LobType;
}

interface CTA {
  text: string;
  type: string;
  link: string;
}

interface TravelDetailCardProps {
  id: string;
  card_name: string;
  lob: LobType;
  expert_source: string;
  tag: {
    text: string;
    color?: string;
  }[];
  bottom_tag: {
    text: string;
    color?: string;
  }[];
  title: string;
  fare_info_text: string;
  fare_amount_text: string;
  meta: Record<string, any>[];
  cta: CTA;
  segments: Segment[];
  messageId: string;
  item: any;
}

type TravelDetailCardType = {
  id: string;
  type: 'travel-detail-card';
  data: TravelDetailCardProps;
} & BookmarkEntity;

type MyTripsCardDataTag = {
  text: string; // "Completed | On Trip | Canceled"
  colors?: string[]; // ["#F5515F", "#9F0469"]
};

type MyTripsCardHeaderIcon = {
  type:
    | 'url'
    | 'cab'
    | 'train'
    | 'bus'
    | 'hotel'
    | 'flight'
    | 'holiday'
    | 'insurance';
  url?: string; // "ONLY_FOR_URL_TYPE"
};

type MyTripsCardHeaderSection = {
  icon?: MyTripsCardHeaderIcon;
  title: string;
  title_color?: string;
  description?: string;
};

type MyTripsCardHeader = {
  left_section?: MyTripsCardHeaderSection;
  right_section?: {
    title: string;
  };
};

type MyTripsCardMainTextProperty = {
  text: string;
  font_size?: '12' | '14';
  font_weight?: 'bold' | 'semibold' | 'normal';
};

type MyTripsCardMainSection = {
  show_horizontal?: 'true' | 'false';
  title?: string;
  sub_title?: string;
  text?: string;
};

type MyTripsCardMain = {
  image_url?: string; // "URL"
  title?: string; // "La Platinum Premium Suites, \n Sea Beach"
  sub_title?: string; // "30 Days Stay Single Entry"
  left_section?: MyTripsCardMainSection;
  right_section?: MyTripsCardMainSection;
  show_arrow?: 'true' | 'false';
};

type MyTripsCardData = {
  lob: string; // Example lob, adjust if needed
  cta_link?: string; // "https://example.com/cta-link"
  card_cta_link?: string; // "https://example.com/card-cta-link"
  tag?: MyTripsCardDataTag;
  header?: MyTripsCardHeader;
  main?: MyTripsCardMain;
  // Assuming it might also need an id and lob like other cards,
  // but these are not in the provided JSON structure.
  // Add them if necessary based on usage.
  // id: string;
  // lob: string;
};

interface MediaInfo {
  media_type: string;
  url: {
    ios: string;
    android: string;
    web: string;
  };
  thumbnail_url?: {
    ios: string;
    android: string;
    web: string;
  };
  title: string;
  duration?: string;
  subtitle: string;
}

interface BookablePackage {
  cta_link: string;
  cta_title: string;
  description: string;
  expert_source: string;
  id: string;
  image_url: string;
  info: {
    text: string;
  };
  lob: string;
  meta: Array<{
    lob: string;
    product_id: string;
  }>;
  sub_title: string;
  sub_title_icon: string;
  title: string;
  connect_cta: {
    text: string;
    meta_data: Record<string, unknown>;
    action: {
      type: 'MESSAGE' | 'LINK';
      value: string;
    };
  } | null;
  ask_question_cta: {
    text: string;
    action: {
      type: 'REPLY';
      value: {
        data: ItineraryDataType;
      };
    };
  } | null;
  quote_metadata: Record<string, unknown>;
}

interface DownloadItinerary {
  cta_title: string;
  description: string;
  icon: string;
  link: string;
}

type ItineraryCardData = {
  itinerary_title: string;
  itinerary_subtitle: string;
  media: MediaInfo;
  bookable_package_title: string;
  bookable_package: BookablePackage;
  download_itinerary: DownloadItinerary;
  share_whatsapp_cta: ShareWhatsappCta;
};

type ItineraryCardType = {
  id: string;
  type: 'package-card';
  data: ItineraryCardData;
} & BookmarkEntity;

// TypeScript interfaces based on the contract
interface ProgressItem {
  id: string;
  title: string;
  status: 'completed' | 'pending';
  order: number;
  time?: string; // Optional time field
}

interface Progress {
  enabled: boolean;
  percentage: number;
  show_percentage: boolean;
}

interface ItineraryProgressCardData {
  id: string;
  card_name: string;
  title: string;
  subtitle: string;
  progress: Progress;
  progress_items: ProgressItem[];
  removeFromUI?: boolean;
}

type ItineraryStatusCardType = {
  id: string;
  type: 'itinerary-progress-card';
  data: ItineraryProgressCardData;
} & BookmarkEntity;

type MyTripsCardType = {
  id: string;
  type: 'mytrips-card'; // Example type, adjust if needed
  data: MyTripsCardData;
} & BookmarkEntity;

type MyTripsTransportCardType = {
  id: string;
  type: 'mytrips-transport-card'; // Example type, adjust if needed
  data: MyTripsCardData;
} & BookmarkEntity;

type CardItem =
  | DetailedCardType
  | TransportCardType
  | InfoCardType
  | TextCardType
  | TravelDetailCardType
  | MyTripsCardType
  | MyTripsTransportCardType
  | DocumentCardType
  | ForexCardType
  | ItineraryCardType
  | ItineraryStatusCardType;
type CardWidget = {
  type: 'CARD' | 'INFO_CARD';
  value: {
    fallbackText: string;
    templateInfo: {
      templateId: string;
      templateVersion: string;
      uiLayout: {
        scrollType: 'HORIZONTAL' | 'VERTICAL';
        shouldApplyGradient?: boolean;
      };
      payload: Array<CardItem>;
      others?: {
        fallback_text?: string | null;
        cards_header?: {
          title: string;
          subTitle?: string | null;
          icon: string;
        };
        view_all?: {
          count: number;
          cta_title: string | null;
          link: string | null;
          moveViewAllToBottom?: boolean;
        } | null;
      } | null;
    };
  };
  cardIndex: number;
  contentId?: string;
};
type TableWidget = {
  type: 'TABLE';
  data: DetailedTableContent['data'];
};

type TransportCardData = {
  id: string;
  cta_link?: string;
  /** @deprecated */
  card_cta_link?: string;
  cta_text: string;
  fare_amount_text: string;
  fare_info_text: string;
  image_url: string;
  /** @deprecated */
  info_text: string;
  description: string;
  rating: string;
  sub_title: string;
  sub_title_icon: string;
  title: string;
  total_rating_count: string;
  lob: string;
};
type TransportCardType = {
  id: string;
  type: 'transport-card';
  data: TransportCardData;
} & BookmarkEntity;
type InfoCardData = {
  id: string;
  icon_url: string;
  title: string;
  sub_title: string;
  description: string;
  lob?: string;
} & BookmarkEntity;
type InfoCardType = {
  id: string;
  type: 'template_3' | 'info-card';
  data: InfoCardData;
} & BookmarkEntity;

type DocumentCardData = {
  id: string;
  lob?: string;
  header?: {
    left_section?: {
      title?: string;
    };
    right_section?: {
      title?: string;
    };
  };
  main?: {
    title?: string;
    icon?: string;
    sub_title?: string;
    description?: string;
    text?: string;
    options_title?: string;
    options?: string[];
  };
  footer?: {
    title?: string;
    description?: string;
    url?: string;
  };
};

type DocumentCardType = {
  id: string;
  type: 'documents-card';
  data: DocumentCardData;
} & BookmarkEntity;

type LinkWidget = {
  type: 'LINK';
  value: { link_text: string; url: string };
  contentId?: string;
};
type HolidayAgentWidget = {
  type: 'HOL_AGENT_CTA';
  value: {
    cta_text: string;
    url: string;
  };
  contentId?: string;
};
type UserPIICard = {
  type: 'USER_PII_CARD';
  value: {
    email: string | null;
    mobileNumber: string | null;
  };
  contentId?: string;
};

// Message Types //////////////////////////////////////////////
type LQSequenceType = 'LQ' | 'SUGG' | 'INSIGHTS' | 'CONTENT';

type LQInsights = {
  sequence: LQSequenceType[];
  content: {
    type: 'INSIGHTS';
    response_text: string;
  }[];
};

type Message = {
  isDraft?: boolean;
  id: string;
  role: Roles;
  content: MessageWidget[] | undefined | null;
  createdAt: EpochTimeStamp;
  lang: Languages;
  speechContent?: string | null;
  debugData?: any;
  isDelivered?: boolean;
  isSendFailed?: boolean;
  retrySend?: () => void;
  requestForFeedback?: boolean;
  leadingQuestion?: string | null;
  lq_insights?: LQInsights;
  isCompleted?: boolean;
  isStreaming?: boolean;
  streamConfig?: any;
  org?: Org;
  lobAppContext?: Record<string, any> | null;
  streamMessage?: boolean;
  forceCompleteStreaming?: boolean;
  meta?: AgentMetaData;
  replyCardData?: ItineraryDataType;
  sequence?: LQSequenceType[];
  audioTalkBack?: AudioTalkbackData | null;
} & BookmarkEntity;

type AudioTalkbackData = {
  summary?: string | null;
  lqNode?: string | null;
  locale?: string | null;
  lqDelay?: number | null;
};

type SendMessage = Omit<Message, 'id' | 'createdAt'> & { voice?: boolean };

type Roles =
  | 'USER'
  | 'ASSISTANT'
  | 'AGENT'
  | 'SYSTEM'
  | 'SYSTEM_MESSAGE'
  | 'CONTEXT_MESSAGE';
type Languages = 'en-IN' | 'en-Hnd';
type MessageStatus = 'sent' | 'delivered';

type Ctas = {
  message_text: string;
  cta_text: string;
  type: string;
  value: string;
};
interface Mode {
  id: string;
  icon: string;
  text: string;
  enabled: boolean;
  title: string;
  description: string;
  image: string;
  visible: boolean;
}

type Conversation = {
  id: string;
  title: string;
  isNewChat?: boolean; // this flag is used for analytics
  writeAllowed?: boolean;
  writeAllowedDebug?: boolean;
  hideInput?: boolean;
  hideT2aCta?: boolean;
  isNewChatInputCtaEnabled?: boolean;
  bookingLob?: IconsEnum;
  tags?: Tags;
  isLoading?: boolean;
  isStreaming?: boolean;
  messages: Message[];
  awaitedUserMessage?: Message[] | null; // sent by user, awaiting response from BE
  lastMessage: { content: MessageWidget[]; createdAt: number };
  hasUnRead: boolean;
  ticketId?: string | null;
  context?: ConversationContext;
  contextId?: string;
  org?: Org;
  shouldFetch?: boolean;
  updatedAt: EpochTimeStamp;
  showTripSummary?: boolean;
  tripSummaryText?: string;
  unreadBookmarks?: number;
  showTripSummaryModal?: boolean;
  suggestions?: {
    leadingQuestion: string | null;
    items: string[] | null;
  } | null;
  helpingHands?: {
    slotsData?: WsSubSuccessResponse<WsHelpingHandCallbackSlotsResponseData>;
    cardsData?: HelpingHandsDetailsResponseData['data']['cards'];
  } | null;
  agentMeta?: AgentMetaData;
  ctas?: Ctas[];
  mode?: Mode[];
  sessionContext?: SessionContext;
  lq_insights?: LQInsights;
  chatHeader?: ChatHeaderConfig;
  isAsyncFlowRunning?: boolean;
} & BookmarkEntity;

type Tag = {
  text: string;
  color: string;
  gradientColors?: string[];
};

type contextMenu = {
  text: string;
  icon: 'delete' | 'open' | 'copy';
  action: 'delete' | 'open' | 'copy';
};

type ConversationHistoryItem = {
  conversationId: string;
  title: string;
  message: string;
  timestamp: string;
  contextId?: string;
  bookingLob?: string;
  tags?: string[];
  enrichedTags?: Tag[];
  icon?: string;
  subIcon?: string;
  unReadCount?: number;
  unRead?: boolean;
  contextMenuId?: string;
};

type ConversationHistoryGroup = {
  title: string;
  conversations: Array<ConversationHistoryItem>;
  contextMenuDataMap: Record<string, contextMenu[]>;
};
type ConversationHistory = {
  conversationGroup: Array<ConversationHistoryGroup>;
};

type HelpingHandCardCallUs = {
  id: string;
  data: {
    header: string;
    sub_header: string;
    call_back_type: 'call_us';
    call_back_content?: string;
    contact_no: string;
    icon: string;
    auto_select?: boolean;
  };
};
type HelpingHandCardChatWithExpert = {
  id: string;
  type?: string;
  data: {
    header: string;
    sub_header: string;
    call_back_type: 'chat_us' | 'CHAT_US' | 'chat-card-with-form';
    call_back_content: string;
    webViewHeader: string;
    meta_data?: object;
    metadata?: object;
    card_name?: string;
    contact_no?: string;
    type?: string;
    icon: string;
    auto_select?: boolean;
  };
};
type HelpingHandCardScheduleCallback = {
  id: string;
  data: {
    header: string;
    sub_header: string;
    call_back_type:
      | '1'
      | 'SCHEDULE_CALLBACK'
      | 'CUSTOM_SCHEDULE'
      | 'schedule-callback-lob-flow';
    call_back_content: string;
    meta_data?: object;
    card_name?: string;
    icon: string;
    deeplink?: string;
    type?: string;
    is_active?: boolean;
    auto_select?: boolean;
  };
};

type SingleSlot = {
  slotText: string;
  timestamp: EpochTimeStamp;
};

type SlotGroup = {
  headerText: string;
  slotDetails: SingleSlot[];
};

type HelpingHandsCallbackSlotsResponseData = {
  data: {
    cardHeader: string;
    ctaText: string;
    configId: number;
    callBackSlots: SlotGroup[];
  };
};

type HelpingHandsDetailsResponseData = {
  data: {
    headerText: string;
    action: {
      type: 'dismiss';
      title: string;
    };
    cards: HelpingHandCard[];
    informationCard: {
      header: string;
      infoList: { icon: string; text: string }[];
    };
    lobMetaData: object;
  };
};

type HelpingHandCard =
  | HelpingHandCardCallUs
  | HelpingHandCardChatWithExpert
  | HelpingHandCardScheduleCallback;

type ItineraryOptionCardType = {
  data: ItineraryDataType;
} & BookmarkEntity;

type ItineraryDataType = {
  id: string;
  card_name: string;
  title: string;
  subTitle: string;
  itineraryDays: string;
  lob?: string;
  callout: {
    text: string;
    textColor: string;
    bgColor: string;
  };
  activities: {
    items: string[];
    title: string;
  };
  cta: ItineraryCta[];
  msgId: string;
  quote_metadata: QuoteMetadata;
};

type ItineraryCta = {
  type: string;
  ctaText: string;
  messageText?: string;
  action: string;
};

// Add new detailed itinerary CTA type
type DetailedItineraryCta = {
  action: string;
  messageText: string | null;
  type: string;
  ctaText: string;
};

// Add table column definition
type TableColumn = {
  width: number;
  header: string;
  isMarkdown?: boolean;
  key: string;
  align: 'center' | 'left' | 'right';
};

// Add table row definition
type TableRow = {
  [key: string]: string;
};

// Add table data structure
type TableData = {
  columns: TableColumn[];
  rows: TableRow[];
  title: string;
};

// Add detailed content structure
type DetailedTableContent = {
  type: 'table';
  data: {
    tableData: TableData;
    isFirstColumnSticky?: boolean;
    layout: 'advanced' | 'basic';
  };
};

// Add info structure
type ItineraryInfo = {
  sub_text: string | null;
  icon: string;
  text: string;
};

// Add comprehensive detailed itinerary type
type DetailedItineraryCardType = {
  cta: DetailedItineraryCta[];
  title: string;
  subTitle: string;
  quote_metadata: {
    name: string;
    source: string;
    itinerary_id: string;
    package_id: string;
  };
  itineraryDays: string;
  info: ItineraryInfo;
  id: string;
  activities: {
    items: string[];
    title: string;
  };
  callout: {
    bgColor: string;
    textColor: string;
    text: string;
  };
  detailedContent: DetailedTableContent;
  card_name: string;
};

interface FlightSection {
  icon: string;
  title: string;
  sub_title: string;
  description: string;
}

interface FlightOptionsCardType extends BookmarkEntity {
  id: string;
  type: 'rt-transport-card';
  data: FlightOptionsCardData;
}

interface FlightOptionsCardData {
  id: string;
  card_name: string;
  lob: string;
  expert_source: string;
  meta: Array<{
    product_id: string;
    lob: string;
  }>;
  header: {
    title: string;
    icons: string[];
  };
  sections: FlightSection[];
  fare_info_text: string;
  fare_amount_text: string;
  cta_link: string;
}

type SetVideoModalDataType = ({
  show,
  media,
}: {
  show: boolean;
  media: MediaInfo;
}) => void | undefined;
