import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  DeviceEventEmitter,
  NativeModules,
  Platform,
  View,
} from 'react-native';
import {
  ActionTypes,
  ChatBotViewState,
  ExpandViewProps,
  OnActionProps,
  TravelPlexBotRef,
} from './types';
import {
  fireAssistAction,
  fireJsReadyEvent,
  fireMinimizedEvent,
  fireTrackingAction,
  setCurrentInstanceId,
} from '../native/bridge';
import { TravelPlexBotWrapper } from './TravelPlexBotWrapper';
import { Logger } from '../utils/logger';
import { snakeToCamelCaseKeys } from '../utils/objectUtils';
import { getPlatform } from '../utils/getPlatform';
import {
  ParentType,
  startNewStartupTrace,
  updateStartupTrace,
} from '../analytics/telemetry';

/* REACT NATIVE & WEB EXPORT & */
// Export the component with forwardRef to allow parent components to access the ref methods
export const TravelPlexBot = React.forwardRef(TravelPlexBotWrapper);

function useStartupTraceId(
  parentType: ParentType,
  props: Record<string, unknown>,
): string {
  const ctaClickTs = useRef<number>();
  if (!ctaClickTs.current) {
    if ('ctaClickEventTs' in props && typeof props.ctaClickEventTs === 'number') {
      ctaClickTs.current = props.ctaClickEventTs;
    } else {
      ctaClickTs.current = -1;
    }
  }

  const [startupTraceId] = useState(() =>
    startNewStartupTrace(parentType, ctaClickTs.current as number),
  );
  return startupTraceId;
}

// Temprary export for MyAccount and BottomBar
export const TravelPlexBotNativeVariant = (props: Record<string, unknown> = {}) => {
  const onViewStateChange = useCallback((state: string) => {
    if (state === 'collapsed') {
      switch (Platform.OS) {
        case 'ios':
          NativeModules.ViewControllerModule?.dismissPresentedViewController();
          break;
        case 'android':
          BackHandler.exitApp();
          break;
        default:
          console.warn('Known platform', Platform.OS);
      }
    }
  }, []);

  const startupTraceId = useStartupTraceId('NativePage', props);
  const internalRef = useRef<TravelPlexBotRef | null>(null);
  return (
    <TravelPlexBot
      ref={internalRef}
      {...props}
      startupTraceId={startupTraceId}
      onViewStateChange={onViewStateChange}
    />
  );
};

/* NATIVE EXPORT */
type ChatContextSerialised = {
  pageContextJson: string;
  chatContextJson: string;
  expertMetadataJson: string;
  searchContextJson: string;
};
type VisibilityEventPayload =
  | {
      actionType: 'visibility';
      value: 'show';
      initialMessage: string;
      instanceId: string;
      chatContext: string;
      forceFetch?: boolean;
    }
  | {
      actionType: 'visibility';
      instanceId: string;
      value: 'hide';
      forceFetch?: boolean;
    }
  | {
      actionType: 'visibility';
      instanceId: string;
      value: 'focus' | 'blur';
    }
  | {
      actionType: 'updateProps';
      instanceId: string;
      initialMessage?: string | null;
      chatContext: string;
      forceFetch?: boolean;
    };

// FIXME this is temporary change.
// Instead of we deriving it here, will ask LOB's respective CB to start sending chatContext node
const deriveContext = (
  serialisedCtx: ChatContextSerialised,
): ChatContext['context'] => {
  const { chatContextJson, pageContextJson } = serialisedCtx;
  if (chatContextJson && chatContextJson !== 'null' && chatContextJson !== '{}') {
    const context = JSON.parse(chatContextJson) as ChatContext['context'];

    // sanitise
    if (context.lob === 'FLIGHTS' && !context.lobCategory) {
      context.lobCategory = 'if';
    }
    context.view = context.view.toUpperCase();

    return context;
  }
  const platform: ParentPlatform = Platform.select({
    default: 'android',
    ios: 'ios',
    web: 'pwa',
  });
  const pageCtx = JSON.parse(
    pageContextJson,
  ) as ChatContext['contextMetadata']['pageContext'];
  const { lob, page_name, pageName, lobCategory } = pageCtx || {};
  switch (lob.toLowerCase()) {
    case 'flights':
      return {
        lob: 'FLIGHTS',
        view: page_name || pageName?.toUpperCase(),
        lobCategory: 'if',
        platform,
        prevPage: null,
      };
    case 'hotel':
      return {
        lob: 'HOTELS',
        view: page_name || pageName?.toUpperCase(),
        lobCategory: lobCategory || 'dh',
        platform,
        prevPage: null,
      };
    default: {
      return {
        lob: 'COMMONS',
        view: page_name,
        lobCategory: null,
        platform,
        prevPage: null,
      };
    }
  }
};

const transformChatContext = (chatContextStr: string): ChatContext => {
  const chatContext = JSON.parse(chatContextStr) as ChatContextSerialised;
  const searchContext = JSON.parse(chatContext.searchContextJson || '{}') || {};
  const pageContext = JSON.parse(chatContext.pageContextJson || '{}') || {};
  const expertMetadata = JSON.parse(chatContext.expertMetadataJson || '{}') || {};
  return {
    context: deriveContext(chatContext),
    expertMetadata: snakeToCamelCaseKeys(expertMetadata),
    contextMetadata: {
      searchContext: snakeToCamelCaseKeys(searchContext),
      pageContext: snakeToCamelCaseKeys(pageContext),
    },
  };
};

export const travelplexBotLogger = Logger.createLogger({
  tag: 'TravelPlexBot',
  level: 'VERBOSE',
});

const TRAVELPLEX_EVENT_NAME = 'travelplex_native_event';
export const TravelPlexBotNative = (props: any) => {
  let currentInstanceId: number | null = null;
  if (typeof props === 'object' && props !== null && 'chatInstanceId' in props) {
    currentInstanceId = props.chatInstanceId as number;
  } else {
    travelplexBotLogger.error(
      'TravelPlexBotNative - chatInstanceId not found in props',
    );
  }
  travelplexBotLogger.info('TravelPlexBotNative props', props);

  const ctaClickTs = useRef<number>();
  if (!ctaClickTs.current) {
    if ('ctaClickEventTs' in props && typeof props.ctaClickEventTs === 'number') {
      ctaClickTs.current = props.ctaClickEventTs;
    } else {
      ctaClickTs.current = -1;
    }
  }
  const [startupTraceId] = useState(() =>
    startNewStartupTrace('NativeView', ctaClickTs.current || -1),
  );
  const chatBotRef = useRef<TravelPlexBotRef | null>(null);
  const [chatConfig, setChatConfig] = useState<ChatContext | null>({
    context: {
      lob: 'FLIGHTS',
      view: 'LANDING',
      lobCategory: 'if',
      platform: 'android',
      prevPage: null,
    },
    expertMetadata: {} as ChatContext['expertMetadata'],
    contextMetadata: {
      pageContext: {},
      searchContext: {},
    },
  });
  useEffect(() => {
    if (currentInstanceId !== null) {
      setCurrentInstanceId(currentInstanceId);
    }
    fireJsReadyEvent();
    updateStartupTrace(startupTraceId, 'onReady');
  }, [currentInstanceId]);
  const currentInstanceIdRef = useRef(currentInstanceId);
  useEffect(() => {
    const subscription = DeviceEventEmitter.addListener(
      TRAVELPLEX_EVENT_NAME,
      async (eventDataStr: string) => {
        travelplexBotLogger.info('TravelPlexBotNative - eventDataStr', eventDataStr);
        const currentChatInstanceId = currentInstanceIdRef.current;
        const eventData = JSON.parse(eventDataStr) as VisibilityEventPayload;
        if (`${eventData.instanceId}` !== `${currentChatInstanceId}`) {
          return;
        }
        if (currentChatInstanceId !== null) {
          setCurrentInstanceId(currentChatInstanceId);
        }
        if (eventData.actionType === 'visibility') {
          if (eventData.value === 'show') {
            const newChatConfig = transformChatContext(eventData.chatContext);
            travelplexBotLogger.info(
              'newChatConfig = ',
              JSON.stringify(newChatConfig),
            );
            chatBotRef.current?.expand?.({
              chatConfig: newChatConfig as ChatContext,
              initialUserPrompt: eventData.initialMessage,
              forceFetch: eventData.forceFetch,
            });
          } else if (eventData.value === 'hide') {
            chatBotRef.current?.collapse?.();
          } else if (eventData.value === 'focus') {
            setCurrentInstanceId(eventData.instanceId);
          }
        }
        if (eventData.actionType === 'updateProps') {
          setChatConfig(transformChatContext(eventData.chatContext));
        }
      },
    );
    return () => {
      subscription.remove();
    };
  }, [chatConfig]);

  const onActionNativeCb = useCallback(
    ({ lob, actionType, actionPayload }: OnActionProps) => {
      switch (actionType) {
        case ActionTypes.MessageAction:
          fireAssistAction({
            lob,
            unreadCount: actionPayload.unreadCount as string,
            payload: actionPayload.payload as Record<string, unknown>,
          });
          break;
        case ActionTypes.Analytics:
          fireTrackingAction({
            lob,
            payload: actionPayload as Record<string, unknown>,
          });
          break;
        default:
          console.log('unknown event', { actionPayload });
      }
    },
    [],
  );

  const onViewStateChange = useCallback((viewState: ChatBotViewState) => {
    if (viewState === 'collapsed') {
      fireMinimizedEvent();
    }
  }, []);

  return (
    <View style={{ flex: 1, flexDirection: 'column' }}>
      <TravelPlexBot
        onViewStateChange={onViewStateChange}
        chatConfig={undefined}
        ref={chatBotRef}
        onAction={onActionNativeCb}
        isNativeMount={true}
        startupTraceId={startupTraceId}
      />
    </View>
  );
};

//NOTE: hardcoded for now
export const defaultConfig: Record<string, ExpandViewProps> = {
  BottomBar: {
    chatConfig: {
      context: {
        lob: 'COMMONS',
        lobCategory: 'COMMONS',
        view: 'homepage',
        prevPage: null,
        platform: getPlatform(),
      },
      contextMetadata: {
        pageContext: {
          lob: 'COMMONS',
          lobCategory: 'COMMONS',
          pageName: 'homepage',
          prevPageName: null,
        },
        searchContext: {},
      },
      expertMetadata: {},
    },
  },
  MyAccount: {
    chatConfig: {
      context: {
        lob: 'COMMONS',
        lobCategory: 'COMMONS',
        view: 'my_account_landing',
        // project: 'destination_expert',
        prevPage: null,
        platform: getPlatform(),
      },
      contextMetadata: {
        pageContext: {
          lob: 'COMMONS',
          lobCategory: 'COMMONS',
          pageName: 'my_account_landing',
          prevPageName: null,
        },
        searchContext: {},
      },
      expertMetadata: {},
    },
  },
};
