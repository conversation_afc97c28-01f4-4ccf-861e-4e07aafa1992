import React, {
  ReactNode,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  BackHandler,
  Modal,
  ModalBaseProps,
  NativeModules,
  Platform,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { QueryClientProvider } from 'react-query';
import type { BottomSheetFooterProps } from '@gorhom/bottom-sheet';
import {
  BottomSheetFooter,
  BottomSheetModal,
  BottomSheetModalProvider,
} from '@gorhom/bottom-sheet';
import BaseBottomSheetScreen from '../screens/BaseBottomSheetScreen';
import { useMessageStore } from '../store/messages';
import { MenuProvider } from 'react-native-popup-menu';
import { useAppStateStore, useBottomBarVisibility } from '../store/app';
import { queryClient } from './queryClientInstance';
import type { BottomSheetBackdropProps } from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetBackdrop';
import Footer from '../components/Footer';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { Toast } from '../components/toast';
import Animated, {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import {
  ChatBotViewState,
  ExpandViewProps,
  TravelPlexBotProps,
  TravelPlexBotRef,
} from './types';
import { SafeAreaProvider, useSafeAreaInsets } from 'react-native-safe-area-context';
import { defaultConfig } from './TravelPlexBot';
import { Analytics, TrackingEvent } from '../analytics';
import ErrorBoundary from '../screens/ErrorBoundary';
import {
  logChatClosedWithPendingBuffer,
  startNewStartupTrace,
  updateStartupTrace,
} from '../analytics/telemetry';
import { TelemetryProvider } from '../analytics/telemetry/TelemetryProvider';
import { SocketClient } from '../store/socket';
import { stopTtsIfPlaying } from '../store/audio-talkback/talkbackStore';

export function TravelPlexBotWrapper(
  props: TravelPlexBotProps,
  ref: React.Ref<TravelPlexBotRef | null>,
) {
  return (
    <SafeAreaProvider>
      <TravelPlexBotWrapperInner {...props} forwardRef={ref} />
    </SafeAreaProvider>
  );
}

// Main component function
export function TravelPlexBotWrapperInner({
  parentContextProvider,
  onViewStateChange,
  onAction,
  deeplinkHandler,
  // Prefetch is not used in the current implementation
  // shouldPrefetch = false,
  parentPage,
  isNativeMount = false,
  forwardRef,
  isDebugMode = false,
  startupTraceId,
  shouldMinimizeAfterAssist = true,
}: TravelPlexBotProps) {
  stopTtsIfPlaying('bot_wrapper_mount');
  const startupTraceIdRef = useRef<string>();
  if (!startupTraceIdRef.current) {
    startupTraceIdRef.current = startupTraceId || startNewStartupTrace('React', -1);
  }

  useEffect(() => {
    if (deeplinkHandler) {
      useAppStateStore.setState({
        deeplinkHandlers: [
          deeplinkHandler,
          ...(useAppStateStore.getState().deeplinkHandlers || []),
        ],
      });
      return () => {
        const { deeplinkHandlers } = useAppStateStore.getState();
        if (!deeplinkHandlers?.length) {
          return;
        }
        useAppStateStore.setState({
          // remove first element from the array
          deeplinkHandlers: deeplinkHandlers.slice(1),
        });
      };
    }
    return () => {
      // no-op
    };
  }, [deeplinkHandler]);

  // Get the state for showing the chat bot overlay
  const [showChatBotOverlay, setShowChatBotOverlay] = useState(false);
  const [bottomSheetState, setBottomSheetState] = useState<ChatBotViewState | null>(
    null,
  );
  const isNativePage = parentPage === 'BottomBar' || parentPage === 'MyAccount';
  // Ref for the BottomSheetModal
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const snapPoints = useMemo(() => ['90%'], []);
  const insets = useSafeAreaInsets();

  /* Expand and Collapse Methods */
  const expandSheet = useCallback((props: ExpandViewProps) => {
    setShowChatBotOverlay(true);
    const invalidateChatViewData =
      props.chatConfig?.expertMetadata?.summarize || props.invalidateChatViewData;
    useAppStateStore.setState({
      ...(props.tempConfig ? { tempConfig: props.tempConfig } : {}),
      chatContext: props.chatConfig,
      initialUserPrompt: props.initialUserPrompt,
      // TODO: this is a temporary fix to force the prefetch speicific to flights
      // it should be replaced with a common forceFetch boolean key
      forceFetchId: invalidateChatViewData ? Date.now() : null,
    });
    Analytics.init({ chatContext: props.chatConfig });

    // to align with the modal opening and bottom sheet expanding
    setTimeout(() => {
      if (props?.chatConfig) {
        bottomSheetModalRef.current?.present();
      }
    }, 0);

    setBottomSheetState('expanded');

    useAppStateStore.setState({
      triggerDismissHandler: () => {
        bottomSheetModalRef.current?.close?.();
      },
    });
    if (startupTraceIdRef.current) {
      updateStartupTrace(startupTraceIdRef.current, 'expanded');
    }
  }, []);
  const bottomSheetStateRef = useRef(bottomSheetState);
  bottomSheetStateRef.current = bottomSheetState;
  const collapseSheet = useCallback(() => {
    stopTtsIfPlaying('chat_minimized');
    if (Platform.OS === 'web') {
      onViewStateChange('collapsed');
      Analytics.trackClickEvent(TrackingEvent.payload_ChatBotMinimizedClicked());
      return;
    }
    bottomSheetModalRef.current?.close();
    setShowChatBotOverlay(false);
    if (bottomSheetStateRef.current !== 'collapsed') {
      onViewStateChange('collapsed');

      if (isNativeMount) {
        // there is a weird issue with the bottom sheet in Flights/Hotels,
        // it is not collapsing properly when pressed back immediately after opening.
        // so adding a timeout to fix this issue
        setTimeout(() => {
          onViewStateChange('collapsed');
        }, 100);
      }
    }
    setBottomSheetState('collapsed');
    Analytics.trackClickEvent(TrackingEvent.payload_ChatBotMinimizedClicked());
  }, [onViewStateChange]);

  // Expose expandView and collapseView methods to the parent component
  useImperativeHandle(forwardRef, () => ({
    expand: expandSheet,
    collapse: collapseSheet,
  }));
  const bottomSheetIndex = useRef<number | null>(null);

  // Handle changes in the bottom sheet index
  const handleSheetChanges = useCallback(
    (index: number) => {
      bottomSheetIndex.current = index;
      const isChatClosed = index < 0;
      if (Platform.OS === 'android') {
        NativeModules.GenericModule.changeBottomBarVisibility(isChatClosed);
      }
      if (isChatClosed) {
        useAppStateStore.setState({
          currentView: 'chat',
        });
        useMessageStore.setState({
          newChatRequested: false,
        });
        collapseSheet();
        if (SocketClient.hasBufferedMessages()) {
          logChatClosedWithPendingBuffer();
        }
      } else {
        // calling the callback here will make sure its called when actually the sheet is opened
        onViewStateChange('expanded');
      }
    },
    [collapseSheet, onViewStateChange],
  );

  const bottomBarVisibility = useBottomBarVisibility();
  const onRequestClose = useCallback(() => {
    collapseSheet();
  }, []);
  // Render the footer component
  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <BottomSheetFooter
          style={{
            display: bottomBarVisibility === 'hide' ? 'none' : 'flex',
          }}
          {...props}
        >
          <Footer />
          <Toast />
        </BottomSheetFooter>
      );
    },
    [bottomBarVisibility],
  );

  // Handle back button press on Android
  useEffect(() => {
    const subscription = BackHandler.addEventListener('hardwareBackPress', () => {
      if (bottomSheetState !== 'collapsed') {
        collapseSheet();
        return true;
      }
      return false;
    });

    return () => {
      subscription.remove();
    };
  }, [bottomSheetState, collapseSheet]);

  {
    /* Prop setting */
  }
  useEffect(() => {
    useAppStateStore.setState({
      parentContextProvider: parentContextProvider,
      parentFunnel: null,
      onAction,
      isDebugMode: isDebugMode,
      shouldMinimizeAfterAssist: shouldMinimizeAfterAssist,
    });
  }, [parentContextProvider, onAction, isDebugMode, shouldMinimizeAfterAssist]);

  //TODO : remove this once the parent app made changes to invoke with usual approach
  {
    /* Mount on Open with config passed */
  }

  useEffect(() => {
    if (isNativePage) {
      expandSheet(defaultConfig[parentPage]);
    }
  }, [expandSheet, isNativePage, parentPage]);

  const animatedIndex = useSharedValue(0);
  const animatedOpacity = useAnimatedStyle(() => {
    const opacity = interpolate(animatedIndex.value, [-1, -0.5, 0], [0, 0.25, 1]);
    return {
      display: animatedIndex.value === -1 ? 'none' : 'flex',
      opacity,
      backgroundColor: '#000A',
    };
  }, [animatedIndex]);

  // Custom backdrop component for the bottom sheet
  const CustomBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => {
      const { style, ...otherProps } = props;
      return (
        <TouchableWithoutFeedback onPress={collapseSheet}>
          <Animated.View {...otherProps} style={[style, animatedOpacity]} />
        </TouchableWithoutFeedback>
      );
    },
    [animatedOpacity, collapseSheet],
  );

  const onDismiss = useCallback(() => {
    setShowChatBotOverlay(false);
    onViewStateChange('collapsed');
  }, [onViewStateChange]);

  return (
    <SafeAreaProvider>
      <ModalWrapper
        enabled={!isNativePage}
        visible={showChatBotOverlay}
        onRequestClose={onRequestClose}
      >
        <QueryClientProvider client={queryClient}>
          <TelemetryProvider>
            <MenuProvider>
              <GestureHandlerRootView>
                <BottomSheetModalProvider>
                  <BottomSheetModal
                    onDismiss={onDismiss}
                    animatedIndex={animatedIndex}
                    backdropComponent={CustomBackdrop}
                    style={{
                      overflow: 'hidden',
                      borderRadius: 40,
                    }}
                    // TODO: keyboardBehavior -> why is this needed
                    //  this is creating the extra height in the bottom sheet
                    // while opening keyboard

                    //  keyboardBehavior={'extend'}
                    keyboardBehavior={
                      Platform.OS === 'android' ? 'interactive' : 'extend'
                    }
                    android_keyboardInputMode="adjustResize"
                    handleComponent={CustomHandle}
                    enableDismissOnClose
                    footerComponent={renderFooter}
                    ref={bottomSheetModalRef}
                    snapPoints={snapPoints}
                    onChange={handleSheetChanges}
                    topInset={insets.top} // Apply top inset
                  >
                    <ErrorBoundary pageName={'chat'}>
                      <BaseBottomSheetScreen
                        bottomSheetState={bottomSheetState}
                        startupTraceId={startupTraceIdRef.current}
                      />
                    </ErrorBoundary>
                  </BottomSheetModal>
                </BottomSheetModalProvider>
              </GestureHandlerRootView>
            </MenuProvider>
          </TelemetryProvider>
        </QueryClientProvider>
      </ModalWrapper>
    </SafeAreaProvider>
  );
}

const ModalWrapper = ({
  children,
  visible,
  onRequestClose,
  enabled,
}: {
  children: ReactNode;
  visible: boolean;
  onRequestClose: ModalBaseProps['onRequestClose'];
  enabled: boolean;
}) => {
  // if native dont render with modal
  if (!enabled) {
    return <View style={{ flex: 1 }}>{children}</View>;
  }

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={onRequestClose}
    >
      <View style={{ flex: 1 }}>{children}</View>
    </Modal>
  );
};

// Custom handle component for the bottom sheet
function CustomHandle() {
  return (
    <View
      style={{
        height: 24,
        top: 0,
        left: 0,
        right: 0,
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute',
        backgroundColor: 'transparent',
        zIndex: 1,
      }}
    >
      <View
        style={{
          height: 6,
          width: 94,
          borderRadius: 3,
          backgroundColor: '#BCBCBC',
        }}
      />
    </View>
  );
}
