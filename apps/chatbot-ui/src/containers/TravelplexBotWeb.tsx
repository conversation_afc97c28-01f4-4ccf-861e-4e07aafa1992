/* eslint-disable no-console */
import React, { useRef, useCallback, useEffect } from 'react';
import { TravelPlexBot } from './TravelPlexBot';
import {
  TravelPlexBotRef,
  ActionTypes,
  OnActionProps,
  ChatBotViewState,
} from './types';
import {
  fireAssistAction,
  fireMinimizedEvent,
  fireTrackingAction,
} from '../native/bridge';
import { getTrafficSource, getValueFromUrl } from '../utils/webUtils';
import {
  fireContextChangeEvent,
  fireOptionsClickEvent,
} from '../native/bridge/webBridge';
// TODO: Have to comeup with context and chat config for debug mode
const chatConfig = {
  context: {
    lob: 'COMMONS',
    lobCategory: 'COMMONS',
    view: 'my_account_landing',
    prevPage: null,
  },
  contextMetadata: {
    pageContext: {
      lob: 'COMMONS',
      lobCategory: 'COMMONS',
      pageName: 'my_account_landing',
      prevPageName: null,
    },
    searchContext: {},
  },
  expertMetadata: {},
};
export const TravelPlexBotWeb = () => {
  const chatBotRef = useRef<TravelPlexBotRef | null>(null);
  const conversationId = getValueFromUrl('conversationId');
  const isDebugMode = getValueFromUrl('xd') === '456';
  const evaluationMode = getValueFromUrl('evaluationMode');
  const trafficSource = getTrafficSource();
  useEffect(() => {
    // This useEffect is only for debug use case
    // when bot is loaded only in browser, not in iframe
    if (!conversationId && trafficSource !== 'myra') {
      return;
    }
    const timer = setTimeout(() => {
      if (chatBotRef?.current) {
        chatBotRef.current.expand({
          chatConfig: {
            ...chatConfig,
            botMetadata: {
              conversationId,
              ...(evaluationMode && { evaluationMode }),
            },
          },
        });
      }
    }, 100);
    return () => {
      clearTimeout(timer);
    };
  }, [conversationId, trafficSource]);
  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      // TODO: For security, you might want to validate event.origin here
      // if (event.origin !== "expected-origin") return;
      console.log('Received message from parent:', event.data);
      const { type, payload } = event.data;

      switch (type) {
        case 'CHAT_BOT__GET_META_DATA':
          if (payload) {
            const parsedConfig =
              typeof payload === 'string' ? JSON.parse(payload) : payload;
            const {
              chatConfig: _chatConfig = {},
              tempConfig,
              initialUserPrompt,
            } = parsedConfig;
            // Extract all the url params and add them to the expertMetaData and add it to the chatConfig
            const expertMetaData = _chatConfig.expertMetadata || {};
            const urlParams = new URLSearchParams(window.location.search);
            urlParams.forEach((value, key) => {
              expertMetaData[key] = value;
            });
            _chatConfig.expertMetadata = expertMetaData;

            // Optionally expand the bot with the new config immediately
            if (chatBotRef?.current) {
              chatBotRef.current.expand({
                chatConfig: _chatConfig,
                tempConfig,
                ...(initialUserPrompt && { initialUserPrompt }),
              });
            }
          }
          break;
        // Add more message types here as needed
        default:
          console.log('Unhandled message type:', type);
          break;
      }
    } catch (error) {
      console.error('Error processing message from parent:', error);
    }
  }, []);
  useEffect(() => {
    window.parent.postMessage(
      {
        type: 'CHAT_BOT__BRIDGE__GET_META_DATA',
      },
      '*', // Using '*' for broad compatibility, consider restricting to specific origin in production
    );

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [handleMessage]);
  const onActionCb = useCallback(
    ({ lob, actionType, actionPayload }: OnActionProps) => {
      switch (actionType) {
        case ActionTypes.MessageAction:
          fireAssistAction({
            lob,
            unreadCount: actionPayload.unreadCount as string,
            payload: actionPayload.payload as Record<string, unknown>,
          });
          break;
        case ActionTypes.Analytics:
          fireTrackingAction({
            lob,
            payload: actionPayload as Record<string, unknown>,
          });
          break;
        case ActionTypes.OptionsClick:
          fireOptionsClickEvent({
            lob,
            payload: actionPayload as Record<string, unknown>,
          });
          break;
        case ActionTypes.ContextChange:
          fireContextChangeEvent({
            lob,
            payload: actionPayload as Record<string, unknown>,
          });
          break;
        default:
          // eslint-disable-next-line no-console
          console.log('unknown event', { actionPayload });
      }
    },
    [],
  );

  const onViewStateChange = useCallback((viewState: ChatBotViewState) => {
    if (viewState === 'collapsed') {
      console.log('collapsed');
      fireMinimizedEvent();
    }
  }, []);
  return (
    <TravelPlexBot
      onViewStateChange={onViewStateChange}
      ref={chatBotRef}
      onAction={onActionCb}
      isDebugMode={isDebugMode}
    />
  );
};
