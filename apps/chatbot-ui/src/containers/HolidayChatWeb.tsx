/* eslint-disable no-console */
import React, { useRef, useCallback, useEffect } from 'react';
import { View } from 'react-native';
import { TravelPlexBot } from './TravelPlexBot';
import {
  TravelPlexBotRef,
  ActionTypes,
  OnActionProps,
  ChatBotViewState,
} from './types';
import {
  fireAssistAction,
  fireMinimizedEvent,
  fireTrackingAction,
} from '../native/bridge';
import { safeJsonParse, parseIfString } from '../utils/parseUtils';
import { InfoAttr, QueryParams } from '../types/webTypes';

const getQueryParams = (): QueryParams => {
  try {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      return {};
    }
    // Get URL query parameters as a map
    const urlParams = new URLSearchParams(window.location.search);
    const paramsMap: QueryParams = {};
    urlParams.forEach((value, key) => {
      paramsMap[key] = value;
    });
    return paramsMap;
  } catch (error) {
    return {};
  }
};

export const HolidayChatWeb = () => {
  const chatBotRef = useRef<TravelPlexBotRef | null>(null);
  const queryParams: QueryParams = getQueryParams();
  let conversationId = queryParams.conversationId;
  const evaluationMode = queryParams.evaluationMode;
  const channel = queryParams.channel;
  const entityType = queryParams.entityType;
  const pageIdentifier = queryParams.pageIdentifier;
  const platform = queryParams.platform;
  const hideInput = queryParams.hideInput === 'true';
  // Default to true if undefined; otherwise, check if value is boolean true or string 'true'
  const multipleCTA = queryParams.multipleCTA === undefined
      ? true
      : queryParams.multipleCTA === 'true' || queryParams.multipleCTA === true;

  // Parse infoAttr if it exists
  const infoAttr = safeJsonParse<InfoAttr>(queryParams.infoAttr, {});
  const {
    attr1,
    attr2,
    attr3,
    attr4,
    fromCity,
    destination,
    pageName,
    device,
    chatId,
    evar83,
    evar57,
    proactive,
    m_v108
  } = infoAttr;

  const trafficSource = "holiday";
  const chatConfig = {
    context: {
      lob: 'HOLIDAYS',
      lobCategory: 'domhld',
      view: pageIdentifier || "landing",
      prevPage: null,
      platform: channel?.toLowerCase() as 'ios' | 'android' | 'pwa' | 'desktop' | undefined
    },
    contextMetadata: {
      pageContext: {
        lob: 'HOLIDAYS',
        lobCategory: 'domhld',
        pageName: pageName || pageIdentifier || "landing",
        prevPageName: '',
        lobFunnel: '',
        subpageName: '',
        funnelStep: pageIdentifier || "landing",
        pgTimeInterval: '',
        navigation: '',
        subLob: "MMT",
        pageUrl: '',
        // Add additional page context from query params
        ...(platform && { platform }),
        ...(device && { device }),
      },
      searchContext: {
        // Add search context if available from infoAttr
        ...(fromCity && {
          fromCity,
          from: {
            locus: {
              name: fromCity,
              cityName: fromCity,
            },
            cityName: fromCity,
          }
        }),
        ...(destination && {
          destination,
          to: {
            locus: {
              name: destination,
              cityName: destination,
            },
            cityName: destination,
          }
        }),
        lob: 'HOLIDAYS',
        funnelSource: 'HOLIDAYS',
        lobCategory: 'domhld',
      },
    },
    expertMetadata: {
      lob: 'HOLIDAYS',
      funnelType: '',
      page: pageName || pageIdentifier || "landing",
      crid: '',
      itid: '',
      redirectType: '',
      summarize: '',
      lobCode: 'B2C',
      src: 'MMT',
      currency: 'inr',
      userCurrency: 'INR',
      botType: 'TRAVELPLEX',
      isWebView: true,
      entityType: entityType || 'Funnel_Holiday',
      // Add attributes from infoAttr
      ...(attr1 && { attr1 }),
      ...(attr2 && { attr2 }),
      ...(attr3 && { attr3 }),
      ...(attr4 && { attr4 }),
      // Add additional metadata from query params and infoAttr
      ...(chatId && { uniqChatId: chatId }),
      ...(evar83 && { evar83 }),
      ...(evar57 && { evar57 }),
      ...(typeof proactive !== 'undefined' && { proactive }),
      ...(m_v108 && { m_v108 }),
      ...(channel && { channel }),
      ...(platform && { platform }),
      ...(destination && { destination }),
      ...(fromCity && { fromCity }),
      multipleCTA,
      // Include all original queryParams for backward compatibility
      ...queryParams,
      // Add extra data similar to TravelPlexContainer
      extraData: {
        ...queryParams,
        ...infoAttr,
        uniqChatId: chatId,
      },
      hideInput,
    },
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      if (chatBotRef?.current) {
        chatBotRef.current.expand({
          chatConfig: {
            ...chatConfig,
            botMetadata: {
              conversationId,
              ...(evaluationMode && { evaluationMode }),
            },
          },
        });
      }
    }, 100);
    return () => {
      clearTimeout(timer);
    };
  }, [conversationId, trafficSource]);

  const handleMessage = useCallback((event: MessageEvent) => {
    try {
      // TODO: For security, you might want to validate event.origin here
      // if (event.origin !== "expected-origin") return;
      console.log('Received message from parent:', event.data);
      const { type, payload } = event.data;

      switch (type) {
        case 'CHAT_BOT__GET_META_DATA':
          if (payload) {
            const parsedConfig = parseIfString(payload, {});
            const { chatConfig: _chatConfig = {}, tempConfig } = parsedConfig;
            // Extract all the url params and add them to the expertMetaData and add it to the chatConfig
            const expertMetaData = _chatConfig.expertMetadata || {};
            const urlParams = new URLSearchParams(window.location.search);
            urlParams.forEach((value, key) => {
              expertMetaData[key] = value;
            });
            _chatConfig.expertMetadata = expertMetaData;

            // Optionally expand the bot with the new config immediately
            if (chatBotRef?.current) {
              chatBotRef.current.expand({
                chatConfig: _chatConfig,
                tempConfig,
              });
            }
          }
          break;
          // Add more message types here as needed
        default:
          console.log('Unhandled message type:', type);
          break;
      }
    } catch (error) {
      console.error('Error processing message from parent:', error);
    }
  }, []);

  useEffect(() => {
    window.parent.postMessage(
        {
          type: 'CHAT_BOT__BRIDGE__GET_META_DATA',
        },
        '*', // Using '*' for broad compatibility, consider restricting to specific origin in production
    );

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [handleMessage]);

  const onViewStateChange = useCallback((viewState: ChatBotViewState) => {
    if (viewState === 'collapsed') {
      console.log('Holiday chat collapsed');
      fireMinimizedEvent();
    }
  }, []);

  const onActionCb = useCallback(
      ({ lob, actionType, actionPayload }: OnActionProps) => {
        switch (actionType) {
          case ActionTypes.MessageAction:
            fireAssistAction({
              lob,
              unreadCount: actionPayload.unreadCount as string,
              payload: actionPayload.payload as Record<string, unknown>,
            });
            break;
          case ActionTypes.Analytics:
            fireTrackingAction({
              lob,
              payload: actionPayload as Record<string, unknown>,
            });
            break;
          default:
            console.log('unknown event HolidayChatWeb', { actionPayload });
        }
      },
      [],
  );

  return (
      <TravelPlexBot
          onViewStateChange={onViewStateChange}
          ref={chatBotRef}
          onAction={onActionCb}
      />
  );
};
