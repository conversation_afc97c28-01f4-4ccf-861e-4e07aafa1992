export type ChatBotViewState = 'expanded' | 'collapsed';

export enum ActionTypes {
  MessageAction = 'MessageAction',
  ContextChange = 'ContextChange',
  OptionsClick = 'OptionsClick',
  Analytics = 'Analytics',
}

export type OnActionProps = {
  lob: string;
  actionType: ActionTypes;
  actionPayload: Record<string, unknown>;
};

type DeeplinkHandler = (deeplink: string) => boolean;

export type TravelPlexBotProps = {
  /* This is used for analytics */
  onViewStateChange: (viewState: ChatBotViewState) => void;
  chatConfig?: ChatContext;

  // to get the context from the parent that will be added to the chat context
  parentContextProvider?: object | null;

  // Callbacks to parent

  // callback for every action performed
  // Available actions
  // 1. MessageAction -> on every message sent
  // 2.ContextChange -> to change the context of the chat
  onAction?: (params: OnActionProps) => void;

  /*
   * If parent app wants to handle the deeplink, it can pass the deeplink handler
   * if parent app handles the deeplink, it should return true
   * if parent returns false, bot will handle the deeplink.
   *
   * @important: If parent handles deeplink but if it returns false,
   * the travelplex-library also will trigger open deeplink,
   * this can cause multiple link opening
   * */
  deeplinkHandler?: DeeplinkHandler;

  shouldPrefetch?: boolean;

  // IMPORTANT : This is used to open the chat on mount
  // make sure the parent app is not enabling this prop with multiple instance of Travelplex comp mounted
  parentPage?: string;

  isNativeMount?: boolean;

  startupTraceId?: string;
  forwardRef?: React.Ref<TravelPlexBotRef | null>;
  isDebugMode?: boolean;
  shouldMinimizeAfterAssist?: boolean;
  compactHeightMode?: boolean;
};

export type ExpandViewProps = {
  initialUserPrompt?: string | null;
  chatConfig: ChatContext;
  tempConfig?: Record<string, Record<string, unknown>>;
  forceFetch?: boolean;
  invalidateChatViewData?: boolean;
};

// Define the ref interface for the TravelPlexBot component
export type TravelPlexBotRef = {
  expand: (props: ExpandViewProps) => void;
  collapse: () => void;
};
