import { HolidayTrackingData } from './types';
import { HolidayTrackingParams } from '../constants/holidayTracking';

export class TrackingEvent {
  static org = 'MMT';

  // Setters
  static setOrg(org: Org) {
    this.org = org;
  }

  // Helpers
  static #getMMTTrackingEventDetails(value: string) {
    return {
      bridgeEventDetail: {
        pdtTrackingId: value,
      },
    };
  }
  static #getGITrackingEventDetails(value: string, ctaType: 'button' = 'button') {
    return {
      omnitureEventDetail: {
        ctaName_v29: value,
        ctaType_v34: ctaType,
        ctaComponentName_v35: 'gia_chatbot',
      },
      bridgeEventDetail: {
        pdtTrackingId: value,
      },
      pdtEventDetail: {
        event_name: 'client_action.name',
        event_value: value,
      },
      apmEventDetail: {
        transactionName: `user_int_${value}`,
      },
    };
  }

  // Tracking Events
  static payload_IntroductoryPopupShown() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_introductory_shown');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_introductory_shown');
    }
  }
  static payload_sendMessageAllModes() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_send_msg_final');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_send_msg_final');
    }
  }
  static payload_CardClicked(lob: string) {
    switch (this.org) {
      case 'MMT':
      default:
        return {
          bridgeEventDetail: {
            pdtTrackingId: 'travelplex_card_clicked',
            lob,
          },
        };
      case 'GI':
        return {
          bridgeEventDetail: {
            pdtTrackingId: 'gia_card_clicked',
            lob,
          },
        };
    }
  }
  static payload_NewChatClicked() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails(
          'travelplex_chat_menu_new_chat_clicked',
        );
      case 'GI':
        return this.#getGITrackingEventDetails('gia_chat_menu_new_chat_clicked');
    }
  }
  static payload_HistoryClicked() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails(
          'travelplex_chat_menu_history_clicked',
        );
      case 'GI':
        return this.#getGITrackingEventDetails('gia_chat_menu_history_clicked');
    }
  }
  static payload_BookmarkClicked() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails(
          'travelplex_chat_menu_bookmark_clicked',
        );
    }
  }
  static payload_PromptWithoutImagesClicked(prompt: string) {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails(
          `travelplex_welcome_prompt_without_images_${prompt}`,
        );
      case 'GI':
        return this.#getGITrackingEventDetails(
          `gia_welcome_prompt_without_images_${prompt}`,
        );
    }
  }
  static payload_WelcomePromptClicked(prompt: string) {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails(
          `travelplex_welcome_prompt_${prompt}`,
        );
      case 'GI':
        return this.#getGITrackingEventDetails(`gia_welcome_prompt_${prompt}`);
    }
  }
  static payload_PromptClicked() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_prompt_tap');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_prompt_tap');
    }
  }
  static payload_SurfacePromptClicked(prompt: string) {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails(
          `travelplex_surface_prompt_${prompt}`,
        );
      case 'GI':
        return this.#getGITrackingEventDetails(`gia_surface_prompt_${prompt}`);
    }
  }
  static payload_ChatBotMinimizedClicked() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_minimized');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_minimized');
    }
  }
  static payload_SendMessageClicked() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_send_msg');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_send_msg');
    }
  }
  static payload_SpeechToTextClicked() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_record_audio');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_record_audio');
    }
  }
  static payload_SpeechToTextSuccess() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_audio_converted');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_audio_converted');
    }
  }
  static payload_SpeechToTextCancelled() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_audio_cancel');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_audio_cancel');
    }
  }
  static payload_FiltersRemovePromptClicked() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_filters_removed_chat');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_results_removed_chat');
    }
  }
  static payload_LikeMessageClicked() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_feedback_liked');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_feedback_liked');
    }
  }
  static payload_DislikeMessageClicked() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_feedback_disliked');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_feedback_disliked');
    }
  }
  static payload_TalkToAgentClicked() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_help_cta_clicked');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_help_cta_clicked');
    }
  }
  static payload_AgentHelpOptionsShown() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_agent_options_shown');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_agent_options_shown');
    }
  }
  static payload_ChatWithAgentClicked() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_agent_chat_click');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_agent_chat_click');
    }
  }

  static payload_WifiCallingClicked() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails(
          'travelplex_agent_wifi_calling_click',
        );
      case 'GI':
        return this.#getGITrackingEventDetails('gia_agent_wifi_calling_click');
    }
  }

  static payload_ChatWithAgentAssigned() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_agent_assigned');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_agent_assigned');
    }
  }
  static payload_ChatWithAgentUserClosed() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_user_closed_chat');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_user_closed_chat');
    }
  }
  static payload_ScheduleCallbackClicked() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_schedule_call');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_schedule_call');
    }
  }
  static payload_ScheduleCallbackSlotConfirmed() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_schedule_confirmed');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_schedule_confirmed');
    }
  }

  static payload_trackHolidays(params: HolidayTrackingParams): HolidayTrackingData {
    const { mv15, m_c54, prop14, eventName, eventType, eventValue } = params;

    switch (this.org) {
      case 'MMT':
      default:
        return {
          bridgeEventDetail: {
            pdtTrackingId: 'travelplex_call_cta',
            omnitureEventDetail: {
              mv15,
              m_c54,
              prop14,
            },
            pdtEventDetails: {
              event_name: eventName,
              event_value: eventValue,
              eventType,
            },
            actionLobHldTravelPlex: true,
          },
        };
    }
  }

  static payload_CallNowClicked() {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails('travelplex_call_cta');
      case 'GI':
        return this.#getGITrackingEventDetails('gia_call_cta');
    }
  }
  static payload_Generic(event: string) {
    switch (this.org) {
      case 'MMT':
      default:
        return this.#getMMTTrackingEventDetails(event);
      case 'GI':
        return this.#getGITrackingEventDetails(event);
    }
  }
}
