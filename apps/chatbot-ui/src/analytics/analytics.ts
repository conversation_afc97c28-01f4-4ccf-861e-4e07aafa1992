import { ActionTypes } from '../containers/types';
import { useAppStateStore } from '../store/app';
import { InitAnalyticsConfig } from './types';

interface IAnalytics {
  init(args: InitAnalyticsConfig): void;
  trackClickEvent(args: { bridgeEventDetail: Record<string, any> }): void;
}

class CAnalytics implements IAnalytics {
  #lob: string | null | undefined;
  #org: Org | null | undefined;

  init(args: InitAnalyticsConfig) {
    const org: Org = 'MMT';
    const chatContext = args.chatContext;
    const { lob } = chatContext?.contextMetadata?.pageContext || {};
    this.#lob = lob;
    this.#org = org;
  }
  trackClickEvent(args: { bridgeEventDetail: Record<string, any> }) {
    const lob = this.#lob ?? 'NA';
    if (this.#org === 'MMT') {
      // BRIDGE METHOD CALLING
      const onActionBridgeCB = useAppStateStore.getState().onAction;
      const bridgePayload = {
        tracking: Object.entries(args.bridgeEventDetail).map(([key, value]) => ({
          [key]: value,
        })),
      };
      onActionBridgeCB?.({
        lob,
        actionType: ActionTypes.Analytics,
        actionPayload: bridgePayload,
      });
    }
  }
}

export const Analytics = new CAnalytics();
