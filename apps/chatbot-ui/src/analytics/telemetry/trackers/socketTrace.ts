import {
  Metric,
  newTraceId,
  sendTraceToServer,
  telemetryLogger,
} from '../telemetryUtils';

type SocketTrace = {
  id: string;
  initiatedConnection: number | null;
  connectionEstablished: number | null;
  firstHeartBeatSent: number | null;
  firstHeartBeatReceived: number | null;
  connectError: number | null;
  heartBeatError: number | null;
};

const socketTraces = new Map<string, SocketTrace>();

export function startNewSocketTrace() {
  const id = newTraceId();
  telemetryLogger.debug('startNewSocketTrace', id);
  socketTraces.set(id, {
    id,
    initiatedConnection: Date.now(),
    connectionEstablished: null,
    firstHeartBeatSent: null,
    firstHeartBeatReceived: null,
    connectError: null,
    heartBeatError: null,
  });
  return id;
}

export function updateSocketTrace(
  id: string | null,
  phase: keyof Omit<SocketTrace, 'id'>,
) {
  telemetryLogger.debug('updateSocketTrace', id, phase);
  if (id === null) {
    return;
  }
  const trace = socketTraces.get(id);
  if (!trace) {
    telemetryLogger.warn('No trace found for id', id);
    return;
  }
  if (trace[phase] !== null) {
    telemetryLogger.warn('Trace already updated for phase', id, phase);
    return;
  }
  socketTraces.set(id, {
    ...trace,
    [phase]: Date.now(),
  });

  // if terminal phases are reached, dispatch the trace
  if (
    phase === 'firstHeartBeatReceived' ||
    phase === 'heartBeatError' ||
    phase === 'connectError'
  ) {
    dispatchSocketTrace(id);
  }
}

function dispatchSocketTrace(id: string) {
  telemetryLogger.debug('dispatchSocketTrace', id);
  const trace = socketTraces.get(id);
  if (!trace) {
    telemetryLogger.warn('No trace found for id during dispatch', id);
    return;
  }
  socketTraces.delete(id);
  telemetryLogger.debug('Sending socket trace to server', id);
  sendTraceToServer(convertToMetrics(trace));
}

export function logChatClosedWithPendingBuffer() {
  sendTraceToServer([
    {
      type: 'count',
      count: 1,
      category: 'socket',
      key: 'BUFFER_APP_EXIT',
    },
  ]);
}

export function logChatAbnormalClose(closeCode: number) {
  sendTraceToServer([
    {
      type: 'count',
      count: 1,
      category: 'socket',
      key: 'SOCKET_ABNORMAL_CLOSE',
    },
  ]);
}

function convertToMetrics(trace: SocketTrace): Array<Metric> {
  const metrics: Array<Metric> = [];
  const {
    initiatedConnection,
    connectionEstablished,
    firstHeartBeatSent,
    firstHeartBeatReceived,
    connectError,
    heartBeatError,
  } = trace;

  if (initiatedConnection && connectionEstablished) {
    metrics.push({
      type: 'timer',
      startTime: initiatedConnection,
      endTime: connectionEstablished,
      category: 'socket',
      key: 'SOCKET_CONNECTION_TIME',
    });
  }
  if (firstHeartBeatSent && firstHeartBeatReceived) {
    metrics.push({
      type: 'timer',
      startTime: firstHeartBeatSent,
      endTime: firstHeartBeatReceived,
      category: 'socket',
      key: 'SOCKET_HEARTBEAT_TIME',
    });
  }
  if (connectError) {
    metrics.push({
      type: 'count',
      count: 1,
      category: 'socket',
      key: 'SOCKET_CONNECTION_ERROR',
    });
  }
  if (heartBeatError) {
    metrics.push({
      type: 'count',
      count: 1,
      category: 'socket',
      key: 'SOCKET_HEARTBEAT_ERROR',
    });
  }

  return metrics;
}
