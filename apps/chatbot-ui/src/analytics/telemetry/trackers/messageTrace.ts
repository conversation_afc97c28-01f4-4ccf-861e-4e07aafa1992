import {
  Metric,
  newTraceId,
  sendTraceToServer,
  telemetryLogger,
} from '../telemetryUtils';

type MessageTrace = {
  id: string;
  sendClick: number | null;
  socketSend: number | null;
  ackReceived: number | null;
  loader: number | null;
  firstReply: number | null;
  end: number | null;
};
const msgTraces = new Map<string, MessageTrace>();

export function startNewMessageTrace() {
  const now = Date.now();
  const id = newTraceId();
  telemetryLogger.debug('startNewMessageTrace', id);
  msgTraces.set(id, {
    id,
    sendClick: now,
    socketSend: null,
    ackReceived: null,
    loader: null,
    firstReply: null,
    end: null,
  });
  return id;
}

export function updateMessageTrace(
  id: string,
  phase: keyof Omit<MessageTrace, 'id' | 'sendClick'>,
) {
  telemetryLogger.debug('updateMessageTrace', id, phase);

  const trace = msgTraces.get(id);
  if (!trace) {
    telemetryLogger.warn('No trace found for id', id);
    return;
  }

  if (trace[phase] !== null) {
    telemetryLogger.warn('Trace already updated for phase', id, phase);
    return;
  }
  msgTraces.set(id, {
    ...trace,
    [phase]: Date.now(),
  });
  if (phase === 'end') {
    dispatchTrace(id);
  }
}

function dispatchTrace(id: string) {
  telemetryLogger.debug('dispatchTrace', id);
  const trace = msgTraces.get(id);
  if (!trace) {
    telemetryLogger.warn('No trace found for id during dispatch', id);
    return;
  }
  msgTraces.delete(id);
  telemetryLogger.debug('Sending message trace to server', id);
  sendTraceToServer(convertToMetrics(trace));
}

function convertToMetrics(trace: MessageTrace): Array<Metric> {
  telemetryLogger.debug('convertToMetrics', trace.id);
  const metrics: Array<Metric> = [];
  const { sendClick, socketSend, ackReceived, loader, firstReply, end } = trace;
  if (sendClick !== null && socketSend !== null) {
    metrics.push({
      type: 'timer',
      category: 'message',
      key: 'MESSAGE_SEND_AFTER_CLICK',
      startTime: sendClick,
      endTime: socketSend,
    });
  }
  if (socketSend !== null && ackReceived !== null) {
    metrics.push({
      type: 'timer',
      category: 'message',
      key: 'MESSAGE_ACK_RECEIVED_AFTER_SEND',
      startTime: socketSend,
      endTime: ackReceived,
    });
  }
  if (socketSend !== null && loader !== null) {
    metrics.push({
      type: 'timer',
      category: 'message',
      key: 'MESSAGE_LOADER_AFTER_SEND',
      startTime: socketSend,
      endTime: loader,
    });
  }
  if (socketSend !== null && firstReply !== null) {
    metrics.push({
      type: 'timer',
      category: 'message',
      key: 'MESSAGE_FIRST_REPLY_AFTER_SEND',
      startTime: socketSend,
      endTime: firstReply,
    });
  }
  if (socketSend !== null && end !== null) {
    metrics.push({
      type: 'timer',
      category: 'message',
      key: 'MESSAGE_END_AFTER_SEND',
      startTime: socketSend,
      endTime: end,
    });
  }
  return metrics;
}

/**
 * Track # of message buffers
 * - that got buffered
 * - that were flushed / processed
 * - that got discarded
 * - that got abandoned by user leaving the chat
 * */

// ========== Track Message Buffers ==========

/* Track # of messages that got bufferred */
export function logBufferItemAdded() {
  sendTraceToServer([
    {
      type: 'count',
      count: 1,
      category: 'socket',
      key: 'BUFFER_QUEUED',
    },
  ]);
}

/* Track long waiting buffered messages  */
export function logBufferItemsNotProcessed(time: number) {
  sendTraceToServer([
    {
      type: 'count',
      count: 1,
      category: 'socket',
      key: `BUFFER_LATENCY_${time}SEC`,
    },
  ]);
}

/* Track discarded messages  */
export function logBufferDiscardedBySocket(count: number) {
  sendTraceToServer([
    {
      type: 'count',
      count,
      category: 'socket',
      key: 'BUFFER_DISCARDED',
    },
  ]);
}

/* Track messages successfully flushed/processed  */
export function logBufferProcessed(count: number) {
  sendTraceToServer([
    {
      type: 'count',
      count,
      category: 'socket',
      key: 'BUFFER_PROCESSED',
    },
  ]);
}

// ========== End of Track Message Buffers ==========
