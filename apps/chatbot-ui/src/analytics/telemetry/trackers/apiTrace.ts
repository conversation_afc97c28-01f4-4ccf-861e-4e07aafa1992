import {
  Metric,
  newTraceId,
  sendTraceToServer,
  telemetryLogger,
} from '../telemetryUtils';

type ApiTrace = {
  id: string;
  apiName: string;
  startTime: number;
  error: number | null;
  errorCode: string | null;
  endTime: number | null;
};
const apiTracesMap = new Map<string, ApiTrace>();

export function startNewApiTrace(apiName: string): string {
  const id = newTraceId();
  telemetryLogger.debug('startNewApiTrace', id);
  const trace: ApiTrace = {
    id,
    apiName,
    startTime: Date.now(),
    error: null,
    errorCode: null,
    endTime: null,
  };
  apiTracesMap.set(trace.id, trace);
  return trace.id;
}

export function updateApiTraceAsSuccess(traceId: string) {
  telemetryLogger.debug('updateApiTraceAsSuccess', traceId);
  const traceData = apiTracesMap.get(traceId);
  if (!traceData) {
    telemetryLogger.warn('traceData is empty. Please check the logic once!');
    return;
  }
  traceData.endTime = Date.now();
  dispatchTrace(traceId);
  // dispatch to analytics
}

export function updateApiTraceAsFailed(traceId: string, errorCode: string) {
  telemetryLogger.debug('updateApiTraceAsFailed', traceId);
  const traceData = apiTracesMap.get(traceId);
  if (!traceData) {
    telemetryLogger.warn('traceData is empty. Please check the logic once!');
    return;
  }
  traceData.errorCode = errorCode;
  dispatchTrace(traceId);
}

function dispatchTrace(id: string) {
  const trace = apiTracesMap.get(id);
  if (!trace) {
    telemetryLogger.warn('traceData is empty. Please check the logic once!');
    return;
  }
  apiTracesMap.delete(id);

  telemetryLogger.debug('sending apiTraces to server');
  sendTraceToServer(convertToMetrics(trace));
}

function convertToMetrics(trace: ApiTrace): Array<Metric> {
  const metrics: Array<Metric> = [];
  if (trace.startTime && trace.endTime) {
    metrics.push({
      key: `API_${trace.apiName}_RT`,
      category: 'api',
      type: 'timer',
      startTime: trace.startTime,
      endTime: trace.endTime,
    });
  }
  if (trace.error) {
    const suffix = trace.errorCode ? `_${trace.errorCode}` : '';
    metrics.push({
      key: `API_${trace.apiName}_ERROR${suffix}`,
      category: 'api',
      type: 'count',
      count: 1,
    });
  }
  return metrics;
}
