import {
  Metric,
  newTraceId,
  sendTraceToServer,
  telemetryLogger,
} from '../telemetryUtils';

export type StartupTrace = {
  id: string;
  parentType: ParentType;
  isColdStart: boolean;
  ctaClick: number;
  onReady: number | null;
  expanded: number | null;
  loader: number | null;
  renderedScreen: null | 'NEW_CHAT' | 'WELCOME' | 'MESSAGES';
  interactive: number | null;
  error: number | null;
};

const startupTraces = new Map<string, StartupTrace>();

export type ParentType = 'NativeView' | 'NativePage' | 'React';

let isColdStart = true;

export function startNewStartupTrace(
  parentType: ParentType,
  ctaClickTimestamp: number,
): string {
  const now = ctaClickTimestamp;
  const id = newTraceId();
  telemetryLogger.debug('startNewStartupTrace', id, parentType, now);
  startupTraces.set(id, {
    id,
    parentType,
    isColdStart,
    ctaClick: now,
    onReady: null,
    expanded: null,
    loader: null,
    interactive: null,
    renderedScreen: null,
    error: null,
  });
  isColdStart = false;
  return id;
}

export function updateStartupTrace(
  id: string,
  phase: keyof Omit<StartupTrace, 'id' | 'ctaClick' | 'interactive'>,
) {
  telemetryLogger.debug('updateStartupTrace', id, phase);
  const trace = startupTraces.get(id);
  if (!trace) {
    telemetryLogger.debug('No trace found for id', id);
    return;
  }
  if (trace[phase] !== null) {
    telemetryLogger.debug('Trace already updated for phase', id, phase);
    return;
  }
  startupTraces.set(id, {
    ...trace,
    [phase]: Date.now(),
  });
  if (phase === 'error') {
    dispatchStartupTrace(id);
  }
}

export function updateStartupTraceAsInteractive(
  id: string,
  screen: StartupTrace['renderedScreen'],
) {
  telemetryLogger.debug('updateStartupTraceAsInteractive', id, screen);
  const trace = startupTraces.get(id);
  if (!trace) {
    telemetryLogger.debug('No trace found for id', id);
    return;
  }
  trace.interactive = Date.now();
  trace.renderedScreen = screen;
  dispatchStartupTrace(id);
}

export function logPageError(
  pageName: string,
  errMsg: string | undefined = undefined,
) {
  sendTraceToServer([
    {
      key: `PAGE_ERROR_${pageName.toUpperCase()}`,
      category: 'startup',
      type: 'count',
      count: 1,
      extra: {
        errMsg,
      },
    } as Metric,
  ]);
}

function dispatchStartupTrace(id: string) {
  telemetryLogger.debug('dispatchStartupTrace', id);
  const trace = startupTraces.get(id);
  if (!trace) {
    telemetryLogger.debug('No trace found for id during dispatch', id);
    return;
  }
  startupTraces.delete(id);
  telemetryLogger.debug('Sending startup trace to server', id);
  sendTraceToServer(convertToMetrics(trace));
}

function convertToMetrics(trace: StartupTrace): Array<Metric> {
  telemetryLogger.debug('convertToMetrics', trace.id);
  const traces: Array<Metric> = [];
  if (trace.ctaClick && trace.ctaClick > 0 && trace.onReady) {
    traces.push({
      key: trace.isColdStart ? 'JS_LOAD_COLD' : 'JS_LOAD_HOT',
      category: 'startup',
      type: 'timer',
      startTime: trace.ctaClick,
      endTime: trace.onReady,
    });
  }

  if (trace.onReady && trace.expanded) {
    traces.push({
      key: 'SHOW_MODAL_AFTER_JS_LOAD',
      category: 'startup',
      type: 'timer',
      startTime: trace.onReady,
      endTime: trace.expanded,
    });
  }

  if (!trace.onReady && trace.expanded) {
    trace.onReady = trace.expanded;
  }

  if (trace.onReady && trace.loader) {
    traces.push({
      key: 'SHOW_LOADER_AFTER_JS_LOAD',
      category: 'startup',
      type: 'timer',
      startTime: trace.onReady,
      endTime: trace.loader,
    });
  }

  if (trace.onReady && trace.interactive && trace.renderedScreen) {
    traces.push({
      key: `SHOW_${trace.renderedScreen}_AFTER_JS_LOAD`,
      category: 'startup',
      type: 'timer',
      startTime: trace.onReady,
      endTime: trace.interactive,
    });
  }

  if (trace.error) {
    traces.push({
      key: 'ERROR_CHAT_WHILE_LOAD',
      category: 'startup',
      type: 'count',
      count: 1,
    });
  }
  return traces;
}
