import React, { ReactNode, useEffect } from 'react';
import {
  setGlobalTelemetryMutation,
  useSendTelemetryMutation,
} from './telemetryUtils';

interface TelemetryProviderProps {
  children: ReactNode;
}

export function TelemetryProvider({ children }: TelemetryProviderProps) {
  const telemetryMutation = useSendTelemetryMutation();

  useEffect(() => {
    // Set the global mutation instance so sendTraceToServer can use it
    setGlobalTelemetryMutation(telemetryMutation);

    // Cleanup on unmount
    return () => {
      setGlobalTelemetryMutation(null);
    };
  }, [telemetryMutation]);

  return <>{children}</>;
}

// Hook for components that want to directly use the telemetry mutation
export function useTelemetry() {
  return useSendTelemetryMutation();
}
