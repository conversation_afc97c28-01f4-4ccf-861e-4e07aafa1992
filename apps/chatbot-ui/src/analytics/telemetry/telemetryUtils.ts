import { Logger } from '../../utils/logger';
import { useAppStateStore } from '../../store/app';
import { fetchWrapper } from '../../network/api/utils/fetchWrapper';
import { Endpoints } from '../../network/endpoints';
import { useMutation } from 'react-query';

let traceIdCounter = 0;
export const newTraceId = () => `${Date.now()}-${traceIdCounter++}`;

export const telemetryLogger = Logger.createLogger({
  tag: 'Telemetry',
  level: 'VERBOSE',
});

export type TelemetryConfig = {
  api?: boolean;
  message?: boolean;
  socket?: boolean;
  startup?: boolean;
};
/** this is used to filter in Grafana */
export type MetricCategory = keyof TelemetryConfig;
export type Metric =
  | {
      type: 'timer';
      key: string;
      startTime: number;
      endTime: number;
      category: MetricCategory;
    }
  | {
      type: 'count';
      key: string;
      count: number;
      category: MetricCategory;
    };

// React Query mutation hook for sending telemetry
export function useSendTelemetryMutation() {
  return useMutation<void, Error, Array<Metric>>({
    mutationFn: async (metrics: Array<Metric>) => {
      const fetch = fetchWrapper('SEND_METRICS', { disableTrace: true });
      await fetch(Endpoints.SEND_METRICS, {
        method: 'POST',
        body: {
          traces: metrics,
        },
      });
    },
    retry: 3,
    retryDelay: (attemptIndex) => {
      // Exponential backoff with jitter: 2s, 4s, 16s + random jitter
      const baseDelay = 2000 * Math.pow(2, attemptIndex);
      const jitter = Math.random() * 1000;
      return Math.min(baseDelay + jitter, 30000); // Cap at 30 seconds
    },
    onMutate: (metrics) => {
      telemetryLogger.debug('Sending telemetry metrics:', metrics);
    },
    onError: (error) => {
      telemetryLogger.warn('Error sending traces to server after retries:', error);
    },
    onSuccess: (data, metrics) => {
      telemetryLogger.info('Sent traces to server successfully');
      telemetryLogger.debug('sent traces:', metrics);
    },
  });
}

// Global mutation instance for non-React contexts invocations
let globalTelemetryMutation: ReturnType<typeof useSendTelemetryMutation> | null =
  null;

export function setGlobalTelemetryMutation(
  mutation: ReturnType<typeof useSendTelemetryMutation> | null,
) {
  globalTelemetryMutation = mutation;
}

export async function sendTraceToServer(metrics: Array<Metric>) {
  telemetryLogger.debug('sendTraceToServer', metrics);

  if (!metrics.length) {
    // No traces to send
    telemetryLogger.warn('Traces are empty');
    return;
  }

  const { telemetryConfig } = useAppStateStore.getState();
  if (!telemetryConfig) {
    telemetryLogger.info('Telemetry not enabled');
    return;
  }
  const enabledMetrics = metrics.filter((value) => telemetryConfig[value.category]);
  if (!enabledMetrics.length) {
    telemetryLogger.info('filtered out all metrics based on telemetry config');
    return;
  }

  if (__DEV__) {
    telemetryLogger.info('Ignoring traces in dev mode');
    return;
  }

  // Use the global mutation instance if available
  if (globalTelemetryMutation) {
    globalTelemetryMutation.mutate(enabledMetrics);
    return;
  }

  // Fallback: direct API call (without retry for backward compatibility)
  try {
    const fetch = fetchWrapper('SEND_METRICS', { disableTrace: true });
    await fetch(Endpoints.SEND_METRICS, {
      method: 'POST',
      body: {
        traces: enabledMetrics,
      },
    });
    telemetryLogger.info('Sent traces to server:');
    telemetryLogger.debug('sent traces:', enabledMetrics);
  } catch (e) {
    telemetryLogger.warn('Error sending traces to server:', e);
  }
}
