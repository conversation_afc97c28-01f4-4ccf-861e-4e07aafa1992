export type InitAnalyticsConfig = {
  chatContext: ChatContext;
};

export type HolidayBridgeEventDetail = {
  pdtTrackingId: string;
  omnitureEventDetail: {
    mv15?: string;
    m_c54?: string;
    prop14?: string;
  };
  pdtEventDetails: {
    event_name?: string;
    event_value?: string;
    eventType?: string;
  };
  actionLobHldTravelPlex?: boolean;
}

export type HolidayTrackingData = {
  bridgeEventDetail: HolidayBridgeEventDetail;
}
