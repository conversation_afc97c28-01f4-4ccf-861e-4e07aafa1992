import { Platform } from 'react-native';
import {
  getValueFromUrl,
  isDebugMode,
  isEvaluationMode,
  isMyraQAEnv,
} from '../utils/webUtils';

const ProdHost = {
  hostName: 'travelbot.makemytrip.com',
  httpScheme: 'https',
  wsScheme: 'wss',
} as const;

const ProdInternalHost = {
  hostName: 'platform-apollo.ecs.mmt',
  httpScheme: 'http',
  wsScheme: 'ws',
} as const;

const devHost = {
  hostName: 'apolloalpha-dev.local:8088',
  httpScheme: 'http',
  wsScheme: 'ws',
} as const;

const QAHost = {
  hostName: '*************:8080',
  httpScheme: 'http',
  wsScheme: 'ws',
} as const;

const QA_FQDN_Host = {
  hostName: 'apolloalpha.makemytrip.com',
  httpScheme: 'https',
  wsScheme: 'wss',
} as const;

const ENV_HOSTS = {
  PROD: ProdHost,
  QA_IP: QAHost,
  QA_FQDN: QA_FQDN_Host,
  PROD_INTERNAL: ProdInternalHost,
  DEV_INTERNAL: devHost,
} as const;

type HostConfigType =
  | typeof ProdHost
  | typeof QAHost
  | typeof QA_FQDN_Host
  | typeof ProdInternalHost
  | typeof devHost;

const getWebHostConfig = (): HostConfigType => {
  // Evaluation mode takes highest priority
  if (isEvaluationMode()) {
    return ENV_HOSTS.PROD;
  }
  // QA variant configuration
  if (process.env.BUILD_VARIANT === 'qa') {
    // Only allow pointTo switching in debug mode for security
    if (isMyraQAEnv()) {
      return ENV_HOSTS.QA_FQDN;
    }
    if (isDebugMode()) {
      const pointTo = getValueFromUrl('pointTo');
      if (pointTo) {
        const hostMap: Record<string, HostConfigType> = {
          'qa-ip': ENV_HOSTS.QA_IP,
          qa: ENV_HOSTS.QA_FQDN,
          devLocal: ENV_HOSTS.DEV_INTERNAL,
          prodInternal: ENV_HOSTS.PROD_INTERNAL,
        };
        return hostMap[pointTo] || ENV_HOSTS.QA_FQDN; // Default to QA_FQDN for qa variant
      }
    }
    // Default QA host when no pointTo or not in debug mode
    return ENV_HOSTS.QA_FQDN;
  }

  // Production variant or default
  return ENV_HOSTS.PROD;
};

const HostConfig: HostConfigType =
  Platform.OS === 'web' ? getWebHostConfig() : ENV_HOSTS.PROD;

const BASE_URL =
  `${HostConfig.httpScheme}://${HostConfig.hostName}/travelplex` as const;
const BASE_WS_URL = `${HostConfig.wsScheme}://${HostConfig.hostName}` as const;

export const Endpoints = {
  WEB_SOCKET: `${BASE_WS_URL}/travelplex/ws/websocket`,
  STT_WEB_SOCKET: `${BASE_WS_URL}/travelplex/stt/stream`,
  // TTS: 'http://localhost:8088/travelplex/tts/synthesize',
  TTS: `${BASE_URL}/tts/synthesize`, // Changed to use HTTP instead of WebSocket
  GET_CONVERSATIONS_HISTORY: `${BASE_URL}/conversations`,
  GET_CONVERSATION_MESSAGES: `${BASE_URL}/messages`,
  ADD_BOOKMARK: `${BASE_URL}/addBookmarks`,
  GET_BOOKMARKS: `${BASE_URL}/getBookmarks`,
  REMOVE_BOOKMARK: `${BASE_URL}/removeBookmarks`,
  POST_FEEDBACK: `${BASE_URL}/feedback`,
  REPORT_ISSUE: `${BASE_URL}/feedback/data`,
  GET_TRIP_SUMMARY: `${BASE_URL}/tripSummary`,
  GET_NEW_CHAT_VIEW: `${BASE_URL}/newchatview`,
  GET_HELPING_HANDS: `${BASE_URL}/apollo/helpingHand/details`,
  GET_HELPING_HANDS_CALLBACK_SLOTS: `${BASE_URL}/apollo/helpingHand/callbackSlots`,
  POST_HELPING_HANDS_SCHEDULE_CALLBACK_SLOTS: `${BASE_URL}/apollo/helpingHand/scheduleCallback`,
  SEND_METRICS: `${BASE_URL}/clientMetrics`,
  ITINERARY: `${BASE_URL}/itinerary`,
  DELETE_CONVERSATION: `${BASE_URL}/conversation`,
} as const;
