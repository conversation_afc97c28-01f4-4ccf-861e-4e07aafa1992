import { useMutation, useQuery } from 'react-query';
import {
  fetchConversationMessages,
  GetConversationMessagesRequest,
  GetConversationMessagesResponse,
} from '../api/conversationApi';
import { queryClient } from '../../containers/queryClientInstance';
import { conversationMessagesSuccessCallback } from '../../store/messages/useConversationMessages';

type UseConversationMessagesQueryArgs = Pick<
  GetConversationMessagesRequest,
  'conversationId'
> & {
  enabled?: boolean;
  onSuccess?: (data: GetConversationMessagesResponse) => void;
};

type UseConversationMessagesMutationArgs = {
  onSuccess?: (data: GetConversationMessagesResponse) => void;
};

const getConversationsQueryKey = (conversationId: string) => [
  'CONVERSATIONS_MESSAGES',
  conversationId,
];

export const useConversationMessagesQuery = (
  args: UseConversationMessagesQueryArgs,
) => {
  const { conversationId, enabled, onSuccess } = args;
  const queryData = useQuery<GetConversationMessagesResponse>({
    queryKey: getConversationsQueryKey(conversationId),
    queryFn: () => fetchConversationMessages({ conversationId }),
    enabled,
    onSuccess,
    retry: 3,
  });

  return { ...queryData };
};

export const useConversationMessagesMutation = (
  args: UseConversationMessagesMutationArgs,
) => {
  const { onSuccess } = args;
  const mutation = useMutation<GetConversationMessagesResponse, unknown, string>({
    mutationFn: (conversationId: string) =>
      fetchConversationMessages({ conversationId }),
    onSuccess,
  });

  return { ...mutation };
};

export async function invalidateConversationMessagesQuery(conversationId: string) {
  await queryClient.invalidateQueries(getConversationsQueryKey(conversationId));
}

export async function setConversationMessagesData(
  conversationId: string,
  data: GetConversationMessagesResponse,
) {
  queryClient.setQueryData(getConversationsQueryKey(conversationId), data);
  // TODO: Instead of creating a common function for success callback
  // Check if we can callback from the useConversationMessagesQuery hooks callback itself
  conversationMessagesSuccessCallback(data, conversationId);
}
