import { useQuery } from 'react-query';
import { api_getItinerary } from '../api';
import { queryClient } from '../../containers/queryClientInstance';
import { ItineraryResponse } from '../api/conversationApi';

const itineraryQueryKey = 'ITINERARY';
export const useItineraryQuery = (conversationId: string) => {
  const queryData = useQuery<ItineraryResponse>({
    queryKey: itineraryQueryKey,
    queryFn: () => api_getItinerary(conversationId),
    retry: false,
  });
  return { ...queryData };
};

export const invalidateConversationsHistoryQuery = () => {
  queryClient.invalidateQueries(itineraryQueryKey);
};
