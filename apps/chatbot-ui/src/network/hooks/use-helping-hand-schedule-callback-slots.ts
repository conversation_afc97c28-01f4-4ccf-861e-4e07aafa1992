import { useMutation } from 'react-query';
import { api_postScheduleCallbackSlots } from '../api';
import { queryClient } from '../../containers/queryClientInstance';
import {
  PostScheduleCallbackArgs,
  PostScheduleCallbackSlotsResponse,
} from '../api/helpingHandScheduleCallbackSlotsApi';
import { ApiError } from '../api/utils/fetchWrapper';
import { useAppStateStore } from '../../store/app';

const helpingHandScheduleCallbackSlotsQueryKey =
  'HELPING_HAND_SCHEDULE_CALLBACK_SLOTS';
export const useHelpingHandScheduleCallbackSlotsMutation = () => {
  const { chatContext } = useAppStateStore();

  const queryData = useMutation<
    PostScheduleCallbackSlotsResponse,
    ApiError,
    PostScheduleCallbackArgs
  >(
    (args: PostScheduleCallbackArgs) =>
      api_postScheduleCallbackSlots({ ...args, chatContext }),
    {
      mutationKey: helpingHandScheduleCallbackSlotsQueryKey,
    },
  );

  return { ...queryData };
};

export const invalidateHelpingHandCallbackSlotsQuery = () => {
  queryClient.invalidateQueries(helpingHandScheduleCallbackSlotsQueryKey);
};
