//@ts-nocheck
import { useQuery } from 'react-query';
import { api_getHelpingHandDetails } from '../api';
import { queryClient } from '../../containers/queryClientInstance';
import { GetHelpingHandsOptionsResponse } from '../api/helpingHandDetailsApi';
import { useAppStateStore } from '../../store/app';
// import { useMessageStore } from '../../store/messages';

const helpingHandQueryKey = 'HELPING_HAND_DETAILS';
export const useHelpingHandDetailsQuery = () => {
  //const { activeConversationId } = useMessageStore();
  // const conversationId = activeConversationId || 'draft';

  const { chatContext } = useAppStateStore();

  const queryData = useQuery<GetHelpingHandsOptionsResponse>({
    queryKey: helpingHandQueryKey,
    queryFn: () =>
      api_getHelpingHandDetails({
        context: chatContext?.context,
        lob: chatContext?.context.lob,
        lobMetadata: chatContext?.expertMetadata,
      }),
    cacheTime: 300000, // 5 minutes in milliseconds
    staleTime: 300000,
  });
  return { ...queryData };
};

export const invalidateHelpingHandDetailsQuery = () => {
  queryClient.invalidateQueries(helpingHandQueryKey);
};
