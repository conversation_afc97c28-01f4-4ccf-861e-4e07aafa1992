import { useQuery } from 'react-query';
import { api_getConversationsHistory } from '../api';
import { queryClient } from '../../containers/queryClientInstance';
import { GetConversationsHistoryResponse } from '../api/conversationApi';

const historyQueryKey = 'CONVERSATIONS_HISTORY';
export const useConversationsHistoryQuery = () => {
  const queryData = useQuery<GetConversationsHistoryResponse>({
    queryKey: historyQueryKey,
    queryFn: () => api_getConversationsHistory(),
  });
  return { ...queryData };
};

export const invalidateConversationsHistoryQuery = () => {
  queryClient.invalidateQueries(historyQueryKey);
};
