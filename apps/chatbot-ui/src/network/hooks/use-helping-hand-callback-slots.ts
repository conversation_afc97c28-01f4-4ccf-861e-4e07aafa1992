//@ts-nocheck
import { useMutation } from 'react-query';
import { api_getHelpingHandCallbackSlots } from '../api';
import { queryClient } from '../../containers/queryClientInstance';
import {
  GetCallbackSlotsArgs,
  GetHelpingHandsCallbackSlotsResponse,
} from '../api/helpingHandCallbackSlotsApi';
import { useAppStateStore } from '../../store/app';
import React from 'react';
import { useHelpingHandDetailsQuery } from './use-helping-hand-details';
import { ApiError } from '../api/utils/fetchWrapper';

const helpingHandCallbackSlotsQueryKey = 'HELPING_HAND_CALLBACK_SLOTS';
export const useHelpingHandCallbackSlotsQuery = () => {
  const { chatContext } = useAppStateStore();

  const { data: helpingHandsData } = useHelpingHandDetailsQuery();
  const helpingHandsMetaData = helpingHandsData?.lobMetaData || {};

  const queryData = useMutation<
    GetHelpingHandsCallbackSlotsResponse,
    ApiError,
    GetCallbackSlotsArgs
  >((args: GetCallbackSlotsArgs) => api_getHelpingHandCallbackSlots(args));

  React.useEffect(() => {
    queryData.mutate({
      lobName: chatContext?.context.lob,
      pageName: helpingHandsMetaData?.pageName,
      uniqueIdentifier: helpingHandsMetaData?.uniqueIdentifier,
    });
  }, []);

  return { ...queryData };
};

export const invalidateHelpingHandCallbackSlotsQuery = () => {
  queryClient.invalidateQueries(helpingHandCallbackSlotsQueryKey);
};
