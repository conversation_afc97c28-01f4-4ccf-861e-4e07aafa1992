import { Endpoints } from '../endpoints';
import { GetConversationMessagesResponseSuccess } from './conversationApi';
import { fetchWrapper } from './utils/fetchWrapper';

import { TelemetryConfig } from '../../analytics/telemetry';
import { StreamConfig } from '../../components/message/AnimatedMarkdownStream';

export type SessionMessagesResponse = GetConversationMessagesResponseSuccess;

export type GetNewChatViewResponse = {
  success: boolean;
  betaMessage: string | null;
  introMsg: string;
  introMsgSubTitleNew: string;
  introMsgSubTitleNewUser?: string;
  introMsgSubTitleMarkdown?: string;
  disclaimer: string;
  examples: PromptsWithImages;
  textBoxPlaceHolderTexts: string[];
  greetingMessage: string;
  isNewUser: boolean;
  userAllowed: boolean;
  recentConversations: ConversationHistoryGroup;
  showWelcomeScreen: boolean;
  sessionMessages: SessionMessagesResponse;
  suggestionOnAction?: boolean;
  showIngressMessage?: boolean;
  ingressMessage?: Message;
  featureConfig: FeatureConfig;
  apolloSocketConfig: ApolloSocketConfig;
  lobGreetingMessageContent: lobGreetingMessage;
  content: ContentsConfig;
  newChatContent: ContentsConfig;
  messageSourceOverride?: string;
  telemetryConfig?: TelemetryConfig;
  streamConfig?: StreamConfig;
  modes?: {
    displayCount: number;
    options: Mode[];
  };
  chatHeader: ChatHeaderConfig;
  voiceConfig?: VoiceConfig | null;
  introPopupConfig?: IntroPopupConfig;
  enrichedTitle?: {
    type: 'text' | 'image';
    value: string;
  };
};

export async function fetchConfigSettings(options?: {
  body: Record<string, unknown>;
}): Promise<GetNewChatViewResponse> {
  const fetchJson = fetchWrapper<GetNewChatViewResponse>('NewChatView');
  const abortController = new AbortController();
  const timeout = setTimeout(() => {
    abortController.abort();
  }, 10_000);
  try {
    return await fetchJson(Endpoints.GET_NEW_CHAT_VIEW, {
      method: 'POST',
      signal: abortController.signal,
      body: options?.body as Record<string, unknown> | undefined,
    });
  } finally {
    clearTimeout(timeout);
  }
}
