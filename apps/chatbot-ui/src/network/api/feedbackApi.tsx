import { Endpoints } from '../endpoints';
import { fetchWrapper } from './utils/fetchWrapper';

type GetHelpingHandsOptionsArgs = {
  conversationId: string | null;
  chatContext: ChatContext;
};

export type PostFeedbackRequest = {
  conversationId: string;
  messageId: string;
  message: {
    role: Roles;
    lang?: string;
    content: Array<{
      type: 'FEEDBACK';
      value: 'LIKE' | 'DISLIKE';
    }>;
  };
};

type PostFeedbackResponse = {
  success: boolean;
};

export const api_postFeedback = async (
  args: PostFeedbackRequest,
): Promise<PostFeedbackResponse> => {
  const fetchJson = fetchWrapper<PostFeedbackResponse>('PostFeedback');
  return await fetchJson(Endpoints.POST_FEEDBACK, {
    method: 'POST',
    body: args,
  });
};

export type ReportIssueRequest = {
  conversationId: string;
  messageId: string;
  feedbackText: string;
  debugParameters: unknown;
};

export async function api_reportIssue(
  request: ReportIssueRequest,
): Promise<PostFeedbackResponse> {
  const fetchJson = fetchWrapper<PostFeedbackResponse>('ReportIssue');
  return await fetchJson(Endpoints.REPORT_ISSUE, {
    method: 'POST',
    body: request,
  });
}
