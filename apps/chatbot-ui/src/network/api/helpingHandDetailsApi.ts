import { Endpoints } from '../endpoints';
import { APIResponse, APISuccessResponse, fetchWrapper } from './utils/fetchWrapper';

export type GetHelpingHandsOptionsRequest = {
  chatContext: ChatContext;
  lob: string | null;
  lobMetadata: Pick<ChatContext, 'expertMetadata'>;
};

export type HelpingHandCardCallUs = {
  header: string;
  subHeader: string;
  callBackType: 'call_us';
  callBackContent?: string;
  contactNo: string;
};
export type HelpingHandCardChatWithExpert = {
  header: string;
  subHeader: string;
  callBackType: 'chat_us';
  callBackContent: string;
  webViewHeader: string;
};
export type HelpingHandCardScheduleCallback = {
  header: string;
  subHeader: string;
  callBackType: '1';
  callBackContent?: {
    previousConversationId: null;
    intent: 'SCHEDULE_CALLBACK';
    deviceId: string;
    loggedIn: boolean;
    source: null;
    lob: string | null;
  };
};

export type HelpingHandCard =
  | HelpingHandCardCallUs
  | HelpingHandCardChatWithExpert
  | HelpingHandCardScheduleCallback;

export type GetHelpingHandsOptionsResponseData = {
  headerText: string;
  action: {
    type: 'dismiss';
    title: string;
  };
  cards: HelpingHandCard[];
  informationCard: {
    header: string;
    infoList: { icon: string; text: string }[];
  };
};

export type GetHelpingHandsOptionsResponse = APIResponse<
  GetHelpingHandsOptionsResponseData,
  { displayMessage: string }
>;

export const api_getHelpingHandDetails = async (
  args: GetHelpingHandsOptionsRequest,
): Promise<GetHelpingHandsOptionsResponse> => {
  console.log('Args', args);
  const fetchJson = fetchWrapper<GetHelpingHandsOptionsResponse>(
    'GetHelpingHandDetails',
  );
  return await fetchJson(Endpoints.GET_HELPING_HANDS, {
    method: 'POST',
    body: args,
  });
};
