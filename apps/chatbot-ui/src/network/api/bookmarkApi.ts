import { Endpoints } from '../endpoints';
import { fetchWrapper } from './utils/fetchWrapper';

export type BaseBookmarkRequest = {
  conversationId: string;
  messageId: string;
  cardId?: string;
  bookmarkId?: string;
  data: unknown;
  bookmarkSource: BookmarkPageSource;
};
export type SaveBookmarkRequest = BaseBookmarkRequest & {
  type: 'create' | 'delete';
};

export type SaveBookmarkResponse = {
  success: boolean;
  bookmarks: Array<{
    bookmarkId: string;
    messageId: string;
  }>;
  displayMessage: string;
};

export async function api_addBookmark(
  request: SaveBookmarkRequest,
): Promise<SaveBookmarkResponse | null> {
  const fetchJson = fetchWrapper<SaveBookmarkResponse>('AddBookmark', {
    logResponse: true,
  });
  return await fetchJson(Endpoints.ADD_BOOKMARK, {
    method: 'POST',
    body: request,
  });
}

type GetBookmarksRequest = {
  conversationId: string;
};

type GetBookmarkResponse = {
  data: {
    conversationId: string;
    bookmarks: Array<{
      bookmark: BookmarkEntity & Message;
    }>;
  };
  chatHeader?: ChatHeaderConfig;
  success: boolean;
  tags: string[];
};

export async function getBookmarks(
  request: GetBookmarksRequest,
): Promise<GetBookmarkResponse | null> {
  const fetchJson = fetchWrapper<GetBookmarkResponse>('GetBookmarks');
  return await fetchJson(Endpoints.GET_BOOKMARKS, {
    method: 'POST',
    body: request,
  });
}

export type RemoveBookmarkRequest = BaseBookmarkRequest & {
  type: 'delete';
  bookmarkId: string;
};

export async function removeBookmarks(request: RemoveBookmarkRequest) {
  const fetchJson = fetchWrapper<unknown>('GetBookmarks', { logResponse: true });
  return await fetchJson(Endpoints.ADD_BOOKMARK, {
    method: 'POST',
    body: request,
  });
}
