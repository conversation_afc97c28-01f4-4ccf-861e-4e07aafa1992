import { Endpoints } from '../endpoints';
import { fetchWrapper } from './utils/fetchWrapper';

export type GetConversationMessagesRequest = {
  conversationId: string;
};

export type GetConversationMessagesResponse =
  | GetConversationMessagesResponseSuccess
  | GetConversationMessagesResponseError;

export type GetConversationMessagesResponseSuccess = {
  success: true;
  data: {
    conversationId: string;
    messages: Array<{
      message: Message;
      leadingQuestion?: string | null;
      suggestions?: Array<string> | null;
      lq_insights?: LQInsights | null;
      audioTalkBack?: AudioTalkbackData | null;
    }>;
    chatTitle?: string;
  };
  title?: string;
  chatHeader?: ChatHeaderConfig;
  writeAllowed: boolean;
  writeAllowedDebug: boolean;
  hideInput?: boolean;
  hideT2aCta?: boolean;
  pageData?: PaginationPayload;
  mode?: Mode[];
  sessionContext?: SessionContext;
};
type PaginationPayload = {
  cursor: {
    bucket: number;
  };
};
type ErrorResponseType = {
  success: false;
  error: Error;
};
type Error = {
  code: string;
  message: string;
};
export type GetConversationMessagesResponseError = ErrorResponseType;

export const fetchConversationMessages = async (
  args: GetConversationMessagesRequest,
): Promise<GetConversationMessagesResponse> => {
  const fetchJson = fetchWrapper<GetConversationMessagesResponse>('FetchMsg', {
    logResponse: true,
  });
  return await fetchJson(Endpoints.GET_CONVERSATION_MESSAGES, {
    method: 'POST',
    body: args,
  });
};

export type GetTripDetailsRequest = {
  conversationId: string;
};

export type TripSummarySuccessResponse = {
  success: true;
  data: Message & {
    displayData: {
      overlay_title: string;
      overlay_icon: string;
      overlay_subtitle: string;
    };
  };
};
export type TripSummaryResponse = { success: false } | TripSummarySuccessResponse;

export const fetchTripSummary = async (
  args: GetTripDetailsRequest,
): Promise<TripSummaryResponse> => {
  const fetchJson = fetchWrapper<TripSummaryResponse>('TripSummary');
  return await fetchJson(Endpoints.GET_TRIP_SUMMARY, {
    method: 'POST',
    body: args,
  });
};

export type GetConversationsHistoryResponse =
  | GetConversationsHistoryResponseSuccess
  | GetConversationsHistoryResponseError;

export type DeleteConversationResponse = {
  success: boolean;
  message?: boolean;
};

export type ItineraryResponse = ItinerarySuccessResponse | ItineraryErrorResponse;

export type ItineraryItem = {
  title: string;
  subTitle: string;
  icon: string;
  itineraryData: DetailedItineraryCardType;
};
export type ItinerarySuccessResponse = {
  title: string;
  itineraries: ItineraryItem[];
  noItineraryData: {
    title: string;
    subTitle: string;
    ctaText: string;
  };
  chatHeader?: ChatHeaderConfig;
};

export type ItineraryErrorResponse = ErrorResponseType;
export type deletePopupConfig = {
  title: string;
  description: string;
  primaryButtonText: string;
  secondaryButtonText: string;
};
export type GetConversationsHistoryResponseSuccess = {
  success: true;
  conversationGroup: ConversationHistoryGroup;
  agentConversationGroup: ConversationHistoryGroup;
  showNewChatCta: boolean;
  conversationGroupsTitle: string;
  agentConversationGroupsTitle: string;
  chatHistoryTitle: string;
  chatHeader?: ChatHeaderConfig;
  popupConfigs?: { delete: deletePopupConfig };
};
export type GetConversationsHistoryResponseError = ErrorResponseType;

export const api_getConversationsHistory =
  async (): Promise<GetConversationsHistoryResponse> => {
    const fetchJson = fetchWrapper<GetConversationsHistoryResponse>('FetchMsg');
    return await fetchJson(Endpoints.GET_CONVERSATIONS_HISTORY, {
      method: 'POST',
      body: {},
    });
  };

export const api_getItinerary = async (
  conversationId: string,
): Promise<ItineraryResponse> => {
  const fetchJson = fetchWrapper<ItineraryResponse>('Itinerary');
  const result = await fetchJson(`${Endpoints.ITINERARY}/${conversationId}`, {
    method: 'GET',
  });
  return result;
};

export const api_deleteConversation = async (
  conversationId: string,
): Promise<DeleteConversationResponse> => {
  const fetchJson = fetchWrapper<DeleteConversationResponse>('DeleteMsg', {
    abortTimeout: 10_000,
  });
  const result = await fetchJson(
    `${Endpoints.DELETE_CONVERSATION}/${conversationId}?source=mobile`,
    {
      method: 'DELETE',
    },
  );
  return result;
};
