import { Logger } from '../../../utils/logger';
import { getHeadersAndContext } from '../../headers';
import { deepFreeze } from '../../../utils/objectUtils';
import {
  startNewApiTrace,
  updateApiTraceAsFailed,
  updateApiTraceAsSuccess,
} from '../../../analytics/telemetry';

export interface ApiError {
  code: string;
  msg: string;
  displayMessage?: string;
}

interface APIErrorResponse<E> {
  success: false;
  error: E;
  lobMetaData?: object;
}

export interface APISuccessResponse<T> {
  success: true;
  error: null;
  data: T;
  lobMetaData?: object;
}

interface APIErrorResponse<E> {
  success: false;
  error: E;
  lobMetaData?: object;
}

export type APIResponse<T, E = ApiError> =
  | APISuccessResponse<T>
  | APIErrorResponse<E>;

type NewRequestInit = Omit<RequestInit, 'body'> & { body?: Record<string, unknown> };
type FetchWrapperOptions = {
  logRequest?: boolean;
  logResponse?: boolean;
  skipContext?: boolean;
  skipHeaders?: boolean;
  disableTrace?: boolean;
  abortTimeout?: number;
};

const generateCurlCommand = (url: string, options: RequestInit): string => {
  const parts = ['curl'];
  // Add method if not GET
  if (options.method && options.method.toUpperCase() !== 'GET') {
    parts.push(`-X ${options.method.toUpperCase()}`);
  }
  // Add headers
  if (options.headers) {
    Object.entries(options.headers).forEach(([key, value]) => {
      parts.push(`-H '${key}: ${value}'`);
    });
  }
  // Add body if exists
  if (options.body) {
    parts.push(`-d '${options.body}'`);
  }
  // Add URL (wrapped in quotes to handle special characters)
  parts.push(`'${url}'`);
  return parts.join(' ');
};

const logger = Logger.createLogger({
  tag: 'MyraApi',
  level: 'VERBOSE',
  enableConsoleLog: true,
});
export const fetchWrapper = <T>(
  operation: string,
  options?: FetchWrapperOptions,
) => {
  const {
    logRequest = true,
    logResponse = true,
    skipContext = false,
    skipHeaders = false,
    disableTrace = false,
    abortTimeout,
  } = options || {};
  const abortController = new AbortController();
  let abortTimeoutId: ReturnType<typeof setTimeout>;
  if (abortTimeout) {
    abortTimeoutId = setTimeout(() => {
      abortController.abort();
    }, abortTimeout);
  }
  return async (url: RequestInfo, options?: NewRequestInit): Promise<T> => {
    const traceId = disableTrace ? null : startNewApiTrace(operation);
    const { httpHeaders, httpContexts } = await getHeadersAndContext();
    const method = options?.method?.toUpperCase() || 'GET';
    let body: RequestInit['body'];
    if (method === 'POST') {
      if (skipContext) {
        body = JSON.stringify(options?.body || {});
      } else {
        body = JSON.stringify({
          ...(options?.body || {}),
          ...httpContexts(),
        });
      }
    }
    const newOptions: RequestInit = {
      ...options,
      headers: {
        ...(skipHeaders ? {} : httpHeaders()),
        ...options?.headers,
      },
      body,
      ...(abortTimeout && { signal: abortController.signal }),
    };
    logger.info(operation, method, url);
    logger.debug(operation, 'headers', newOptions.headers);
    if (logRequest && newOptions.body) {
      logger.debug(operation, 'body', newOptions.body);
    }

    if (__DEV__) {
      // Log curl command
      const curlCommand = generateCurlCommand(url.toString(), newOptions);
      logger.debug(operation, 'curl command', curlCommand);
    }

    return fetch(url, newOptions)
      .then((response) => {
        if (abortTimeout && abortTimeoutId) {
          clearTimeout(abortTimeoutId);
        }
        logger.info(
          operation,
          'response',
          `code: ${response.status}`,
          `status: ${response.statusText}`,
        );
        if (!response.ok) {
          throw new Error(response.statusText);
        }
        return response.json();
      })
      .then((json) => {
        console.log('@mmt10296:: json = ', JSON.stringify(json, null, 4));
        if (logResponse) {
          logger.debug(operation, 'response', json);
        }
        deepFreeze(json);
        traceId && updateApiTraceAsSuccess(traceId);
        return json;
      })
      .catch((error) => {
        if (abortTimeout && abortTimeoutId) {
          clearTimeout(abortTimeoutId);
        }
        logger.error(operation, 'error', JSON.stringify(error, null, 4));
        traceId && updateApiTraceAsFailed(traceId, 'failed');
        throw error;
      });
  };
};
