import { EpochTimeStamp } from '../../types/typeUtils';
import { Endpoints } from '../endpoints';
import { fetchWrapper } from './utils/fetchWrapper';

export type PostScheduleCallbackArgs = {
  //REMOVE
  conversationId?: string | null;
  cmConversationId?: string | null;
  chatContext: ChatContext | null;
  configId: number | undefined;
  timeSlot: EpochTimeStamp;
  requestSource: string;
};

type PostScheduleCallbackSlotsResponseData = {
  data: {
    success: boolean;
    error: string | null;
  };
};

export type PostScheduleCallbackSlotsResponse =
  PostScheduleCallbackSlotsResponseData;

export const api_postScheduleCallbackSlots = async (
  args: PostScheduleCallbackArgs,
): Promise<PostScheduleCallbackSlotsResponse> => {
  const fetchJson = fetchWrapper<PostScheduleCallbackSlotsResponse>(
    'PostHelpingHandScheduleCallbackSlots',
  );
  return await fetchJson(Endpoints.POST_HELPING_HANDS_SCHEDULE_CALLBACK_SLOTS, {
    method: 'POST',
    body: args,
  });
};
