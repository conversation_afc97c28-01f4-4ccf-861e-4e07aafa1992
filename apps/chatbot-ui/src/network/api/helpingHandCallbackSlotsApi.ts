import { EpochTimeStamp } from '../../types/typeUtils';
import { Endpoints } from '../endpoints';
import {
  ApiError,
  APIResponse,
  APISuccessResponse,
  fetchWrapper,
} from './utils/fetchWrapper';

export type GetCallbackSlotsArgs = {
  conversationId: string | null;
  chatContext: ChatContext;
  lobMetaData: object;
};
type GetCallbackSlotsBody = {
  conversationId: string | null;
};

type SingleSlot = {
  slotText: string;
  timestamp: EpochTimeStamp;
};

type SlotGroup = {
  headerText: string;
  slotDetails: SingleSlot[];
};

type GetCallbackSlotsResponseData = {
  data: {
    cardHeader: string;
    ctaText: string;
    configId: number;
    callBackSlots: SlotGroup[];
    error?: ApiError;
  };
};

export type GetHelpingHandsCallbackSlotsResponse = GetCallbackSlotsResponseData;

export const api_getHelpingHandCallbackSlots = async (
  args: GetCallbackSlotsArgs,
): Promise<GetHelpingHandsCallbackSlotsResponse> => {
  const fetchJson = fetchWrapper<GetHelpingHandsCallbackSlotsResponse>(
    'GetHelpingHandCallbackSlots',
  );
  return await fetchJson(Endpoints.GET_HELPING_HANDS_CALLBACK_SLOTS, {
    method: 'POST',
    body: args,
  });
};
