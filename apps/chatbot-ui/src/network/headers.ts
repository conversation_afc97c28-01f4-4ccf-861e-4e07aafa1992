import {
  getPdtContext,
  getPdtSessionId,
  newRequestId,
} from '../native/tracking/pdt';
import { Platform } from 'react-native';
import { filterUndefinedValues } from '../utils/objectUtils';
import { useAppStateStore } from '../store/app';
import { getPlatform } from '../utils/getPlatform';
import { getAppHeaders } from '../native/network';
import { getDeviceId, getTrafficSource } from '../utils/webUtils';
import { getPageVars } from '../utils/pageUtils';

enum ChatProfileTypes {
  PERSONAL = 'PERSONAL',
  AGENT = 'AGENT',
}

type CommonHeaderKeys =
  | 'Content-Type'
  | 'session-id'
  | 'journey-id'
  | 'request-id'
  | 'auth'
  | 'org'
  | 'uuid'
  | 'profileType'
  | 'deviceId'
  | 'deviceType'
  // | 'timezone' // timezone is not standardized across platforms
  | 'os'
  | 'osVersion'
  | 'appVersion'
  | 'lang';

type CommonHeaders = Record<CommonHeaderKeys, string | undefined>;
type SocketHeaders = CommonHeaders & {
  debugData?: boolean;
};
type SocketChatContext = {
  context: ChatContext['context'];
  contextMetadata: {
    pageContext: ChatContext['contextMetadata']['pageContext'];
    searchContext: ChatContext['contextMetadata']['searchContext'];
  };
  expertMetadata: ChatContext['expertMetadata'];
  botMetadata?: ChatContext['botMetadata'];
  lobMetadata?: {
    showCard?: boolean;
    page: string;
  };
};

let contextView = 'W2G_BOTTOM_ICON';

export const updateContextView = (view: string) => {
  contextView = view;
};

export async function getHeadersAndContext() {
  const appHeaders = await getAppHeaders();
  const { deviceContext, userContext } = (await getPdtContext()) || {};
  const isDebugMode = useAppStateStore.getState().isDebugMode;
  const sessionId = await getPdtSessionId();
  const requestId = await newRequestId();
  const { PROP_PAGE_NAME: page } = getPageVars();
  const getCommonHeaders = (): SocketHeaders =>
    filterUndefinedValues({
      'request-id': requestId || undefined,
      'session-id': sessionId || undefined,
      'journey-id': sessionId || undefined,
      org: 'MMT',
      auth: appHeaders['mmt-auth'] || undefined,
      // User details
      uuid: userContext?.uuid,
      profileType: ChatProfileTypes.PERSONAL,
      // Device details
      deviceId: Platform.OS === 'web' ? getDeviceId() : deviceContext?.deviceId,
      deviceType: getPlatform(),
      platform: getPlatform(),
      appVersion: deviceContext?.appVer,
      buildVersion: deviceContext?.buildVer,
      os: deviceContext?.os,
      osVersion: deviceContext?.osVersion,
      timezone: 'Asia/Kolkata', // FIXME: This should not be hardcoded
      lang: 'en',
      entityName: appHeaders['entity-name'] || undefined,
      country: appHeaders['user-country'] || undefined,
      region: appHeaders.region || undefined,
      'Content-Type': 'application/json',
      uiVersion: 'travelplex_newUI',
      travelplexPage: page,
      test: 'travelplex',
      ...(isDebugMode && { debugData: true }),
      ...(Platform.OS === 'web' && { trafficSource: getTrafficSource() }),
    });

  const DEFAULT_LOB_CATEGORY = null;
  const DEFAULT_PAGE = 'LANDING';
  const DEFAULT_PREV_PAGE = null;

  const getCtxs = (): ChatContext => {
    const chatContext = useAppStateStore.getState().chatContext;
    if (chatContext) {
      const { context, expertMetadata, contextMetadata, botMetadata } = chatContext;
      const tempConfig = useAppStateStore.getState().tempConfig;
      useAppStateStore.setState({
        tempConfig: null,
      });
      return {
        context: { ...context, platform: context?.platform || getPlatform() },
        expertMetadata,
        contextMetadata,
        ...(botMetadata && { botMetadata }),
        ...(tempConfig && tempConfig),
      };
    }
    return {
      context: {
        lob: undefined,
        lobCategory: DEFAULT_LOB_CATEGORY,
        view: DEFAULT_PAGE,
        prevPage: DEFAULT_PREV_PAGE,
        platform: getPlatform(),
      },
      expertMetadata: {},
      contextMetadata: {
        pageContext: {},
        searchContext: {},
      },
    };
  };

  const socketHeaders = (): SocketHeaders => getCommonHeaders();
  const socketContext = (): SocketChatContext => getCtxs();
  const httpHeaders = (): CommonHeaders => getCommonHeaders();
  const httpContexts = (): ChatContext => getCtxs();

  const expertMetadata = (): ChatContext['expertMetadata'] | undefined =>
    useAppStateStore.getState().chatContext?.expertMetadata;
  return {
    socketHeaders,
    socketContext,
    httpHeaders,
    expertMetadata,
    httpContexts,
  };
}
