import { Analytics, TrackingEvent } from '../analytics';
import { CLICK_TYPE, HOLIDAY_TRACKING_CONSTANTS } from '../constants/holidayTracking';
import { ClickEventSchema } from '../native/omnitureConst';
import { CALLBACK_TYPES } from '../components/message/helping-hands/HelpingHandCards';
import { isEmpty } from "../components/message/helping-hands/HelpingHandUtils";

export class HolidayTrackingService {
  private static instance: HolidayTrackingService;

  private constructor() {}

  public static getInstance(): HolidayTrackingService {
    if (!HolidayTrackingService.instance) {
      HolidayTrackingService.instance = new HolidayTrackingService();
    }
    return HolidayTrackingService.instance;
  }

  /**
   * Track media shown event with generated key
   */
  public trackMediaShown(cardsData: HelpingHandsDetailsResponseData['data']['cards']): void {
    try {
      const key = this.generateTrackingKey(cardsData);
      this.trackEvent({
        m_c54: HOLIDAY_TRACKING_CONSTANTS.MEDIA_ACTIONS.SHOWN,
        mv15: HOLIDAY_TRACKING_CONSTANTS.MV15_BASE,
        prop14: `${HOLIDAY_TRACKING_CONSTANTS.MEDIA_PREFIX}${key}`,
        eventName: HOLIDAY_TRACKING_CONSTANTS.EVENT_NAME,
        eventType: HOLIDAY_TRACKING_CONSTANTS.EVENT_TYPE,
        eventValue: `${HOLIDAY_TRACKING_CONSTANTS.MYRA_PREFIX}${key}`,
      });
    } catch (error) {
      console.warn('Failed to track media shown event:', error);
    }
  }

  /**
   * Track call button click
   */
  public trackCallClick(autoClick: boolean = false): void {
    const clickType = autoClick ? CLICK_TYPE.AUTO : CLICK_TYPE.MENU;
    try {
      this.trackEvent({
        m_c54: HOLIDAY_TRACKING_CONSTANTS.MEDIA_ACTIONS.CLICKED,
        mv15: HOLIDAY_TRACKING_CONSTANTS.MV15_BASE,
        prop14: HOLIDAY_TRACKING_CONSTANTS.PROP14_VALUES.CALL + clickType,
        eventName: HOLIDAY_TRACKING_CONSTANTS.EVENT_NAME,
        eventType: HOLIDAY_TRACKING_CONSTANTS.EVENT_TYPE,
        eventValue: HOLIDAY_TRACKING_CONSTANTS.EVENT_VALUES.MYRA_CALL + clickType,
      });
    } catch (error) {
      console.warn('Failed to track call click event:', error);
    }
  }

  /**
   * Track chat with expert click
   */
  public trackChatClick(uniqChatId: string = '', autoClick: boolean = false): void {
    const clickType = autoClick ? CLICK_TYPE.AUTO : CLICK_TYPE.MENU;
    try {
      const baseProp14Value = HOLIDAY_TRACKING_CONSTANTS.PROP14_VALUES.CHAT_WITH_EXPERT + clickType;
      const baseEventValue = HOLIDAY_TRACKING_CONSTANTS.EVENT_VALUES.MYRA_CHAT + clickType;

      const prop14 = !isEmpty(uniqChatId) ? `${baseProp14Value}|${uniqChatId}` : baseProp14Value;
      const eventValue = !isEmpty(uniqChatId) ? `${baseEventValue}|${uniqChatId}` : baseEventValue;

      this.trackEvent({
        m_c54: HOLIDAY_TRACKING_CONSTANTS.MEDIA_ACTIONS.CLICKED,
        mv15: HOLIDAY_TRACKING_CONSTANTS.MV15_BASE,
        prop14,
        eventName: HOLIDAY_TRACKING_CONSTANTS.EVENT_NAME,
        eventType: HOLIDAY_TRACKING_CONSTANTS.EVENT_TYPE,
        eventValue,
      });
    } catch (error) {
      console.warn('Failed to track chat click event:', error);
    }
  }

  /**
   * Track query/media click
   */
  public trackQueryClick(autoClick: boolean = false): void {
    const clickType = autoClick ? CLICK_TYPE.AUTO : CLICK_TYPE.MENU;
    try {
      this.trackEvent({
        m_c54: HOLIDAY_TRACKING_CONSTANTS.MEDIA_ACTIONS.CLICKED,
        mv15: HOLIDAY_TRACKING_CONSTANTS.MV15_BASE,
        prop14: HOLIDAY_TRACKING_CONSTANTS.PROP14_VALUES.MEDIA_QUERY + clickType,
        eventName: HOLIDAY_TRACKING_CONSTANTS.EVENT_NAME,
        eventType: HOLIDAY_TRACKING_CONSTANTS.EVENT_TYPE,
        eventValue: HOLIDAY_TRACKING_CONSTANTS.EVENT_VALUES.MYRA_QUERY + clickType,
      });
    } catch (error) {
      console.warn('Failed to track query click event:', error);
    }
  }

  /**
   * Track destination shown in chat modal
   */
  public trackDestinationShown(destination?: string): void {
    if (!destination) return;

    try {
      this.trackEvent({
        mv15: HOLIDAY_TRACKING_CONSTANTS.MV15_CHAT_DESTINATION,
        prop14: destination,
        m_c54: ClickEventSchema.MYRA_CHAT_DESTINATION_SHOWN,
      });
    } catch (error) {
      console.warn('Failed to track destination shown event:', error);
    }
  }

  /**
   * Track destination dropdown change
   */
  public trackDestinationChange(destination?: string): void {
    if (!destination) return;

    try {
      this.trackEvent({
        mv15: HOLIDAY_TRACKING_CONSTANTS.MV15_CHAT_DESTINATION,
        m_c54: ClickEventSchema.MYRA_CHAT_DESTINATION_CHANGE,
        prop14: destination,
        eventName: HOLIDAY_TRACKING_CONSTANTS.EVENT_NAME,
        eventType: HOLIDAY_TRACKING_CONSTANTS.EVENT_TYPE,
        eventValue: ClickEventSchema.MYRA_CHAT_DESTINATION_CHANGE,
      });
    } catch (error) {
      console.warn('Failed to track destination change event:', error);
    }
  }

  /**
   * Track phone number field click
   */
  public trackPhoneClick(destination?: string): void {
    try {
      this.trackEvent({
        mv15: HOLIDAY_TRACKING_CONSTANTS.MV15_CHAT_DESTINATION,
        m_c54: ClickEventSchema.MYRA_CHAT_PHONE_CLICKED,
        prop14: destination,
        eventName: HOLIDAY_TRACKING_CONSTANTS.EVENT_NAME,
        eventType: HOLIDAY_TRACKING_CONSTANTS.EVENT_TYPE,
        eventValue: HOLIDAY_TRACKING_CONSTANTS.EVENT_VALUES.MYRA_CHAT_PHONE,
      });
    } catch (error) {
      console.warn('Failed to track phone click event:', error);
    }
  }

  /**
   * Track destination selection
   */
  public trackDestinationSelected(destination: string): void {
    try {
      this.trackEvent({
        mv15: HOLIDAY_TRACKING_CONSTANTS.MV15_CHAT_DESTINATION,
        m_c54: `${ClickEventSchema.MYRA_CHAT_DESTINATION_SELECTED}|${destination}`,
        eventName: HOLIDAY_TRACKING_CONSTANTS.EVENT_NAME,
        eventType: HOLIDAY_TRACKING_CONSTANTS.EVENT_TYPE,
        eventValue: `${ClickEventSchema.MYRA_CHAT_DESTINATION_SELECTED}|${destination}`,
      });
    } catch (error) {
      console.warn('Failed to track destination selected event:', error);
    }
  }

  /**
   * Track submit button click
   */
  public trackSubmitClick(destination?: string, uniqChatId: string = ''): void {
    try {
      const baseEventValue = HOLIDAY_TRACKING_CONSTANTS.EVENT_VALUES.MYRA_CHAT_SUBMIT;
      const basemC54 = ClickEventSchema.MYRA_CHAT_SUBMIT_CLICKED;

      const eventValue = !isEmpty(uniqChatId) ? `${baseEventValue}|${uniqChatId}` : baseEventValue;
      const m_c54 = !isEmpty(uniqChatId) ? `${basemC54}|${uniqChatId}` : basemC54;

      this.trackEvent({
        mv15: HOLIDAY_TRACKING_CONSTANTS.MV15_CHAT_DESTINATION,
        prop14: destination,
        m_c54,
        eventName: HOLIDAY_TRACKING_CONSTANTS.EVENT_NAME,
        eventType: HOLIDAY_TRACKING_CONSTANTS.EVENT_TYPE,
        eventValue,
      });
    } catch (error) {
      console.warn('Failed to track submit click event:', error);
    }
  }

  /**
   * Generate tracking key based on callback types in cards data
   */
  private generateTrackingKey(cardsData?: HelpingHandsDetailsResponseData['data']['cards'] | null): string {
    if (!cardsData?.length) return '';

    const typeMap = {
      [CALLBACK_TYPES.SCHEDULE_CALLBACK]: HOLIDAY_TRACKING_CONSTANTS.CALLBACK_TYPE_TRACKING.SCHEDULE_CALLBACK,
      [CALLBACK_TYPES.CHAT_CARD_WITH_FORM]: HOLIDAY_TRACKING_CONSTANTS.CALLBACK_TYPE_TRACKING.CHAT_CARD_WITH_FORM,
      [CALLBACK_TYPES.CALL_US]: HOLIDAY_TRACKING_CONSTANTS.CALLBACK_TYPE_TRACKING.CALL_US,
    };

    const contentTypes = new Set(
      cardsData
        .map(card => {
          const callBackType = card?.data?.call_back_type;
          return callBackType ? typeMap[callBackType] : null;
        })
        .filter(Boolean)
    );

    return contentTypes.size > 0 ? Array.from(contentTypes).join('') : '';
  }

  /**
   * Internal method to track events
   */
  private trackEvent(params: {
    mv15?: string;
    m_c54?: string;
    prop14?: string;
    eventName?: string;
    eventType?: string;
    eventValue?: string;
  }): void {
    Analytics.trackClickEvent(TrackingEvent.payload_trackHolidays(params));
  }
}

// Export singleton instance
export const holidayTrackingService = HolidayTrackingService.getInstance();
