// import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import { NativeModules } from 'react-native';

export const triggerSuccessHaptic = () => {
  const { GoMMTCommonModule } = NativeModules;
  if (!GoMMTCommonModule || !GoMMTCommonModule.hapticFeedback) {
    return;
  }
  GoMMTCommonModule.hapticFeedback('IMPACT_LIGHT');
};

export const triggerTripleHaptic = () => {
  const triggerHapticWithDelay = (count: number, delay: number) => {
    if (count <= 0) return;
    triggerSuccessHaptic();
    setTimeout(() => triggerHapticWithDelay(count - 1, delay), delay);
  };

  triggerHapticWithDelay(2, 100);
};

export const triggerErrorHaptic = () => {
  const { GoMMTCommonModule } = NativeModules;
  if (!GoMMTCommonModule || !GoMMTCommonModule.hapticFeedback) {
    return;
  }
  GoMMTCommonModule.hapticFeedback?.('NOTIFICATION_ERROR');
};
