import { Platform, PermissionsAndroid, Share } from 'react-native';
import ReactNativeB<PERSON>bUtil from 'react-native-blob-util';
import { Logger } from './logger';
import { Toast } from '../components/toast';

const logger = Logger.createLogger({ tag: 'PdfDownloadUtils' });

export interface DownloadPdfOptions {
  url: string;
  filename?: string;
  title?: string;
  description?: string;
  showDownloadProgress?: boolean;
  headers?: Record<string, string>;
}

export interface DownloadResult {
  success: boolean;
  filePath?: string;
  error?: string;
}

class PdfDownloadUtils {
  /**
   * Downloads a PDF file to the device's downloads folder
   * @param options - Download configuration options
   * @returns Promise<DownloadResult>
   */
  static async downloadPdf(options: DownloadPdfOptions): Promise<DownloadResult> {
    const {
      url,
      filename = `document_${Date.now()}.pdf`,
      title = 'Downloading PDF',
      description = 'Please wait...',
      showDownloadProgress = true,
      headers = {},
    } = options;

    try {
      logger.info('Starting PDF download', { url, filename });

      // Check if URL is valid
      if (!url || !this.isValidUrl(url)) {
        throw new Error('Invalid URL provided');
      }

      // Get platform-specific configuration
      const downloadConfig = await this.getDownloadConfig(
        filename,
        title,
        description,
        showDownloadProgress,
      );

      // Request permissions if needed
      const hasPermission = await this.requestStoragePermission();
      if (!hasPermission) {
        throw new Error('Storage permission denied');
      }

      // Start download
      const response = await ReactNativeBlobUtil.config(downloadConfig).fetch(
        'GET',
        url,
        headers,
      );

      // Handle success
      const filePath = response.path();
      logger.info('PDF download completed successfully', { filePath });

      // Show success notification
      if (showDownloadProgress) {
        this.showDownloadCompleteNotification(filename);
      }

      return {
        success: true,
        filePath,
      };
    } catch (error) {
      logger.error('PDF download failed', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Opens a PDF file using the device's default PDF viewer
   * @param filePath - Path to the PDF file
   * @returns Promise<boolean> - Success status
   */
  static async openPdf(filePath: string): Promise<boolean> {
    try {
      logger.info('Opening PDF file', { filePath });

      if (Platform.OS === 'ios') {
        await ReactNativeBlobUtil.ios.openDocument(filePath);
      } else {
        await ReactNativeBlobUtil.android.actionViewIntent(
          filePath,
          'application/pdf',
        );
      }

      return true;
    } catch (error) {
      logger.error('Failed to open PDF', error);
      Toast.show('Unable to open PDF file');
      return false;
    }
  }

  /**
   * Downloads and immediately opens a PDF file
   * @param options - Download configuration options
   * @returns Promise<DownloadResult>
   */
  static async downloadAndOpenPdf(
    options: DownloadPdfOptions,
  ): Promise<DownloadResult> {
    const result = await this.downloadPdf(options);

    if (result.success && result.filePath) {
      await this.openPdf(result.filePath);
    }

    return result;
  }

  /**
   * Shares a downloaded PDF file
   * @param filePath - Path to the PDF file
   * @param title - Share dialog title
   * @returns Promise<boolean> - Success status
   */
  static async sharePdf(filePath: string, title = 'Share PDF'): Promise<boolean> {
    try {
      logger.info('Sharing PDF file', { filePath, title });

      await Share.share({
        url: `file://${filePath}`,
        title,
      });
      return true;
    } catch (error) {
      logger.error('Failed to share PDF', error);
      Toast.show('Unable to share PDF file');
      return false;
    }
  }

  /**
   * Gets platform-specific download configuration
   */
  private static async getDownloadConfig(
    filename: string,
    title: string,
    description: string,
    showProgress: boolean,
  ) {
    const { dirs } = ReactNativeBlobUtil.fs;

    const baseConfig = {
      fileCache: true,
      addAndroidDownloads:
        Platform.OS === 'android'
          ? {
              useDownloadManager: true,
              notification: showProgress,
              title,
              description,
              path: `${dirs.DownloadDir}/${filename}`,
              mediaScannable: true,
            }
          : undefined,
      path: Platform.OS === 'ios' ? `${dirs.DocumentDir}/${filename}` : undefined,
    };

    return baseConfig;
  }

  /**
   * Requests storage permission on Android
   */
  private static async requestStoragePermission(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      return true;
    }

    try {
      if (Platform.Version >= 33) {
        // Android 13+ doesn't require WRITE_EXTERNAL_STORAGE permission
        return true;
      }

      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: 'Storage Permission',
          message: 'This app needs access to storage to download PDF files.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        },
      );

      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (error) {
      logger.error('Permission request failed', error);
      return false;
    }
  }

  /**
   * Validates if the provided string is a valid URL
   */
  private static isValidUrl(string: string): boolean {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }

  /**
   * Shows download complete notification on Android
   */
  private static showDownloadCompleteNotification(filename: string): void {
    Toast.show(`${filename} has been downloaded successfully`);
  }

  /**
   * Gets the file size of a remote URL without downloading
   * @param url - The URL to check
   * @returns Promise<number | null> - File size in bytes or null if unable to determine
   */
  static async getRemoteFileSize(url: string): Promise<number | null> {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      const contentLength = response.headers.get('content-length');
      return contentLength ? parseInt(contentLength, 10) : null;
    } catch (error) {
      logger.error('Failed to get remote file size', error);
      return null;
    }
  }

  /**
   * Checks if there's enough storage space for the download
   * @param requiredSpace - Required space in bytes
   * @returns Promise<boolean> - Whether there's enough space
   */
  static async checkStorageSpace(requiredSpace: number): Promise<boolean> {
    try {
      const { free } = await ReactNativeBlobUtil.fs.df();
      return (free ?? 0) > requiredSpace;
    } catch (error) {
      logger.error('Failed to check storage space', error);
      return true; // Assume there's space if we can't check
    }
  }

  /**
   * Deletes a downloaded PDF file
   * @param filePath - Path to the file to delete
   * @returns Promise<boolean> - Success status
   */
  static async deletePdf(filePath: string): Promise<boolean> {
    try {
      const exists = await ReactNativeBlobUtil.fs.exists(filePath);
      if (exists) {
        await ReactNativeBlobUtil.fs.unlink(filePath);
        logger.info('PDF file deleted successfully', { filePath });
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Failed to delete PDF file', error);
      return false;
    }
  }
}

export default PdfDownloadUtils;
