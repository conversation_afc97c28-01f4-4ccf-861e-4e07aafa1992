import { NativeModules } from 'react-native';

export const LOG_LEVEL = {
  VERBOSE: 4,
  DEBUG: 3,
  INFO: 2,
  WARN: 1,
  ERROR: 0,
  NONE: -1,
} as const;

type Level = keyof typeof LOG_LEVEL;

export class Logger {
  readonly level: number;
  readonly tag: string;
  private readonly enableConsoleLog: boolean;

  private constructor(
    options: Partial<{ tag: string; level: Level; enableConsoleLog?: boolean }>,
  ) {
    this.level = LOG_LEVEL[options.level || 'INFO'];
    this.tag = options.tag || 'MYRA';
    this.enableConsoleLog = options.enableConsoleLog || false;
  }

  static createLogger(
    options: Partial<{ tag: string; level: Level; enableConsoleLog?: boolean }>,
  ) {
    return new Logger(options);
  }

  warn(msg: string, ...args: any[]) {
    if (this.level >= LOG_LEVEL.WARN) {
      this.nativeLog('WARN', msg, ...args);
    }
  }

  info(msg: string, ...args: any[]) {
    if (this.level >= LOG_LEVEL.INFO) {
      this.nativeLog('INFO', msg, ...args);
    }
  }

  debug(msg: string, ...args: any[]) {
    if (this.level >= LOG_LEVEL.DEBUG) {
      this.nativeLog('DEBUG', msg, ...args);
    }
  }

  error(msg: string, ...args: any[]) {
    if (this.level >= LOG_LEVEL.ERROR) {
      this.nativeLog('ERROR', msg, ...args);
    }
  }

  verbose(msg: string, ...args: any[]) {
    if (this.level >= LOG_LEVEL.VERBOSE) {
      this.nativeLog('VERBOSE', msg, ...args);
    }
  }

  log(level: Level, msg: string, ...args: any[]) {
    if (this.level >= LOG_LEVEL[level]) {
      this.nativeLog(level, msg, ...args);
    }
  }

  private nativeLog(level: Level, msg: string, ...args: any[]) {
    const { LoggerModule: NativeLogger } = NativeModules;
    if (NativeLogger) {
      NativeLogger.log(level, this.tag, msg, JSON.stringify([...(args || [])]));
      // return;
    }
    if (!this.enableConsoleLog) {
      return;
    }
    const logTime = new Date().toISOString();
    switch (level) {
      case 'ERROR':
        // eslint-disable-next-line no-console
        console.error(`${logTime}:\t`, `${this.tag}:\t`, msg, toJson(args));
        return;
      case 'WARN':
        // eslint-disable-next-line no-console
        console.warn(`${logTime}:\t`, `${this.tag}:\t`, msg, toJson(args));
        return;
      case 'INFO':
        // eslint-disable-next-line no-console
        console.info(`${logTime}:\t`, `${this.tag}:\t`, msg, toJson(args));
        return;
      case 'DEBUG':
        // eslint-disable-next-line no-console
        console.debug(`${logTime}:\t`, `${this.tag}:\t`, msg, toJson(args));
        return;
      case 'VERBOSE':
        // eslint-disable-next-line no-console
        console.log(`${logTime}:\t`, `${this.tag}:\t`, msg, toJson(args));
        return;
      case 'NONE':
        // No logging
        return;
    }
  }
}

function toJson(args: unknown): string {
  if ((Array.isArray(args) && args.length === 0) || args === undefined) {
    return '';
  }
  if (typeof args === 'string') {
    return args;
  }
  return JSON.stringify(args, null, 1);
}
