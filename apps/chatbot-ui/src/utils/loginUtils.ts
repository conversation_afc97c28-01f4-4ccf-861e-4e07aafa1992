import { DeviceEventEmitter, NativeModules } from 'react-native';

const LOGIN_GENERIC_HEADING = 'Login for the Best Travel Offers';

export const performLogin = async (
  showAsBottomSheet = false,
  heading = LOGIN_GENERIC_HEADING,
  verifyMobile = false,
  verifyHeader = null,
  verifySubText = null,
) => {
  const { UserSessionModule } = NativeModules;
  return (
    UserSessionModule &&
    UserSessionModule.performLogin(
      showAsBottomSheet,
      heading,
      verifyMobile,
      verifyHeader,
      verifySubText,
    )
  );
};

const LOGIN_EVENT_RN = 'login_event';

export const loginWithPromise = async (
  showAsBottomSheet = false,
  heading = LOGIN_GENERIC_HEADING,
  verifyMobile = false,
  verifyHeader = null,
  verifySubText = null,
): Promise<any> => {
  const { GoMMTCommonModule } = NativeModules;
  if (GoMMTCommonModule && GoMMTCommonModule.login) {
    try {
      await GoMMTCommonModule.login(
        JSON.stringify({
          header: heading,
          showAsBottomSheet,
        }),
      );
      return await isUserLoggedIn();
    } catch (e) {
      throw new Error('Login failed');
    }
  }

  return new Promise((resolve, reject) => {
    const subscribe = DeviceEventEmitter.addListener(
      LOGIN_EVENT_RN,
      async (data) => {
        subscribe.remove();

        // make sure login is done
        const isLoggedIn = await isUserLoggedIn();
        if (isLoggedIn) {
          resolve(true);
        } else {
          reject(new Error('Login failed'));
        }
      },
    );
    performLogin(
      showAsBottomSheet,
      heading,
      verifyMobile,
      verifyHeader,
      verifySubText,
    );
  });
};

export const isUserLoggedIn = async () => {
  const { UserSessionModule } = NativeModules;
  return UserSessionModule && UserSessionModule.isUserLoggedIn();
};

export async function ensureLogin(): Promise<boolean> {
  const isLoggedIn = await isUserLoggedIn();
  if (isLoggedIn) {
    return true;
  } else {
    await performLogin(false, 'Login to bookmark messages');
    return false;
  }
}
