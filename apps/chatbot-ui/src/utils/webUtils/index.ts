import { Platform } from 'react-native';
import { v4 as uuid } from 'uuid';
export * from './cookie';
export * from './const';
export * from './timezone';

/**
 * @function
 * @description - Get the navigation type of the current window
 * @returns {string | null} - Returns 'forward' if the current window is not inside an iframe, 'modal' otherwise. If the window object is not available, it returns null
 */
export function getNavigationType() {
  if (!window) return null;
  return window.location === window.parent.location ? 'forward' : 'modal';
}

export function getTrafficSource(): 'myra' | 'lob' {
  if (typeof window === 'undefined') return 'myra';
  // This is a temporary fix to handle the holiday chat
  if (window?.location?.pathname?.includes('/chat')) {
    return 'lob';
  }
  if (window === window.top) {
    //iframe
    return 'myra';
  }
  return 'lob';
}

/**
 * @function
 * @description - Generate a UUID
 * @returns {string} - Returns a UUID
 */
export function generateUUID() {
  return uuid();
}

/**
 * @function
 * @description - Get the current date in the format DD_MM_YYYY
 * @returns {string} - Returns the current date in the format DD_MM_YYYY
 */
export function getCurrentDateStamp() {
  const currentDate = new Date();

  const day = currentDate.getDate().toString().padStart(2, '0');
  const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
  const year = currentDate.getFullYear();

  return `${day}_${month}_${year}`;
}

export const getValueFromUrl = (key: string) => {
  try {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      return null;
    }

    // Check URL query parameters (?conversationId=abc)
    const urlParams = new URLSearchParams(window.location.search);
    const fromQuery = urlParams.get(key);

    if (fromQuery && fromQuery.trim()) {
      return fromQuery.trim();
    }
    return null;
  } catch (error) {
    return null;
  }
};
export const postIframeMessage = (message: object, target: string) => {
  if (typeof window === 'undefined') {
    return;
  }

  window.parent.postMessage(message, target);
};

export const isBrowser = Platform.OS === 'web' && typeof window !== 'undefined';

export const isEvaluationMode = () => {
  if (isBrowser && getValueFromUrl('evaluationMode')) {
    return true;
  }
  return false;
};

export const isDebugMode = () => {
  if (isBrowser && getValueFromUrl('xd') === '456') {
    return true;
  }
  return false;
};

export const isMyraQAEnv = (): boolean => {
  // Early return if not in browser environment
  if (!isBrowser) {
    return false;
  }

  try {
    const { protocol, hostname } = window.location;

    // Check for HTTPS protocol (security requirement)
    const isSecure = protocol === 'https:';

    // Check for exact hostname match (more secure than includes)
    const isQAHostname = hostname === 'myra-qa.makemytrip.com';

    return isSecure && isQAHostname;
  } catch (error) {
    // Handle any potential errors gracefully
    return false;
  }
};
