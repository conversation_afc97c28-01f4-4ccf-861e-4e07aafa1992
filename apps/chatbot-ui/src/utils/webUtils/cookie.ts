import Cookies from 'react-cookies';
import { STORAGE_KEYS, NOT_AVAILABLE } from './const';
import { generateUUID } from '.';
// Exported functions
export const getMmtAuth = () => {
  const mmtAuth = Cookies.load(STORAGE_KEYS.COOKIE.MMT_AUTH);
  if (mmtAuth) return mmtAuth;
  return null;
};

export const getUserId = () => {
  return Cookies.load(STORAGE_KEYS.COOKIE.MMT_UUID) || null;
};

/**
 * @function
 * @description - Get the device id from the local storage
 * @returns {string} Device id
 */
export const getDeviceId = () => {
  if (typeof window === 'undefined') {
    return generateUUID();
  }
  let dvId = Cookies.load(STORAGE_KEYS.COOKIE.DVID);
  if (!dvId) {
    dvId = generateUUID();
    Cookies.save(STORAGE_KEYS.COOKIE.DVID, dvId, { path: '/' });
  }
  return dvId;
};
