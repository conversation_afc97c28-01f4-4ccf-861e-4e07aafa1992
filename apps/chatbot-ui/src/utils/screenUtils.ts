import { useEffect, useState } from 'react';
import { Keyboard } from 'react-native';

export type KeyboardState = 'SHOWN' | 'HIDDEN' | 'UNDETERMINED';

export function useKeyboardState(): KeyboardState {
  const [keyboardState, setKeyboardState] = useState<KeyboardState>('UNDETERMINED');

  useEffect(() => {
    const onKeyboardDidShow = () => setKeyboardState('SHOWN');
    const onKeyboardDidHide = () => setKeyboardState('HIDDEN');

    const showSubscription = Keyboard.addListener(
      'keyboardDidShow',
      onKeyboardDidShow,
    );
    const hideSubscription = Keyboard.addListener(
      'keyboardDidHide',
      onKeyboardDidHide,
    );

    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);

  return keyboardState;
}
