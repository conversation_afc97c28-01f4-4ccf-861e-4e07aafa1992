import { useCallback, useEffect } from 'react';
import { BackHandler } from 'react-native';
import { useAppStateStore } from '../store/app';

export type ActionResult = boolean | undefined | void;

export function useBackAction(
  action: () => ActionResult,
  options: { enabled: boolean } = { enabled: true },
) {
  useEffect(() => {
    const subscription = BackHandler.addEventListener(
      'hardwareBackPress',
      () => !!(options.enabled && action()),
    );
    return () => {
      subscription.remove();
    };
  }, [options.enabled, action]);
}

export function useBackToChats() {
  const action = useCallback(() => {
    if (useAppStateStore.getState().currentView === 'chat') {
      return false;
    }
    useAppStateStore.setState({ currentView: 'chat' });
    return true;
  }, []);
  useBackAction(action);
}
