import { Clipboard, Platform } from 'react-native';
import { MESSAGES } from '../constants/messages';

class ClipboardUtils {
    async copyText(text: string): Promise<{ success: boolean; error?: string }> {
        try {
            if (!text || text.trim().length === 0) {
                return {
                    success: false,
                    error: 'Text is empty or invalid'
                };
            }

            await Clipboard.setString(text.trim());
            return { success: true };
        } catch (error) {
            console.error('ClipboardUtils: Failed to copy text:', error);
            return {
                success: false,
                error: error instanceof Error ? error.message : MESSAGES.UNABLE_TO_COPY_CONTENT
            };
        }
    }

    async copyTextWithFeedback(
        text: string,
        onSuccess?: () => void,
        onError?: (error: string) => void
    ): Promise<void> {
        const result = await this.copyText(text);

        if (result.success) {
            onSuccess?.();
        } else {
            onError?.(result.error || 'Failed to copy text');
        }
    }

    shouldShowCustomToast(): boolean {
        if (Platform.OS !== 'android') return true;

        const version = Platform.Version;
        if (typeof version === 'number') {
            return version < 13;
        }

        return true;
    }
}

const clipboardUtils = new ClipboardUtils();

export default clipboardUtils;

const extractPlainText = (content: string): string => {
    if (!content || typeof content !== 'string') {
        return '';
    }

    let plainText = content;

    // Remove HTML tags
    plainText = plainText.replace(/<[^>]*>/g, '');

    // Remove markdown formatting
    plainText = plainText
        // Remove headers (# ## ###, etc.)
        .replace(/^#{1,6}\s+/gm, '')
        // Remove bold (**text** or __text__)
        .replace(/\*\*([^*]+)\*\*/g, '$1')
        .replace(/__([^_]+)__/g, '$1')
        // Remove italic (*text* or _text_)
        .replace(/\*([^*]+)\*/g, '$1')
        .replace(/_([^_]+)_/g, '$1')
        // Remove strikethrough (~~text~~)
        .replace(/~~([^~]+)~~/g, '$1')
        // Remove code blocks (```code```)
        .replace(/```[\s\S]*?```/g, '')
        // Remove inline code (`code`)
        .replace(/`([^`]+)`/g, '$1')
        // Remove links [text](url)
        .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
        // Remove images ![alt](url)
        .replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1')
        // Remove list markers (-, *, +, 1., etc.)
        .replace(/^\s*[-*+]\s+/gm, '')
        .replace(/^\s*\d+\.\s+/gm, '')
        // Remove blockquote markers (>)
        .replace(/^\s*>\s+/gm, '')
        // Remove horizontal rules (---, ***, ___)
        .replace(/^[-*_]{3,}$/gm, '');

    // Clean up extra whitespace
    plainText = plainText
        // Replace multiple newlines with double newlines
        .replace(/\n{3,}/g, '\n\n')
        // Replace multiple spaces with single space
        .replace(/[ \t]{2,}/g, ' ')
        // Trim whitespace from start and end of each line
        .split('\n')
        .map(line => line.trim())
        .join('\n')
        // Remove leading/trailing whitespace
        .trim();

    return plainText;
};

/**
 * Extract all text content from a message object's content array
 * Combines text from all TextWidget items in the message
 * @param msg - Message object containing content array
 * @returns Combined plain text from all text widgets in the message
 */
export const extractMessageText = (msg: any): string => {
    if (!msg || !Array.isArray(msg.content)) {
        return '';
    }

    const textContents: string[] = [];

    msg.content.forEach((contentItem: any) => {
        if (contentItem &&
            (contentItem.type === 'TEXT' || contentItem.type === 'LARGE_TEXT') &&
            contentItem.value &&
            typeof contentItem.value === 'string') {

            const plainText = extractPlainText(contentItem.value);
            if (plainText.trim()) {
                textContents.push(plainText);
            }
        }
    });

    return textContents.join('\n\n').trim();
};