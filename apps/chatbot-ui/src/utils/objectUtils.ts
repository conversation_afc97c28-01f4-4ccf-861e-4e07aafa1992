type ObjectLike = { [p: string]: unknown } | ArrayLike<unknown>;

export function unique<T>(values: T[]): T[] {
  if (!values.length) {
    return values;
  }
  return [...new Set(values)];
}

export function findLastIndex<T>(
  array: T[],
  predicate: (value: T, index: number, obj: T[]) => boolean,
): number {
  for (let i = array.length - 1; i >= 0; i--) {
    if (predicate(array[i], i, array)) {
      return i;
    }
  }
  return -1;
}

export function filterUndefinedValues<T extends ObjectLike>(obj: T): T {
  return Object.fromEntries(
    Object.entries(obj).filter(([, value]) => value !== undefined),
  ) as T;
}

export function deepFreeze<T extends ObjectLike>(obj: T): T {
  for (const value of Object.values(obj)) {
    if (typeof value === 'object' && value !== null) {
      deepFreeze(value as ObjectLike);
    }
  }
  return Object.freeze(obj);
}

export function snakeToCamelCaseKeys(obj: ObjectLike): ObjectLike {
  return mapKeysDeep(obj, snakeToCamelCase);
}
export function snakeToCamelCase(str: string): string {
  return str.replace(/([-_]\w)/g, (match) =>
    match.toUpperCase().replace('-', '').replace('_', ''),
  );
}

export function mapKeysDeep<T extends ObjectLike>(
  obj: T,
  mapper: (key: string) => string,
): T {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }
  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => {
      if (typeof value === 'object' && value !== null) {
        if (Array.isArray(value)) {
          return [
            mapper(key),
            value.map((v) => mapKeysDeep(v as ObjectLike, mapper)),
          ];
        }
        return [mapper(key), mapKeysDeep(value as ObjectLike, mapper)];
      }
      return [mapper(key), value];
    }),
  ) as T;
}
