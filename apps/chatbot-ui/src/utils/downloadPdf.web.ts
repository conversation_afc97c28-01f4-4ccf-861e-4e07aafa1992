import { Logger } from './logger';
import { Toast } from '../components/toast';

const logger = Logger.createLogger({ tag: 'WebPdfDownloadUtils' });

export interface WebDownloadPdfOptions {
  url: string;
  filename?: string;
  showDownloadProgress?: boolean;
  headers?: Record<string, string>;
}

export interface WebDownloadResult {
  success: boolean;
  blob?: Blob;
  error?: string;
}

class WebPdfDownloadUtils {
  /**
   * Downloads a PDF file in the browser
   * @param options - Download configuration options
   * @returns Promise<WebDownloadResult>
   */
  static async downloadPdf(
    options: WebDownloadPdfOptions,
  ): Promise<WebDownloadResult> {
    const {
      url,
      filename = `document_${Date.now()}.pdf`,
      showDownloadProgress = true,
      headers = {},
    } = options;

    try {
      logger.info('Starting web PDF download', { url, filename });

      // Check if we're in a browser environment
      if (typeof window === 'undefined') {
        throw new Error('Download is not supported in this environment');
      }

      // Check if URL is valid
      if (!url || !this.isValidUrl(url)) {
        throw new Error('Invalid URL provided');
      }

      // Show download start message
      if (showDownloadProgress) {
        Toast.show('Starting PDF download...');
      }

      // Fetch the PDF file
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/pdf',
          ...headers,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Convert response to blob
      const blob = await response.blob();

      // Verify it's a PDF
      if (!blob.type.includes('pdf') && !this.isPdfByExtension(url)) {
        logger.warn('Downloaded file might not be a PDF', {
          contentType: blob.type,
        });
      }

      // Trigger download
      this.triggerDownload(blob, filename);

      logger.info('Web PDF download initiated successfully', {
        filename,
        size: blob.size,
      });

      // Show download initiated notification
      if (showDownloadProgress) {
        Toast.show(`Download started for ${filename}`);
      }

      return {
        success: true,
        blob,
      };
    } catch (error) {
      logger.error('Web PDF download failed', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error occurred';

      if (showDownloadProgress) {
        Toast.show(`Download failed: ${errorMessage}`);
      }

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Opens a PDF in a new tab/window
   * @param url - URL of the PDF to open
   * @returns Promise<boolean> - Success status
   */
  static async openPdfInNewTab(url: string): Promise<boolean> {
    try {
      logger.info('Opening PDF in new tab', { url });

      if (typeof window === 'undefined') {
        throw new Error('Window object not available');
      }

      if (!this.isValidUrl(url)) {
        throw new Error('Invalid URL provided');
      }

      const newWindow = window.open(url, '_blank', 'noopener,noreferrer');

      if (!newWindow) {
        throw new Error('Popup blocked or failed to open new window');
      }

      return true;
    } catch (error) {
      logger.error('Failed to open PDF in new tab', error);
      Toast.show('Unable to open PDF in new tab');
      return false;
    }
  }

  /**
   * Downloads and opens a PDF file
   * @param options - Download configuration options
   * @returns Promise<WebDownloadResult>
   */
  static async downloadAndOpenPdf(
    options: WebDownloadPdfOptions,
  ): Promise<WebDownloadResult> {
    const result = await this.downloadPdf(options);

    if (result.success && result.blob) {
      // Create a temporary URL for the blob and open it
      const blobUrl = URL.createObjectURL(result.blob);
      window.open(blobUrl, '_blank', 'noopener,noreferrer');

      // Clean up the URL after a delay
      setTimeout(() => {
        URL.revokeObjectURL(blobUrl);
      }, 1000);
    }

    return result;
  }

  /**
   * Shares a PDF URL (copies to clipboard)
   * @param url - URL to share
   * @param title - Share title
   * @returns Promise<boolean> - Success status
   */
  static async sharePdfUrl(url: string, title = 'Share PDF'): Promise<boolean> {
    try {
      logger.info('Sharing PDF URL', { url, title });

      if (typeof window === 'undefined') {
        throw new Error('Window object not available');
      }

      // Try to use Web Share API if available
      if (navigator.share) {
        await navigator.share({
          title,
          url,
        });
        return true;
      }

      // Fallback to clipboard
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(url);
        Toast.show('PDF link copied to clipboard');
        return true;
      }

      throw new Error('Sharing not supported in this browser');
    } catch (error) {
      logger.error('Failed to share PDF URL', error);
      Toast.show('Unable to share PDF');
      return false;
    }
  }

  /**
   * Gets the size of a remote PDF without downloading
   * @param url - The URL to check
   * @returns Promise<number | null> - File size in bytes or null if unable to determine
   */
  static async getRemoteFileSize(url: string): Promise<number | null> {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      const contentLength = response.headers.get('content-length');
      return contentLength ? parseInt(contentLength, 10) : null;
    } catch (error) {
      logger.error('Failed to get remote file size', error);
      return null;
    }
  }

  /**
   * Checks if the browser supports PDF downloads
   * @returns boolean - Whether downloads are supported
   */
  static isDownloadSupported(): boolean {
    return (
      typeof window !== 'undefined' && 'URL' in window && 'createObjectURL' in URL
    );
  }

  /**
   * Triggers the actual download by creating a temporary link
   */
  private static triggerDownload(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');

    link.href = url;
    link.download = filename;
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the object URL
    setTimeout(() => {
      URL.revokeObjectURL(url);
    }, 100);
  }

  /**
   * Validates if the provided string is a valid URL
   */
  private static isValidUrl(string: string): boolean {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }

  /**
   * Checks if the URL appears to be a PDF based on file extension
   */
  private static isPdfByExtension(url: string): boolean {
    try {
      const urlObj = new URL(url);
      const pathname = urlObj.pathname.toLowerCase();
      return pathname.endsWith('.pdf');
    } catch (_) {
      return false;
    }
  }

  /**
   * Converts a File or Blob to base64 data URL
   * @param blob - The blob to convert
   * @returns Promise<string> - Base64 data URL
   */
  static async blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  /**
   * Creates a PDF preview URL that can be used in iframe or embed
   * @param url - PDF URL
   * @returns string - Preview URL
   */
  static createPdfPreviewUrl(url: string): string {
    // Use browser's built-in PDF viewer
    return url;
  }
}

export default WebPdfDownloadUtils;
