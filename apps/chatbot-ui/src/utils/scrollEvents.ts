type ScrollCallback = () => void;

class ScrollEventManager {
    private listeners: ScrollCallback[] = [];

    subscribe(callback: ScrollCallback) {
        this.listeners.push(callback);
        return () => {
            this.listeners = this.listeners.filter(cb => cb !== callback);
        };
    }

    triggerScrollToBottom() {
        console.log('ScrollEventManager: triggerScrollToBottom called');
        this.listeners.forEach(callback => callback());
    }
}

export const scrollEvents = new ScrollEventManager();