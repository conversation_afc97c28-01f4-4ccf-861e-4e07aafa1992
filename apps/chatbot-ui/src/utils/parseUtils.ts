/**
 * Safely parses a JSON string and returns the parsed object.
 * Returns a fallback value if parsing fails.
 * 
 * @param jsonString - The JSON string to parse
 * @param fallback - The fallback value to return if parsing fails (default: {})
 * @returns The parsed object or the fallback value
 */
export const safeJsonParse = <T = any>(
  jsonString: string | undefined | null,
  fallback: T = {} as T
): T => {
  if (!jsonString) {
    return fallback;
  }

  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('Error parsing JSON:', error);
    return fallback;
  }
};

/**
 * Safely parses a value that might be a JSON string or already an object.
 * Useful when dealing with data that could be either stringified or already parsed.
 * 
 * @param value - The value to parse (string or object)
 * @param fallback - The fallback value to return if parsing fails
 * @returns The parsed object or the original value if it's already an object
 */
export const parseIfString = <T = any>(
  value: string | T | undefined | null,
  fallback: T = {} as T
): T => {
  if (value === undefined || value === null) {
    return fallback;
  }

  if (typeof value === 'string') {
    return safeJsonParse(value, fallback);
  }

  return value;
};