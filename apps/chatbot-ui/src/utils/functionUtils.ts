/**
 * This function is used to add a delay to the execution of the code.
 * */
export async function sleep(time: number) {
  return new Promise<void>((resolve) => {
    setTimeout(() => {
      resolve();
    }, time);
  });
}

/**
 * This function is used to add a timeout to a promise/task.
 * @param timeout - The time in milliseconds after which the promise should be rejected.
 * @param task - The task that should be executed.
 * */
export function withTimeout<T = void>(
  timeout: number,
  task: (done: (arg: T) => void) => void,
): Promise<T> {
  return Promise.race([
    new Promise<T>((resolve) => {
      task(resolve);
    }),
    new Promise<T>((resolve, reject) => {
      setTimeout(() => reject('timeout'), timeout);
    }),
  ]);
}

// Helper function to safely get nested values with fallbacks
export const safeGet = <T,>(obj: any, path: string[], fallback: T): T => {
  try {
    return path.reduce((current, key) => current?.[key], obj) ?? fallback;
  } catch {
    return fallback;
  }
};