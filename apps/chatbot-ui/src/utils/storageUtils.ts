import AsyncStorage from '@react-native-async-storage/async-storage';
import { isUserLoggedIn } from './loginUtils';
import { Logger } from './logger';

const logger = Logger.createLogger({ tag: 'StorageUtils' });

export const STORAGE_KEYS = {
  TRAVELPLEX_SESSION: 'travelplexSession',
  LAST_MESSAGE_RECEIVED_TIME: 'lastMessageReceivedTime',
  INTRO_POPUP_CONFIG: 'travelplexIntroPopupConfig',
  SPEAKER_COACHMARK_SHOWN: 'speakerCoachmarkShown',
  MIC_COACHMARK_FIRST_SHOWN: 'micCoachmarkFirstShown',
  MIC_COACHMARK_SECOND_SHOWN: 'micCoachmarkSecondShown',
  USER_HAS_USED_MIC: 'userHasUsedMic',
};

class StorageUtils {
  // Static method to get an item from storage
  static async getItem(key: string) {
    if (!key) {
      throw new Error('Key is required');
    }
    const result = await AsyncStorage.getItem(key);
    return result;
  }

  // Static method to set an item in storage
  static async setItem(key: string, value: any) {
    if (!key || !value) {
      throw new Error('Key and value are required');
    }
    await AsyncStorage.setItem(key, value);
  }

  // Static method to remove an item from storage
  static async removeItem(key: string) {
    if (!key) {
      throw new Error('Key is required');
    }
    await AsyncStorage.removeItem(key);
  }

  // Static method to set conversation ID in storage
  static async setLastConversationId(conversationId: string) {
    if (!conversationId) {
      throw new Error('ConversationId is required');
    }
    const isLoggedIn = await isUserLoggedIn();
    if (isLoggedIn) {
      this.setItem(
        STORAGE_KEYS.TRAVELPLEX_SESSION,
        JSON.stringify({ conversationId, sessionLoggedInState: 'true' }),
      );
    } else {
      this.setItem(
        STORAGE_KEYS.TRAVELPLEX_SESSION,
        JSON.stringify({ conversationId, sessionLoggedInState: 'false' }),
      );
    }
  }

  static async getIntroPopupConfig() {
    const config = await this.getItem(STORAGE_KEYS.INTRO_POPUP_CONFIG);
    if (!config) {
      return null;
    }
    return JSON.parse(config);
  }

  static async setIntroPopupConfig(
    config: Pick<IntroPopupConfig, 'showIntroPopupOnce'>,
  ) {
    if (!config) {
      throw new Error('Config is required');
    }
    this.setItem(STORAGE_KEYS.INTRO_POPUP_CONFIG, JSON.stringify(config));
  }

  static async clearIntroPopupConfig() {
    await this.removeItem(STORAGE_KEYS.INTRO_POPUP_CONFIG);
  }

  static async getSpeakerCoachmarkShown() {
    try {
      const shown = await this.getItem(STORAGE_KEYS.SPEAKER_COACHMARK_SHOWN);
      return shown === 'true';
    } catch (error) {
      logger.error('[getSpeakerCoachmarkShown] Error getting speaker coachmark flag', error);
      return false;
    }
  }

  static async setSpeakerCoachmarkShown() {
    try {
      await this.setItem(STORAGE_KEYS.SPEAKER_COACHMARK_SHOWN, 'true');
    } catch (error) {
      logger.error('[setSpeakerCoachmarkShown] Error setting speaker coachmark flag', error);
    }
  }

  // Mic Coachmark - First Time
  static async getMicCoachmarkFirstShown() {
    try {
      const shown = await this.getItem(STORAGE_KEYS.MIC_COACHMARK_FIRST_SHOWN);
      return shown === 'true';
    } catch (error) {
      logger.error('[getMicCoachmarkFirstShown] Error getting mic coachmark first flag', error);
      return false;
    }
  }

  static async setMicCoachmarkFirstShown() {
    try {
      await this.setItem(STORAGE_KEYS.MIC_COACHMARK_FIRST_SHOWN, 'true');
    } catch (error) {
      logger.error('[setMicCoachmarkFirstShown] Error setting mic coachmark first flag', error);
    }
  }

  // Mic Coachmark - Second Time  
  static async getMicCoachmarkSecondShown() {
    try {
      const shown = await this.getItem(STORAGE_KEYS.MIC_COACHMARK_SECOND_SHOWN);
      return shown === 'true';
    } catch (error) {
      logger.error('[getMicCoachmarkSecondShown] Error getting mic coachmark second flag', error);
      return false;
    }
  }

  static async setMicCoachmarkSecondShown() {
    try {
      await this.setItem(STORAGE_KEYS.MIC_COACHMARK_SECOND_SHOWN, 'true');
    } catch (error) {
      logger.error('[setMicCoachmarkSecondShown] Error setting mic coachmark second flag', error);
    }
  }

  // User has used mic tracking
  static async getUserHasUsedMic() {
    try {
      const used = await this.getItem(STORAGE_KEYS.USER_HAS_USED_MIC);
      return used === 'true';
    } catch (error) {
      logger.error('[getUserHasUsedMic] Error getting user has used mic flag', error);
      return false;
    }
  }

  static async setUserHasUsedMic() {
    try {
      await this.setItem(STORAGE_KEYS.USER_HAS_USED_MIC, 'true');
    } catch (error) {
      logger.error('[setUserHasUsedMic] Error setting user has used mic flag', error);
    }
  }

  static async clearSpeakerCoachmarkShown() {
    try {
      await this.removeItem(STORAGE_KEYS.SPEAKER_COACHMARK_SHOWN);
    } catch (error) {
      logger.error('[clearSpeakerCoachmarkShown] Error clearing speaker coachmark flag', error);
    }
  }

  // Static method to clear session storage
  static async clearSessionStorage() {
    await this.removeItem(STORAGE_KEYS.TRAVELPLEX_SESSION);
    await this.removeItem(STORAGE_KEYS.LAST_MESSAGE_RECEIVED_TIME);
  }

  static async getSessionData() {
    const travelplexSession = await this.getItem(STORAGE_KEYS.TRAVELPLEX_SESSION);
    if (!travelplexSession) {
      return null;
    }
    try {
      const { conversationId, sessionLoggedInState } = JSON.parse(travelplexSession);
      if (!conversationId || !sessionLoggedInState) {
        return null;
      }
      return {
        activeConversationIdFromStorage: conversationId,
        sessionLoggedInState,
      };
    } catch (error) {
      logger.error('[getTravelPlexSession] Error parsing travelplexSession', error);
      return null;
    }
  }

  static async getLastMessageReceivedTime() {
    try {
      return await StorageUtils.getItem(STORAGE_KEYS.LAST_MESSAGE_RECEIVED_TIME);
    } catch (e) {
      logger.error(
        '[getLastMessageReceivedTime] Error getting last message received time',
        e,
      );
      return null;
    }
  }

  static async setLastMessageReceivedTime(time: string) {
    if (!time) {
      throw new Error('Time is required');
    }
    this.setItem(STORAGE_KEYS.LAST_MESSAGE_RECEIVED_TIME, time);
  }
}

// Export the StorageUtils class as a singleton
export default StorageUtils;
