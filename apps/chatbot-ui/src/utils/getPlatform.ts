import { Platform } from 'react-native';
import { getValueFromUrl } from './webUtils';

export const getPlatform: () => ParentPlatform = () => {
  if (Platform.OS === 'web') {
    const platformUrlParam = getValueFromUrl('platform');
    if (platformUrlParam && platformUrlParam === 'desktop') {
      return 'desktop';
    }
    return 'pwa';
  }
  return Platform.select({
    ios: 'ios',
    android: 'android',
    default: 'pwa',
  });
};

export const isPlatformWeb = (): boolean => {
  return getPlatform() === 'pwa' || getPlatform() === 'desktop';
};

export const getPlatformSpecificUrl = (urlObject: any) => {
  const platform = getPlatform();
  let platformKey: string;

  // Map platform to key
  switch (platform) {
    case 'ios':
      platformKey = 'ios';
      break;
    case 'android':
      platformKey = 'android';
      break;
    case 'desktop':
    case 'pwa':
      platformKey = 'web';
      break;
    default:
      return null;
  }

  return urlObject?.[platformKey] || null;
};
