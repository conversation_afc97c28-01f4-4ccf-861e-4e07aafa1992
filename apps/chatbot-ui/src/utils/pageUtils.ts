import { useAppStateStore } from '../store/app';
import { useMessageStore } from '../store/messages';
import { SubPageName, PageData, CHANNEL, JS_VERSION } from '../native/omnitureConst';

let prevPageName: SubPageName | null = null;
let prevValOfCurrPage: SubPageName | undefined;

export function getPageVars(): PageData {
  const { currentView, parentFunnel, parentPage } = useAppStateStore.getState();
  let currPage: SubPageName;
  switch (currentView) {
    case 'chat': {
      const { activeConversationId, conversationById } = useMessageStore.getState();
      if (conversationById[activeConversationId || 'draft']?.messages?.length) {
        currPage = 'chat:chat_page|travelplex';
        break;
      }
      currPage = 'chat:home_page|travelplex';
      break;
    }
    case 'bookmarks':
      currPage = 'chat:bookmark_page|travelplex';
      break;
    case 'history':
      currPage = 'chat:history_page|travelplex';
      break;
    case 'trips':
      currPage = 'itinerary_list|travelplex';
      break;
    case 'itinerary':
      currPage = 'itinerary_details|travelplex';
      break;
    default:
      currPage = 'chat:home_page|travelplex';
      break;
  }
  if (prevValOfCurrPage && prevValOfCurrPage !== currPage) {
    prevPageName = prevValOfCurrPage;
  }
  prevValOfCurrPage = currPage;
  const parentPageUrl = `${parentFunnel || ''}/${parentPage || ''}`;
  return {
    PROP_LOB_DETAILS: CHANNEL,
    PROP_PAGE_NAME: currPage,
    VAR_PAGE_NAME: currPage,
    PROP_PREV_PAGE_NAME: prevPageName || null,
    PROP_PAGE_URL: parentPageUrl,
    VAR_JS_VERSION: JS_VERSION,
  };
}
