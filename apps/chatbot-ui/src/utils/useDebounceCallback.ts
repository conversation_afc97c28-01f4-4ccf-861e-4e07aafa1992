import { useCallback, useRef } from 'react';

interface DebounceOptions {
  /**
   * If true, the callback will be called on the leading edge of the timeout
   * instead of the trailing edge
   */
  leading?: boolean;
  /**
   * If true and leading=true, the callback will also be called on the trailing edge
   */
  trailing?: boolean;
}

export const useDebounceCallback = <T extends (...args: any[]) => void>(
  callback: T,
  delay: number,
  options: DebounceOptions = { leading: false, trailing: true },
): [debouncedCallback: (...args: Parameters<T>) => void, cancel: () => void] => {
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const lastCallTimeRef = useRef<number>(0);
  const lastArgsRef = useRef<Parameters<T> | null>(null);
  const { leading = false, trailing = true } = options;

  const cancel = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      const now = Date.now();
      const isFirstCall = lastCallTimeRef.current === 0;

      // Cancel any pending executions
      cancel();

      // If leading=true and this is either the first call or enough time has passed
      if (leading && (isFirstCall || now - lastCallTimeRef.current >= delay)) {
        lastCallTimeRef.current = now;
        callback(...args);
      } else if (trailing) {
        // Store the most recent arguments for the trailing call
        lastArgsRef.current = args;

        // Schedule the trailing edge execution
        timeoutRef.current = setTimeout(() => {
          if (lastArgsRef.current) {
            callback(...lastArgsRef.current);
            lastCallTimeRef.current = Date.now();
            lastArgsRef.current = null;
          }
        }, delay);
      }
    },
    [callback, delay, leading, trailing, cancel],
  );

  return [debouncedCallback, cancel];
};
