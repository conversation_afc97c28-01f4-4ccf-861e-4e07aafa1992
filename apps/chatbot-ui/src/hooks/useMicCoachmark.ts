import { useEffect, useState, useRef, useCallback } from 'react';
import { LayoutAnimation } from 'react-native';
import { useTalkBackStore } from '../store/audio-talkback/talkbackStore';
import StorageUtils from '../utils/storageUtils';
import { Logger } from '../utils/logger';

const logger = Logger.createLogger({ tag: 'MicCoachmark' });

export interface MicCoachmarkState {
    // UI State
    showFirstCoachmark: boolean;
    showSecondCoachmark: boolean;

    // Callbacks
    onCloseFirstCoachmark: () => void;
    onCloseSecondCoachmark: () => void;
    onMicUsed: () => void;
}

/**
 * Hook to manage mic coachmark display logic
 * 
 * Shows two coachmarks:
 * 1. First time: "Tap to speak" - shown when user first sees mic
 * 2. Second time: "Tap to speak again" - shown after user has used mic once and bot finished reading
 * 
 * Each coachmark is shown only once per user lifetime
 */
export const useMicCoachmark = (): MicCoachmarkState => {
    // Storage state flags
    const [hasShownFirstCoachmark, setHasShownFirstCoachmark] = useState<boolean | null>(null);
    const [hasShownSecondCoachmark, setHasShownSecondCoachmark] = useState<boolean | null>(null);
    const [userHasUsedMic, setUserHasUsedMic] = useState<boolean | null>(null);

    // UI visibility state
    const [showFirstCoachmark, setShowFirstCoachmark] = useState(false);
    const [showSecondCoachmark, setShowSecondCoachmark] = useState(false);

    // TTS monitoring for second coachmark trigger
    const { isPlaying, isInitRunning, state, currentTaskId } = useTalkBackStore();
    const wasPlayingRef = useRef(false);
    const pendingSecondCoachmarkRef = useRef(false);

    // Load all storage flags on mount
    useEffect(() => {
        const loadStorageFlags = async () => {
            try {
                const [firstShown, secondShown, micUsed] = await Promise.all([
                    StorageUtils.getMicCoachmarkFirstShown(),
                    StorageUtils.getMicCoachmarkSecondShown(),
                    StorageUtils.getUserHasUsedMic(),
                ]);

                setHasShownFirstCoachmark(firstShown);
                setHasShownSecondCoachmark(secondShown);
                setUserHasUsedMic(micUsed);

                logger.info('Storage flags loaded:', { firstShown, secondShown, micUsed });
            } catch (error) {
                logger.error('Error loading storage flags:', error);
                // Default to false on error
                setHasShownFirstCoachmark(false);
                setHasShownSecondCoachmark(false);
                setUserHasUsedMic(false);
            }
        };

        loadStorageFlags();
    }, []);

    // Show first coachmark if not shown before
    useEffect(() => {
        if (hasShownFirstCoachmark === false && userHasUsedMic === false) {
            const timer = setTimeout(async () => {
                logger.info('Showing first mic coachmark');

                LayoutAnimation.configureNext({
                    duration: 400,
                    create: {
                        type: LayoutAnimation.Types.easeInEaseOut,
                        property: LayoutAnimation.Properties.opacity,
                    },
                    update: {
                        type: LayoutAnimation.Types.easeInEaseOut,
                    },
                });

                setShowFirstCoachmark(true);
                setHasShownFirstCoachmark(true);
                await StorageUtils.setMicCoachmarkFirstShown();
            }, 2000);

            return () => clearTimeout(timer);
        }
    }, [hasShownFirstCoachmark, userHasUsedMic]);

    // Monitor TTS state for second coachmark trigger
    useEffect(() => {
        // Track when TTS starts playing
        if ((isPlaying || isInitRunning) && !wasPlayingRef.current) {
            wasPlayingRef.current = true;
        }

        // Detect when TTS completes
        const isComplete =
            wasPlayingRef.current && // Was previously playing
            !isPlaying && // Not currently playing
            !isInitRunning && // Not initializing
            state === 'stopped' && // State is stopped
            currentTaskId === null; // No active task

        // Trigger second coachmark if conditions are met
        if (isComplete && pendingSecondCoachmarkRef.current) {
            pendingSecondCoachmarkRef.current = false;
            wasPlayingRef.current = false;

            // Show second coachmark after bot finishes reading
            const timer = setTimeout(() => {
                if (hasShownSecondCoachmark === false && userHasUsedMic === true) {
                    logger.info('Showing second mic coachmark after bot finished reading');

                    // Close first coachmark if still open
                    if (showFirstCoachmark) {
                        logger.info('Closing first coachmark before showing second');
                        setShowFirstCoachmark(false);
                    }

                    LayoutAnimation.configureNext({
                        duration: 400,
                        create: {
                            type: LayoutAnimation.Types.easeInEaseOut,
                            property: LayoutAnimation.Properties.opacity,
                        },
                        update: {
                            type: LayoutAnimation.Types.easeInEaseOut,
                        },
                    });

                    setShowSecondCoachmark(true);
                }
            }, 5000);

            return () => clearTimeout(timer);
        }
    }, [isPlaying, isInitRunning, state, currentTaskId, hasShownSecondCoachmark, userHasUsedMic]);

    // Callbacks
    const onCloseFirstCoachmark = useCallback(() => {
        setShowFirstCoachmark(false);
    }, []);

    const onCloseSecondCoachmark = useCallback(async () => {
        setShowSecondCoachmark(false);
        setHasShownSecondCoachmark(true);
        await StorageUtils.setMicCoachmarkSecondShown();
    }, []);

    const onMicUsed = useCallback(async () => {
        // Mark that user has used mic
        if (userHasUsedMic === false) {
            setUserHasUsedMic(true);
            await StorageUtils.setUserHasUsedMic();
            logger.info('User used mic for first time');

            // Set flag to show second coachmark after next TTS completion
            if (hasShownSecondCoachmark === false) {
                pendingSecondCoachmarkRef.current = true;
                logger.info('Pending second coachmark after next TTS completion');
            }
        }
    }, [userHasUsedMic, hasShownSecondCoachmark]);

    return {
        showFirstCoachmark,
        showSecondCoachmark,
        onCloseFirstCoachmark,
        onCloseSecondCoachmark,
        onMicUsed,
    };
};