import { useCallback } from 'react';
import { holidayTrackingService } from '../services/HolidayTrackingService';
import { useAppStateStore } from '../store/app';
import { LOB } from '../constants/lob';

/**
 * Custom hook for holiday tracking functionality
 * Provides a clean interface for components to track holiday-related events
 */
export const useHolidayTracking = () => {
  const chatContext = useAppStateStore(state => state.chatContext);

  // Check if current context is holidays
  const isHolidays = useCallback(() => {
    const lob = chatContext?.contextMetadata?.pageContext?.lob || '';
    return lob === LOB.HOLIDAYS;
  }, [chatContext]);

  // Track media shown event
  const trackMediaShown = useCallback((cardsData: HelpingHandsDetailsResponseData['data']['cards']) => {
    if (!isHolidays()) return;
    holidayTrackingService.trackMediaShown(cardsData);
  }, [isHolidays]);

  // Track call button click
  const trackCallClick = useCallback((autoClick: boolean = false) => {
    if (!isHolidays()) return;
    holidayTrackingService.trackCallClick(autoClick);
  }, [isHolidays]);

  // Track chat with expert click
  const trackChatClick = useCallback((uniqChatId?:string, autoClick: boolean = false) => {
    if (!isHolidays()) return;
    holidayTrackingService.trackChatClick(uniqChatId, autoClick);
  }, [isHolidays]);

  // Track query/media click
  const trackQueryClick = useCallback((autoClick: boolean = false) => {
    if (!isHolidays()) return;
    holidayTrackingService.trackQueryClick(autoClick);
  }, [isHolidays]);

  // Track destination shown in chat modal
  const trackDestinationShown = useCallback((destination?: string) => {
    if (!isHolidays()) return;
    holidayTrackingService.trackDestinationShown(destination);
  }, [isHolidays]);

  // Track destination dropdown change
  const trackDestinationChange = useCallback((destination?: string) => {
    if (!isHolidays()) return;
    holidayTrackingService.trackDestinationChange(destination);
  }, [isHolidays]);

  // Track phone number field click
  const trackPhoneClick = useCallback((destination?: string) => {
    if (!isHolidays()) return;
    holidayTrackingService.trackPhoneClick(destination);
  }, [isHolidays]);

  // Track destination selection
  const trackDestinationSelected = useCallback((destination: string) => {
    if (!isHolidays()) return;
    holidayTrackingService.trackDestinationSelected(destination);
  }, [isHolidays]);

  // Track submit button click
  const trackSubmitClick = useCallback((destination?: string, uniqChatId?: string) => {
    if (!isHolidays()) return;
    holidayTrackingService.trackSubmitClick(destination, uniqChatId);
  }, [isHolidays]);

  return {
    isHolidays: isHolidays(),
    trackMediaShown,
    trackCallClick,
    trackChatClick,
    trackQueryClick,
    trackDestinationShown,
    trackDestinationChange,
    trackPhoneClick,
    trackDestinationSelected,
    trackSubmitClick,
  };
};
