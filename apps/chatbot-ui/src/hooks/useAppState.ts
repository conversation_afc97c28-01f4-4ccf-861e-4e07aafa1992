import { useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';

type AppStateCallback = () => void;

interface UseAppStateOptions {
  onForeground?: AppStateCallback;
  onBackground?: AppStateCallback;
}

export function useAppState({ onForeground, onBackground }: UseAppStateOptions) {
  const appState = useRef<AppStateStatus>(AppState.currentState);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        onForeground?.();
      }

      if (
        appState.current === 'active' &&
        nextAppState.match(/inactive|background/)
      ) {
        onBackground?.();
      }

      appState.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, [onForeground, onBackground]);
}
