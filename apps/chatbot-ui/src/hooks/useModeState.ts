import { useMemo, useEffect } from 'react';
import { useConfigSettings } from '../store/messages/newChatView';
import { useAppStateStore } from '../store/app';
import { useMessageStore } from '../store/messages/messageStore';

export const TRIP_PLAN_MODE_ID = 'TRIP-PLAN';
export const useModeState = () => {
  const { data: configData } = useConfigSettings();
  const { modes: _modesFromAppState } = useAppStateStore();
  const activeConversationId = useMessageStore(
    (state) => state.activeConversationId,
  );
  const conversation = useMessageStore(
    (state) => state.conversationById[activeConversationId || 'draft'],
  );
  const modeFromConversation = conversation?.mode;
  const modesToShow = useMemo(
    () => modeFromConversation || _modesFromAppState || [],
    [modeFromConversation, _modesFromAppState],
  );

  // Auto-enable mode if it comes as enabled from backend (first time only)
  useEffect(() => {
    if (modesToShow?.length > 0) {
      return; // Don't override if mode is already enabled locally
    }
    const displayCount = configData?.modes?.displayCount || 0;
    const visibleModesFromConfig =
      configData?.modes?.options?.filter((mode: Mode) => mode.visible === true) ||
      [];
    if (displayCount > 0) {
      useAppStateStore.setState({
        modes: visibleModesFromConfig,
      });
    }
  }, [configData?.modes?.displayCount, modesToShow, configData?.modes?.options]);

  const visibleModesFromConversation = useMemo(() => {
    return modeFromConversation?.filter((mode: Mode) => mode.visible === true) || [];
  }, [modeFromConversation]);

  const visibleModesFromAppState = useMemo(() => {
    return _modesFromAppState?.filter((mode: Mode) => mode.visible === true) || [];
  }, [_modesFromAppState]);
  // Derived values from single processing step
  const canShowModes = useMemo(() => {
    return activeConversationId === 'draft'
      ? Boolean(visibleModesFromAppState?.length)
      : Boolean(visibleModesFromConversation?.length);
  }, [
    activeConversationId,
    visibleModesFromAppState?.length,
    visibleModesFromConversation?.length,
  ]);
  return {
    modes:
      activeConversationId === 'draft'
        ? visibleModesFromAppState
        : visibleModesFromConversation,
    canShowModes,
  };
};
