import { useCallback } from 'react';
import {
  updateChatViewData,
  useConfigSettings,
} from '../store/messages/newChatView';
import { useAppStateStore } from '../store/app';
import { useMessageAction } from '../store/messages';

const useAutoPushMessagesToChat = () => {
  const { data } = useConfigSettings();
  const { sendMessage } = useMessageAction();
  const {
    showIngressMessage,
    ingressMessage,
    lobGreetingMessageContent,
    messageSourceOverride,
  } = data || {};
  const initialUserPrompt = useAppStateStore((state) => state.initialUserPrompt);

  const checkAndAutoPushToChat = useCallback(() => {
    if (lobGreetingMessageContent?.greetingMessageContent) {
      sendMessage(undefined, {
        completeMessage: {
          role: 'SYSTEM_MESSAGE',
          lang: 'en-IN',
          content: [lobGreetingMessageContent?.greetingMessageContent],
        },
        eventType: 'POST_GREETING_MESSAGE',
        dataProps: {
          lobGreetingMessageContent: lobGreetingMessageContent,
        },
      });
    }

    if (showIngressMessage) {
      sendMessage(undefined, {
        completeMessage: ingressMessage,
        dataProps: {
          chatSource: 'INGRESS',
          messageSource: messageSourceOverride ?? 'INGRESS',
        },
      });
    } else if (!ingressMessage && initialUserPrompt) {
      sendMessage(initialUserPrompt, {
        dataProps: {
          chatSource: 'INGRESS',
          messageSource: 'INGRESS',
        },
      });
      useAppStateStore.setState({ initialUserPrompt: null });
    }

    if (showIngressMessage || lobGreetingMessageContent?.greetingMessageContent) {
      updateChatViewData({
        showIngressMessage: false,
        lobGreetingMessageContent: {
          greetingMessageContent: undefined,
          tempGreetingMessage: false,
        },
        messageSourceOverride: undefined,
      });
    }

    if (showIngressMessage && initialUserPrompt) {
      useAppStateStore.setState({
        initialUserPrompt: null,
      });
    }
  }, [showIngressMessage, lobGreetingMessageContent, initialUserPrompt]);

  return { checkAndAutoPushToChat };
};

export default useAutoPushMessagesToChat;
