import { useCallback, useEffect } from 'react';
import { SocketClient } from '../store/socket';
import { useMessageStore } from '../store/messages';
import { SocketClientConfig, WebSocketState } from '../store/socket/SocketClient';
import { Endpoints } from '../network/endpoints';
import { config } from '../config';
import { Logger } from '../utils/logger';
import { useAppState } from './useAppState';

const subscriberCountRef = { current: 0 };

const socketConfig = {
  url: Endpoints.WEB_SOCKET,
  pingTimeout: config.heartBeatTimeoutInMs,
  pingInterval: config.heartBeatIntervalInMs,
  connectionTimeout: 5000,
  reconnectDelay: 1500,
  maxReconnectAttempts: config.reconnectAttemptsLimit,
  gcTime: config.socketGcTime,
};
const logger = Logger.createLogger({
  tag: 'TravelplexInstanceManager',
  level: 'DEBUG',
});

export function useInstanceManager() {
  const checkAndSetupSocket = useCallback(() => {
    const count = subscriberCountRef.current || 0;
    logger.debug(`onMount(). subscriberCount = ${count}`);
    const isSetupCalled = count > 0;

    subscriberCountRef.current = count + 1;

    if (!isSetupCalled) {
      // setup
      setup({ socketConfig });
    }
  }, []);

  const closeSocketSetup = useCallback(() => {
    subscriberCountRef.current = subscriberCountRef.current - 1;
    logger.info(`post GC-Time count=${subscriberCountRef.current}`);
    // check of new subscriber was added
    if (subscriberCountRef.current <= 0) {
      logger.info('Cleaning up');
      cleanUp();
    }
  }, []);

  const onBackground = useCallback(() => {}, []);

  const onForeground = useCallback(() => {
    logger.debug('onForeground()');

    const socketState = SocketClient.getSocketReadyState();
    if (
      socketState === WebSocketState.CLOSED ||
      socketState === WebSocketState.ERROR
    ) {
      logger.debug(
        `onForeground(). Socket is not open, current state: ${socketState}`,
      );
      // Reconnect if socket is not open
      SocketClient.reconnectManually();
    }
  }, []);

  useAppState({ onBackground: onBackground, onForeground: onForeground });

  useEffect(() => {
    checkAndSetupSocket();
    return () => {
      logger.debug(
        'onUnmount(). pre::subscriberCount = ',
        subscriberCountRef.current,
      );

      // wait for gcTime and then clean up
      setTimeout(() => {
        closeSocketSetup();
      }, socketConfig.gcTime);
    };
  }, [checkAndSetupSocket, closeSocketSetup]);
}

let socketMessageStoreListener: (() => void) | undefined;
let socketStateChangeListener: (() => void) | undefined;

function setup(options: { socketConfig: SocketClientConfig }) {
  logger.debug('setup()');
  SocketClient.connect(options.socketConfig);
  socketMessageStoreListener = SocketClient.subscribe((msg) => {
    useMessageStore.getState().handleMessageEvent(msg);
  });
  socketStateChangeListener = SocketClient.onSocketStateChange(
    (newState, isActive) => {
      useMessageStore.getState().handleSocketStateChange(newState, isActive);
    },
  );
}

function cleanUp() {
  logger.debug('cleanUp()');
  SocketClient.disconnect();
  // useMessageStore.setState(useMessageStore.getInitialState());
  // useAppStateStore.setState(useAppStateStore.getInitialState());
  if (socketMessageStoreListener) {
    socketMessageStoreListener();
    socketMessageStoreListener = undefined;
  }
  if (socketStateChangeListener) {
    socketStateChangeListener();
    socketStateChangeListener = undefined;
  }
}
