import { useConfigSettings } from '../store/messages/newChatView';

const defaultSettings = {
  featureConfig: {
    showMimaOptions: false,
    showBookmarkOptions: true,
    showReadMore: false,
    streamMarkdown: true,
    showMimaOptionsCTA: false,
    showIntroPopupModal: true,
    shouldIncreaseFontSize: false,
    showCopyIcon: true,
    showSpeakerCoachmark: true,
    showMicCoachmark: true,
  },
};
function useGetFeatureFlags() {
  const { data } = useConfigSettings();
  const { featureConfig = defaultSettings.featureConfig } = data || defaultSettings;

  return featureConfig;
}

export default useGetFeatureFlags;
