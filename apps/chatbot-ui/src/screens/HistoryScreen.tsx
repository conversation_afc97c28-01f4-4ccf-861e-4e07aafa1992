import React, { useDeferredValue, useEffect, useMemo, useRef } from 'react';
import { Animated, Easing, StyleSheet, Text, View, ViewStyle } from 'react-native';
import { useConversationsHistory } from '../store/messages';
import { COLORS, FONTS, Z_INDEX } from '../constants/globalStyles';
import ConversationCard from '../components/message/cards/ConversationCard';
import { useBackToChats } from '../utils/useBackHandler';
import { BottomSheetScreen } from './BaseBottomSheetScreen';
import { BaseHeader } from '../components/header/BaseHeader';
import { trackOmnitureClickEvent } from '../native/omniture';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../native/tracking/pdt';
import { useAppStateStore } from '../store/app';
import { ActionTypes } from '../containers/types';
import { config } from '../config';
import { HEADER_CONTEXT, INPUT_BOX_HEIGHT } from '../const';
import TabSwitcher from '../components/tab-switcher';
import { FlatList } from 'react-native-gesture-handler';
import { MenuSection } from './MessagesScreen';
import { useHandleConversationClick } from '../store/messages/useConversationsHistory';
import { deletePopupConfig } from '../network/api/conversationApi';

type ChatHistoryProps = {
  showOnlyRecent?: boolean;
  title: string;
};

type ViewState = 'loading' | 'error' | 'empty' | 'loaded';
function useAnimatedValue(initialValue: number): Animated.Value {
  const ref = useRef<null | Animated.Value>(null);
  if (ref.current == null) {
    ref.current = new Animated.Value(initialValue);
  }
  return ref.current;
}
export const HistoryScreen = React.memo(
  ({ showOnlyRecent, title }: ChatHistoryProps) => {
    // click back-button to go back to chat
    useBackToChats();

    useEffect(() => {
      if (showOnlyRecent) {
        return;
      }
      trackOmnitureClickEvent('HISTORY_SCREEN_SHOWN');
    }, [showOnlyRecent]);

    const {
      isLoading,
      isError,
      conversationGroup,
      agentConversationGroup,
      conversationGroupsTitle,
      agentConversationGroupsTitle,
      pageTitle,
      chatHeaderConfig,
      popupConfigs,
    } = useConversationsHistory();

    const [activeIndex, setActiveIndex] = React.useState(0);
    const tabsData = useMemo(
      () => [
        conversationGroupsTitle || config.conversationGroupsTitle,
        agentConversationGroupsTitle || config.agentConversationGroupsTitle,
      ],
      [conversationGroupsTitle, agentConversationGroupsTitle],
    );

    const activeConversationsGroup = useDeferredValue(
      activeIndex === 0 ? conversationGroup : agentConversationGroup,
    );

    const viewState: ViewState = isLoading
      ? 'loading'
      : isError
        ? 'error'
        : 'loaded';

    if (showOnlyRecent) {
      if (viewState !== 'loaded') {
        return null;
      }
      return (
        <View style={styles.chatHistoryContainer}>
          <Text style={styles.chatHistoryGroupTitle}>{title}</Text>
        </View>
      );
    }

    const menuContext = chatHeaderConfig || HEADER_CONTEXT.historyScreen;

    const { header } = menuContext;
    const { leftItems, rightItems } = header;
    const hasLeftItems = leftItems && leftItems.length > 0;
    const hasRightItems = rightItems && rightItems.length > 0;

    return (
      <BottomSheetScreen viewName={'history'}>
        <BaseHeader
          leftMenu={hasLeftItems ? <MenuSection menuContext={header} /> : null}
          rightMenu={
            hasRightItems ? <MenuSection menuContext={header} type="right" /> : null
          }
          title={pageTitle}
          isRightMenuDetailed={!!header.rightItems?.[0]?.text}
        />
        {viewState === 'loading' && (
          <View style={styles.groupContainer}>
            <LoadingState />
          </View>
        )}
        {viewState === 'error' && (
          <View style={[styles.noContentContainer, { marginTop: 200 }]}>
            <Text style={styles.noContentText}>No conversations found</Text>
          </View>
        )}
        {viewState === 'loaded' && (
          <View style={[styles.groupContainer, styles.bottomSheetContainer]}>
            <View style={styles.tabContainer}>
              <TabSwitcher
                tabs={tabsData}
                activeIndex={activeIndex}
                setActiveIndex={setActiveIndex}
              />
            </View>
            <ConversationListView
              conversationList={activeConversationsGroup}
              popupConfigs={popupConfigs}
            />
          </View>
        )}
      </BottomSheetScreen>
    );
  },
);

const ConversationListView = React.memo(
  ({
    conversationList,
    popupConfigs,
  }: {
    conversationList: ConversationHistoryGroup[];
    popupConfigs: { delete: deletePopupConfig } | null | undefined;
  }) => {
    const chatContext = useAppStateStore((state) => state.chatContext);
    const lob = chatContext?.context?.lob || '';
    const handleConversationClick = useHandleConversationClick();
    const onAction = useAppStateStore((state) => state.onAction);
    const renderConversationGroup = ({
      item,
    }: {
      item: ConversationHistoryGroup;
    }) => (
      <View style={{ flexDirection: 'column' }}>
        <Text style={styles.groupTitle}>{item.title}</Text>
        <FlatList
          data={item.conversations}
          keyExtractor={(conversation) => conversation.conversationId}
          renderItem={({ item: conversation }) => (
            <ConversationCard
              contextMenuDataMap={item.contextMenuDataMap}
              key={conversation.conversationId}
              compact={false}
              conversation={conversation}
              popupConfigs={popupConfigs}
              handleConversationClick={() => {
                handleConversationClick(
                  conversation.conversationId,
                  conversation.title,
                );

                // Callback to parent
                onAction?.({
                  lob,
                  actionType: ActionTypes.ContextChange,
                  actionPayload: {
                    conversationId: conversation.conversationId,
                    contextId: conversation?.contextId,
                    lobName: conversation?.bookingLob,
                  },
                });

                trackOmnitureClickEvent('OLD_CHAT_SELECTED', {
                  VAR_CHAT_TYPE: 'chat_old',
                });
                trackPDTEvent({
                  eventName: eventNameSchema.CHAT_INTERACTED,
                  eventType: eventTypeSchema.ACTION,
                  eventValue: eventValueSchema.CHAT_HISTORY_CLICKED,
                });
              }}
            />
          )}
          initialNumToRender={5}
        />
      </View>
    );

    return (
      <FlatList
        data={conversationList}
        keyExtractor={(item) => item.title}
        renderItem={({ item }) => renderConversationGroup({ item })}
        ListEmptyComponent={
          <View style={[styles.noContentContainer, { marginTop: 200 }]}>
            <Text style={styles.noContentText}>No conversations found</Text>
          </View>
        }
        contentContainerStyle={{ flexDirection: 'column', gap: 16 }}
        initialNumToRender={3}
        showsVerticalScrollIndicator={false}
      />
    );
  },
);

function Skeleton({
  value,
  width,
  height,
}: {
  width: ViewStyle['width'];
  height: ViewStyle['height'];
  value: Animated.Value;
}) {
  return (
    <Animated.View
      style={{
        borderRadius: 8,
        backgroundColor: COLORS.GREY_VAR_1,
        width,
        height,
        opacity: value,
      }}
    />
  );
}

function LoadingState() {
  const value = useAnimatedValue(0.1);
  useEffect(() => {
    //FIXME add interpolation later
    Animated.loop(
      Animated.sequence([
        Animated.timing(value, {
          toValue: 0.2,
          easing: Easing.back(0.1),
          duration: 1500,
          useNativeDriver: true,
        }),
      ]),
    ).start();
  }, []);
  return (
    <View>
      <Skeleton height={22} width={60} value={value} />
      {[1, 2, 3].map((_, index) => {
        return (
          <View key={index} style={styles.conversationContainer}>
            <View style={styles.cardLeftSection}>
              <Skeleton height={20} width={'60%'} value={value} />
              <Skeleton height={16} width={'30%'} value={value} />
            </View>
            <Skeleton height={16} width={50} value={value} />
          </View>
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  bottomSheetContainer: {
    flex: 1,
    marginBottom: INPUT_BOX_HEIGHT,
    zIndex: Z_INDEX.BOTTOM_SHEET,
  },
  chatHistoryContainer: {
    flexDirection: 'column',
    flex: 1,
    width: '100%',
  },
  chatHistoryGroupTitle: {
    paddingHorizontal: 16,
    fontSize: 12,
    color: COLORS.TEXT_LOW_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_900,
  },
  groupContainer: {
    flexDirection: 'column',
    position: 'relative',
    flex: 1,
    paddingHorizontal: 16,
    marginTop: 16,
    marginBottom: 32,
    gap: 6,
  },
  noContentContainer: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  noContentText: {
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_700,
    marginBottom: 8,
  },
  groupTitle: {
    fontSize: 12,
    color: COLORS.GREY_VAR_2,
    fontFamily: FONTS.FONT_FAMILY_900,
    marginBottom: 8,
  },
  conversationContainer: {
    padding: 8,
    flexDirection: 'row',
    borderRadius: 16,
    borderWidth: 1,
    overflow: 'hidden',
    marginVertical: 6,
    borderColor: '#D8D8D8',
    backgroundColor: COLORS.WHITE,
  },
  cardLeftSection: { flexDirection: 'column', flex: 1, gap: 4 },

  tabContainer: {
    marginBottom: 15,
  },
});
