import React, { useEffect, useCallback } from 'react';
import { Text, View, StyleSheet } from 'react-native';
import { EmptyBookmark } from '../assets/EmptyBookmark';
import GenericErrorIcon from '../assets/GenericErrorIcon';
import { config } from '../config';
import { COLORS, FONTS, Z_INDEX } from '../constants/globalStyles';
import { TouchableFeedback } from '../components/base/TouchableFeeback';
import LinearGradient from 'react-native-linear-gradient';
import PageErrorIcon from '../assets/PageErrorIcon';
import { PageNotFoundIcon } from '../assets/PageNotFoundIcon';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
  EventCombinationFinal,
} from '../native/tracking/pdt';

type ErrorTypes =
  | 'config'
  | 'messages'
  | 'bookmark'
  | 'pageNotFound'
  | 'errorBoundary';

type ErrorStateType = {
  icon: React.ReactNode;
  title: string;
  description: string;
  cta?: string;
  doLogging?: boolean;
  loggingConfig?: {
    pdtConfig: {
      shownPDT?: EventCombinationFinal;
      ctaClickedPDT?: EventCombinationFinal;
    };
  };
};

type ErrorStateProps = {
  [key in ErrorTypes]: ErrorStateType;
};

const ErrorState: ErrorStateProps = {
  config: {
    icon: <GenericErrorIcon />,
    title: config.somethingWentWrongText,
    description: config.tryAgainText,
    cta: 'Retry',
    doLogging: true,
    loggingConfig: {
      pdtConfig: {
        shownPDT: {
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: eventValueSchema.CONFIG_ERROR_SHOWN,
        },
        ctaClickedPDT: {
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: eventValueSchema.CONFIG_ERROR_RETRY_CLICKED,
        },
      },
    },
  },
  bookmark: {
    icon: <EmptyBookmark />,
    title: 'No Bookmarks Yet!',
    description:
      "Bookmark your top picks for easy access when you're ready to book.",
    cta: 'Explore Now',
  },
  messages: {
    icon: <PageErrorIcon />,
    title: config.somethingWentWrongText,
    description: config.tryAgainText,
    cta: 'Start a new chat',
    doLogging: true,
    loggingConfig: {
      pdtConfig: {
        shownPDT: {
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: eventValueSchema.MESSAGES_ERROR_SHOWN,
        },
        ctaClickedPDT: {
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: eventValueSchema.MESSAGES_ERROR_RETRY_CLICKED,
        },
      },
    },
  },
  errorBoundary: {
    icon: <GenericErrorIcon />,
    title: config.somethingWentWrongText,
    description: config.tryAgainTextErrorBoundary,
    cta: 'Retry',
    doLogging: true,
    loggingConfig: {
      pdtConfig: {
        shownPDT: {
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: eventValueSchema.GENERIC_ERROR_SHOWN,
        },
        ctaClickedPDT: {
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: eventValueSchema.GENERIC_ERROR_RETRY_CLICKED,
        },
      },
    },
  },
  pageNotFound: {
    icon: <PageNotFoundIcon />,
    title: config.pageNotFoundTitle,
    description: config.pageNotFoundSubTitle,
    doLogging: true,
    loggingConfig: {
      pdtConfig: {
        shownPDT: {
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: eventValueSchema.PAGE_NOT_FOUND_SHOWN,
        },
      },
    },
  },
};

export function ErrorScreen({
  type,
  onCtaClick,
}: {
  type?: ErrorTypes;
  onCtaClick?: () => void;
}) {
  useEffect(() => {
    const errorState = type && ErrorState[type];
    const shownPDT = errorState?.loggingConfig?.pdtConfig?.shownPDT;
    if (shownPDT) {
      trackPDTEvent(shownPDT);
    }
  }, [type]);
  const { icon, title, description, cta } =
    (type && ErrorState?.[type]) || ErrorState.config;
  const handleCtaClick = useCallback(() => {
    const errorState = type && ErrorState[type];
    const ctaClickedPDT = errorState?.loggingConfig?.pdtConfig?.ctaClickedPDT;
    if (ctaClickedPDT) {
      trackPDTEvent(ctaClickedPDT as EventCombinationFinal);
    }
    onCtaClick?.();
  }, [type, onCtaClick]);
  return (
    <View style={styles.container}>
      {icon}
      <Text
        style={{
          color: COLORS.BLACK,
          fontSize: 18,
          marginTop: 32,
          marginBottom: 12,
          fontFamily: FONTS.FONT_FAMILY_700,
        }}
      >
        {title}
      </Text>
      <Text
        style={{
          color: COLORS.GREY_VAR_2,
          fontSize: 14,
          marginBottom: 36,
          fontFamily: FONTS.FONT_FAMILY_400,
          paddingHorizontal: 32,
          textAlign: 'center',
          maxWidth: '80%',
        }}
      >
        {description}
      </Text>
      {cta ? (
        <TouchableFeedback
          onPress={handleCtaClick}
          containerStyle={{
            marginBottom: 32,
            // backgroundColor: 'red',
            flexDirection: 'row',
            borderRadius: 8,
            overflow: 'hidden',
            marginHorizontal: 16,
            alignSelf: 'stretch',
          }}
          style={{
            flex: 1,
          }}
        >
          <LinearGradient
            colors={['rgb(83,178, 254)', 'rgb(6,90, 243)']}
            useAngle
            angle={90}
            style={{
              alignItems: 'center',
              padding: 16,
              borderRadius: 8,
            }}
          >
            <Text
              style={{
                color: COLORS.WHITE,
                fontSize: 16,
                fontFamily: FONTS.FONT_FAMILY_700,
              }}
            >
              {cta}
            </Text>
          </LinearGradient>
        </TouchableFeedback>
      ) : null}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingBottom: 48,
    zIndex: Z_INDEX.ERROR_SCREEN,
    backgroundColor: COLORS.WHITE,
  },
});
