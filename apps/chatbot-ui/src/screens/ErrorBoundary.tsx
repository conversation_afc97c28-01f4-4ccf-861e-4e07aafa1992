import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Logger } from '../utils/logger';
import { ErrorScreen } from './ErrorScreen';
import { logPageError } from '../analytics/telemetry';

const logger = Logger.createLogger({
  tag: 'ErrorBoundary',
  level: 'DEBUG',
});

interface ErrorBoundaryProps {
  children: ReactNode;
  pageName: string;
  onReset?: () => void;
  onErrorReset?: () => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): ErrorBoundaryState {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    logger.error('ErrorBoundary caught an error', error, errorInfo);
    logPageError(this.props.pageName, error.message);
  }

  handleReset = () => {
    this.setState({ hasError: false });
    // Optionally, you can call a prop callback if you want parent to reset as well
    if (this.props.onErrorReset) {
      this.props.onErrorReset();
    } else if (this.props.onReset) {
      this.props.onReset();
    }
  };

  render() {
    if (this.state.hasError) {
      return <ErrorScreen type="errorBoundary" onCtaClick={this.handleReset} />;
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
