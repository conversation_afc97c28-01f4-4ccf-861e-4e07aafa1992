import React, { useCallback, useEffect } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { BaseHeader } from '../components/header/BaseHeader';
import { COLORS, FONTS } from '../constants/globalStyles';
import { ScrollView } from 'react-native-gesture-handler';
import { ShadowedView } from 'react-native-fast-shadow';
import { useAppStateStore } from '../store/app';
import FastImageWrapper from '../components/image/FastImageWrapper';
import { Button, ButtonType } from '../components/button';
import { TableRenderer } from '../components/base/table/TableRenderer';
import { useMessageAction, useMessageStore } from '../store/messages';
import { config } from '../config';
import { Toast } from '../components/toast';
import { HEADER_CONTEXT } from '../const';
import { MenuSection } from './MessagesScreen';
import { trackOmnitureClickEvent } from '../native/omniture';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../native/tracking/pdt';
import { MarkdownText } from '../components/message/MarkdownText';
import { Logger } from '../utils/logger';

const logger = Logger.createLogger({ tag: 'ItineraryScreen' });

export function ItineraryScreen() {
  const { itineraryData, setCurrentView } = useAppStateStore();
  const { sendMessage } = useMessageAction();
  const updateConversationById = useMessageStore(
    (state) => state.updateConversationById,
  );
  const activeConversationId = useMessageStore(
    (state) => state.activeConversationId || 'draft',
  );
  const title = itineraryData?.data?.title;
  const subTitle = itineraryData?.data?.subTitle;
  const itineraryDays = itineraryData?.data?.itineraryDays;
  const calloutText = itineraryData?.data?.callout?.text;
  const cta = itineraryData?.data?.cta;
  const detailedContentType = itineraryData?.data?.detailedContent?.type;
  const detailedContentData = itineraryData?.data?.detailedContent?.data;
  const infoText = itineraryData?.data?.info?.text;
  const handleClick = useCallback(
    (obj: ItineraryCta) => {
      // Add safety checks at the beginning
      if (!obj || !itineraryData?.data) {
        logger.warn('Invalid data in handleClick', { obj, itineraryData });
        return;
      }

      trackOmnitureClickEvent(
        obj.action === 'MESSAGE' ? 'GO_AHEAD_CLICKED' : 'ASK_QUESTION_CLICKED',
      );

      trackPDTEvent({
        eventName: eventNameSchema.CHAT_INTERACTED,
        eventType: eventTypeSchema.ACTION,
        eventValue:
          obj.action === 'MESSAGE'
            ? eventValueSchema.GO_AHEAD_CLICKED
            : eventValueSchema.ASK_QUESTION_CLICKED,
      });

      try {
        if (obj.action === 'MESSAGE') {
          // GO Ahead with itinerary option
          // TODO: Implement primary button click
          // add to store
          if (!activeConversationId) {
            logger.warn('No active conversation ID available');
            return;
          }

          updateConversationById(activeConversationId, {
            sessionContext: {
              type: 'CONTEXT',
              data: itineraryData?.data,
              conversationMetadata: {
                messageId: itineraryData?.data?.msgId || '',
                contentId: itineraryData?.data?.id || '',
                cardId: itineraryData?.data?.id || '',
                quoteMetadata: itineraryData?.data?.quote_metadata || {},
                templatePayload: itineraryData,
                type: 'CARD',
              },
            },
          });

          // Safe toast display
          const toastMessage = `Your preference has been set to ${itineraryData?.data?.title || 'selected itinerary'}`;
          Toast.show(toastMessage);

          const messageText =
            obj.messageText ||
            config?.itineraryMessageText ||
            'I want to go ahead with the itinerary';
          if (!messageText) {
            logger.warn('No message text available for sending');
            return;
          }

          sendMessage(messageText, {
            completeMessage: {
              lang: 'en-IN',
              role: 'USER',
              content: [
                {
                  type: 'TEXT',
                  value: messageText,
                  quotedContentInfo: {
                    messageId: itineraryData?.data?.msgId || '',
                    contentId: itineraryData?.data?.id || '',
                    cardId: itineraryData?.data?.id || '',
                    quoteMetadata: itineraryData?.data?.quote_metadata || {},
                    templatePayload: itineraryData,
                    type: 'CARD',
                  },
                },
              ],
            },
            dataProps: {
              messageSource: 'CTA',
            },
          });
        } else if (obj.action === 'REPLY') {
          // Ask for more details
          // TODO: Implement secondary button click
          if (!activeConversationId) {
            logger.warn('No active conversation ID available for REPLY action');
            return;
          }

          updateConversationById(activeConversationId, {
            sessionContext: {
              type: obj.action,
              data: itineraryData?.data,
              conversationMetadata: {
                messageId: itineraryData?.data?.msgId || '',
                contentId: itineraryData?.data?.id || '',
                cardId: itineraryData?.data?.id || '',
                quoteMetadata: itineraryData?.data?.quote_metadata || {},
                templatePayload: itineraryData,
                type: 'CARD',
              },
            },
          });
        }
        setCurrentView?.('chat');
      } catch (error) {
        logger.error('Error in handleClick', error);
        // Don't crash the app, just log the error
        Toast.show('An error occurred. Please try again.');
      }
    },
    [
      setCurrentView,
      sendMessage,
      itineraryData?.data,
      activeConversationId,
      updateConversationById,
    ],
  );

  const menuContext = HEADER_CONTEXT.itineraryScreen;

  const { header } = menuContext;
  const { leftItems, rightItems } = header;
  const hasLeftItems = leftItems && leftItems.length > 0;
  const hasRightItems = rightItems && rightItems.length > 0;

  useEffect(() => {
    try {
      trackOmnitureClickEvent('ITINERARY_SCREEN_SHOWN');
      trackPDTEvent({
        eventName: eventNameSchema.CHAT_INTERACTED,
        eventType: eventTypeSchema.ACTION,
        eventValue: eventValueSchema.ITINERARY_DETAILS_SHOWN,
      });
    } catch (error) {
      logger.warn('Failed to track page load events', error);
    }
  }, []);

  return (
    <View style={styles.container}>
      <BaseHeader
        leftMenu={hasLeftItems ? <MenuSection menuContext={header} /> : null}
        rightMenu={
          hasRightItems ? <MenuSection menuContext={header} type="right" /> : null
        }
        title={title}
        isRightMenuDetailed={!!header.rightItems?.[0]?.text}
      />
      <View style={styles.content}>
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <ShadowedView style={styles.shadow}>
            <View>
              <View style={styles.header}>
                {title && <Text style={styles.itineraryName}>{title}</Text>}
                {itineraryDays && (
                  <Text style={styles.headerBadge}>{itineraryDays}</Text>
                )}
              </View>
              {subTitle && <Text style={styles.journeyText}>{subTitle}</Text>}
              {calloutText && <Text style={styles.matchingText}>{calloutText}</Text>}
              {infoText && (
                <View style={styles.botAlert}>
                  <View style={styles.botIcon}>
                    <FastImageWrapper
                      style={styles.botIconImage}
                      source={require('../assets/MyraIcon.webp')}
                      resizeMode="cover"
                    />
                  </View>
                  <MarkdownText
                    text={infoText}
                    color={{
                      light: '#3366F1',
                      default: '#3366F1',
                      dark: '#3366F1',
                    }}
                    marginBottom={0}
                    marginTop={0}
                  />
                </View>
              )}
            </View>
            {/* {table && <TableRenderer data={table} />} */}
          </ShadowedView>
          {detailedContentType === 'table' && detailedContentData && (
            <View
              style={[
                styles.tableContainer,
                detailedContentData.layout === 'basic' && { marginRight: 16 },
              ]}
            >
              <TableRenderer data={detailedContentData} />
            </View>
          )}
        </ScrollView>
        <View style={styles.floatingButtons}>
          {cta?.map((obj: ItineraryCta, index: number) => (
            <Button
              key={`${obj.ctaText}-${index}`}
              title={obj.ctaText}
              onPress={() => handleClick(obj)}
              buttonType={obj.type as ButtonType}
            />
          ))}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
  content: {
    flex: 1,
    borderTopWidth: 1.5,
    borderTopColor: '#EAEAEA',
  },
  scrollView: {
    flex: 1,
  },
  shadow: {
    backgroundColor: '#FDFDFD',
    borderColor: '#FFFFFF',
    borderWidth: 1,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    margin: 16,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  itineraryName: {
    fontFamily: FONTS.FONT_FAMILY_700,
    fontSize: 18,
    lineHeight: 20,
    color: COLORS.BLACK,
    flex: 1,
    marginRight: 8,
  },
  headerBadge: {
    paddingVertical: 2,
    paddingHorizontal: 4,
    backgroundColor: '#0C58B4',
    borderRadius: 4,
    fontSize: 12,
    lineHeight: 14,
    color: COLORS.WHITE,
    fontFamily: FONTS.FONT_FAMILY_700,
    textAlign: 'center',
    alignSelf: 'flex-start',
    overflow: 'hidden',
  },
  journeyText: {
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 14,
    lineHeight: 16,
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
    marginBottom: 6,
  },
  matchingText: {
    color: '#007E7D',
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_700,
    lineHeight: 16,
  },
  botAlert: {
    borderWidth: 1,
    borderColor: '#51AFE6',
    borderRadius: 12,
    backgroundColor: '#F7F9FF',
    paddingVertical: 12,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 15,
  },
  botIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  botIconImage: {
    width: 30,
    height: 29,
  },
  botText: {
    color: '#3366F1',
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 14,
    lineHeight: 16,
    flex: 1,
  },
  floatingButtons: {
    paddingTop: 1,
    paddingBottom: 24,
    paddingHorizontal: 16,
    backgroundColor: COLORS.WHITE,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
    gap: 12,
  },
  buttonDivider: {
    height: 12,
  },
  button: {
    width: '100%',
    height: 44,
    borderRadius: 8,
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
  },
  secondaryButton: {
    borderWidth: 1,
    borderColor: '#008CFF',
  },
  disabledButton: {
    opacity: 0.6,
  },
  primaryButtonText: {
    color: COLORS.WHITE,
  },
  secondaryButtonText: {
    color: COLORS.BLUE_VAR_1,
  },
  commonButtonText: {
    fontFamily: FONTS.FONT_FAMILY_900,
    fontSize: 16,
    lineHeight: 18,
    textAlign: 'center',
  },
  primaryGradient: {
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tableContainer: {
    marginLeft: 16,
  },
});
