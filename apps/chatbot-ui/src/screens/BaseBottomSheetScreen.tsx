import { StyleSheet, View } from 'react-native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { MessagesScreen } from './MessagesScreen';
import { useAppStateStore } from '../store/app';
import { HistoryScreen } from './HistoryScreen';
import { BookmarkScreen } from './BookmarkScreen';
import { useMessageStore } from '../store/messages';
import { ModalProvider } from './ModalProvider';
import { COLORS, Z_INDEX } from '../constants/globalStyles';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { useInstanceManager } from '../hooks/UseInstanceManager';
import { ChatBotViewState } from '../containers/types';
import ErrorBoundary from './ErrorBoundary';
import { ItineraryScreen } from './ItineraryScreen';
import TripsScreen from './Trips/TripsScreen';
import IntroPopupModal from './IntroPopupModal';

interface BaseBottomSheetScreenProps {
  bottomSheetState: ChatBotViewState | null;
  startupTraceId: string;
}

const BaseBottomSheetScreen = ({
  bottomSheetState,
  startupTraceId,
}: BaseBottomSheetScreenProps) => {
  const { currentView, setCurrentView } = useAppStateStore();
  const [resetKey, setResetKey] = useState(0);

  const reset = useCallback(() => {
    setResetKey((i) => i + 1);
  }, []);
  const convId = useMessageStore((state) => state.activeConversationId || 'draft');
  const convIdPrevVal = useRef(convId);

  useEffect(() => {
    const isConversationSwitched =
      convIdPrevVal.current !== 'draft' && convIdPrevVal.current !== convId;
    if (bottomSheetState === 'collapsed' || isConversationSwitched) {
      reset();
    }
    convIdPrevVal.current = convId;
  }, [convId, reset, bottomSheetState]);

  // bind the socket client to lifecycle of chat screen instance
  useInstanceManager();

  const onErrorReset = useCallback(() => {
    setCurrentView?.('chat');
  }, [setCurrentView]);

  return (
    <View style={styles.root}>
      <ErrorBoundary pageName={'messages'} onErrorReset={reset}>
        <MessagesScreen key={resetKey} startupTraceId={startupTraceId} />
      </ErrorBoundary>
      {currentView === 'bookmarks' && (
        <Animated.View entering={FadeIn} exiting={FadeOut} style={styles.overlay}>
          <SafeAreaProvider>
            <ErrorBoundary pageName={'bookmark'} onErrorReset={onErrorReset}>
              <BookmarkScreen />
            </ErrorBoundary>
          </SafeAreaProvider>
        </Animated.View>
      )}
      {currentView === 'history' && (
        <Animated.View entering={FadeIn} exiting={FadeOut} style={styles.overlay}>
          <ErrorBoundary pageName={'history'} onErrorReset={onErrorReset}>
            <HistoryScreen title={''} />
          </ErrorBoundary>
        </Animated.View>
      )}
      {currentView === 'itinerary' && (
        <Animated.View entering={FadeIn} exiting={FadeOut} style={styles.overlay}>
          <SafeAreaProvider>
            <ErrorBoundary pageName={'itinerary'} onErrorReset={onErrorReset}>
              <ItineraryScreen />
            </ErrorBoundary>
          </SafeAreaProvider>
        </Animated.View>
      )}
      {currentView === 'trips' && (
        <Animated.View entering={FadeIn} exiting={FadeOut} style={styles.overlay}>
          <ErrorBoundary pageName={'trips'} onErrorReset={onErrorReset}>
            <TripsScreen />
          </ErrorBoundary>
        </Animated.View>
      )}
      <IntroPopupModal />
    </View>
  );
};

type BottomSheetScreenProps = {
  viewName: 'chat' | 'history' | 'bookmarks' | 'introPopup';
  children: React.ReactNode;
};

export function BottomSheetScreen({ viewName, children }: BottomSheetScreenProps) {
  return (
    <ModalProvider
      style={{
        flexDirection: 'column',
        flex: 1,
        height: '100%',
        position: 'relative',
      }}
    >
      {children}
    </ModalProvider>
  );
}

const styles = StyleSheet.create({
  root: {
    flexDirection: 'column',
    flex: 1,
    height: '100%',
    position: 'relative',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: COLORS.WHITE,
    zIndex: Z_INDEX.MODAL, // just above the FAB
  },
});

export default BaseBottomSheetScreen;
