import { StyleSheet, Text, useWindowDimensions, View } from 'react-native';
import React, { useCallback, useEffect, useState } from 'react';
import { BottomModalPopup } from '../components/BottomModalPopup/index';
import { BottomSheetScreen } from './BaseBottomSheetScreen';
import FastImageWrapper from '../components/image/FastImageWrapper';
import StorageUtils from '../utils/storageUtils';
import {
  updateChatViewData,
  useConfigSettings,
} from '../store/messages/newChatView';
import useGetFeatureFlags from '../hooks/useGetFeatureFlags';
import { Toast } from '../components/toast';
import { MessageListLoader } from '../components/message-list-loader';
import GradientText from '../components/base/GradientText';
import { COLORS, FONTS } from '../constants/globalStyles';
import { AiStarComp } from '../components/base/AiStarComp';
import { Logger } from '../utils/logger';
import { useBackAction } from '../utils/useBackHandler';
import { Button, ButtonType } from '../components/button';
import { useAppStateStore } from '../store/app';

const log = Logger.createLogger({ tag: 'IntroPopupModal' });

// Simple placeholder loader component
const ImageLoader = ({ height }: { height: number }) => {
  return (
    <View
      style={{
        width: '100%',
        height: height,
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <MessageListLoader />
    </View>
  );
};

const TextOverlay = ({
  heading,
  title,
  subtitle,
  ctaText,
  modeId,
  onClose,
}: {
  heading?: string;
  title?: string;
  subtitle?: string;
  ctaText?: string;
  modeId?: string;
  onClose: () => void;
}) => {
  const { updateMode } = useAppStateStore();
  return (
    <View style={styles.textOverlay}>
      {heading && <Text style={styles.heading}>{heading}</Text>}
      {title && (
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}
        >
          <GradientText
            gradientColors={['#51AFE6', '#355FF2', '#11287A']}
            style={styles.title}
            start={{ x: 0, y: 0 }}
            end={{ x: 0.65, y: 0 }}
          >
            {title}
          </GradientText>
          <AiStarComp
            containerStyle={{
              height: 28,
              width: 28,
              alignSelf: 'flex-start',
            }}
            primaryStarSize={18}
            secondaryStarSize={10}
            primaryStarStyle={{ top: -4, left: -10 }}
            secondaryStarStyle={{ top: 8, left: 2 }}
          />
        </View>
      )}
      {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      {ctaText && (
        <Button
          buttonType={ButtonType.Primary}
          title={ctaText}
          onPress={() => {
            if (modeId) {
              updateMode?.(modeId, true);
            }
            onClose();
          }}
          customStyle={{ marginTop: 20 }}
        />
      )}
    </View>
  );
};

const IntroPopupModalWrapper = ({
  introPopupConfig,
  isOpen,
  setIsOpen,
}: {
  introPopupConfig: IntroPopupConfig;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}) => {
  const { height: windowHeight } = useWindowDimensions();
  const [isLoading, setIsLoading] = useState(true);
  const [fallbackImageLoaded, setFallbackImageLoaded] = useState(false);
  const [animatedImageLoaded, setAnimatedImageLoaded] = useState(false);
  const [fallbackImageError, setFallbackImageError] = useState(false);
  const [animatedImageError, setAnimatedImageError] = useState(false);

  const popupHeight = 0.6 * windowHeight;
  const resizeMode = introPopupConfig?.resizeMode || 'cover';

  // Extract text content from config
  const heading = introPopupConfig?.heading;
  const title = introPopupConfig?.title;
  const subtitle = introPopupConfig?.subtitle;
  const ctaText = introPopupConfig?.ctaText;
  const modeId = introPopupConfig?.modeId;

  // Show text overlay only if title exists
  const showTextOverlay = Boolean(title);

  // Determine which image to show
  const shouldShowAnimatedImage = animatedImageLoaded && !animatedImageError;
  const shouldShowFallbackImage = fallbackImageLoaded && !fallbackImageError;
  const canShowAnyImage = shouldShowFallbackImage || shouldShowAnimatedImage;

  // Common function to update chat view data and storage
  const updateChatViewDataAndStorage = () => {
    // Update chat view data and storage
    updateChatViewData({
      introPopupConfig: {
        ...introPopupConfig,
        forceShowIntroPopup: false,
        showPopup: false,
      },
    });

    // Fire off async operations without awaiting them (fire and forget)
    StorageUtils.getIntroPopupConfig()
      .then((config) => {
        // Only update storage if showIntroPopupOnce was true
        if (config?.showIntroPopupOnce) {
          return StorageUtils.setIntroPopupConfig({
            showIntroPopupOnce: false,
          });
        }
        return undefined;
      })
      .catch(() => {
        // Handle error silently or log appropriately
      });
  };

  // Handle fallback image load
  const handleFallbackImageLoad = () => {
    setFallbackImageLoaded(true);
    setFallbackImageError(false);

    // If this is the first image to load, hide the loader
    if (!animatedImageLoaded) {
      setIsLoading(false);
    }

    // If animated image failed to load, update chat view data and storage
    if (animatedImageError) {
      updateChatViewDataAndStorage();
    }
  };

  // Handle fallback image error
  const handleFallbackImageError = () => {
    setFallbackImageError(true);
    log.error('Failed to load fallback image');

    // If animated image also failed, close the modal
    if (animatedImageError) {
      setIsOpen(false);
      Toast.show('Error loading images');
    }
  };

  // Handle animated image load
  const handleAnimatedImageLoad = () => {
    setAnimatedImageLoaded(true);
    setAnimatedImageError(false);
    setIsLoading(false); // Hide loader when animated image loads

    // Update chat view data and storage
    updateChatViewDataAndStorage();
  };

  // Handle animated image error
  const handleAnimatedImageError = () => {
    setAnimatedImageError(true);
    log.error('Failed to load animated image, falling back to static image');

    // If fallback image also failed, close the modal
    if (fallbackImageError) {
      setIsOpen(false);
      Toast.show('Error loading images');
    }
  };

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      setFallbackImageLoaded(false);
      setAnimatedImageLoaded(false);
      setFallbackImageError(false);
      setAnimatedImageError(false);
    }
  }, [isOpen]);
  const imageHeight = showTextOverlay ? 0.7 * popupHeight : popupHeight;
  return isOpen ? (
    <View
      style={{ flex: 1, position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}
    >
      <BottomSheetScreen viewName={'introPopup'}>
        <BottomModalPopup
          enabled={isOpen}
          onClose={() => setIsOpen(false)}
          hideHeader={true}
          heightPercentage={0.6}
        >
          <View style={[styles.container, { height: popupHeight }]}>
            {/* Loading State */}
            {isLoading && (
              <View style={styles.imageContainer}>
                <ImageLoader height={popupHeight} />
              </View>
            )}

            {/* Fallback Image - Always render but control visibility */}
            {introPopupConfig?.introPopupImage && (
              <View
                style={[
                  styles.imageContainer,
                  {
                    opacity:
                      shouldShowFallbackImage && !shouldShowAnimatedImage ? 1 : 0,
                    height: imageHeight,
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                  },
                ]}
              >
                <FastImageWrapper
                  source={{
                    uri: introPopupConfig.introPopupImage,
                  }}
                  style={[
                    styles.image,
                    {
                      flex: 1,
                    },
                  ]}
                  resizeMode={resizeMode}
                  onLoad={handleFallbackImageLoad}
                  onError={handleFallbackImageError}
                />
              </View>
            )}

            {/* Animated Image - Show when loaded and no error */}
            {introPopupConfig?.introPopupImageAnimated && (
              <View
                style={[
                  styles.imageContainer,
                  {
                    opacity: shouldShowAnimatedImage ? 1 : 0,
                    height: imageHeight,
                  },
                ]}
              >
                <FastImageWrapper
                  source={{
                    uri: introPopupConfig.introPopupImageAnimated,
                  }}
                  style={[
                    styles.image,
                    {
                      flex: 1,
                    },
                  ]}
                  resizeMode={resizeMode}
                  onLoad={handleAnimatedImageLoad}
                  onError={handleAnimatedImageError}
                />
              </View>
            )}

            {/* Text Overlay - Only render if title exists and we can show an image */}
            {showTextOverlay && canShowAnyImage && (
              <View style={styles.textOverlayContainer}>
                <TextOverlay
                  heading={heading}
                  title={title}
                  subtitle={subtitle}
                  ctaText={ctaText}
                  modeId={modeId}
                  onClose={() => setIsOpen(false)}
                />
              </View>
            )}
          </View>
        </BottomModalPopup>
      </BottomSheetScreen>
    </View>
  ) : null;
};

const IntroPopupModal = () => {
  const { data } = useConfigSettings();
  const introPopupConfig = data?.introPopupConfig;
  const introPopupImage = introPopupConfig?.introPopupImage;
  const { showIntroPopupModal } = useGetFeatureFlags();
  const [isOpen, setIsOpen] = useState(false);
  const onBack = useCallback(() => {
    setIsOpen(false);
  }, []);
  useBackAction(onBack, { enabled: isOpen });
  useEffect(() => {
    const initializeModal = async () => {
      try {
        const config = await StorageUtils.getIntroPopupConfig();
        // Check if showIntroPopupOnce is true
        if (config?.showIntroPopupOnce) {
          setIsOpen(true);
          return;
        }

        // Check if forceShowIntroPopup is true and showIntroPopupOnce is false
        if (introPopupConfig?.forceShowIntroPopup && !config?.showIntroPopupOnce) {
          setIsOpen(true);
        }
      } catch (error) {
        // Handle error silently or log appropriately
      }
    };

    initializeModal();
    log.info('IntroPopupModal initialized', {
      showPopup: introPopupConfig?.showPopup,
    });
  }, [introPopupConfig?.forceShowIntroPopup, introPopupConfig?.showPopup]);

  return showIntroPopupModal && introPopupImage && isOpen ? (
    <IntroPopupModalWrapper
      introPopupConfig={introPopupConfig}
      isOpen={isOpen}
      setIsOpen={setIsOpen}
    />
  ) : null;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
  },
  textOverlayContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.WHITE + 'E6', // Semi-transparent white (90% opacity)
  },
  textOverlay: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  heading: {
    fontSize: 12,
    fontFamily: FONTS.FONT_FAMILY_700,
    textAlign: 'center',
    color: COLORS.BLUE_VAR_16,
    letterSpacing: 4,
    marginBottom: 2,
  },
  title: {
    fontSize: 30,
    lineHeight: 40,
    fontFamily: FONTS.FONT_FAMILY_900,
    color: COLORS.BLACK,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: FONTS.FONT_FAMILY_400,
    textAlign: 'center',
    lineHeight: 22,
    color: COLORS.GREY_VAR_1,
    maxWidth: '50%',
  },
});

export default IntroPopupModal;
