import React, { useCallback, useEffect, useMemo } from 'react';
import { View, ViewStyle } from 'react-native';
import { default as UUID } from 'react-native-uuid';
import { useAppStateStore } from '../store/app';

type ModalContextType = {
  render: (key: string, modal: React.ReactNode) => void;
};
const ModalContext = React.createContext<ModalContextType>({
  render: () => {
    /* no-op */
  },
});

export function ModalProvider({
  children,
  style,
}: {
  children: React.ReactNode;
  style?: ViewStyle;
}) {
  const [modals, setModals] = React.useState<Record<string, React.ReactNode>>({});

  const render = useCallback((key: string, slot: React.ReactNode) => {
    setModals((prev) => ({ ...prev, [key]: slot }));
  }, []);
  const contextVal: ModalContextType = useMemo(() => {
    return { render };
  }, [render]);
  const modalContent = useMemo(() => {
    return Object.values(modals).filter((modal) => modal !== null);
  }, [modals]);
  useEffect(() => {
    useAppStateStore.setState({
      bottomBarVisibility: modalContent.length > 0 ? 'hide' : 'show',
    });
    return () => {
      useAppStateStore.setState({
        bottomBarVisibility: 'show',
      });
    };
  }, [modalContent.length]);
  return (
    <ModalContext.Provider value={contextVal}>
      <View style={style}>
        {children}
        {modalContent}
      </View>
    </ModalContext.Provider>
  );
}

export function ModalSlot({
  children,
}: {
  children: React.ReactNode;
}): React.ReactNode {
  const { render } = React.useContext(ModalContext);
  const key = useMemo(() => `${UUID.v4()}`, []);
  useEffect(() => {
    render(key, children);
    return () => {
      render(key, null);
    };
  }, [key, render, children]);
  return null;
}
