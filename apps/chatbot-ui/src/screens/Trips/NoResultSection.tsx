import React, { memo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { COLORS, FONTS } from '../../constants/globalStyles';
import NoResult from '../../assets/NoResult';
import { Button, ButtonType } from '../../components/button';
import { NoItineraryData } from './types';

export interface NoResultSectionProps {
  data: NoItineraryData;
  onPlanTripPress: () => void;
}

const NoResultSection = memo(({ data, onPlanTripPress }: NoResultSectionProps) => {
  return (
    <View style={styles.container}>
      <NoResult />
      <Text style={styles.title}>{data.title}</Text>
      <Text style={styles.subtitle}>{data.subTitle}</Text>
      <Button
        title={data.ctaText}
        onPress={onPlanTripPress}
        buttonType={ButtonType.Primary}
        customStyle={styles.button}
      />
    </View>
  );
});

NoResultSection.displayName = 'NoResultSection';

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    marginTop: 60,
  },
  title: {
    fontFamily: FONTS.FONT_FAMILY_700,
    fontSize: 20,
    lineHeight: 24,
    color: COLORS.BLACK,
    marginBottom: 8,
    marginTop: 32,
  },
  subtitle: {
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 14,
    lineHeight: 18,
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
    textAlign: 'center',
    marginTop: 4,
    width: 247,
  },
  button: {
    marginTop: 32,
    height: 44,
  },
});

export default NoResultSection; 