import React, { memo, useEffect, useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { BaseHeader } from '../../components/header/BaseHeader';
import { COLORS } from '../../constants/globalStyles';
import {
  ItineraryItem,
  ItinerarySuccessResponse,
} from '../../network/api/conversationApi';
import TripsList from './TripsList';
import NoResultSection from './NoResultSection';
import { TRIPS_TEST_DATA } from '../../dummyData';
import { HEADER_CONTEXT } from '../../const';
import { MenuSection } from '../MessagesScreen';
import { useItineraryQuery } from '../../network/hooks/use-itinerary-screen-data';
import { useMessageStore } from '../../store/messages/messageStore';
import { trackOmnitureClickEvent, trackPageLoadEvent } from '../../native/omniture';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import { useAppStateStore } from '../../store/app';
import { MessageListLoader } from '../../components/message-list-loader';

const TripsScreen = memo(() => {
  const activeConversationId =
    useMessageStore.getState().activeConversationId || 'draft';
  const { setCurrentView } = useAppStateStore();
  const {
    data: itineraryResponse,
    isLoading,
    isError,
  } = useItineraryQuery(activeConversationId);

  useEffect(() => {
    trackOmnitureClickEvent('ITINERARY_LIST_SHOWN');
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.ITINERARY_LIST_SHOWN,
    });
  }, []);

  // Type guard to check if it's a successful response
  const isSuccessResponse =
    itineraryResponse &&
    'title' in itineraryResponse &&
    'itineraries' in itineraryResponse;
  const chatHeaderConfig =
    (isSuccessResponse && itineraryResponse.chatHeader) || HEADER_CONTEXT.tripScreen;

  // Use the API data if available, otherwise fall back to test data
  const data: ItinerarySuccessResponse = isSuccessResponse
    ? {
        title: itineraryResponse.title,
        itineraries: itineraryResponse.itineraries,
        noItineraryData: itineraryResponse.noItineraryData,
      }
    : {
        title: TRIPS_TEST_DATA.title,
        itineraries: [],
        noItineraryData: TRIPS_TEST_DATA.noItineraryData,
      };

  const hasItineraries = useMemo(
    () => data?.itineraries && data.itineraries.length > 0,
    [data?.itineraries],
  );

  const viewState: 'loading' | 'error' | 'content' = isLoading
    ? 'loading'
    : isError
      ? 'error'
      : 'content';

  function onPlanTripPress() {
    trackOmnitureClickEvent('PLAN_A_TRIP_CLICKED');
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.PLAN_A_TRIP_CLICKED,
    });
    setCurrentView?.('chat');
  }

  function onTripPress(itinerary: ItineraryItem) {
    trackOmnitureClickEvent('CARD_CLICKED', {
      CONTENT_TYPE: `media_async_itinerary`,
    });
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.MEDIA_CLICKED_ASYNC_ITINERARY,
    });
  }

  const menuContext = chatHeaderConfig || HEADER_CONTEXT.tripScreen;
  const { header } = menuContext;
  const { leftItems, rightItems } = header;
  const hasLeftItems = leftItems && leftItems.length > 0;
  const hasRightItems = rightItems && rightItems.length > 0;

  return (
    <View style={styles.container}>
      <BaseHeader
        leftMenu={hasLeftItems ? <MenuSection menuContext={header} /> : null}
        rightMenu={hasRightItems ? <MenuSection menuContext={header} /> : null}
        title={data?.title}
        isRightMenuDetailed={!!header.rightItems?.[0]?.text}
      />
      {viewState === 'loading' && <MessageListLoader />}
      {viewState === 'content' && (
        <>
          {hasItineraries ? (
            <TripsList itineraries={data.itineraries} onTripPress={onTripPress} />
          ) : (
            <NoResultSection
              data={data.noItineraryData}
              onPlanTripPress={onPlanTripPress}
            />
          )}
        </>
      )}
      {viewState === 'error' && (
        <NoResultSection
          data={data.noItineraryData}
          onPlanTripPress={onPlanTripPress}
        />
      )}
    </View>
  );
});
export default TripsScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.WHITE,
  },
});
