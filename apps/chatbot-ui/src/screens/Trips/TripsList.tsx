import React, { memo, useCallback } from 'react';
import { ScrollView, StyleSheet } from 'react-native';
import {
  ItineraryItem,
  ItinerarySuccessResponse,
} from '../../network/api/conversationApi';
import TripCard from './TripCard';

interface TripsListProps {
  itineraries: ItinerarySuccessResponse['itineraries'];
  onTripPress: (itinerary: ItineraryItem) => void;
}

const TripsList = memo(({ itineraries, onTripPress }: TripsListProps) => {
  const renderTripCard = useCallback(
    (itinerary: ItineraryItem, index: number) => (
      <TripCard key={index} itinerary={itinerary} onPress={onTripPress} />
    ),
    [onTripPress],
  );

  return (
    <ScrollView
      style={styles.scrollView}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.scrollContent}
    >
      {itineraries.map(renderTripCard)}
    </ScrollView>
  );
});

TripsList.displayName = 'TripsList';

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
});

export default TripsList;
