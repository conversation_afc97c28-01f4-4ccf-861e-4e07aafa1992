import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import FastImageWrapper from '../../components/image/FastImageWrapper';
import ChevronRight from '../../assets/ChevronRight';
import { TouchableFeedback } from '../../components/base/TouchableFeeback';
import { ItineraryItem } from '../../network/api/conversationApi';
import { COLORS, FONTS } from '../../constants/globalStyles';
import { useAppStateStore } from '../../store/app';

export default function TripCard({
  itinerary,
  onPress,
}: {
  itinerary: ItineraryItem;
  onPress: (itinerary: ItineraryItem) => void;
}) {
  const { setCurrentView, setItineraryData } = useAppStateStore();
  const { itineraryData } = itinerary;
  const handleTripCardPress = () => {
    setItineraryData?.({ data: itineraryData });
    setCurrentView?.('itinerary');
  };
  return (
    <View style={styles.tripCardContainer}>
      <TouchableFeedback onPress={handleTripCardPress} style={styles.tripCard}>
        <FastImageWrapper
          source={{ uri: itinerary.icon }}
          style={styles.tripCardImage}
          resizeMode="cover"
        />
        <View style={styles.tripCardContent}>
          <Text style={styles.tripTitle}>
            {itinerary.title}
          </Text>
          <Text style={styles.tripSubtitle}>
            {itinerary.subTitle}
          </Text>
        </View>
        <ChevronRight height={21} width={21} />
      </TouchableFeedback>
    </View>
  );
}

const styles = StyleSheet.create({
  tripCardContainer: {
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: COLORS.ORANGE_VAR_8,
    backgroundColor: COLORS.WHITE,
    marginBottom: 12,
  },
  tripCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  tripCardImage: {
    width: 32,
    height: 32,
    borderRadius: 100,
    overflow: 'hidden',
    backgroundColor: COLORS.ORANGE_VAR_8,
  },
  tripCardContent: {
    flex: 1,
    marginLeft: 20,
    marginRight: 8,
  },
  tripTitle: {
    fontFamily: FONTS.FONT_FAMILY_700,
    fontSize: 14,
    lineHeight: 16,
    color: COLORS.BLACK,
    marginBottom: 4,
  },
  tripSubtitle: {
    fontFamily: FONTS.FONT_FAMILY_400,
    fontSize: 12,
    lineHeight: 14,
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
  },
});
