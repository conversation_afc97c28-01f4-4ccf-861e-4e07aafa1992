import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useBackToChats } from '../utils/useBackHandler';
import {
  Pressable,
  StyleSheet,
  Text,
  useWindowDimensions,
  View,
} from 'react-native';
import { ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import { COLORS, FONTS } from '../constants/globalStyles';
import { RadioButton } from '../components/radio/RadioButton';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { UnBookmark } from '../assets/Unbookmark';
import { LinearGradient } from 'react-native-linear-gradient';
import {
  isCardWidgetType,
  useBookmarksData,
  useMessageStore,
} from '../store/messages';
import { RemoveBookmarkRequest } from '../network/api/bookmarkApi';
import { useAppStateStore } from '../store/app';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { BaseHeader } from '../components/header/BaseHeader';
import { BottomSheetScreen } from './BaseBottomSheetScreen';
import { MessageListLoader } from '../components/message-list-loader';
import {
  trackOmnitureClickEvent,
  trackOmnitureGenericEvent,
} from '../native/omniture';
import { transformToListItems } from '../store/messages/useMessageListData';
import {
  BotCardMessage,
  BotTextMessage,
} from '../components/message/ChatMessageBubble';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../native/tracking/pdt';
import { ErrorScreen } from './ErrorScreen';
import { HEADER_CONTEXT } from '../const';
import { MenuSection } from './MessagesScreen';

type RadioItemWrapperProps = {
  isEditing: boolean;
  selected: boolean;
  onChange: (value: boolean) => void;
  children: React.ReactNode;
};

function RadioItemWrapper(props: RadioItemWrapperProps) {
  const { isEditing, onChange, children, selected } = props;

  const size = useSharedValue(isEditing ? 32 : 0);
  const opacity = useSharedValue(isEditing ? 1 : 0);

  useEffect(() => {
    size.value = withTiming(isEditing ? 32 : 0, {
      duration: 300,
      easing: Easing.inOut(Easing.ease),
    });
    opacity.value = withTiming(isEditing ? 1 : 0, {
      duration: 300,
      easing: Easing.inOut(Easing.ease),
    });
  }, [isEditing, opacity, size]);

  const animatedStyle = useAnimatedStyle(
    () => ({
      width: size.value,
      // height: size.value,
      opacity: opacity.value,
    }),
    [size, opacity],
  );

  return (
    <TouchableOpacity
      disabled={!isEditing}
      activeOpacity={1}
      onPress={() => {
        onChange(!selected);
      }}
    >
      <View style={{ flexDirection: 'row', gap: 0 }}>
        <Animated.View
          style={[
            {
              marginTop: 24,
              marginLeft: 8,
              marginRight: -8,
              alignItems: 'center',
              justifyContent: 'flex-start',
            },
            animatedStyle,
          ]}
        >
          <RadioButton isSelected={selected} />
        </Animated.View>
        {children}
      </View>
    </TouchableOpacity>
  );
}

function Divider() {
  return (
    <View style={{ width: '100%', height: 2, marginVertical: 8 }}>
      <LinearGradient
        style={{ width: '100%', height: 2 }}
        useAngle
        angle={90}
        locations={[0, 0.66, 0.99]}
        colors={[
          'rgba(216,216,216,0.1)',
          'rgba(216,216,216,0.5)',
          'rgba(216,216,216,0.1)',
        ]}
      />
    </View>
  );
}

export function BookmarkScreen() {
  const { width } = useWindowDimensions();
  const { activeConversationId } = useMessageStore();
  const { isLoading, data, isError } = useBookmarksData(
    activeConversationId as string,
  );
  useEffect(() => {
    trackOmnitureClickEvent('BOOKMARK_SCREEN_SHOWN');
  }, []);
  const allBookmarks = data?.data?.bookmarks || [];
  const chatHeaderConfig = data?.chatHeader;
  const [activeBookmarkTab, setActiveBookmarkTab] = useState('All');
  const onTabClicked = useCallback((tab: string) => {
    setActiveBookmarkTab(tab);
    trackOmnitureGenericEvent(
      {
        CLICK_EVENT: `bookmark_${tab.toLowerCase()}`,
      },
      'Bookmark tab clicked',
    );
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: `tab_${tab.toLowerCase()}_clicked`,
    });
  }, []);
  const bookmarks = allBookmarks.filter(
    ({ bookmark }) =>
      activeBookmarkTab === 'All' || bookmark.tags?.includes(activeBookmarkTab),
  );

  const messages = useMemo(
    () =>
      allBookmarks
        .filter(
          ({ bookmark }) =>
            activeBookmarkTab === 'All' ||
            bookmark.tags?.includes(activeBookmarkTab),
        )
        .flatMap((msg) =>
          transformToListItems(msg.bookmark, { disableFeedback: true }),
        ),
    [allBookmarks, activeBookmarkTab],
  );
  const [isEditing, setIsEditing] = useState(false);
  const insets = useSafeAreaInsets();

  useBackToChats();
  const [selectedItems, setSelectedItems] = useState<Set<string>>(() => new Set([])); // set of bookmarkId

  useEffect(() => {
    if (!isEditing) {
      setSelectedItems(new Set([])); // reset on cancel
    }
  }, [isEditing]);
  const removeBookmark = useCallback(async () => {
    if (!selectedItems.size) {
      return;
    }
    trackOmnitureClickEvent('BOOKMARK_REMOVED');
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.BOOKMARK_REMOVED,
    });

    const currentView = useAppStateStore.getState().currentView;
    const pageSource = currentView === 'bookmarks' ? 'BOOKMARK_PAGE' : 'CHAT_PAGE';

    const selectedBookmarks: Array<Omit<RemoveBookmarkRequest, 'conversationId'>> =
      [];
    allBookmarks.forEach(({ bookmark }) => {
      if (!selectedItems.has(bookmark.id)) {
        return;
      }
      selectedBookmarks.push({
        bookmarkId: bookmark.bookmarkId,
        messageId: bookmark.id,
        ...(bookmark.isMessageBookmarked
          ? {}
          : {
              data: {
                payload: (bookmark.content || []).flatMap((cardItem) =>
                  isCardWidgetType(cardItem)
                    ? cardItem.value.templateInfo.payload
                    : [],
                ),
              },
            }),
      } as Omit<RemoveBookmarkRequest, 'conversationId'>);
    });
    await useMessageStore
      .getState()
      .removeBookmarks(
        selectedBookmarks,
        activeConversationId as string,
        pageSource,
      );
    setIsEditing(false);
  }, [selectedItems, activeConversationId, allBookmarks]);
  const viewState: 'empty' | 'loading' | 'error' | 'content' = isLoading
    ? 'loading'
    : isError
      ? 'error'
      : bookmarks.length === 0
        ? 'empty'
        : 'content';
  useEffect(() => {
    if (viewState === 'content') {
      trackOmnitureClickEvent('BOOKMARK_VIEWED');
      trackPDTEvent({
        eventName: eventNameSchema.CHAT_INTERACTED,
        eventType: eventTypeSchema.ACTION,
        eventValue: eventValueSchema.BOOKMARK_VIEWED,
      });
    }
  }, [viewState]);

  const onCtaClick = useCallback(() => {
    useAppStateStore.setState({ currentView: 'chat' });
  }, []);

  const menuContext = chatHeaderConfig || HEADER_CONTEXT.bookmarkScreen;

  const { header, title } = menuContext;
  const { leftItems, rightItems } = header;
  const hasLeftItems = leftItems && leftItems.length > 0;
  const hasRightItems = rightItems && rightItems.length > 0;
  return (
    <BottomSheetScreen viewName={'bookmarks'}>
      <>
        <BaseHeader
          title={title || 'Bookmarks'}
          leftMenu={hasLeftItems ? <MenuSection menuContext={header} /> : null}
          rightMenu={
            hasRightItems ? <MenuSection menuContext={header} type="right" /> : null
          }
          isRightMenuDetailed={!!header.rightItems?.[0]?.text}
        >
          {viewState === 'content' && (
            <BookmarkTab
              activeBookmarkTab={activeBookmarkTab}
              setActiveBookmarkTab={onTabClicked}
            />
          )}
        </BaseHeader>
        {viewState === 'loading' && <MessageListLoader />}
        {/* FIXME better to have a error view. Currently using empty view on error */}
        {(viewState === 'empty' || viewState === 'error') && (
          <ErrorScreen type="bookmark" onCtaClick={onCtaClick} />
        )}
        {viewState === 'content' && (
          <View
            style={{
              flexDirection: 'column',
              flex: 1,
              alignItems: 'flex-start',
              width,
            }}
          >
            <ScrollView style={{ flex: 1, width }}>
              <View
                style={{
                  paddingTop: 16,
                  paddingBottom: 96,
                  flex: 1,
                  flexDirection: 'column',
                }}
              >
                {messages.map((listItem, msgIndex) => {
                  if (
                    listItem.type !== 'BOT_MSG_TEXT' &&
                    listItem.type !== 'BOT_MSG_CARDS'
                  ) {
                    return null;
                  }
                  const { msg } = listItem;
                  return (
                    <>
                      <View key={msg.id}>
                        <RadioItemWrapper
                          selected={selectedItems.has(msg.id as string)}
                          isEditing={isEditing}
                          onChange={(isSelected) => {
                            if (isSelected) {
                              setSelectedItems((itemsSet) => {
                                const newSet = new Set(itemsSet);
                                newSet.add(msg.id as string);
                                return newSet;
                              });
                            } else {
                              setSelectedItems((itemsSet) => {
                                const newSet: typeof itemsSet = new Set([]);
                                newSet.delete(msg.id as string);
                                return newSet;
                              });
                            }
                          }}
                        >
                          {listItem.type === 'BOT_MSG_TEXT' && (
                            <BotTextMessage key={listItem.listKey} {...listItem} />
                          )}
                          {listItem.type === 'BOT_MSG_CARDS' && (
                            <BotCardMessage key={listItem.listKey} {...listItem} />
                          )}
                        </RadioItemWrapper>
                      </View>
                      {msgIndex < bookmarks.length - 1 && <Divider />}
                    </>
                  );
                })}
              </View>
            </ScrollView>
            <View
              testID={'bookmark-footer'}
              style={{
                backgroundColor: 'rgba(255,255,255,1)',
                borderColor: 'rgba(0,0,0,0.15)',
                overflow: 'hidden',
                // borderTopRightRadius: 8,
                // borderTopLeftRadius: 8,
                borderWidth: 1,
                minHeight: 48,
                display: bookmarks.length ? 'flex' : 'none',
                flexDirection: 'row',
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                paddingBottom: insets.bottom,
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              <TouchableOpacity
                disabled={!isEditing}
                onPress={removeBookmark}
                style={{
                  marginLeft: 8,
                  minWidth: 24,
                  paddingVertical: 4,
                  paddingHorizontal: 8,
                }}
                containerStyle={{
                  opacity: selectedItems.size > 0 ? 1 : 0.3,
                }}
                activeOpacity={0.5}
              >
                {isEditing && <UnBookmark />}
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  setIsEditing((val) => !val);
                }}
                activeOpacity={0.5}
              >
                <Text
                  style={{
                    color: COLORS.BLUE_VAR_1,
                    fontSize: 16,
                    padding: 16,
                    fontFamily: FONTS.FONT_FAMILY_700,
                  }}
                >
                  {isEditing ? 'Cancel' : 'Edit'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </>
    </BottomSheetScreen>
  );
}

type BookmarkTabProps = {
  activeBookmarkTab?: string;
  setActiveBookmarkTab?: (tab: string) => void;
};

function BookmarkTab({ activeBookmarkTab, setActiveBookmarkTab }: BookmarkTabProps) {
  const { activeConversationId } = useMessageStore();
  const { data } = useBookmarksData(activeConversationId as string);
  const tags = data?.tags || [];
  if (tags.length > 1) {
    // return null;
  }

  const capitalizeTab = (tab?: string) => {
    if (!tab) {
      return '';
    }
    const [firstLetter, ...rest] = tab;
    return `${firstLetter.toUpperCase()}${rest.join('').toLocaleLowerCase()}`;
  };

  return (
    <ScrollView
      horizontal
      style={{
        width: '100%',
        flex: 0,
        flexGrow: 0,
        height: 60,
      }}
      contentContainerStyle={{
        flex: 0,
        height: 60,
        width: '100%',
      }}
      showsHorizontalScrollIndicator={false}
      overScrollMode={'always'}
      bounces
    >
      <View style={styles.tabContainer}>
        {['All', ...tags.filter((tag) => tag !== 'ALL')]?.map?.((tab) => (
          <Pressable onPress={() => setActiveBookmarkTab?.(tab)}>
            <View
              key={tab}
              style={[styles.tab, tab === activeBookmarkTab && styles.activeTab]}
            >
              <Text
                style={[
                  styles.tabText,
                  ,
                  tab === activeBookmarkTab && styles.activeTabText,
                ]}
              >
                {capitalizeTab(tab)}
              </Text>
            </View>
          </Pressable>
        ))}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  tabContainer: {
    flexDirection: 'row',
    gap: 8,
    paddingHorizontal: 16,
    height: 60,
    alignItems: 'center',
  },
  tab: {
    minWidth: 66,
    minHeight: 32,
    paddingHorizontal: 8,
    paddingVertical: 10,
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: COLORS.GREY_VAR_5,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: COLORS.BLUE_VAR_2,
    borderColor: COLORS.BLUE_VAR_1,
  },
  tabText: {
    fontSize: 12,
    color: COLORS.TEXT_MEDIUM_EMPHASIS,
    fontFamily: FONTS.FONT_FAMILY_400,
    lineHeight: 14,
  },
  activeTabText: {
    color: COLORS.BLUE_VAR_1,
    fontFamily: FONTS.FONT_FAMILY_700,
  },
});
