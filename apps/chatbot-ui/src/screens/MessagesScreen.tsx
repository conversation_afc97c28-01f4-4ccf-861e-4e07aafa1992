import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Welcome from '../components/welcome/Welcome';
import { useMessageListData } from '../store/messages/useMessageListData';
import { MessageListLoader } from '../components/message-list-loader';
import { BottomSheetScreen } from './BaseBottomSheetScreen';
import { MessageList } from '../components/message-list';
import { BaseHeader } from '../components/header/BaseHeader';
import { useMessageAction, useMessageStore } from '../store/messages';
import { useAppStateStore } from '../store/app';
import { MessagesHeaderMenu } from '../components/messages-header-menu';
import { trackOmnitureClickEvent } from '../native/omniture';
import StorageUtils from '../utils/storageUtils';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../native/tracking/pdt';
import WelcomeNewChat from '../components/welcome/WelcomeNewChat';
import useAutoPushMessagesToChat from '../hooks/useAutoPushMessagesToChat';
import { Analytics, TrackingEvent } from '../analytics';
import { ErrorScreen } from './ErrorScreen';
import useGetFeatureFlags from '../hooks/useGetFeatureFlags';
import {
  updateStartupTrace,
  updateStartupTraceAsInteractive,
} from '../analytics/telemetry';
import { canWriteToConversation } from '../utils/storeUtils';
import { isBrowser } from '../utils/webUtils';
import {
  refetchChatViewData,
  updateChatViewData,
  useConfigSettings,
} from '../store/messages/newChatView';
import ModeScreen from '../components/ModesScreen';
import { HEADER_CONTEXT } from '../const';
import { TRIP_PLAN_MODE_ID } from '../hooks/useModeState';
import { useBackAction } from '../utils/useBackHandler';
import { stopTtsIfPlaying } from '../store/audio-talkback/talkbackStore';
import { DeleteConversationModal } from '../components/DeleteConversationModal';

// Move the dynamic import to top level to avoid conditional hook issues
const Evaluation = require('../components/Evaluation').default;

interface MessagesScreenProps {
  startupTraceId: string;
}

export const MessagesScreen = ({ startupTraceId }: MessagesScreenProps) => {
  const [resetKey, setResetKey] = useState(0);

  // Move evaluation mode check to the very top, before any hooks
  const appState = useAppStateStore.getState();
  const evaluationMode = useMemo(
    () => appState?.chatContext?.botMetadata?.evaluationMode,
    [appState?.chatContext?.botMetadata?.evaluationMode],
  );
  const shouldUseEvaluationMode = useMemo(
    () => evaluationMode && isBrowser,
    [evaluationMode],
  );

  //need to change
  const {
    listData,
    screenState,
    title,
    isForbiddenError,
    refetchConfig,
    refetchMessages,
    enrichedTitle,
    chatHeaderConfig: conversationChatHeaderConfig,
  } = useMessageListData();

  const { showBookmarkOptions } = useGetFeatureFlags();
  const { data } = useConfigSettings();
  const { chatHeader: chatHeaderConfig } = data || {};

  const setNewChatRequested = useMessageStore((state) => state.setNewChatRequested);
  const convId = useMessageStore((state) => state.activeConversationId) || 'draft';
  const isWriteToConversationAllowed = useMemo(() => canWriteToConversation(), []);
  const updateConversationById = useMessageStore(
    (state) => state.updateConversationById,
  );

  // INGRESS HANDLING

  const onNewChat = useCallback(
    (initiator: 'user' | 'back_nav' = 'user') => {
      if (initiator === 'user') {
        trackOmnitureClickEvent('NEW_CHAT_CLICK');
        trackPDTEvent({
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: eventValueSchema.CHAT_MENU_NEW_CHAT_CLICKED,
        });
        Analytics.trackClickEvent(TrackingEvent.payload_NewChatClicked());
      }
      setResetKey((i) => i + 1);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars,unused-imports/no-unused-vars
      updateChatViewData({
        content: { hideInput: data?.content?.hideInput },
      });

      // let hideInput = false;
      // if (data?.content?.hideInput === undefined) {
      //   hideInput = false;
      // } else {
      //   hideInput = data?.content?.hideInput;
      // }
      updateConversationById(convId, {
        isNewChatInputCtaEnabled: false,
      });

      setNewChatRequested(true);
      useAppStateStore.setState({ modes: [], enabledMode: null });
      StorageUtils.clearSessionStorage();
    },
    [setNewChatRequested, updateConversationById, convId],
  );
  const onBack = useCallback(() => {
    onNewChat('back_nav');
    return true;
  }, [onNewChat]);
  useBackAction(onBack, { enabled: screenState === 'messages' });
  const screenStatePrevValRef = useRef<typeof screenState | null>(null);

  useEffect(() => {
    if (screenStatePrevValRef.current === screenState) {
      return;
    }
    screenStatePrevValRef.current = screenState;
    if (screenState === 'messages') {
      trackOmnitureClickEvent('MESSAGE_LIST_SHOWN');
    } else if (screenState === 'welcome' || screenState === 'new-chat') {
      trackOmnitureClickEvent('WELCOME_SHOWN');
    }
  }, [screenState]);

  // screen state
  const isWelcomeState = useMemo(() => screenState === 'welcome', [screenState]);
  const isNewChatState = useMemo(() => screenState === 'new-chat', [screenState]);
  const isMessagesState = useMemo(() => screenState === 'messages', [screenState]);

  // error states
  const isConfigLoadFailed = useMemo(() => screenState === 'error', [screenState]);
  const isMessagesErrorState = useMemo(
    () => screenState === 'messages-error',
    [screenState],
  );
  // loading states
  const isConfigLoadingState = useMemo(
    () => screenState === 'loading',
    [screenState],
  );
  const isMessagesLoadingState = useMemo(
    () => screenState === 'messages-loading',
    [screenState],
  );

  const isLeftMenuEnabled = useMemo(
    () => !isConfigLoadingState && !isConfigLoadFailed,
    [isConfigLoadingState, isConfigLoadFailed],
  );

  const isRightMenuEnabled = useMemo(
    () =>
      !isConfigLoadingState &&
      !isConfigLoadFailed &&
      convId !== null &&
      showBookmarkOptions &&
      isWriteToConversationAllowed,
    [
      isConfigLoadingState,
      isConfigLoadFailed,
      convId,
      showBookmarkOptions,
      isWriteToConversationAllowed,
    ],
  );

  const { checkAndAutoPushToChat } = useAutoPushMessagesToChat();

  // after the screen state is ready, we can check if we need to push the messages to chat
  useEffect(() => {
    if (isNewChatState || isWelcomeState || isMessagesState) {
      checkAndAutoPushToChat();
    }
  }, [isNewChatState, isWelcomeState, isMessagesState, checkAndAutoPushToChat]);

  const retryMsgFetch = useCallback(() => {
    // refetchMessages({});
    onNewChat();
  }, [refetchMessages]);
  const retryNewChatFetch = useCallback(() => {
    refetchConfig({});
  }, [refetchConfig]);

  // Memoize values before early return to avoid conditional hook calls
  const showLoader = useMemo(
    () => isConfigLoadingState || isMessagesLoadingState,
    [isConfigLoadingState, isMessagesLoadingState],
  );

  const menuContext = useMemo(() => {
    return isMessagesState
      ? conversationChatHeaderConfig || HEADER_CONTEXT.messagesScreen
      : chatHeaderConfig || HEADER_CONTEXT.newChatScreen;
  }, [isMessagesState, chatHeaderConfig, conversationChatHeaderConfig]);

  const { header } = menuContext || {};

  // Create a wrapper component for the message list that conditionally includes Evaluation
  const MessageListWrapper = useMemo(() => {
    return ({ children }: { children: React.ReactNode }) => {
      if (shouldUseEvaluationMode) {
        return <Evaluation>{children}</Evaluation>;
      }
      return <>{children}</>;
    };
  }, [shouldUseEvaluationMode]);

  const leftMenuComponent = useMemo(() => {
    return isLeftMenuEnabled ? (
      <MemoizedMenuSection menuContext={header} onNewChat={onNewChat} />
    ) : null;
  }, [isLeftMenuEnabled, header, isNewChatState, onNewChat]);

  const rightMenuComponent = useMemo(() => {
    return isRightMenuEnabled ? (
      <MemoizedMenuSection menuContext={header} onNewChat={onNewChat} type="right" />
    ) : null;
  }, [isRightMenuEnabled, header, isNewChatState, onNewChat]);

  if (isForbiddenError) {
    return (
      <BottomSheetScreen viewName={'chat'}>
        <BaseHeader
          title={''}
          leftMenu={null}
          rightMenu={null}
          isRightMenuDetailed={false}
        />
        <ErrorScreen type="pageNotFound" />
      </BottomSheetScreen>
    );
  }

  // Remove the conditional early return and integrate evaluation mode into the main render
  if (showLoader) {
    updateStartupTrace(startupTraceId, 'loader');
  } else if (isMessagesErrorState || isConfigLoadFailed) {
    updateStartupTrace(startupTraceId, 'error');
  } else {
    if (isNewChatState) {
      updateStartupTraceAsInteractive(startupTraceId, 'NEW_CHAT');
    } else if (isWelcomeState) {
      updateStartupTraceAsInteractive(startupTraceId, 'WELCOME');
    } else if (isMessagesState) {
      updateStartupTraceAsInteractive(startupTraceId, 'MESSAGES');
    } else {
      // eslint-disable-next-line no-console
      console.log('@mmt10296:: unhandled state for tracking', screenState);
    }
  }

  return (
    <BottomSheetScreen viewName={'chat'}>
      <BaseHeader
        enrichedTitle={isWelcomeState || isNewChatState ? enrichedTitle : undefined}
        title={isMessagesState ? title : ''}
        leftMenu={leftMenuComponent}
        rightMenu={rightMenuComponent}
        isRightMenuDetailed={!!header.rightItems?.[0]?.text}
      />
      {screenState === TRIP_PLAN_MODE_ID && <ModeScreen mode={TRIP_PLAN_MODE_ID} />}
      {isConfigLoadFailed && (
        <ErrorScreen type="config" onCtaClick={retryNewChatFetch} />
      )}
      {isMessagesErrorState && (
        <ErrorScreen type="messages" onCtaClick={retryMsgFetch} />
      )}
      {isWelcomeState && <Welcome />}
      {showLoader && <MessageListLoader />}
      {isNewChatState && <WelcomeNewChat />}
      {isMessagesState && (
        <MessageListWrapper>
          <MessageList
            key={`${resetKey}`}
            listData={listData}
            compactHeightMode={!!appState.compactHeightMode}
          />
        </MessageListWrapper>
      )}
    </BottomSheetScreen>
  );
};

const GlobalErrorScreen = () => {
  return <ErrorScreen />;
};

interface MenuSectionProps {
  menuContext: any;
  onNewChat?: () => void;
  type?: 'left' | 'right';
}

export const MenuSection = ({ menuContext, onNewChat, type }: MenuSectionProps) => {
  const [showDeleteConfirmationModal, setShowDeleteConfirmationModal] =
    useState(false);
  const activeConversationId = useMessageStore(
    (state) => state.activeConversationId,
  );
  const { sendMessage } = useMessageAction();
  const { data } = useConfigSettings();
  const updateConversationById = useMessageStore(
    (state) => state.updateConversationById,
  );
  const setNewChatRequested = useMessageStore((state) => state.setNewChatRequested);
  const convId = useMessageStore((state) => state.activeConversationId) || 'draft';

  const onMenuClicked = useCallback(() => {
    stopTtsIfPlaying('menu_clicked');
    trackOmnitureClickEvent('CHAT_MENU_CLICK');
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.CHAT_MENU_CLICKED,
    });
  }, []);

  const onHistoryClicked = useCallback(() => {
    stopTtsIfPlaying('history_clicked');
    trackOmnitureClickEvent('CHAT_HISTORY_CLICK');
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.CHAT_MENU_HISTORY_CLICKED,
    });
    Analytics.trackClickEvent(TrackingEvent.payload_HistoryClicked());
    useAppStateStore.setState({ currentView: 'history' });
  }, []);

  const onTalkToAgentClicked = useCallback(() => {
    stopTtsIfPlaying('talk_to_agent_clicked');
    trackOmnitureClickEvent('TALK_TO_AGENT_CLICK');
    trackPDTEvent({
      eventName: eventNameSchema.BUTTON_CLICKED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.CTA_CONNECT_TO_AGENT_CLICKED,
    });

    Analytics.trackClickEvent(TrackingEvent.payload_TalkToAgentClicked());
    sendMessage('i need to talk to an agent');
  }, [sendMessage]);

  const onBookmarkClicked = useCallback(() => {
    stopTtsIfPlaying('bookmark_clicked');
    trackOmnitureClickEvent('BOOKMARK_CLICKED');
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.BOOKMARK_CLICKED,
    });
    Analytics.trackClickEvent(TrackingEvent.payload_BookmarkClicked());
    useAppStateStore.getState().setCurrentView?.('bookmarks');
  }, []);

  const onChatClicked = useCallback(() => {
    useAppStateStore.getState().setCurrentView?.('chat');
  }, []);

  const onItineraryClicked = useCallback(() => {
    stopTtsIfPlaying('itinerary_clicked');
    trackOmnitureClickEvent('ITINERARY_OPTION_CLICKED');
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.ITINERARY_OPTION_CLICKED,
    });
    useAppStateStore.getState().setCurrentView?.('trips');
  }, []);

  const onNewChatClicked = useCallback(() => {
    stopTtsIfPlaying('new_chat_clicked');
    trackOmnitureClickEvent('NEW_CHAT_CLICK');
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.CHAT_MENU_NEW_CHAT_CLICKED,
    });
    Analytics.trackClickEvent(TrackingEvent.payload_NewChatClicked());
    // eslint-disable-next-line @typescript-eslint/no-unused-vars,unused-imports/no-unused-vars
    updateChatViewData({
      content: { hideInput: data?.content?.hideInput },
    });

    updateConversationById(convId, {
      isNewChatInputCtaEnabled: false,
    });
    useAppStateStore.getState().setCurrentView?.('chat');
    setNewChatRequested(true);
    useAppStateStore.setState({ modes: [], enabledMode: null });
    StorageUtils.clearSessionStorage();
  }, [setNewChatRequested, updateConversationById, convId]);
  const onDeleteClicked = useCallback(() => {
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.CHAT_MENU_DELETE_CLICKED,
      conversationId: activeConversationId || undefined,
    });
    setShowDeleteConfirmationModal(true);
  }, [activeConversationId]);
  const handleDeleteConversation = useCallback(() => {
    setShowDeleteConfirmationModal(false);
    updateConversationById(convId, {
      isNewChatInputCtaEnabled: false,
    });
    useAppStateStore.getState().setCurrentView?.('chat');
    setNewChatRequested(true);
    useAppStateStore.setState({ modes: [], enabledMode: null });
    StorageUtils.clearSessionStorage();
    refetchChatViewData({ body: { requestReason: 'deleteConversation' } }, false);
  }, [setNewChatRequested, updateConversationById, convId]);

  return (
    <>
      <MessagesHeaderMenu
        onHistoryClicked={onHistoryClicked}
        // this will disable the new chat button when the new chat screen is already open
        onNewChat={onNewChat ? onNewChat : onNewChatClicked}
        onMenuClicked={onMenuClicked}
        onTalkToAgentClicked={onTalkToAgentClicked}
        onBookmarkClicked={onBookmarkClicked}
        onChatClicked={onChatClicked}
        onItineraryClicked={onItineraryClicked}
        menuContext={menuContext}
        type={type}
        onDeleteClicked={onDeleteClicked}
      />
      {showDeleteConfirmationModal && activeConversationId && (
        <DeleteConversationModal
          onClose={() => setShowDeleteConfirmationModal(false)}
          onDeleteConversation={handleDeleteConversation}
          conversationId={activeConversationId}
          visible={showDeleteConfirmationModal}
          source="menu"
        />
      )}
    </>
  );
};

// Memoize the MenuSection component to prevent unnecessary re-renders
const MemoizedMenuSection = React.memo(MenuSection);
