import { Platform } from 'react-native';

export const CARDS_WIDTH_MAP = {
  'detailed-card': 250,
  'simple-card': 250,
  'transport-card': 290,
  'rt-transport-card': 294,
  'text-card': 250,
  template_3: 250,
  'info-card': 250,
  'travel-detail-card': 300,
  'document-card': 250,
  'itinerary-card': 267,
};

const isIos = Platform.OS === 'ios';
export const INPUT_BOX_HEIGHT = 120;
export const INPUT_BOX_HEIGHT_WITHOUT_MODES = 88;
export const INPUT_VIEW_HEIGHT = 20;
export const INPUT_BOX_BOTTOM_PADDING = 34;
export const INPUT_BOX_PLACEHOLDER = ' Ask me anything';

export const REPLY_PREVIEW_ADDITIONAL_HEIGHT = 87; // GRADIENT_HEIGHT from ReplyPreviewCard
export const CONTEXT_MENU_ADDITIONAL_HEIGHT = 40; // Additional height from ContextMenuBar

export const HEADER_CONTEXT: Record<string, ChatHeaderConfig> = {
  messagesScreen: {
    title: '',
    header: {
      menuItems: [
        {
          icon: 'newchat',
          text: 'New Chat',
          action: {
            type: 'navigate',
            value: 'newchat',
          },
        },
        {
          icon: 'chats',
          text: 'Chat History',
          action: {
            type: 'navigate',
            value: 'history',
          },
        },
      ],
      rightItems: [
        {
          icon: 'bookmarkssmall',
          text: '',
          action: {
            type: 'navigate',
            value: 'bookmarks',
          },
        },
      ],
    },
  },
  bookmarkScreen: {
    title: '',
    header: {
      leftItems: [
        {
          icon: 'back',
          text: '',
          action: {
            type: 'navigate',
            value: 'chat',
          },
        },
      ],
    },
  },
  newChatScreen: {
    title: '',
    header: {
      menuItems: [
        {
          icon: 'chats',
          text: 'Chat History',
          action: {
            type: 'navigate',
            value: 'history',
          },
        },
      ],
    },
  },
  historyScreen: {
    title: '',
    header: {
      leftItems: [
        {
          icon: 'back',
          text: '',
          action: {
            type: 'navigate',
            value: 'chat',
          },
        },
      ],
    },
  },
  itineraryScreen: {
    title: '',
    header: {
      leftItems: [
        {
          icon: 'back',
          text: '',
          action: {
            type: 'navigate',
            value: 'chat',
          },
        },
      ],
    },
  },
  tripScreen: {
    title: '',
    header: {
      leftItems: [
        {
          icon: 'back',
          text: '',
          action: {
            type: 'navigate',
            value: 'chat',
          },
        },
      ],
    },
  },
};
