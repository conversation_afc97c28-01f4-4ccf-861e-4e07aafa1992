export const ITINERARY_TEST_DATA = [
  {
    data: {
      id: '<unique_id>',
      card_name: 'itinerary-card',
      title: 'Itinerary 2: Culinary Focus Adventure',
      subTitle: '3N Tokyo → 3N Kyoto → 3N Osaka',
      itineraryDays: '9D 10N',
      callout: {
        text: '95% match to your requirement',
        textColor: '#007E7D',
        bgColor: '#E6FFF9',
      },
      activities: {
        items: ['act1', 'act2'],
        title: 'Inclusions(##NO_OF_ITEMS## items)',
      },
      info: {
        icon: '',
        text: '',
      },
      cta: [
        {
          type: 'primary',
          ctaText: 'Go Ahead with this Itninerary',
          messageText: 'I want to proceed with this intinerary',
          action: 'MESSAGE',
        },
        {
          type: 'secondary',
          ctaText: 'Ask Questions',
          action: 'REPLY',
        },
      ],
      // This will be replaced with table
      detailedContent: ['<markdown-text>', '<markdown-text>'],
      quoteMetadata: {},
    },
    id: '<unique_id>',
    type: 'itinerary-card',
  },
  {
    data: {
      id: '<unique_id>',
      card_name: 'itinerary-card',
      title: 'Itinerary 2: Culinary Focus Adventure',
      subTitle: '3N Tokyo → 3N Kyoto → 3N Osaka',
      itineraryDays: '9D 10N',
      callout: {
        text: '95% match to your requirement',
        textColor: '#007E7D',
        bgColor: '#E6FFF9',
      },
      activities: {
        items: ['act1', 'act2'],
        title: 'Inclusions(##NO_OF_ITEMS## items)',
      },
      info: {
        icon: '',
        text: '',
      },
      cta: [
        {
          type: 'primary',
          ctaText: 'Go Ahead with this Itninerary',
          messageText: 'I want to proceed with this intinerary',
          action: 'MESSAGE',
        },
        {
          type: 'secondary',
          ctaText: 'Ask Questions',
          action: 'REPLY',
        },
      ],
      // This will be replaced with table
      detailedContent: ['<markdown-text>', '<markdown-text>'],
      quoteMetadata: {},
    },
    id: '<unique_id>',
    type: 'itinerary-card',
  },
];

export const TRIPS_TEST_DATA = {
  title: 'Trip itineraries',
  itineraries: [
    {
      title: 'Goa Trip | 3N 4D',
      subTitle: '28 Apr - 2 May | Amit +2',
      icon: 'https://promos.makemytrip.com/appfest/2x//desktop-Monsoon-Camp-130625.jpg?im=Resize=(134,134)',
    },
    {
      title: 'Goa Trip | 3N 4D',
      subTitle: '28 Apr - 2 May | Amit +2',
      icon: 'https://promos.makemytrip.com/appfest/2x//desktop-Monsoon-Camp-130625.jpg?im=Resize=(134,134)',
    },
  ],
  noItineraryData: {
    title: 'No Itineraries Yet!',
    subTitle: 'Plan trips with Myra and explore your itineraries instantly',
    ctaText: 'Plan A Trip',
  },
};
