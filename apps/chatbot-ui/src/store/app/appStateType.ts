import { OnActionProps } from '../../containers/types';
import { TelemetryConfig } from '../../analytics/telemetry';

export type ViewType = 'chat' | 'history' | 'bookmarks' | 'itinerary' | 'trips';

/** @deprecated
 * lets use chatConfig instead of Funnels and Pages
 * */
export type Funnels = 'W2G' | 'MyAccount' | 'MyTrips' | 'Native';
export type Pages = 'W2G_Landing' | 'MyAccount_Drawer' | 'MyTrips_Landing';
export type DeeplinkHandler = (deeplink: string) => boolean;

export type EnabledMode = Mode | null;

export interface ChatAppState {
  org: Org;
  target: Target;
  lang: Language;
  featureConfig: FeatureConfig;
  tempConfig: Record<string, Record<string, unknown>> | null;
  chatContext: ChatContext;
  bottomBarVisibility?: 'show' | 'hide';
  setBottomBarVisibility: (state: 'show' | 'hide') => void;
  currentView: ViewType;
  setCurrentView: (view: ViewType) => void;
  parentFunnel: Funnels | null;
  parentPage: Pages | null;
  parentContextProvider: object | null;
  onAction: (params: OnActionProps) => void;
  triggerDismissHandler: (() => void) | undefined;
  deeplinkHandlers: Array<DeeplinkHandler> | undefined;
  initialUserPrompt: string | null;
  forceFetchId: number | null;
  telemetryConfig: TelemetryConfig | null;
  isDebugMode: boolean;
  itineraryData: any;
  setItineraryData: (data: any) => void;
  modes: Mode[];
  enabledMode: Mode['id'] | null;
  updateMode: (modeId: Mode['id'] | null, enabled: boolean) => void;
  resetModes: () => void;
  shouldMinimizeAfterAssist: boolean;
  compactHeightMode: boolean;
}
