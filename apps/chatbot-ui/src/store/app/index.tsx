import { ChatAppState, ViewType } from './appStateType';
import { create } from 'zustand';
import { Nullable } from '../../types/typeUtils';

type ChatAppStateInit =
  | ({ isStoreInitialised: true } & ChatAppState)
  | ({ isStoreInitialised: false } & Nullable<ChatAppState>);

export const useAppStateStore = create<Nullable<ChatAppStateInit>>((set, get) => ({
  //region Fields
  isStoreInitialised: false,
  chatContext: null,
  bottomBarVisibility: null,
  featureConfig: null,
  lang: null,
  org: null,
  target: null,
  currentView: 'chat',
  parentFunnel: null,
  parentPage: null,
  parentContextProvider: null,
  onAction: null,
  triggerDismissHandler: null,
  deeplinkHandlers: [],
  initialUserPrompt: null,
  forceFetchId: null,
  telemetryConfig: null,
  isDebugMode: false,
  tempConfig: null,
  itineraryData: null,
  modes: [],
  enabledMode: null,
  shouldMinimizeAfterAssist: true,
  compactHeightMode: false,
  //endregion

  //region Updaters
  init: (appState: ChatAppState) => {
    set({
      isStoreInitialised: true,
      bottomBarVisibility: 'show',
      ...appState,
    });
  },
  updateChatContext: (chatContext: ChatContext) => {
    set((state) => ({
      ...state,
      chatContext,
    }));
  },
  setCurrentView: (view: ViewType) => {
    set((state) => ({
      ...state,
      currentView: view,
    }));
  },
  setBottomBarVisibility: (visibility: 'show' | 'hide') => {
    set((state) => ({
      ...state,
      bottomBarVisibility: visibility,
    }));
  },
  setItineraryData: (data: any) => {
    set((state) => ({
      ...state,
      itineraryData: data,
    }));
  },
  updateMode: (modeId: Mode['id'] | null, enabled: boolean) => {
    set((state) => ({
      ...state,
      enabledMode: enabled ? modeId : null,
      modes:
        state.modes?.map((mode) =>
          mode.id === modeId ? { ...mode, enabled } : mode,
        ) || [],
    }));
  },

  // Optional: Reset modes (for new chat)
  resetModes: () => {
    set((state) => ({
      ...state,
      enabledMode: null,
      modes: [],
    }));
  },
  //endregion
}));

export const useBottomBarVisibility = () =>
  useAppStateStore((state) => {
    if (state.currentView !== 'chat' && state.currentView !== 'history') {
      return 'hide';
    }
    return state.bottomBarVisibility;
  });

export const updateBottomBarVisibility = (visibility: 'show' | 'hide') => {
  useAppStateStore.setState({
    bottomBarVisibility: visibility,
  });
};

// export const setCurrentView = useAppStateStore((state) => state.setCurrentView);
