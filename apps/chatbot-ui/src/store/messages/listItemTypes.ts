import {
  WsHelpingHandCallbackSlotsResponseData,
  WsSubSuccessResponse,
} from '../socket/socketType';
import { RefetchOptions } from 'react-query';

export type CardViewType =
  | 'DESTINATION_CARD'
  | 'TRANSPORT_CARD'
  | 'TEXT_CARD'
  | 'INFO_CARD'
  | 'TRAVEL_DETAIL_CARD'
  | 'MYTRIPS_TRANSPORT_CARD'
  | 'MYTRIPS_CARD'
  | 'DOCUMENTS_CARD'
  | 'ITINERARY_CARD'
  | 'PACKAGE_CARD'
  | 'ITINERARY_PROGRESS_CARD'
  | 'TRANSPORT_RT_CARD'
  | 'VIDEO_CARD';

type WithListKey = {
  listKey: string;
};
export type BotCardMessageType = {
  type: 'BOT_MSG_CARDS';
  viewType: CardViewType;
  data: CardWidget;
  msg: Message;
  showFeedback: boolean;
  isFirstItem: boolean;
  isLastMessage?: boolean;
  setVideoModalData?: ({
    show,
    media,
  }: {
    show: boolean;
    media: MediaInfo;
  }) => void | undefined;
} & WithListKey;

export type BotTextMessageType = {
  type: 'BOT_MSG_TEXT';
  data: TextWidget;
  msg: Message;
  showFeedback: boolean;
  isFirstItem: boolean;
  isLastMessage?: boolean;
} & WithListKey;

export type AgentTextMessageType = {
  type: 'AGENT_MSG_TEXT';
  data: TextWidget;
  msg: Message;
  showFeedback: boolean;
  showAvatar: boolean;
  isLastMessage?: boolean;
  agentMeta?: AgentMetaData;
} & WithListKey;

export type UserTextMessageType = {
  type: 'USER_MSG';
  data: TextWidget;
  msg: Message;
} & WithListKey;

export type UserQuotedTextMessageType = {
  type: 'USER_QUOTED_MSG';
  data: TextWidget;
  msg: Message;
} & WithListKey;

export type SystemTextMessageType = {
  type: 'SYSTEM_MSG_TEXT';
  data: TextWidget;
  msg: Message;
} & WithListKey;

export type BotItineraryMessageType = {
  type: 'BOT_MSG_PACKAGE_CARD';
  data: CardWidget;
  msg: Message;
  showFeedback: boolean;
  showAvatar: boolean;
  isLastMessage?: boolean;
} & WithListKey;

export type ItineraryProgressCardType = {
  type: 'BOT_MSG_ITINERARY_PROGRESS_CARD';
  data: CardWidget;
  msg: Message;
} & WithListKey;

export type BotSuggestionType = {
  type: 'SUGGESTION';
  data: Conversation['suggestions'];
  showOnAction: boolean;
};
export type HelpingHandCards = {
  type: 'HELPING_HAND_CARDS';
  data: HelpingHandCard[];
};
export type HelpingHandSlots = {
  type: 'HELPING_HAND_SLOTS';
  data: WsSubSuccessResponse<WsHelpingHandCallbackSlotsResponseData>;
};

export type BotLeadingQuestionType = {
  type: 'LEADING_QUESTION';
  data: string;
  isFirstItem: boolean;
} & WithListKey;

export type BotTableMessageType = {
  type: 'TABLE';
  data: TableWidget;
  msg: Message;
} & WithListKey;

export type BotTripSummaryType = {
  type: 'TRIP_SUMMARY';
  ctaText: string;
};
export type LoaderType = {
  type: 'MSG_LOADER';
};
export type NewPromptType = {
  type: 'NEW_CHAT_PROMPT';
};
type NoOpListItemType = {
  type: 'NO_OP_MESSAGE'; // this will be used as fallback for any unknown message type
};
export type CtasType = {
  type: 'SUGGESTION_CTAS';
  data: Ctas[];
};
export type TravellerInsightType = {
  type: 'TRAVELLER_INSIGHT';
  data: {
    text: string;
  };
  msg: Message;
  listKey: string;
};
export type MessageListItems =
  | UserTextMessageType
  | UserQuotedTextMessageType
  | BotTextMessageType
  | SystemTextMessageType
  | BotCardMessageType
  | BotSuggestionType
  | BotLeadingQuestionType
  | BotTripSummaryType
  | LoaderType
  | NewPromptType
  | NoOpListItemType
  | HelpingHandCards
  | HelpingHandSlots
  | AgentTextMessageType
  | CtasType
  | BotItineraryMessageType
  | TravellerInsightType
  | ItineraryProgressCardType
  | BotTableMessageType;
export type ScreenState =
  | 'welcome'
  | 'new-chat'
  | 'loading'
  | 'messages'
  | 'error'
  | 'messages-error'
  | 'messages-loading'
  | 'forbidden-error'
  | 'TRIP-PLAN'
  | '';
export type MessageListViewData = {
  screenState: ScreenState;
  refetchConfig: (options: RefetchOptions) => Promise<unknown>;
  refetchMessages: (options: RefetchOptions) => Promise<unknown>;
  listData: MessageListItems[];
  isMessageLoading: boolean;
  title: string | null;
  isForbiddenError: boolean;
  conversationsWriteAllowed?: boolean;
  chatHeaderConfig?: ChatHeaderConfig | undefined;
  enrichedTitle?: {
    type: 'text' | 'image';
    value: string;
  };
};
