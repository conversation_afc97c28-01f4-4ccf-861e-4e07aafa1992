import { useMemo } from 'react';
import {
  isCardWidgetType,
  isSystemTextType,
  isTextWidgetType,
  useMessageStore,
  isInsightWidgetType,
  isTableWidgetType,
} from './messageStore';
import { useConversationMessages } from './useConversationMessages';
import {
  CardViewType,
  MessageListItems,
  MessageListViewData,
  ScreenState,
} from './listItemTypes';
import { useConfigSettings } from './newChatView';
import { useAppStateStore } from '../app';
import { TRIP_PLAN_MODE_ID } from '../../hooks/useModeState';

const DEFAULT_SEQUENCE = ['CONTENT', 'INSIGHTS', 'LEADING_QUESTION'] as const;
function organizeContentByType(
  msg: Message,
  previousMessage: Message | null,
  hasMessageChunkChanged: boolean,
  agentMeta?: AgentMetaData,
  isLastMessage: boolean = false,
): Record<string, MessageListItems[]> {
  const contentMap: Record<string, MessageListItems[]> = {
    CONTENT: [],
    LEADING_QUESTION: [],
    INSIGHTS: [],
  };

  // Transform regular content
  const listItems = transformToListItems(msg, {
    showAvatar: hasMessageChunkChanged,
    agentMeta,
    isLastMessage,
  });
  contentMap.CONTENT.push(...(listItems || []));

  // Add leading questions
  if (msg.leadingQuestion) {
    contentMap.LEADING_QUESTION.push({
      type: 'LEADING_QUESTION',
      data: msg.leadingQuestion,
      listKey: `leading_question_${msg.id}`,
      isFirstItem:
        (previousMessage?.id === msg.id &&
          !previousMessage?.content?.length &&
          !msg?.content?.length) ||
        (previousMessage?.id !== msg.id && !msg?.content?.length),
    });
  }
  // Add insights
  if (Array.isArray(msg.lq_insights?.content) && msg.lq_insights?.content?.length) {
    msg.lq_insights.content.forEach((insight) => {
      contentMap.INSIGHTS.push({
        type: 'TRAVELLER_INSIGHT',
        listKey: `insight_${msg.id}`,
        data: {
          text: insight?.response_text,
        },
        msg,
      });
    });
  }
  return contentMap;
}

export function useMessageListData(): MessageListViewData {
  const {
    isFetching: isConfigApiLoading,
    data: configData,
    isError: isConfigApiError,
    error: configApiError,
    refetch: refetchConfig,
  } = useConfigSettings();

  const isForbiddenError = useMemo(
    () => {
      if (
        configApiError instanceof Error &&
        configApiError?.message === 'Forbidden'
      ) {
        return true;
      }
      return false;
    },
    [configApiError], // Include the entire object as a dependency
  );

  const hasWelcomeScreenContent = useMemo(
    () => configData?.content?.type === 'PROMPTS_WITH_IMAGES',
    [configData?.content?.type],
  );
  const { enabledMode } = useAppStateStore();
  const { activeConversationId, conversationById, newChatRequested } =
    useMessageStore();

  const convId = activeConversationId || 'draft';

  const conversation = conversationById[convId];
  const {
    isLoading: isMessagesLoading,
    isError: isConversationsError,
    refetch: refetchMessages,
    data: conversationData,
  } = useConversationMessages(conversation?.shouldFetch || false, convId);
  //endregion

  //region Messages Transformation
  const messages = useMemo(() => {
    return [
      ...(conversation?.messages || []),
      ...(conversation?.awaitedUserMessage || []),
    ];
  }, [conversation?.messages, conversation?.awaitedUserMessage]);

  const suggestions = conversation?.suggestions;
  const helpingHandSlotsResponse = conversation?.helpingHands?.slotsData;
  const helpingHandCardsResponse = conversation?.helpingHands?.cardsData;
  const ctas = conversation?.ctas;

  const isMessageLoading = conversation?.isLoading || false;
  const isMessagesStreaming = conversation?.isStreaming || false;
  const listData: Array<MessageListItems> = useMemo(() => {
    let data: Array<MessageListItems> = [];

    // Process each message
    messages.forEach((msg, index) => {
      const previousMessage: Message | null = index > 0 ? messages[index - 1] : null;
      const hasMessageChunkChanged = checkMessageChunkChange(previousMessage, msg);
      // Get sequence from current message or use default
      const sequence = msg?.sequence || DEFAULT_SEQUENCE;

      // Organize content by type
      const contentMap = organizeContentByType(
        msg,
        previousMessage,
        hasMessageChunkChanged,
        conversation.agentMeta,
        index === messages.length - 1,
      );
      // If CONTENT is not in sequence, show listItems first
      if (!sequence.includes('CONTENT') && contentMap.CONTENT.length > 0) {
        data = data.concat(contentMap.CONTENT);
      }

      // Add content in the sequence specified by backend
      sequence.forEach((type) => {
        // Skip CONTENT type if it was already added
        if (type === 'CONTENT' && !sequence.includes('CONTENT')) {
          return;
        }
        if (type in contentMap && contentMap[type].length > 0) {
          data = data.concat(contentMap[type]);
        }
      });
    });

    // Add additional items that are not part of the sequence
    if (helpingHandCardsResponse) {
      data.push({
        type: 'HELPING_HAND_CARDS',
        data: helpingHandCardsResponse,
      });
    }

    if (helpingHandSlotsResponse) {
      data.push({
        type: 'HELPING_HAND_SLOTS',
        data: helpingHandSlotsResponse,
      });
    }

    if (suggestions && suggestions.items?.length) {
      data.push({
        type: 'SUGGESTION',
        data: suggestions,
        showOnAction:
          typeof configData?.suggestionOnAction !== 'boolean'
            ? true
            : configData.suggestionOnAction,
      });
    }

    if (ctas && ctas.length) {
      data.push({
        type: 'SUGGESTION_CTAS',
        data: ctas,
      });
    }

    if (!isMessageLoading && conversation?.showTripSummary) {
      data.push({
        type: 'TRIP_SUMMARY',
        ctaText: conversation.tripSummaryText || 'Your trip itinerary',
      });
    }

    if (isMessageLoading && !isMessagesStreaming) {
      data.push({
        type: 'MSG_LOADER',
      });
    }

    if (newChatRequested && !data.length) {
      data.push({
        type: 'NEW_CHAT_PROMPT',
      });
    }
    return [...data];
  }, [
    configData,
    messages,
    suggestions,
    isMessageLoading,
    conversation?.showTripSummary,
    conversation?.tripSummaryText,
    newChatRequested,
    helpingHandCardsResponse,
    helpingHandSlotsResponse,
    ctas,
    isMessagesStreaming,
  ]);

  const chatHeaderConfig = useMemo(() => {
    if (conversation?.chatHeader) {
      return conversation?.chatHeader;
    }
    return undefined;
  }, [conversation]);
  //endregion
  const screenState: ScreenState = useMemo(() => {
    if (isConfigApiError) {
      return 'error';
    }

    if (isConfigApiLoading) {
      return 'loading';
    }
    if (
      enabledMode === TRIP_PLAN_MODE_ID &&
      messages.length === 0 &&
      (activeConversationId === 'draft' || activeConversationId === null)
    ) {
      return TRIP_PLAN_MODE_ID;
    }
    if (isMessagesLoading) {
      return 'messages-loading';
    }
    if (isConversationsError) {
      return 'messages-error';
    }

    if (newChatRequested) {
      return 'new-chat';
    }
    if (
      messages.length !== 0 ||
      (activeConversationId !== 'draft' && activeConversationId !== null)
    ) {
      return 'messages';
    }

    if (!hasWelcomeScreenContent) {
      return 'new-chat';
    }

    // this condition should be at last, because as a fallback we can atleast render 'new-chat'
    if (isConversationsError) {
      return 'messages-error';
    }
    return 'welcome';
  }, [
    isMessagesLoading,
    isConversationsError,
    messages,
    activeConversationId,
    newChatRequested,
    isConfigApiLoading,
    hasWelcomeScreenContent,
    isConfigApiError,
    enabledMode,
  ]);

  return {
    refetchConfig,
    refetchMessages,
    isForbiddenError,
    listData,
    screenState,
    isMessageLoading,
    title: conversation?.title || null,
    enrichedTitle: configData?.enrichedTitle,
    chatHeaderConfig,
  };
}

const checkMessageChunkChange = (
  previousMessage: Message | null,
  message: Message,
) => {
  return previousMessage?.role !== message.role;
};

export function transformToListItems(
  msg: Message,
  {
    disableFeedback = false,
    showAvatar = false,
    agentMeta,
    isLastMessage = false,
  }: {
    disableFeedback?: boolean;
    showAvatar?: boolean;
    agentMeta?: AgentMetaData;
    isLastMessage?: boolean;
  } = {},
): MessageListItems[] {
  const content = msg?.content;
  if (!Array.isArray(content)) {
    return [];
  }
  return content.map((msgItem, i): MessageListItems => {
    const showFeedback = !!(
      !disableFeedback &&
      msg.showBookmarkFeedback &&
      i === content.length - 1
    );
    let listKey = `${msg.role}_${msg.id}_${msg.isCompleted}_${i}`;
    if (isCardWidgetType(msgItem)) {
      const viewType = getViewType(msgItem);
      if (viewType) {
        if (viewType === 'PACKAGE_CARD') {
          return {
            type: 'BOT_MSG_PACKAGE_CARD',
            listKey,
            data: msgItem,
            msg: msg,
            showFeedback,
            showAvatar,
            isLastMessage,
          };
        }
        if (viewType === 'ITINERARY_PROGRESS_CARD') {
          return {
            type: 'BOT_MSG_ITINERARY_PROGRESS_CARD',
            listKey,
            data: msgItem,
            msg: msg,
          };
        }
        if (viewType !== null) {
          listKey = `${listKey}_${viewType}`;
          return {
            type: 'BOT_MSG_CARDS',
            listKey,
            data: msgItem,
            msg: msg,
            viewType,
            showFeedback,
            isFirstItem: i === 0,
            isLastMessage,
          };
        }
      }
    }
    if (isTextWidgetType(msgItem)) {
      listKey = `${listKey}_text`;
      if (msg.role === 'USER' && msgItem.quotedContentInfo) {
        return {
          type: 'USER_QUOTED_MSG',
          listKey,
          data: msgItem,
          msg: msg,
        };
      }
      if (msg.role === 'USER') {
        return {
          type: 'USER_MSG',
          listKey,
          data: msgItem,
          msg: msg,
        };
      }
      if (msg.role === 'SYSTEM_MESSAGE') {
        return {
          type: 'SYSTEM_MSG_TEXT',
          listKey,
          data: msgItem,
          msg: msg,
        };
      }
      if (msg.role === 'AGENT') {
        return {
          type: 'AGENT_MSG_TEXT',
          listKey,
          data: msgItem,
          msg: msg,
          showFeedback,
          showAvatar,
          isLastMessage,
        };
      }

      return {
        type: 'BOT_MSG_TEXT',
        listKey,
        data: msgItem,
        msg: msg,
        showFeedback,
        isFirstItem: i === 0,
        isLastMessage,
      };
    }

    if (isSystemTextType(msgItem)) {
      return {
        type: 'SYSTEM_MSG_TEXT',
        listKey,
        data: msgItem,
        msg: msg,
      };
    }
    if (isInsightWidgetType(msgItem)) {
      return {
        type: 'TRAVELLER_INSIGHT',
        listKey,
        data: {
          text:
            typeof msgItem.response_text === 'string' ? msgItem.response_text : '',
        },
        msg,
      };
    }
    if (isTableWidgetType(msgItem)) {
      return {
        type: 'TABLE',
        listKey,
        data: msgItem,
        msg,
      };
    }
    // this case should not be reached
    return {
      type: 'NO_OP_MESSAGE',
    };
  });
}

function getViewType(msgItem: CardWidget): CardViewType | null {
  const cardItem = msgItem.value.templateInfo.payload || [];
  const cardItemType = cardItem[0]?.type;
  let viewType: CardViewType | null = null;
  console.log('cardItemType', cardItemType);
  if (cardItemType === 'detailed-card' || cardItemType === 'simple-card') {
    viewType = 'DESTINATION_CARD';
  } else if (cardItemType === 'transport-card') {
    viewType = 'TRANSPORT_CARD';
  } else if (cardItemType === 'info-card' || cardItemType === 'template_3') {
    viewType = 'INFO_CARD';
  } else if (cardItemType === 'text-card') {
    viewType = 'TEXT_CARD';
  } else if (cardItemType === 'travel-detail-card') {
    viewType = 'TRAVEL_DETAIL_CARD';
  } else if (cardItemType === 'mytrips-card') {
    viewType = 'MYTRIPS_CARD';
  } else if (cardItemType === 'mytrips-transport-card') {
    viewType = 'MYTRIPS_TRANSPORT_CARD';
  } else if (cardItemType === 'documents-card') {
    viewType = 'DOCUMENTS_CARD';
  } else if (cardItemType === 'package-card') {
    viewType = 'PACKAGE_CARD';
  } else if (cardItemType === 'itinerary-card') {
    viewType = 'ITINERARY_CARD';
  } else if (cardItemType === 'itinerary-progress-card') {
    viewType = 'ITINERARY_PROGRESS_CARD';
  } else if (cardItemType === 'rt-transport-card') {
    viewType = 'TRANSPORT_RT_CARD';
  } else if (cardItemType === 'video-card') {
    viewType = 'VIDEO_CARD';
  }
  return viewType;
}
