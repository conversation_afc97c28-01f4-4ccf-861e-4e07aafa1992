import { useCallback, useEffect, useRef, useState } from 'react';
import { useMessageStore } from './messageStore';
import {
  api_postFeedback,
  PostFeedbackRequest,
} from '../../network/api/feedbackApi';
import { Analytics, TrackingEvent } from '../../analytics';

export type FeedbackType = 'LIKE' | 'DISLIKE' | null;

export function useFeedbackAction(msg: Message) {
  const [feedback, setFeedback] = useState<FeedbackType>(null);
  const [responseText, setResponseText] = useState<string | null>(null);
  const timerRef = useRef<ReturnType<typeof setTimeout>>();
  const resetResponseTextWithDelay = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    timerRef.current = setTimeout(() => {
      setResponseText(null);
    }, 3000);
  }, []);
  useEffect(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
  }, []);
  const sendFeedback = useCallback(
    (selectedAction: FeedbackType) => {
      if (feedback === selectedAction) {
        return;
      }
      setFeedback(selectedAction);
      if (selectedAction === 'DISLIKE') {
        setResponseText('Thanks for your feedback! We’ll look into it!');
        Analytics.trackClickEvent(TrackingEvent.payload_DislikeMessageClicked());
      } else if (selectedAction === 'LIKE') {
        setResponseText('Thanks for your positive feedback!');
        Analytics.trackClickEvent(TrackingEvent.payload_LikeMessageClicked());
      }

      if (selectedAction === null) {
        return;
      }

      const conversationId = useMessageStore.getState()
        .activeConversationId as string;
      const req: PostFeedbackRequest = {
        messageId: msg.id,
        conversationId,
        message: {
          role: 'USER',
          content: [
            {
              type: 'FEEDBACK',
              value: selectedAction,
            },
          ],
        },
      };
      api_postFeedback(req)
        .then((res) => {})
        .catch(() => {
          setResponseText('Failed to send feedback. Please try again later.');
        })
        .finally(() => resetResponseTextWithDelay());
    },
    [msg.id, resetResponseTextWithDelay, feedback],
  );
  return {
    feedback,
    responseText,
    sendFeedback,
  };
}
