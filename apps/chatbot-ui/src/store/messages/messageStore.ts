// @ts-nocheck
import { create } from 'zustand';
import {
  WsLoggingResponseData,
  WsSubNewChatCreatedResponseData,
  WsSubNewMessageResponseData,
  WsSubscribeEvent,
  WsSubSuccessResponse,
  WsSubSystemActionIntentResponseData,
  WsSubUIElementResponseData,
} from '../socket/socketType';
import { invalidateConversationsHistoryQuery } from '../../network/hooks/use-conversations-history-query';
import { Logger } from '../../utils/logger';
import {
  api_addBookmark,
  getBookmarks,
  RemoveBookmarkRequest,
  removeBookmarks,
  SaveBookmarkResponse,
} from '../../network/api/bookmarkApi';

import { useQuery } from 'react-query';
import { queryClient } from '../../containers/queryClientInstance';
import { invalidateConversationMessagesQuery } from '../../network/hooks/use-conversation-messages-query';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Toast } from '../../components/toast';
import { ToastAction } from '../../components/toast/toast';
import { useAppStateStore } from '../app';
import {
  BookmarkClickTypes,
  trackMsg,
  trackOmnitureClickEvent,
} from '../../native/omniture';
import { unique } from '../../utils/objectUtils';
import StorageUtils from '../../utils/storageUtils';
import { fireLoginAction } from '../../native/bridge';
import { refetchChatViewData } from './newChatView';

import { config } from '../../config';
import { ActionTypes } from '../../containers/types';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import { Analytics, TrackingEvent } from '../../analytics';
import { getPlatform } from '../../utils/getPlatform';
import { WebSocketState } from '../socket/SocketClient';
import { canWriteToConversation } from '../../utils/storeUtils';
import { updateMessageTrace } from '../../analytics/telemetry';
import { INPUT_BOX_HEIGHT, INPUT_VIEW_HEIGHT } from '../../const';
import { openDeepLink } from '../../native/deepLinks';
import { triggerSuccessHaptic } from '../../utils/hapticUtils';
import {
  stopTtsIfPlaying,
  useTalkBackStore,
  waitForTalkbackComplete,
} from '../audio-talkback/talkbackStore';

const MessageLogger = Logger.createLogger({ tag: 'MessageStore', level: 'DEBUG' });

type MessagesState = {
  activeConversationId: string | null;
  conversationById: Record<string, Conversation>;
  // inputStateByConversationId: Record<string, string>;
  newChatRequested: boolean;
  tmpConvId: string | null;
};

type MessagesActions = {
  resetState: () => void;
  updateConversationById(convId: string, update: Partial<Conversation>): void;
  setActiveConversationId: (id: string) => void;
  // updateInputStateByConversationId: (id: string, value: string) => void;
  handleMessageEvent: (event: WsSubscribeEvent) => void;
  handleSocketStateChange: (newState: WebSocketState, isActive: boolean) => void;
  bookmarkMessage: (
    message: Message,
    convId: string,
    pageSource: BookmarkPageSource,
  ) => void;
  bookmarkCard: (
    message: Message,
    convId: string,
    cardId: string,
    pageSource: BookmarkPageSource,
  ) => Promise<void>;
  removeBookmarks: (
    requests: Array<Omit<RemoveBookmarkRequest, 'conversationId'>>,
    convId: string,
    pageSource: BookmarkPageSource,
  ) => Promise<void>;
  resetBookmarkReadCount: () => void;
  setNewChatRequested: (value: boolean) => void;
  removeConversationById;
};

export type MessageStoreType = MessagesState & MessagesActions;

function showBookmarkSuccessToast(
  saveBookmarkResponse: SaveBookmarkResponse,
  type: string,
) {
  if (type !== 'create') {
    return;
  }
  if (!saveBookmarkResponse.displayMessage) {
    return;
  }
  const action: ToastAction = {
    text: 'View',
    onPress: () => {
      useAppStateStore.setState({
        currentView: 'bookmarks',
      });
    },
  };
  Toast.show(saveBookmarkResponse.displayMessage, action);
}

export const useInputStateStore = create<{
  inputStateByConversationId: Record<string, string>;
  updateInputStateByConversationId: (id: string, value: string) => void;
  disableInput: boolean;
  updateDisableInput: (value: boolean) => void;
  totalInputAreaHeight: number;
  setTotalInputAreaHeight: (height: number) => void;
}>((set, get) => ({
  inputStateByConversationId: {},
  updateInputStateByConversationId: (id, value) => {
    set((state) => ({
      inputStateByConversationId: {
        ...state.inputStateByConversationId,
        [id]: value,
      },
    }));
  },
  disableInput: false,
  updateDisableInput: (value: boolean) => {
    set(() => ({
      disableInput: value,
    }));
  },
  totalInputAreaHeight: INPUT_BOX_HEIGHT + INPUT_VIEW_HEIGHT, // Default
  setTotalInputAreaHeight: (height: number) => {
    set(() => ({
      totalInputAreaHeight: height,
    }));
  },
}));

let convIdRefreshList: string[] = [];
export const useMessageStore = create<MessageStoreType>()((set, get) => ({
  resetState: () => {
    set(() => ({
      activeConversationId: null,
      inputStateByConversationId: {},
    }));
  },
  activeConversationId: null,
  setActiveConversationId: (id) => {
    if (get().activeConversationId !== id) {
      stopTtsIfPlaying('conversationId changed');
    }
    set(() => ({ activeConversationId: id }));
  },
  conversationById: {},
  newChatRequested: false,
  setNewChatRequested: (value: boolean) => {
    set(() => ({
      newChatRequested: value,
      activeConversationId: 'draft',
      tmpConvId: null,
    }));
  },
  tmpConvId: null,
  updateConversationById: (id: string, value: Partial<Conversation>) => {
    set((state) => ({
      conversationById: {
        ...state.conversationById,
        [id]: {
          ...(state.conversationById[id] || {}),
          ...value,
        },
      },
    }));
  },
  removeConversationById: (id: string) => {
    set((state) => ({
      conversationById: {
        ...state.conversationById,
        [id]: undefined,
      },
    }));
  },
  handleSocketStateChange: (newState: WebSocketState) => {
    if (newState === WebSocketState.CLOSED || newState === WebSocketState.ERROR) {
      const { updateConversationById, conversationById } = get();
      Object.values(conversationById).forEach((conv: Conversation) => {
        if (conv?.isLoading) {
          convIdRefreshList.push(conv.id);
          updateConversationById(conv.id, {
            isLoading: false,
          });
        }
      });
    }
    if (newState === WebSocketState.OPEN) {
      const { conversationById } = get();
      convIdRefreshList.forEach((convId: Conversation['id']) => {
        if (conversationById[convId]) {
          invalidateConversationMessagesQuery(convId);
        }
      });
      convIdRefreshList = [];
    }
  },
  handleMessageEvent: (event: WsSubscribeEvent) => {
    MessageLogger.info('handleMessageEvent', event.eventType);
    switch (event.eventType) {
      case 'TITLE_UPDATE': {
        if (!event.success) {
          return;
        }
        const { conversationId, chatTitle } = event.data;
        const conversation = get().conversationById[conversationId];
        if (conversation) {
          set((state) => ({
            conversationById: {
              ...state.conversationById,
              [conversationId]: {
                ...conversation,
                title: chatTitle,
              },
            },
          }));
        }
        return;
      }
      case 'HEADER_CONFIG_UPDATE': {
        if (!event.success) {
          return;
        }
        const { conversationId, chatHeader } = event.data;
        const conversation = get().conversationById[conversationId];
        if (conversation && chatHeader) {
          set((state) => ({
            conversationById: {
              ...state.conversationById,
              [conversationId]: {
                ...conversation,
                chatHeader,
              },
            },
          }));
        }
        return;
      }
      case 'NEW_CHAT_CREATED': {
        MessageLogger.debug('NEW_CHAT_CREATED', JSON.stringify(event, null, 2));
        const _event =
          event as WsSubSuccessResponse<WsSubNewChatCreatedResponseData>;
        // @ts-ignore
        if (_event.data.leadingQuestion) {
          // @ts-ignore
          _event.data.message.leadingQuestion = _event.data.leadingQuestion;
        }
        const msgConvId = _event.data.conversationId;
        const msgTmpConvId = _event.uiMetadata?.tempId;
        MessageLogger.verbose('activeConversationId', msgConvId);
        const {
          activeConversationId: sessionConvId,
          tmpConvId,
          conversationById,
        } = get();
        const enabledModeFromStore = useAppStateStore.getState().enabledMode;
        const modesFromStore = useAppStateStore.getState().modes;
        const isNewChat = conversationById[sessionConvId || 'draft']?.isNewChat;
        useAppStateStore.setState({ modes: [], enabledMode: null });
        set((state) => {
          if (sessionConvId === msgConvId) {
            return state;
          }
          if (
            (sessionConvId === null || sessionConvId === 'draft') &&
            msgTmpConvId === tmpConvId
          ) {
            state.activeConversationId = msgConvId;

            // CHECKME
            // to make created draft conversation is cleared
            if (state.conversationById?.['draft']) {
              state.conversationById['draft'] = {
                ...state.conversationById['draft'],
                messages: [],
                awaitedUserMessage: null,
              };
            }
            StorageUtils.setLastConversationId(msgConvId);
          }
          state.conversationById[msgConvId] = {
            id: msgConvId,
            contextId: 'NA',
            messages: [_event.data.message],
            isLoading: true,
            isNewChat,
            lastMessage: { content: [], createdAt: 0 },
            org: 'MMT',
            bookingLob: 'CABS',
            context: { chatType: 'BOT' },
            title: '',
            hasUnRead: false,
            updatedAt: Date.now(),
            ...(enabledModeFromStore && { mode: modesFromStore }),
            // mode: modesFromStore,
          };

          return {
            ...state,
            conversationById: {
              ...state.conversationById,
            },
          };
        });
        invalidateConversationsHistoryQuery();
        break;
      }
      case 'NEW_MESSAGE': {
        MessageLogger.debug('NEW_MESSAGE', event);
        const _event = event as WsSubSuccessResponse<WsSubNewMessageResponseData>;
        const data = _event.data;
        const { message } = data;
        if (data.leadingQuestion) {
          message.leadingQuestion = data.leadingQuestion;
        }
        if (data.lq_insights) {
          message.lq_insights = data.lq_insights;
        }
        let { audioTalkBack } = data;
        console.log('@mmt10296:: audioTalkBack = ', audioTalkBack);

        /*
// for testing talkback
        if (!audioTalkBack) {
          audioTalkBack = {};
          const textContent = data.message.content?.find(
            (msg: MessageWidget) => msg.type === 'TEXT',
          );
          if (textContent) {
            audioTalkBack = {
              summary: (textContent as TextWidget).value,
            };
          }
          if (data.leadingQuestion) {
            audioTalkBack = {
              ...audioTalkBack,
              lqNode: data.leadingQuestion,
            };
          }
        }
*/

        if (audioTalkBack && get().activeConversationId === data.conversationId) {
          message.audioTalkBack = audioTalkBack;
          if (message.id) {
            useTalkBackStore.getState().setAudioData(message.id, audioTalkBack);
          }
          // Handle TTS in sequence
          const talkback = useTalkBackStore.getState();
          if (audioTalkBack.summary) {
            // Play summary first
            talkback.speak(audioTalkBack.summary, audioTalkBack.locale);

            // If LQ exists, enqueue it with delay
            if (audioTalkBack.lqNode) {
              talkback.enqueue(
                audioTalkBack.lqNode,
                audioTalkBack.locale,
                audioTalkBack?.lqDelay || 2000,
              );
            }
          } else if (audioTalkBack.lqNode) {
            // If only LQ exists, play it immediately
            talkback.speak(audioTalkBack.lqNode, audioTalkBack.locale);
          }
        }
        if (!event.success) {
          MessageLogger.warn('failure in NEW_MESSAGE');
          // return;
        }
        const traceId = getTraceId(_event);
        const activeConversationId = data.conversationId || 'draft';

        const content = message.content || [];
        if (content.length) {
          if (content[0].type === 'LOADER_TEXT') {
            useLoaderStore.setState({
              [activeConversationId]: content[0].value as string,
            });
            return;
          }

          // HELPING_HAND_CARDS HANDLING
          if (
            content.length > 1 &&
            content[1].type === 'CARD' &&
            content[1].value?.templateInfo?.templateId === 'helping-hand-card'
          ) {
            const state = get();
            const conversation = state.conversationById[activeConversationId];
            conversation.helpingHands = {
              cardsData: content[1].value.templateInfo.payload,
            };
          }
        }
        const state = get();
        const conversation = state.conversationById[activeConversationId];

        let loadingFlag = conversation?.isLoading;
        let isStreamingFlag = conversation?.isStreaming;

        if (message.role !== 'USER') {
          loadingFlag = !message.isCompleted;
          isStreamingFlag = message.isStreaming;
        }

        if (message?.meta?.agentType === 'HUMAN') {
          loadingFlag = false;
        }
        if (traceId) {
          if (message.role === 'USER') {
            updateMessageTrace(traceId, 'ackReceived');
          } else {
            updateMessageTrace(traceId, 'firstReply');
            if (!loadingFlag) {
              updateMessageTrace(traceId, 'end');
            }
          }
        }

        // check if the last message is same as the current message
        // if yes then append the content to the last message
        // else add the message to the conversation
        // Note: only text content is appended

        const lastMessage: Message | undefined =
          conversation?.messages && conversation.messages?.length > 0
            ? conversation.messages[conversation.messages.length - 1]
            : undefined;

        // check if the last message is same as the current message
        const addToExistingStreamedMessage =
          'isStreaming' in message &&
          lastMessage?.role === message.role &&
          lastMessage.id === message.id;

        if (lastMessage) {
          if (!lastMessage.streamMessage && message?.isStreaming) {
            message.streamMessage = true;
          }
          lastMessage.isStreaming = message?.isStreaming;
          lastMessage.isCompleted = !loadingFlag;
        }

        const itineraryStatusCardIndex = message?.content.findIndex((item) =>
          isItineraryStatusCardType(item),
        );

        if (addToExistingStreamedMessage && itineraryStatusCardIndex === -1) {
          message.content?.forEach((item) => {
            // check if the contentId is already present in the last message
            // note: only checking for TEXT type contentId
            const textContentIdFound =
              item.type === 'TEXT' &&
              lastMessage.content?.find(
                (lastItem) => lastItem.contentId === item.contentId,
              );
            if (textContentIdFound) {
              // append the text value to the last message content
              lastMessage.content = lastMessage.content?.map((lastItem) => {
                if (lastItem.contentId === item.contentId) {
                  return {
                    ...lastItem,
                    value: `${lastItem.value}${item.value}`,
                  };
                }
                return lastItem;
              });
            } else {
              // if the contentId is not found or if the content is not text , then add the item to the last message content
              lastMessage.streamMessage = false;
              lastMessage.content = [...(lastMessage.content ?? []), item];
            }
          });
          if (message?.leadingQuestion || message?.lq_insights) {
            lastMessage.leadingQuestion = message?.leadingQuestion || null;
            lastMessage.lq_insights = message?.lq_insights || null;
            lastMessage.sequence = message?.lq_insights?.sequence || null;
            lastMessage.streamMessage = false;
          }
        } else {
          // Fixed: Ensure conversation.messages is always an array before concatenating
          // conversation.messages = (conversation?.messages || []).concat(message);

          if (itineraryStatusCardIndex !== -1) {
            // check for exisitng itinery status card if not presetnt add it as it is
            // if presetnt append only the payload content of that particular
            const existingItineraryStatusCard = conversation?.messages?.find(
              (msgs) =>
                msgs.content?.some(
                  (item) =>
                    isItineraryStatusCardType(item) &&
                    item.contentId ===
                      message.content[itineraryStatusCardIndex].contentId,
                ),
            );
            if (existingItineraryStatusCard) {
              const cardData = message.content.find((item) =>
                isItineraryStatusCardType(item),
              );

              // if removeFromUI is true then remove the card from the conversation

              if (cardData?.value.templateInfo.payload[0].data?.removeFromUI) {
                conversation.messages = conversation.messages.filter(
                  (msg) => msg.id !== existingItineraryStatusCard.id,
                );
              } else {
                existingItineraryStatusCard.content = message.content;
              }
            } else {
              conversation.messages = conversation?.messages?.concat(message) || [];
            }
          } else {
            conversation.messages = conversation?.messages?.concat(message) || [];
          }
        }
        message.sequence = data?.lq_insights?.sequence;
        conversation.awaitedUserMessage = null;
        conversation.isLoading = loadingFlag;
        conversation.isStreaming = isStreamingFlag;
        conversation.suggestions = {
          leadingQuestion: data.leadingQuestion || null,
          items: data.suggestions as string[],
        };
        // ctas
        if (data.ctas) {
          conversation.ctas = data.ctas;
        }
        // agent meta setting
        conversation.agentMeta =
          typeof message?.meta === 'object' && Object.keys(message.meta).length > 0
            ? message.meta
            : undefined;
        StorageUtils.setLastMessageReceivedTime(Date.now().toString());
        if (data.showOverlay) {
          conversation.showTripSummary = true;
        }
        if (data.overlayCtaText) {
          conversation.tripSummaryText = data.overlayCtaText;
        }
        conversation.title = data.chatTitle || conversation.title;
        state.updateConversationById(activeConversationId, {
          ...conversation,
        });
        if (loadingFlag) {
          useLoaderStore.setState({
            [activeConversationId]: null,
          });
        }

        if (
          /*user message will be logged from useMessageAction, so logging only bot messages here */
          message.role !== 'USER'
        ) {
          trackMsg(
            {
              VAR_CONVERSATION_ID: data.conversationId as string,
              VAR_CHAT_TYPE: conversation.isNewChat ? 'chat_new' : 'chat_old',
              VAR_MSG_ROLE: 'msg_src_trvlplex',
              // VAR_CHAT_SRC & CHAT_SRC are not applicable here
            },
            'NEW_MESSAGE',
          );

          // track details of the leading question & suggestions shown in the message
          if (data.leadingQuestion) {
            trackOmnitureClickEvent('LEAD_QUESTION_SHOWN');
          }
          if (data.suggestions?.length) {
            trackOmnitureClickEvent('SUGGESTION_PROMPTS_SHOWN');
          }

          // track details of the cards shown in the message
          const cardLobs = unique(
            message.content.flatMap((card) => {
              if (!isCardWidgetType(card)) {
                return [];
              }
              return card.value?.templateInfo?.payload?.map(
                (cardItem) => cardItem.data.lob,
              );
            }),
          );
          if (cardLobs.length) {
            cardLobs.forEach((cardLob) => {
              trackOmnitureClickEvent('CARDS_SHOWN', {
                CONTENT_TYPE: `media_${cardLob}`,
              });
              trackPDTEvent({
                eventName: eventNameSchema.CHAT_INTERACTED,
                eventType: eventTypeSchema.ACTION,
                eventValue: eventValueSchema.MEDIA_SHOWN,
              });
            });
          }

          const { chatContext, onAction } = useAppStateStore.getState();
          const context: any = chatContext
            ? chatContext?.contextMetadata?.pageContext
            : {};
          // TODO: Have to check why FLIGHTS is hardcoded here
          const { lob = 'FLIGHTS' } = context;
          if (data.assistedFlowFlag && data.assistedFlowPayload) {
            const action = data.assistedFlowPayload.action;
            const waitTime = data.assistedFlowPayload.data?.waitTime || 2_000;
            console.log('@mmt10296:: action = ', action);
            const dismissAfterAssistFlag =
              data.assistedFlowPayload.data?.dismissAfterAssist || false;
            if (action === 'TRAVELPLEX_REDIRECT') {
              waitForTalkbackComplete({ initTimeout: 2_000 })
                .then(() => {
                  const deepLink = data.assistedFlowPayload.data?.deeplink;
                  if (deepLink) {
                    setTimeout(() => {
                      openDeepLink(deepLink);
                      if (
                        dismissAfterAssistFlag &&
                        typeof triggerDismissHandler === 'function'
                      ) {
                        triggerDismissHandler?.();
                      }
                    }, waitTime);
                  }
                })
                .catch(() => {
                  // no-op
                });
              return;
            }
            if (!lob) {
              break;
            }
            trackPDTEvent({
              eventName: eventNameSchema.CHAT_INTERACTED,
              eventType: eventTypeSchema.ACTION,
              eventValue: eventValueSchema.ASSIST_TRIGGERED,
            });
            const { triggerDismissHandler, shouldMinimizeAfterAssist } =
              useAppStateStore.getState();
            if (
              shouldMinimizeAfterAssist &&
              typeof triggerDismissHandler === 'function'
            ) {
              triggerDismissHandler();
            }

            onAction?.({
              lob,
              actionType: ActionTypes.MessageAction,
              actionPayload: {
                unreadCount: '1',
                payload: data.assistedFlowPayload,
                assistedPayload: true,
              },
            });
          } else {
            onAction?.({
              lob,
              actionType: ActionTypes.MessageAction,
              actionPayload: {
                unreadCount: '1',
                payload: undefined,
              },
            });
          }
        }

        break;
      }
      case 'CALLBACK_SLOTS': {
        MessageLogger.debug('CALLBACK_SLOTS', event);
        const _event =
          event as WsSubSuccessResponse<WsHelpingHandCallbackSlotsResponseData>;
        if (!_event || !event?.data?.success) {
          MessageLogger.warn('failure in CALLBACK_SLOTS');
          return;
        }

        const state = get();
        const activeConversationId = state.activeConversationId;
        if (!activeConversationId) return;

        const conversation = state.conversationById[activeConversationId];
        const data = _event;
        conversation.helpingHands = {
          slotsData: data,
        };
        conversation.isLoading = false;
        state.updateConversationById(activeConversationId, {
          ...conversation,
        });
        break;
      }
      case 'SYSTEM_ACTION': {
        const _event =
          event as WsSubSuccessResponse<WsSubSystemActionIntentResponseData>;
        if (!_event.success) {
          MessageLogger.warn('failure in SYSTEM_ACTION');
          return;
        }
        if (_event?.data?.message?.content[0]?.type === 'LOGIN_ACTION') {
          const { triggerDismissHandler } = useAppStateStore.getState();

          // Specific for RN-> Close the bot and open login popup
          if (getPlatform() === 'ios') {
            triggerDismissHandler?.();
          }
          const callbackData = _event.data.message?.content[0]?.value;
          fireLoginAction({
            header: 'Login to continue',
            callbackData,
          })
            .then(() => {
              if (isLoggedIn) {
                refetchChatViewData({ body: { userLoginContext: callbackData } });
              }
              return;
            })
            .catch((error) => {
              // handle error
              // console.error('Login failed:', error);
              MessageLogger.error('Login failed:', error);
              const conversationId = _event.data.conversationId;
              const state = get();
              const conversation = state.conversationById[conversationId];
              if (conversation) {
                conversation.isLoading = false;
                state.updateConversationById(conversationId, {
                  ...conversation,
                });
              }
            });
        }
        break;
      }
      case 'LOGIN_ACK': {
        MessageLogger.debug('LOGIN_ACK', event);
        const state = useInputStateStore.getState();
        state.updateDisableInput(false);
        if (!event.success) {
          MessageLogger.warn('failure in LOGIN_ACK');
          Toast.show(config.defaultErrorMessage);
          return;
        }
        break;
      }
      case 'POST_GREETING_MESSAGE': {
        MessageLogger.debug('POST_GREETING_MESSAGE', event);
        const _event = event as WsSubSuccessResponse<WsSubNewMessageResponseData>;
        const data = _event.data;
        const message = data.message;
        if (!event?.success) {
          MessageLogger.warn('failure in POST_GREETING_MESSAGE');
          // return;
        }

        const activeConversationId = data.conversationId || 'draft';

        const content = message.content || [];
        if (content.length) {
          if (content[0].type === 'LOADER_TEXT') {
            useLoaderStore.setState({
              [activeConversationId]: content[0].value as string,
            });
            return;
          }
        }
        const state = get();
        const conversation = state.conversationById[activeConversationId];
        // let loadingFlag = conversation?.isLoading;
        // if (message.role !== 'USER') {
        //   loadingFlag = !message.isCompleted;
        // }

        conversation.messages = (conversation.messages || []).concat(message);
        conversation.awaitedUserMessage = null;

        conversation.title = data.chatTitle || conversation.title;
        state.updateConversationById(activeConversationId, {
          ...conversation,
        });

        break;
      }
      case 'LOGGING': {
        MessageLogger.debug('LOGGING', event);
        const _event = event as WsSubSuccessResponse<WsLoggingResponseData>;
        if (_event.success && _event.data?.loggingEvent) {
          const eventValue = _event.data.loggingEvent;
          if (eventValue === 'myra_agent_assigned') {
            Analytics.trackClickEvent(TrackingEvent.payload_ChatWithAgentAssigned());
          } else {
            Analytics.trackClickEvent(TrackingEvent.payload_Generic(eventValue));
          }
        }

        break;
      }

      case 'SCHEDULE_CALLBACK': {
        MessageLogger.debug('SCHEDULE_CALLBACK', event);
        const _event = event as WsSubSuccessResponse<WsLoggingResponseData>;
        if (_event?.success) {
          Analytics.trackClickEvent(
            TrackingEvent.payload_ScheduleCallbackSlotConfirmed(),
          );
        }

        break;
      }
      case 'UI_ELEMENT': {
        MessageLogger.debug('UI_ELEMENT', event);
        const _event = event as WsSubSuccessResponse<WsSubUIElementResponseData>;
        if (!_event?.success) {
          return;
        }
        const { conversationId, enableElements, elementData } = _event.data;

        const state = get();
        const conversation = state.conversationById?.[conversationId];
        if (!conversation) {
          return;
        }

        enableElements.forEach((element) => {
          switch (element) {
            case 'NEW_CHAT_CTA': {
              conversation.isNewChatInputCtaEnabled = true;
              conversation.writeAllowed = false;
              state.updateConversationById(conversationId, {
                ...conversation,
              });

              break;
            }
            case 'HIDE_INPUT': {
              conversation.hideInput = true;
              state.updateConversationById(conversationId, {
                ...conversation,
              });
              break;
            }
            case 'SHOW_INPUT': {
              conversation.hideInput = false;
              state.updateConversationById(conversationId, {
                ...conversation,
              });
              break;
            }
            case 'HIDE_T2A_CTA': {
              conversation.hideT2aCta = true;
              state.updateConversationById(conversationId, {
                ...conversation,
              });
              break;
            }
            case 'SHOW_T2A_CTA': {
              conversation.hideT2aCta = false;
              state.updateConversationById(conversationId, {
                ...conversation,
              });
              break;
            }
            case 'MODE_CTA': {
              const modeCtaArray = elementData?.MODE_CTA ?? [];
              const modesInAppState = useAppStateStore.getState().modes;
              const newModes = modesInAppState?.map((mode) => {
                const itemToUpdate = modeCtaArray.find(
                  (item) => item.id === mode.id,
                );
                return itemToUpdate
                  ? {
                      ...mode,
                      visible: itemToUpdate.visible ?? mode.visible,
                      enabled: itemToUpdate.enabled ?? mode.enabled,
                    }
                  : mode;
              });
              useAppStateStore.setState({
                enabledMode: null,
                modes: newModes,
              });
              const updatedConversationModes = conversation.mode?.map((mode) => {
                const itemToUpdate = modeCtaArray.find(
                  (item) => item.id === mode.id,
                );
                return itemToUpdate
                  ? {
                      ...mode,
                      visible: itemToUpdate.visible ?? mode.visible,
                      enabled: itemToUpdate.enabled ?? mode.enabled,
                    }
                  : mode;
              });
              state.updateConversationById(conversationId, {
                ...conversation,
                mode: updatedConversationModes,
              });
              break;
            }
            default:
              break;
          }
        });

        break;
      }
      default: {
        MessageLogger.error('UNHANDLED_EVENT', JSON.stringify(event, null, 2));
        break;
      }
    }
  },
  bookmarkMessage: async (
    msg: Message,
    convId: string,
    pageSource: BookmarkPageSource,
  ) => {
    triggerSuccessHaptic();
    const originalMessage = msg;
    // Server update
    const type = originalMessage.bookmarkFlag ? 'delete' : 'create';
    const saveBookmarkResponse = await api_addBookmark({
      messageId: originalMessage.id,
      bookmarkId: originalMessage.bookmarkId,
      conversationId: convId as string,
      type: type,
      data: msg.bookmarkPayload,
      bookmarkSource: pageSource,
    });

    // Error Case
    if (!(saveBookmarkResponse && saveBookmarkResponse.success)) {
      return;
    }
    showBookmarkSuccessToast(saveBookmarkResponse, type);

    invalidateConversationMessagesQuery(convId);
    // Client State Update
    const { conversationById, updateConversationById } = get();
    const conv = conversationById[convId];
    if (!conv) {
      return;
    }

    const messageBookmarkMap: Record<string, string> =
      saveBookmarkResponse.bookmarks?.reduce(
        (acc, item) => ({
          ...acc,
          [item.messageId]: item.bookmarkId,
        }),
        {} as Record<string, string>,
      ) || {};

    const updatedMessages = conv.messages?.map((currMsg: Message) => {
      if (currMsg === msg) {
        return {
          ...currMsg,
          bookmarkFlag: !currMsg.bookmarkFlag,
          bookmarkedAt: Date.now(),
          bookmarkId: messageBookmarkMap[msg.id],
        };
      }
      return currMsg;
    });

    updateConversationById(convId, {
      messages: updatedMessages,
    });
    invalidateConversationMessagesQuery(convId);
  },

  bookmarkCard: async (
    msg: Message,
    convId: string,
    cardId: string,
    pageSource: BookmarkPageSource,
  ) => {
    const cardWidget = msg.content.filter((item) => isCardWidgetType(item)) as
      | CardWidget
      | undefined;
    if (cardWidget.length < 1) {
      return;
    }
    const card = cardWidget
      .flatMap((cardObj) => cardObj.value.templateInfo.payload)
      .find((cardItem) => cardItem.id === cardId);
    if (!card) {
      return;
    }

    triggerSuccessHaptic();

    const { bookmarkFlag: currentBookmarkStatus, bookmarkId } = card;
    const type = currentBookmarkStatus ? 'delete' : 'create';
    const saveBookmarkResponse = await api_addBookmark({
      messageId: msg.id,
      bookmarkId,
      conversationId: convId as string,
      type,
      cardId,
      data: {
        payload: [card],
      },
      bookmarkSource: pageSource,
    });

    // Error Case
    if (!(saveBookmarkResponse && saveBookmarkResponse.success)) {
      throw new Error('Failed to bookmark card');
    }
    showBookmarkSuccessToast(saveBookmarkResponse, type);
    // Success
    const { conversationById, updateConversationById } = get();
    const conversation = conversationById[convId];
    if (!conversation) {
      return;
    }

    const updatedMessages = conversation.messages.map((currMsg: Message) => {
      if (currMsg.id !== msg.id) {
        return currMsg;
      }
      const newContent = currMsg.content.map((item) => {
        if (!isCardWidgetType(item)) {
          return item;
        }

        const cardArr = item.value.templateInfo.payload;
        const newCardsArr = cardArr.map((cardItem) => {
          if (cardItem.id !== cardId) {
            return cardItem;
          }
          return {
            ...cardItem,
            bookmarkId: saveBookmarkResponse.bookmarks[0]?.bookmarkId,
            bookmarkFlag: !cardItem.bookmarkFlag,
          };
        });
        return {
          ...item,
          value: {
            ...item.value,
            templateInfo: {
              ...item.value.templateInfo,
              payload: newCardsArr,
            },
          },
        };
      });
      return {
        ...currMsg,
        content: newContent,
      };
    });
    updateConversationById(convId, {
      messages: updatedMessages,
    });
    await invalidateBookmarksQuery(convId);
  },
  removeBookmarks: async (
    requests: Array<Omit<RemoveBookmarkRequest, 'conversationId'>>,
    convId: string,
    pageSource: BookmarkPageSource,
  ) => {
    for await (const request of requests) {
      await removeBookmarks({
        ...request,
        type: 'delete',
        conversationId: convId as string,
        bookmarkSource: pageSource,
      });
    }
    await invalidateBookmarksQuery(convId);
    get().updateConversationById(convId, {
      shouldFetch: true,
    });
  },
  resetBookmarkReadCount: () => {
    const { updateConversationById, activeConversationId } = get();
    updateConversationById(activeConversationId as string, {
      unreadBookmarks: 0,
    });
  },
}));

export function useBookmarkCard(options: CardItem & { msg: Message }, lob: string) {
  const { bookmarkFlag, id: cardId, msg } = options;
  const [isBookmarked, setBookmarked] = useState(Boolean(bookmarkFlag));
  const currentView = useAppStateStore((state) => state.currentView);
  const isBookmarkAllowed = canWriteToConversation();
  const pageSource = currentView === 'bookmarks' ? 'BOOKMARK_PAGE' : 'CHAT_PAGE';
  useEffect(() => {
    setBookmarked(Boolean(options.bookmarkFlag));
  }, [options]);

  const isBookmarkActionInProgress = useRef(false);
  const onBookmark = useCallback(async () => {
    if (isBookmarkActionInProgress.current) {
      return;
    }
    if (!isBookmarkAllowed) {
      return;
    }
    isBookmarkActionInProgress.current = true;
    setBookmarked((val) => !val);
    const type: BookmarkClickTypes = `bookmark_${lob}_clicked`;
    trackOmnitureClickEvent(isBookmarked ? 'BOOKMARK_REMOVED' : 'BOOKMARK_ADDED', {
      CONTENT_TYPE: type,
    });
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: isBookmarked
        ? eventValueSchema.BOOKMARK_REMOVED
        : eventValueSchema.BOOKMARK_ADDED,
    });
    const { bookmarkCard, activeConversationId } = useMessageStore.getState();
    try {
      await bookmarkCard(msg, activeConversationId as string, cardId, pageSource);
    } catch (e) {
      setBookmarked((val) => !val); // revert back
    } finally {
      isBookmarkActionInProgress.current = false;
    }
  }, [msg, cardId]);
  return { isBookmarked, onBookmark };
}

export function useBookmarkMessage(msg: Message) {
  const { bookmarkFlag, id: cardId } = msg;
  const [isBookmarked, setBookmarked] = useState(Boolean(bookmarkFlag));
  const currentView = useAppStateStore((state) => state.currentView);
  const pageSource = currentView === 'bookmarks' ? 'BOOKMARK_PAGE' : 'CHAT_PAGE';
  useEffect(() => {
    setBookmarked(Boolean(bookmarkFlag));
  }, [bookmarkFlag]);

  const isBookmarkActionInProgress = useRef(false);
  const onBookmark = useCallback(() => {
    if (isBookmarkActionInProgress.current) {
      return;
    }
    isBookmarkActionInProgress.current = true;
    setBookmarked((val) => !val);
    trackOmnitureClickEvent(isBookmarked ? 'BOOKMARK_REMOVED' : 'BOOKMARK_ADDED', {
      CONTENT_TYPE: 'bookmark_text',
    });
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: isBookmarked
        ? eventValueSchema.BOOKMARK_REMOVED
        : eventValueSchema.BOOKMARK_ADDED,
    });
    setTimeout(async () => {
      const { bookmarkMessage, activeConversationId } = useMessageStore.getState();
      try {
        await bookmarkMessage(msg, activeConversationId as string, pageSource);
      } catch (e) {
        setBookmarked((val) => !val); // revert back
      } finally {
        isBookmarkActionInProgress.current = false;
      }
    }, 300); // Scheduling for later to improve interactivity, using setTimeout as InteractionManager was not yielding the desired result
  }, [msg, cardId]);
  return { isBookmarked, onBookmark };
}

export function useBookmarksData(conversationId: string) {
  return useQuery({
    queryKey: ['bookmarks', conversationId],
    queryFn: async () => {
      return await getBookmarks({
        conversationId,
      });
    },
  });
}

export function invalidateBookmarksQuery(conversationId: string) {
  return queryClient.invalidateQueries(['bookmarks', conversationId]);
}

export function isCardWidgetType(
  widget: MessageWidget,
): widget is CardWidget | ItineraryCardType {
  return (
    widget.type === 'CARD' ||
    widget.type === 'INFO_CARD' ||
    widget.type === 'PACKAGE_CARD' ||
    widget.type === 'ITINERARY_PROGRESS_CARD'
  );
}
export function isItineraryStatusCardType(
  widget: MessageWidget,
): widget is ItineraryStatusCardType {
  return (
    widget?.value?.templateInfo?.payload?.[0]?.type === 'itinerary-progress-card'
  );
}

export function isTextWidgetType(widget: MessageWidget): widget is TextWidget {
  return widget.type === 'TEXT' || widget.type === 'LARGE_TEXT';
}

export function isSystemTextType(widget: MessageWidget): widget is TextWidget {
  return widget.type === 'SYSTEM_TEXT';
}

export function isHelpingHandsDetailsType(
  widget: MessageWidget,
): widget is TextWidget {
  return widget.type === 'HELPING_HANDS_DETAILS';
}

export function isInsightWidgetType(widget: MessageWidget): widget is InsightWidget {
  return widget.type === 'INSIGHTS';
}

export function isTableWidgetType(widget: MessageWidget): widget is TableWidget {
  return widget.type === 'TABLE';
}

export const useActiveConversationId = () =>
  useMessageStore((state) => state.activeConversationId || 'draft');
export type LoaderStoreType = Record<string, string | null>;
export const useLoaderStore = create<LoaderStoreType>(() => ({}));

export const useLoaderText = (): string | null => {
  const conversationId = useActiveConversationId();
  return useLoaderStore((state) => state[conversationId] || null);
};

export function getTraceId(event: WsSubscribeEvent): string | null {
  if (event.uiMetadata && 'clientTraceId' in event.uiMetadata) {
    return event.uiMetadata.clientTraceId as string;
  }
  return null;
}
