import { useQuery } from 'react-query';
import {
  fetchConfigSettings,
  GetNewChatViewResponse,
} from '../../network/api/newChatViewApi';
import { queryClient } from '../../containers/queryClientInstance';
import { useMessageStore } from './messageStore';
import { SocketClient } from '../socket';
import { setConversationMessagesData } from '../../network/hooks/use-conversation-messages-query';
import { useAppStateStore } from '../app';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import { UseQueryOptions } from 'react-query/types/react/types';
import StorageUtils from '../../utils/storageUtils';

let isSuccessCallbackCalled = false;
const CONFIG_SETTING_QUERY_KEY = 'NewChatView';
const onConfigSuccess = async (data: GetNewChatViewResponse) => {
  if (!data || isSuccessCallbackCalled) return;
  const {
    content: {
      type: contentType = '',
      value: contentData = null,
      disableChat = false,
      hideInput = false,
      hideT2aCta = false,
    } = {},
    apolloSocketConfig,
    telemetryConfig,
    introPopupConfig,
  } = data || {};

  if (introPopupConfig) {
    if (introPopupConfig?.clear) {
      StorageUtils.clearIntroPopupConfig();
    }

    const config = await StorageUtils.getIntroPopupConfig();
    if (!config && introPopupConfig.showIntroPopupOnce) {
      await StorageUtils.setIntroPopupConfig({
        showIntroPopupOnce: true,
      });
      updateChatViewData({
        introPopupConfig: {
          ...introPopupConfig,
          showPopup: true,
        },
      });
    }
  }

  const setInputBoxState = () => {
    const newState: any = {
      activeConversationId: 'draft',
      newChatRequested: true,
    };
    // Always update the conversationById state with the incoming values
    newState.conversationById = {
      ...useMessageStore.getState().conversationById,
      draft: {
        ...useMessageStore.getState().conversationById.draft,
        isNewChatInputCtaEnabled: disableChat,
        hideInput: hideInput,
        hideT2aCta: hideT2aCta,
      },
    };

    useMessageStore.setState(newState);
  };
  switch (contentType) {
    // SESSION MESSAGE HANDLING
    // since messages list and activeConvid is managed by useMessageStore
    // updating that onevery new api call will handle the session logic
    case 'CONVERSATION_VIEW': {
      // TO render ChatHistory Screen
      trackPDTEvent({
        eventName: eventNameSchema.PAGE_ENTRY,
        eventType: eventTypeSchema.PAGE,
        eventValue: eventValueSchema.SESSION_MESSAGES,
      });
      const activeConversationIdFromContent = contentData?.data?.conversationId;
      useMessageStore.setState({
        activeConversationId: activeConversationIdFromContent,
        newChatRequested: false,
      });
      setConversationMessagesData(activeConversationIdFromContent, contentData);
      break;
    }
    case 'RECENT_CHATS':
    case 'PROMPTS_WITHOUT_IMAGES': {
      // set the newchat screen only when there is no ingress/greeting pushing
      // TO render NewChat Screen with recent chats or Prompts

      trackPDTEvent({
        eventName: eventNameSchema.PAGE_ENTRY,
        eventType: eventTypeSchema.PAGE,
        eventValue:
          contentType === 'PROMPTS_WITHOUT_IMAGES'
            ? 'prompts_without_images'
            : 'recent_chats',
      });
      setInputBoxState();
      break;
    }
    case 'PROMPTS_WITH_IMAGES':
      // To render Welcome Screen
      // do nothing since by default screenState will be Welcome

      trackPDTEvent({
        eventName: eventNameSchema.PAGE_ENTRY,
        eventType: eventTypeSchema.PAGE,
        eventValue: eventValueSchema.PROMPTS_WITH_IMAGES,
      });

      useMessageStore.setState({
        activeConversationId: 'draft',
        newChatRequested: false,
      });
      setInputBoxState();
      break;
    case 'CHAT_HISTORY':
      useAppStateStore.setState({
        currentView: 'history',
      });
      break;
    case 'HELPING_HAND_CARDS':
      setInputBoxState();
      useAppStateStore.setState({
        currentView: 'chat',
      });

      break;
    default:
      break;
  }

  // Update apolloSocketConfig
  if (apolloSocketConfig) {
    const newConfig = {
      ...(apolloSocketConfig.hbInterval && {
        pingInterval: apolloSocketConfig.hbInterval,
      }),
    };
    SocketClient.updateConfig(newConfig);
  }

  useAppStateStore.setState({
    telemetryConfig,
  });

  isSuccessCallbackCalled = true;
};

const onConfigFetching = () => {
  isSuccessCallbackCalled = false;
};

const fetchConfigQueryOptions = () => {
  const appStore = useAppStateStore.getState();
  const chatContext = appStore.chatContext;
  const { lob, page_name } = chatContext?.contextMetadata?.pageContext || {};
  const expertMetadata = chatContext?.expertMetadata || {};
  const forceFetchId = appStore?.forceFetchId || null;

  return {
    queryKey: [
      CONFIG_SETTING_QUERY_KEY,
      {
        lob,
        page_name,
        expertMetadata,
        forceFetchId,
      },
    ],
    queryFn: () => {
      onConfigFetching();
      return fetchConfigSettings();
    },
    // we want to minimize the loader time, so we cache the data forever but refetch every 2 minutes
    cacheTime: Infinity,
    staleTime: 1000 * 60 * 15, // 15 minutes
    onSuccess: onConfigSuccess,
    retry: (failureCount: number, error: unknown) => {
      if (error instanceof Error && error.message === 'Forbidden') {
        return false;
      }
      return failureCount < 3;
    },
  } satisfies UseQueryOptions<GetNewChatViewResponse>;
};
export const useConfigSettings = () => {
  const queryData = useQuery<GetNewChatViewResponse>(fetchConfigQueryOptions());
  return queryData;
};

export const invalidateChatViewData = () => {
  queryClient.clear();
};

export const invalidateChatViewDataQuery = () => {
  queryClient.invalidateQueries(fetchConfigQueryOptions().queryKey);
};

export const prefetchChatViewData = () => {
  queryClient.prefetchQuery(fetchConfigQueryOptions().queryKey, {
    queryFn: fetchConfigQueryOptions().queryFn,
  });
};

export const refetchChatViewData = (
  options?: { body: Record<string, unknown> },
  shouldInvalidate = true,
) => {
  if (shouldInvalidate) {
    invalidateChatViewData();
  }
  return (async () => {
    try {
      onConfigFetching();
      const data = await fetchConfigSettings(options);
      onConfigSuccess(data);
      queryClient.setQueryData(fetchConfigQueryOptions().queryKey, data);
    } catch (error) {}
  })();
};

export const updateChatViewData = (data: Partial<GetNewChatViewResponse>) => {
  const currentData = queryClient.getQueryData(fetchConfigQueryOptions().queryKey);
  if (!currentData) return;
  queryClient.setQueryData(fetchConfigQueryOptions().queryKey, {
    ...currentData,
    ...data,
  });
};
