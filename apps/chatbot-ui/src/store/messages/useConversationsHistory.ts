import { useConversationsHistoryQuery } from '../../network/hooks';
import { useAppStateStore } from '../app';
import { useCallback, useMemo } from 'react';
import { useMessageStore } from './messageStore';
import StorageUtils from '../../utils/storageUtils';

export function useConversationsHistory() {
  const { data, isLoading, isError, refetch, error } =
    useConversationsHistoryQuery();
  const { setCurrentView } = useAppStateStore();
  const conversationGroup = useMemo<ConversationHistoryGroup[]>(() => {
    if (
      !data?.success ||
      !data.conversationGroup ||
      !Array.isArray(data.conversationGroup)
    ) {
      return [];
    }

    let index = 1;
    return (data.conversationGroup as ConversationHistoryGroup[]).map((group) => ({
      title: group.title,
      contextMenuDataMap: group.contextMenuDataMap,
      conversations: group.conversations.map((conversation) => ({
        ...conversation,
        title: conversation.title || `New Chat ${index++}`,
      })),
    }));

    // return data.conversationGroup;
  }, [data]);

  const popupConfigs = useMemo(() => {
    if (!data?.success) {
      return null;
    }
    return data.popupConfigs;
  }, [data]);

  const agentConversationGroup = useMemo<ConversationHistoryGroup[]>(() => {
    if (
      !data?.success ||
      !data.agentConversationGroup ||
      !Array.isArray(data.agentConversationGroup)
    ) {
      return [];
    }

    let index = 1;
    return (data.agentConversationGroup as ConversationHistoryGroup[]).map(
      (group) => ({
        title: group.title,
        contextMenuDataMap: group.contextMenuDataMap,
        conversations: group.conversations.map((conversation) => ({
          ...conversation,
          title: conversation.title || `New Chat ${index++}`,
        })),
      }),
    );

    // return data.agentConversationGroup;
  }, [data]);

  const showNewChatCta = useMemo(
    () => (data?.success ? data?.showNewChatCta : false),
    [data],
  );

  const conversationGroupsTitle = useMemo(
    () => (data?.success ? data?.conversationGroupsTitle : ''),
    [data],
  );
  const agentConversationGroupsTitle = useMemo(
    () => (data?.success ? data?.agentConversationGroupsTitle : ''),
    [data],
  );

  const pageTitle = useMemo(() => {
    if (data?.success) {
      return data?.chatHistoryTitle || 'Chat History';
    }
    return 'Chat History';
  }, [data]);

  const chatHeaderConfig = useMemo(() => {
    if (data?.success) {
      return data?.chatHeader;
    }
    return null;
  }, [data]);

  return {
    pageTitle,
    conversationGroup,
    agentConversationGroup,
    showNewChatCta,
    conversationGroupsTitle,
    agentConversationGroupsTitle,
    isLoading,
    isError,
    error,
    refetch,
    chatHeaderConfig,
    popupConfigs,
  };
}

export function useHandleConversationClick() {
  const { setCurrentView } = useAppStateStore();
  return useCallback(
    (conversationId: string, chatTitle: string) => {
      const state = useMessageStore.getState();
      if (state.activeConversationId !== conversationId) {
        StorageUtils.setLastConversationId(conversationId);
        useMessageStore.setState({
          activeConversationId: conversationId,
          newChatRequested: false,
        });
        const conv =
          state.conversationById[conversationId] ||
          ({
            title: chatTitle,
          } as Conversation);
        state.updateConversationById(conversationId, {
          ...conv,
          isNewChat: conversationId === 'draft',
          shouldFetch: true,
        });
      }
      setCurrentView?.('chat');
    },
    [setCurrentView],
  );
}
