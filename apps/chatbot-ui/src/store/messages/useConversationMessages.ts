import StorageUtils from '../../utils/storageUtils';
import { useConversationMessagesQuery } from '../../network/hooks';
import { useMessageStore } from './messageStore';
import { GetConversationMessagesResponse } from '../../network/api/conversationApi';
import { useConversationMessagesMutation } from '../../network/hooks/use-conversation-messages-query';

export const conversationMessagesSuccessCallback = (
  response: GetConversationMessagesResponse,
  conversationId: string,
) => {
  if (
    !response?.success ||
    !response.data?.messages ||
    !Array.isArray(response.data.messages)
  ) {
    useMessageStore
      .getState()
      .updateConversationById(conversationId, { shouldFetch: false, messages: [] });
    return;
  }
  const messages = response.data.messages;
  let userSuggestion: Conversation['suggestions'] | null = null;
  if (messages.length > 0) {
    const { leadingQuestion, suggestions } = messages[messages.length - 1];
    userSuggestion = {
      leadingQuestion: leadingQuestion || null,
      items: suggestions || null,
    };
  }
  useMessageStore.getState().updateConversationById(conversationId, {
    shouldFetch: false,
    suggestions: userSuggestion,
    writeAllowed: response.writeAllowed,
    isNewChatInputCtaEnabled: !response.writeAllowed,
    hideInput: response.hideInput,
    hideT2aCta: response.hideT2aCta,
    mode: response.mode,
    title: response.title || '',
    sessionContext: response.sessionContext,
    writeAllowedDebug: response.writeAllowedDebug,
    messages: messages.map(
      ({ message, leadingQuestion, lq_insights, audioTalkBack }) => {
        return {
          ...message,
          audioTalkBack,
          leadingQuestion,
          lq_insights: lq_insights || undefined,
          sequence: lq_insights?.sequence,
        };
      },
    ),
    chatHeader: response.chatHeader,
  });
  StorageUtils.setLastMessageReceivedTime(Date.now().toString());
};

export function useConversationMessages(shouldFetch = true, conversationId: string) {
  const { isLoading, isError, error, refetch, data } = useConversationMessagesQuery({
    conversationId,
    enabled: shouldFetch && conversationId !== 'draft',
    onSuccess: (response) =>
      conversationMessagesSuccessCallback(response, conversationId),
  });
  return {
    isLoading,
    isError,
    error,
    refetch,
    data,
  };
}

export const useConversationMessagesWithMutation = () => {
  const { mutate, isLoading, isError, error } = useConversationMessagesMutation({
    onSuccess: (response) =>
      conversationMessagesSuccessCallback(
        response,
        (response?.success && response.data.conversationId) || '',
      ),
  });

  return {
    mutate,
    isLoading,
    isError,
    error,
  };
};
