import { useCallback } from 'react';
import { useInputStateStore, useLoaderStore, useMessageStore } from './messageStore';
import { Logger } from '../../utils/logger';
import { trackMsg } from '../../native/omniture';
import { SocketClient } from '../socket';
import { useAppStateStore } from '../app';
import { WsPublishEvent } from '../socket/socketType';

import { startNewMessageTrace } from '../../analytics/telemetry';
import { openDeepLink } from '../../native/deepLinks';
import { Toast } from '../../components/toast';
import { triggerSuccessHaptic } from '../../utils/hapticUtils';
import { stopTtsIfPlaying } from '../audio-talkback/talkbackStore';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import { Analytics } from '../../analytics/analytics';
import { TrackingEvent } from '../../analytics/events';

type SendMessageOverrideProps = {
  dataProps?: WsPublishEvent['data'];
  //FIXME: eventType should be of type WsPublishEvent['eventType']
  eventType?: any;
  uiMetadataProps?: WsPublishEvent['uiMetadata'];
  completeMessage?: Omit<Message, 'id' | 'createdAt'>;
  botMetadata?: Record<string, unknown>;
};

const MessageLogger = Logger.createLogger({ tag: 'MessageAction' });
export function useMessageAction() {
  const canSend = useCallback(() => SocketClient.isActive(), []);
  const getModesToSend = useCallback(
    (conversation: Conversation, maybeActiveConvId: string) => {
      const { modes, enabledMode } = useAppStateStore.getState();
      if (maybeActiveConvId === 'draft') {
        return enabledMode ? modes?.filter((mode) => mode.id === enabledMode) : null;
      } else {
        return conversation?.mode?.[0]?.enabled ? conversation?.mode : null;
      }
    },
    [],
  );
  const send = useCallback(
    async (
      maybeActiveConvId: string,
      _inputText: string,
      leadingQuestion: string | null = null,
      overRideDataProps: SendMessageOverrideProps = {},
    ) => {
      const inputText = _inputText?.trim();
      // If the message is empty and there is no completeMessage, then return
      // completeMessage used for ingress push from Backend
      if (!overRideDataProps?.completeMessage && !inputText) {
        return;
      }

      triggerSuccessHaptic();
      stopTtsIfPlaying('new_message_sent');

      const tmpConvId = maybeActiveConvId === 'draft' ? `${Date.now()}` : null;
      useMessageStore.setState({ newChatRequested: false, tmpConvId });
      const { updateConversationById, setNewChatRequested, newChatRequested } =
        useMessageStore.getState();

      // get parentAppContext from app state store-> sent from parent app
      const { parentContextProvider } = useAppStateStore.getState();

      // customData is an object that can be used to override data to the server
      const {
        dataProps = {},
        eventType: eventTypeFromProp,
        uiMetadataProps,
        completeMessage,
        botMetadata: _botMetadata,
      } = overRideDataProps;

      //context from conversation session mode
      const conversation =
        useMessageStore.getState().conversationById[maybeActiveConvId];
      const modesToSend = getModesToSend(conversation, maybeActiveConvId);

      // session context to be sent to the backend
      const sessionContextType = conversation?.sessionContext?.type;
      const sessionContextToCB = conversation?.sessionContext?.conversationMetadata;
      const botMetadata = {
        ..._botMetadata,
        ...(modesToSend && {
          mode: modesToSend,
        }),
      };
      // if complete message is there send it as it is
      const message: Message = completeMessage
        ? { ...completeMessage, id: `${Date.now()}`, createdAt: Date.now() }
        : {
            isDraft: true,
            lang: 'en-IN',
            id: `${Date.now()}`,
            createdAt: Date.now(),
            content: [
              {
                type: 'TEXT',
                value: inputText,
                quotedContentInfo:
                  sessionContextType === 'REPLY' ? sessionContextToCB : undefined,
              },
            ],
            role: 'USER',
          };

      useLoaderStore.setState({
        [maybeActiveConvId]: null,
      });
      updateConversationById(maybeActiveConvId, {
        ...(conversation || {}),
        suggestions: null,
        helpingHands: null,
        id: maybeActiveConvId,
        isNewChat:
          typeof conversation?.isNewChat === 'boolean'
            ? conversation.isNewChat
            : maybeActiveConvId === 'draft',
        contextId: 'NA',
        messages: [...(conversation?.messages || [])],
        awaitedUserMessage: [message],
        // dont show loader if the agentMeta is already present -> agent mode
        // and if the pushed message is greeting message
        isLoading:
          !conversation?.agentMeta && eventTypeFromProp !== 'POST_GREETING_MESSAGE',
        lastMessage: conversation?.lastMessage || { content: [], createdAt: 0 },
        // FIXME, remove these hardcoded values
        org: 'MMT',
        bookingLob: 'CABS',
        context: { chatType: 'BOT' },

        title: conversation?.title || '',
        hasUnRead: conversation?.hasUnRead || false,
        updatedAt: Date.now(),
        sessionContext:
          sessionContextType === 'REPLY'
            ? ({
                ...conversation?.sessionContext,
                type: 'CONTEXT',
              } as SessionContext)
            : conversation?.sessionContext,
      });
      const clientTraceId = startNewMessageTrace();
      type PublishPayload = Parameters<typeof SocketClient.publish>[0];
      let publishPayload: PublishPayload;
      if (maybeActiveConvId === 'draft') {
        publishPayload = {
          eventType: eventTypeFromProp ?? 'NEW_CHAT',
          data: {
            message: message,
            ...dataProps,
          },
          uiMetadata: {
            tempId: tmpConvId as string,
            clientTraceId,
            ...uiMetadataProps,
          },
          lobAppContext: parentContextProvider,
          botMetadata,
          quotedContentInfo: sessionContextToCB,
        };

        trackPDTEvent({
          eventName: eventNameSchema.CHAT_INTERACTED,
          eventType: eventTypeSchema.ACTION,
          eventValue: eventValueSchema.INPUT_BOX,
        });
      } else {
        publishPayload = {
          eventType: eventTypeFromProp ?? 'POST_MESSAGE',
          chatType: 'BOT',
          data: {
            conversationId: maybeActiveConvId,
            leadingQuestion,
            message: message,
            ...dataProps,
          },
          uiMetadata: {
            tempId: `${Date.now()}`,
            clientTraceId,
            ...uiMetadataProps,
          },
          lobAppContext: parentContextProvider,
          botMetadata,
          quotedContentInfo: sessionContextToCB,
        };
      }
      // to make sure the new chat is resetted
      if (newChatRequested) setNewChatRequested(false);

      // Let's not call .publish(payload) if socket is not open
      try {
        await SocketClient.waitForOpenWithTimeout();
      } catch (e) {
        Toast.show('Failed to send the message', null, 'short');

        // reset loader and enable manual send retry
        const conversationPrevVal =
          useMessageStore.getState().conversationById[maybeActiveConvId];
        updateConversationById(maybeActiveConvId, {
          ...conversationPrevVal,
          isLoading: false,
          awaitedUserMessage: (conversationPrevVal.awaitedUserMessage || []).map(
            (value) => ({
              ...value,
              isSendFailed: true,
              retrySend: () => {
                send(
                  maybeActiveConvId,
                  _inputText,
                  leadingQuestion,
                  overRideDataProps,
                );
              },
            }),
          ),
        });
        return;
      }
      Analytics.trackClickEvent(TrackingEvent.payload_sendMessageAllModes());
      // Socket is open, can call .publish(payload)
      SocketClient.publish(publishPayload);
    },
    [],
  );

  const addMessageSource = useCallback(
    (messageSource: string, overRideDataProps?: SendMessageOverrideProps) => {
      return {
        ...overRideDataProps,
        dataProps: {
          messageSource: messageSource,
          ...(overRideDataProps?.dataProps || {}),
        },
      };
    },
    [],
  );

  const selectSuggestion = useCallback(
    (
      selectedSuggestion: string,
      allSuggestions: NonNullable<Conversation['suggestions']>,
    ) => {
      if (!canSend()) {
        return;
      }
      try {
        const { activeConversationId, conversationById } =
          useMessageStore.getState();
        const isNewChat =
          conversationById[activeConversationId || 'draft']?.isNewChat;

        trackMsg(
          {
            VAR_MSG_ROLE: 'msg_src_user',
            VAR_CHAT_TYPE: isNewChat ? 'chat_new' : 'chat_old',
            CHAT_SRC: 'prompt_src_sugg',
            VAR_CHAT_SRC: 'prompt_src_sugg',
          },
          'SELECT_SUGGESTION',
        );
        const { leadingQuestion } = allSuggestions;

        const overRideProps = addMessageSource('SUGGESTION');

        send(
          activeConversationId || 'draft',
          selectedSuggestion,
          leadingQuestion,
          overRideProps,
        );
      } catch (e) {
        MessageLogger.error('error in selectSuggestion', e);
      }
    },
    [],
  );
  const sendMessage = useCallback(
    (textFromProp?: string, overRideDataProps?: SendMessageOverrideProps) => {
      const { inputStateByConversationId, updateInputStateByConversationId } =
        useInputStateStore.getState();
      const { activeConversationId, conversationById } = useMessageStore.getState();
      const maybeActiveConvId = activeConversationId || 'draft';
      const inputText =
        textFromProp ?? inputStateByConversationId[maybeActiveConvId];
      MessageLogger.verbose('inputText', inputText);

      updateInputStateByConversationId(maybeActiveConvId, '');
      // FIXME this is an hack for iOS, need to remove this
      setTimeout(() => {
        updateInputStateByConversationId(maybeActiveConvId, '');
      }, 300);

      const isNewChat = conversationById[maybeActiveConvId]?.isNewChat;
      trackMsg(
        {
          VAR_MSG_ROLE: 'msg_src_user',
          VAR_CHAT_TYPE: isNewChat ? 'chat_new' : 'chat_old',
          CHAT_SRC: 'prompt_src_text',
          VAR_CHAT_SRC: 'prompt_src_text',
        },
        'SEND_MESSAGE',
      );

      const overRideProps = addMessageSource('TEXT', overRideDataProps);

      send(maybeActiveConvId, inputText, null, overRideProps);
    },
    [addMessageSource, send],
  );
  const selectSuggestionCTA = useCallback((cta: Ctas) => {
    if (!cta) {
      return;
    }
    const { type, value } = cta;
    if (type === 'LINK' && value) {
      openDeepLink(value);
    }
  }, []);

  const selectInitialPrompt = useCallback((prompt: string) => {
    if (!canSend()) {
      return;
    }
    trackMsg(
      {
        CHAT_SRC: 'prompt_src_sugg',
        VAR_CHAT_SRC: 'prompt_src_sugg',
        VAR_CHAT_TYPE: 'chat_new',
        VAR_MSG_ROLE: 'msg_src_user',
      },
      'SELECT_INITIAL_PROMPT',
    );
    const { updateInputStateByConversationId } = useInputStateStore.getState();
    const { activeConversationId } = useMessageStore.getState();
    const maybeActiveConvId = activeConversationId || 'draft';
    MessageLogger.verbose('prompt', prompt);

    updateInputStateByConversationId(maybeActiveConvId, '');
    const overRideProps = addMessageSource('PROMPT');
    send(maybeActiveConvId, prompt, null, overRideProps);
  }, []);

  const publishEventOnly = useCallback(
    (
      eventType: any,
      maybeActiveConvId: string,
      overRideDataProps: SendMessageOverrideProps = {},
    ) => {
      if (!canSend()) {
        return;
      }

      // get parentAppContext from app state store-> sent from parent app
      const { parentContextProvider } = useAppStateStore.getState();

      const {
        dataProps = {},
        uiMetadataProps,
        botMetadata: _botMetadata,
      } = overRideDataProps;

      //context from conversation session mode
      const conversation =
        useMessageStore.getState().conversationById[maybeActiveConvId];
      const modesToSend = getModesToSend(conversation, maybeActiveConvId);

      // session context to be sent to the backend
      const sessionContextToCB = conversation?.sessionContext?.conversationMetadata;
      const botMetadata = {
        ..._botMetadata,
        ...(modesToSend && {
          mode: modesToSend,
        }),
      };

      const clientTraceId = startNewMessageTrace();

      SocketClient.publish({
        eventType,
        chatType: 'BOT',
        data: {
          ...(maybeActiveConvId !== 'draft' && {
            conversationId: maybeActiveConvId,
          }),
          ...dataProps,
        },
        uiMetadata: {
          tempId: `${Date.now()}`,
          clientTraceId,
          ...uiMetadataProps,
        },
        lobAppContext: parentContextProvider,
        botMetadata,
        quotedContentInfo: sessionContextToCB,
      });
    },
    [canSend, getModesToSend],
  );

  return {
    sendMessage,
    selectSuggestion,
    selectInitialPrompt,
    selectSuggestionCTA,
    publishEventOnly,
  };
}
