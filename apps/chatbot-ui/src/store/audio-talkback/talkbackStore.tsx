// Types
import { create } from 'zustand';
import { TtsConfig } from '../../native/audio/tts-types';
import { Endpoints } from '../../network/endpoints';
import { getAudioStreamModule } from '../../native/audio/audio-native-module';
import { AppState, DeviceEventEmitter, EventSubscription } from 'react-native';
import { useEffect } from 'react';
import { getHeadersAndContext } from '../../network/headers';
import { sleep } from '../../utils/functionUtils';
import { Logger } from '../../utils/logger';
import { AppStateStatus } from 'react-native/Libraries/AppState/AppState';
import {
  trackPDTEvent,
  eventNameSchema,
  eventTypeSchema,
  eventValueSchema,
} from '../../native/tracking/pdt';
import { useAppStateStore } from '../app';

export type TtsState = 'playing' | 'paused' | 'stopped';
type QueuedPlaybackItem = {
  text: string;
  delay: number;
  locale: string | null;
};

interface TalkBackState {
  // State
  isInitRunning: boolean;
  isPlaying: boolean;
  currentTaskId: string | null;
  progress: number;
  state: TtsState;
  error: string | null;
  // Actions
  speak: (
    text: string,
    locale: string | null,
    clearQueue?: boolean,
  ) => Promise<void>;
  enqueue: (text: string, locale: string | null, delay: number) => Promise<void>;
  pause: () => Promise<void>;
  resume: () => Promise<void>;
  stop: () => Promise<void>;

  /* Audio talkback data node is sent in pull(or fetch) mode, but not is push mode(via WS)
   * So, we're going to use this state to hold it for push mode
   * */
  messagesAudioData: Record<string, AudioTalkbackData | null>;
  setAudioData: (messageId: string, data: AudioTalkbackData) => void;
}

let queue: QueuedPlaybackItem[] = [];

export function useAudioEventListeners() {
  useEffect(() => {
    const audioStreamModule = getAudioStreamModule();
    let progressSubscription: EventSubscription | null = null;
    let stateChangeSubscription: EventSubscription | null = null;
    if (audioStreamModule) {
      logger.info('Setting up audio event listeners');
      progressSubscription = DeviceEventEmitter.addListener(
        audioStreamModule.EVENT_TTS_PROGRESS,
        ({ taskId, progress }) => {
          logger.debug(
            `Progress event - taskId: ${taskId}, progress: ${progress}, queue length: ${queue.length}`,
          );
          if (useTalkBackStore.getState().currentTaskId === taskId) {
            useTalkBackStore.setState({ progress });
          }
        },
      );
      stateChangeSubscription = DeviceEventEmitter.addListener(
        audioStreamModule.EVENT_TTS_STATE,
        ({ taskId, state: ttsState }) => {
          logger.info(`State change event - taskId: ${taskId}, state: ${ttsState}`);
          const state = useTalkBackStore.getState();
          if (taskId === state.currentTaskId) {
            logger.debug(
              `Updating state for taskId: ${taskId}, new state: ${ttsState}`,
            );
            useTalkBackStore.setState({
              currentTaskId: ttsState === 'stopped' ? null : state.currentTaskId,
              state: ttsState as TtsState,
              isPlaying: ttsState === 'playing',
            });

            if (ttsState === 'stopped' && queue.length) {
              const nextItem = queue[0];
              logger.info(
                `Queue has items, scheduling next item with delay: ${nextItem.delay}ms`,
              );
              setTimeout(() => {
                if (useTalkBackStore.getState().currentTaskId == null) {
                  const head = queue.shift();
                  if (head) {
                    logger.info(`Playing queued item: ${head.text}`);
                    useTalkBackStore.getState().speak(head.text, head.locale, false);
                  }
                }
              }, nextItem.delay);
            }
          }
        },
      );

      const appStateSubscription = AppState.addEventListener(
        'change',
        (state: AppStateStatus) => {
          if (state !== 'active') {
            const talkBackState = useTalkBackStore.getState();
            talkBackState.stop();
          }
        },
      );
      return () => {
        logger.info('Cleaning up audio event listeners');
        if (stateChangeSubscription) {
          stateChangeSubscription.remove();
        }
        if (progressSubscription) {
          progressSubscription.remove();
        }
        if (appStateSubscription) {
          appStateSubscription.remove();
        }
      };
    }
  }, []);
}

let taskIdCounter = 0;

const logger = Logger.createLogger({
  tag: 'TlxTalkback',
  level: 'VERBOSE',
  enableConsoleLog: true,
});

export const useTalkBackStore = create<TalkBackState>((set, get) => ({
  // Initial state
  isPlaying: false,
  isInitRunning: false,
  currentTaskId: null,
  progress: 0,
  state: 'stopped',
  error: null,
  messagesAudioData: {},
  setAudioData: (messageId, data) => {
    set({
      messagesAudioData: {
        ...get().messagesAudioData,
        [messageId]: data,
      },
    });
  },

  // Actions
  speak: async (text: string, locale: string | null) => {
    logger.info(`speak(${text}, ${locale})`);
    if (!text || !text.length) {
      logger.warn('Speak called with empty text, ignoring');
      return;
    }
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.VOICE_TALKBACK_STARTED,
    });
    if (AppState.currentState !== 'active') {
      logger.warn('AppState is not active, ignoring');
      return;
    }
    try {
      const featureConfig = useAppStateStore.getState().featureConfig;
      const useStreaming = featureConfig?.ttsUseStreaming ?? false;
      const useTtsCache = featureConfig?.ttsUseCache ?? true;
      const ttsConnTimeoutMs = featureConfig?.ttsConnTimeoutMs ?? 10_000;
      const ttsReadTimeoutMs = featureConfig?.ttsReadTimeoutMs ?? 30_000;
      const currState = get();
      logger.info(`Current state before speak: ${JSON.stringify(currState)}`);
      if (currState.currentTaskId !== null) {
        logger.info(`Stopping current task: ${currState.currentTaskId}`);
        await currState.stop();
      }
      set({
        isInitRunning: true,
      });
      const taskId = `tts_${++taskIdCounter}_${Date.now()}`;
      logger.info(`Generated new taskId: ${taskId}`);

      const { httpHeaders } = await getHeadersAndContext();
      // Configure TTS
      const headers: Record<string, string|undefined> = httpHeaders();
      headers['X-Audio-Protocol'] = 'streaming-v1';
      const config: TtsConfig = {
        url: Endpoints.TTS,
        timeoutMs: ttsConnTimeoutMs,
        readTimeoutMs: ttsReadTimeoutMs,
        useStreaming,
        cacheEnabled: useTtsCache,
        language: locale || 'en-IN',
        modelOverride: 'gcp',
        headers,
      };
      logger.debug(`TTS config: ${JSON.stringify(config)}`);

      // Start TTS
      const audioStreamModule = getAudioStreamModule();
      logger.info(`Starting TTS for taskId: ${taskId}`);
      await audioStreamModule.speakText(text, taskId, config);

      set({
        currentTaskId: taskId,
        error: null,
        state: 'playing',
        isPlaying: true,
        isInitRunning: false,
        progress: 0,
      });
      logger.info(`TTS started successfully for taskId: ${taskId}`);
    } catch (error) {
      logger.error(
        `Error in speak: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      trackPDTEvent({
        eventName: eventNameSchema.CHAT_INTERACTED,
        eventType: eventTypeSchema.ACTION,
        eventValue: eventValueSchema.VOICE_TALKBACK_FAILED,
      });
      set({
        error: error instanceof Error ? error.message : 'Unknown error',
        state: 'stopped',
        isPlaying: false,
        isInitRunning: false,
      });
    }
  },

  enqueue: async (text: string, locale: string | null, delay: number) => {
    logger.info(`Enqueueing text: "${text}", locale: ${locale}, delay: ${delay}ms`);
    queue.push({
      delay,
      text,
      locale,
    });
    logger.debug(`Queue length after enqueue: ${queue.length}`);
  },

  pause: async () => {
    const { currentTaskId } = get();
    logger.info(`Attempting to pause taskId: ${currentTaskId}`);
    if (!currentTaskId) {
      logger.warn('No active task to pause');
      return;
    }
    try {
      const audioStreamModule = getAudioStreamModule();
      await audioStreamModule.pauseTts(currentTaskId);
      logger.info(`Successfully paused taskId: ${currentTaskId}`);
      set({ isPlaying: false, state: 'paused' });
    } catch (error) {
      logger.error(
        `Error pausing TTS: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      set({ error: error instanceof Error ? error.message : 'Unknown error' });
    }
  },

  resume: async () => {
    const { currentTaskId } = get();
    logger.info(`Attempting to resume taskId: ${currentTaskId}`);
    if (!currentTaskId) {
      logger.warn('No active task to resume');
      return;
    }
    try {
      const audioStreamModule = getAudioStreamModule();
      await audioStreamModule.resumeTts(currentTaskId);
      logger.info(`Successfully resumed taskId: ${currentTaskId}`);
      set({ isPlaying: true, state: 'playing' });
    } catch (error) {
      logger.error(
        `Error resuming TTS: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      set({ error: error instanceof Error ? error.message : 'Unknown error' });
    }
  },

  stop: async () => {
    const queueLength = queue.length;
    queue = [];
    logger.info(`Cleared queue (had ${queueLength} items)`);

    const { currentTaskId } = get();
    logger.info(`Attempting to stop taskId: ${currentTaskId}`);
    if (!currentTaskId) {
      logger.warn('No active task to stop');
      return;
    }
    try {
      const audioStreamModule = getAudioStreamModule();
      await audioStreamModule.stopTts(currentTaskId);
      logger.info(`Successfully stopped taskId: ${currentTaskId}`);
    } catch (error) {
      logger.error(
        `Error stopping TTS: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      set({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      set({
        isPlaying: false,
        state: 'stopped',
        progress: 0,
        currentTaskId: null,
      });
    }
  },
}));

export function stopTtsIfPlaying(reason = 'unknown') {
  logger.info(`stopTtsIfPlaying called - reason: ${reason}`);
  try {
    const { stop } = useTalkBackStore.getState();
    stop();
  } catch (e) {
    logger.error(
      `Error in stopTtsIfPlaying (reason: ${reason}): ${e instanceof Error ? e.message : 'Unknown error'}`,
    );
  }
}

export async function waitForTalkbackComplete({
  initTimeout,
}: {
  initTimeout: number;
}) {
  logger.info(`waitForTalkbackComplete called with initTimeout: ${initTimeout}ms`);
  let ttsState = useTalkBackStore.getState();
  const startTime = Date.now();
  // there are cases when talkback node is sent after deeplink, so lets wait for that
  await sleep(2_000);
  while (ttsState.isInitRunning) {
    if (startTime + initTimeout < Date.now()) {
      logger.warn('Init timeout reached');
      return;
    }
    await sleep(300);
    ttsState = useTalkBackStore.getState();
    logger.debug(`Waiting for init, current state: ${JSON.stringify(ttsState)}`);
  }

  ttsState = useTalkBackStore.getState();
  let progress: number | null = null;
  logger.info(`TTS playing state: ${ttsState.state}`);

  while (ttsState.isPlaying) {
    logger.debug(`Progress check - old: ${progress}, latest: ${ttsState.progress}`);
    if (ttsState.progress !== progress) {
      progress = ttsState.progress;
      await sleep(2_000);
    } else {
      logger.warn('Progress timeout reached');
      return;
    }
    ttsState = useTalkBackStore.getState();
  }
  logger.info('waitForTalkbackComplete finished');
}
