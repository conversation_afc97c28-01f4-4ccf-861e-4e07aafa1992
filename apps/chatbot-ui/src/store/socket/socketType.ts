export interface SocketState {
  socketState: string;
  reconnectAttempt: number;
  hasSocketConnected: boolean;
  shouldRetry?: boolean;
  Socket: WebSocket | null;
  callbackQueue: WsPublishEvent[];
  sentHeartBeat: boolean;
  receivedHeartBeat: boolean;
  initializeWebSocket: (cb?: SocketCallbacks) => void;
  closeWebSocket: (debugInfo: { reason: string; shouldRetry: boolean }) => void;
  reconnectWebSocket: (cb?: SocketCallbacks) => void;
  publishWebSocketMessage: (event: WsPublishEvent) => Promise<void>;
  initiateHeartBeat: () => void;
}

type CloseEvent = any;
export type SocketCallbacks = {
  onOpen?: () => void;
  onClose?: (event: any) => void;
  // TODO: onClose?: (event: CloseEvent) => void;
  onError?: (event: Event) => void;
};

type EpochTimeStamp = number;

// ######################
// ### Publish Events ###
// ######################
export type WsPublishEvent =
  | WSPubNewChat
  | WSPubPostMessage
  | WsPubGetConversations
  | WSPubLastSeen
  | WsPubGetConfigs
  | WsPubGetMessages
  | WsPubLoginStateChange
  | WsPubPostFeedback
  | WsPubHeartBeat
  | WsPubLoginStateChange
  | WsPubCallbackSlots
  | WsPubScheduleCallback;

interface WsPubEvent<T extends string, K, U = {}> {
  eventType: T;
  data: K;
  uiMetadata?: U;
  chatType?: ConversationType;
  contextId?: string;
  lobAppContext?: object | null;
}

// Event Types
export type WSPubNewChat = WsPubEvent<
  'NEW_CHAT',
  WSPubNewChatPayload,
  { tempId: string }
>;
export type WSPubPostMessage = WsPubEvent<'POST_MESSAGE', WSPubPostMessagePayload>;
type WsPubGetConversations = WsPubEvent<
  'GET_CONVERSATIONS',
  WsPubGetConversationsPayload
>;
type WSPubLastSeen = WsPubEvent<'LAST_SEEN_MESSAGE', WSPubLastSeenPayload>;
type WsPubGetConfigs = WsPubEvent<'GET_CONFIGS', WsPubGetConfigsPayload>;
type WsPubGetMessages = WsPubEvent<'GET_MESSAGES', WsPubGetMessagesPayload>;
type WsPubLoginStateChange = WsPubEvent<
  'LOGIN_STATE_CHANGE',
  WsPubLoginStateChangePayload
>;
type WsPubCallbackSlots = WsPubEvent<'CALLBACK_SLOTS', WsPubCallbackSlotsPayload>;
type WsPubScheduleCallback = WsPubEvent<
  'SCHEDULE_CALLBACK',
  WsPubScheduleCallbackPayload
>;
type WsPubPostFeedback = WsPubEvent<'POST_FEEDBACK', WsWsPubPostFeedbackPayload>;
type WsPubHeartBeat = WsPubEvent<'HEART_BEAT', WsPubHeartBeatPayload>;

// Payloads
interface WSPubNewChatPayload {
  message: SendMessage;
  greetingMessage?: GreetingMessage;
}

interface WSPubPostMessagePayload {
  conversationId: Conversation['id'] | null;
  leadingQuestion?: string | null;
  message: SendMessage;
  sourceViewType?: SourceView;
}

interface WsPubGetConversationsPayload {
  page: PaginationParameters;
}

interface WSPubLastSeenPayload {
  conversationId: Conversation['id'];
  messageId: Message['id'];
}

interface WsPubGetConfigsPayload {
  botConfKeys: BotConfKeys;
  userDetails: boolean;
}

interface WsPubGetMessagesPayload {
  conversationId: Conversation['id'] | null;
  page: PaginationParameters;
  viewType?: SourceView;
}

interface WsPubLoginStateChangePayload {
  previousConversationId: Conversation['id'] | null;
  intent: 'CONNECT_TO_AGENT' | 'SCHEDULE_CALLBACK';
  deviceId: string;
  loggedIn: boolean;
  source: null;
  lob: string | null;
}

interface WsWsPubPostFeedbackPayload {
  conversationId: Conversation['id'];
  messageId: Message['id'];
  message: {
    role: Message['role'];
    lang: Message['lang'];
    content: Array<{ type: 'FEEDBACK'; value: Feedback }>;
  };
}

interface WsPubHeartBeatPayload {}

interface WsPubCallbackSlotsPayload {}
interface WsPubScheduleCallbackPayload {}
// ######################
// ## Subscribe Events ##
// ######################
export type WsSubscribeEvent =
  | WsSubClientSendFailed
  | WsSubNewChatCreated
  | WsSubNewMessage
  | WsSubGetConversations
  | WsSubTitleUpdate
  | WsSubGetConfigs
  | WsSubGetMessages
  | WsSubNewSuggestions
  | WsSubSystemActionIntent
  | WsSubLoginAck
  | WsSubHeartBeat
  | WsSubLogging
  | WsSubAgentAssigned
  | WsSubAgentUnassigned
  | WsSubNavigateTo
  | WsSubUIElement
  | WsSubHelpingHandIntent;

type WsSubEventG<T extends string, K> = {
  eventType: T;
} & K;
type UIMetadata = {
  tempId: string;
};

type WsSubClientSendFailed = WsSubEventG<
  'CLIENT_SEND_FAILED',
  { payload: WsPublishEvent }
>;

type WsSubNewChatCreated = WsSubEventG<
  'NEW_CHAT_CREATED',
  WsSubResponse<
    WsSubNewChatCreatedResponseData,
    WsSubNewChatCreatedResponseAdditionalData
  >
>;
type WsSubNewMessage = WsSubEventG<
  'NEW_MESSAGE',
  WsSubResponse<WsSubNewMessageResponseData, WsSubNewMessageResponseAdditionalData>
>;
type WsSubGetConversations = WsSubEventG<
  'GET_CONVERSATIONS',
  WsSubResponse<
    WsSubGetConversationsResponseData,
    WsSubGetConversationsResponseAdditionalData
  >
>;
type WsSubTitleUpdate = WsSubEventG<
  'TITLE_UPDATE',
  WsSubResponse<WsSubTitleUpdateResponseData>
>;
type WsSubGetConfigs = WsSubEventG<
  'GET_CONFIGS',
  WsSubResponse<WsSubGetConfigsResponseData>
>;
type WsSubGetMessages = WsSubEventG<
  'GET_MESSAGES',
  WsSubResponse<WsSubGetMessagesResponseData>
>;
type WsSubNewSuggestions = WsSubEventG<
  'NEW_SUGGESTIONS',
  WsSubResponse<WsSubNewSuggestionsResponseData>
>;
type WsSubSystemActionIntent = WsSubEventG<
  'SYSTEM_ACTION',
  WsSubResponse<WsSubSystemActionIntentResponseData>
>;

type WsSubHelpingHandIntent = WsSubEventG<
  'HELPING_HAND_MESSAGE',
  WsSubResponse<WsHelpingHandDetailsResponseData>
>;

type WsSubLoginAck = WsSubEventG<
  'LOGIN_ACK',
  WsSubResponse<WsSubLoginAckResponseData>
>;
type WsSubHeartBeat = WsSubEventG<
  'HEART_BEAT',
  WsSubResponse<WsSubHeartBeatResponseData>
>;
type WsSubLogging = WsSubEventG<'LOGGING', WsSubResponse<WsSubLoggingResponseData>>;
type WsSubAgentAssigned = WsSubEventG<
  'CHAT_ASSIGNED',
  WsSubResponse<WsSubAgentAssignedResponse>
>;
type WsSubAgentUnassigned = WsSubEventG<
  'CHAT_UNASSIGNED',
  WsSubResponse<WsSubAgentUnassignedResponse>
>;
type WsSubNavigateTo = WsSubEventG<
  'NAVIGATE',
  WsSubResponse<WsSubNavigateToResponseData>
>;
type WsSubUIElement = WsSubEventG<
  'UI_ELEMENT',
  WsSubResponse<WsSubUIElementResponseData>
>;

export type WsSubSuccessResponse<T, K = {}> = {
  success: true;
  error: null;
  data: T;
  uiMetadata?: {
    tempId?: string;
    metadata?: object;
  };
} & K;
export type WsSubErrorResponse = {
  success: false;
  error: {
    code: number;
    message: string | null;
  };
};
export type WsSubResponse<T, K = {}> =
  | WsSubSuccessResponse<T, K>
  | WsSubErrorResponse;

export type WsSubNewChatCreatedResponseData = {
  conversationId: Conversation['id'];
  chatTitle: string;
  message: Message;
  hasUnRead: boolean;
  updatedAt: EpochTimeStamp;
};
type WsSubNewChatCreatedResponseAdditionalData = {
  uiMetadata: UIMetadata;
  conversationUpdate?: Conversation;
};

export type WsHelpingHandDetailsResponseData = HelpingHandsDetailsResponseData;

export type WsHelpingHandCallbackSlotsResponseData =
  HelpingHandsCallbackSlotsResponseData;

export type WsLoginAckResponseData = {};
export type WsLoggingResponseData = {};

export type WsSubNewMessageResponseData = {
  conversationId: Conversation['id'] | null;
  message: Message;
  chatTitle?: Conversation['title'];
  suggestions?: Suggestion[];
  leadingQuestion?: string | null;
  lobMetadata?: object;
  showOverlay?: boolean;
  overlayCtaText?: string;
  assistedFlowPayload?: any;
  assistedFlowFlag?: boolean;
  audioTalkBack?: AudioTalkbackData | null;
};
type WsSubNewMessageResponseAdditionalData = {
  conversationUpdate?: Conversation;
  uiMetadata?: UIMetadata;
};

type WsSubGetConversationsResponseData = {
  conversations: Conversation[];
};
type WsSubGetConversationsResponseAdditionalData = {
  pageData: PaginationParameters;
  uiMetadata?: UIMetadata;
};

type WsSubTitleUpdateResponseData = {
  conversationId: Conversation['id'];
  chatTitle: string;
};

type WsSubGetConfigsResponseData = ChatConfig;

type WsSubGetMessagesResponseData = {
  conversationId?: Conversation['id'] | null;
  isWriteAllowed: boolean;
  messages: Message[];
  context?: ConversationContext;
};

type WsSubNewSuggestionsResponseData = {
  conversationId: string;
  suggestions: Suggestion[];
};

export type WsSubSystemActionIntentResponseData = {
  conversationId: string;
  message: Omit<Message, 'content'> & {
    content: [
      {
        type: 'LOGIN_ACTION';
        value: {
          previousConversationId: string;
          intent: 'CONNECT_TO_AGENT';
          deviceId: string;
          loggedIn: boolean;
          source: null;
          lob: string;
        };
      },
    ];
  };
};

type WsSubLoginAckResponseData = {
  conversationId: string;
};

type WsSubHeartBeatResponseData = {};

type WsSubLoggingResponseData = {
  loggingEvent: string;
};

type WsSubAgentAssignedResponse = {
  id: Conversation['id'];
  chatTitle: string;
  lastMessage: Pick<Message, 'content' | 'createdAt'>;
  hasUnread: boolean;
  ticketId?: string | null;
  isWriteAllowed: boolean;
  updatedAt: EpochTimeStamp;
};
type WsSubAgentUnassignedResponse = {
  id: Conversation['id'];
};

type WsSubNavigateToResponseData = {
  conversationId: Conversation['id'];
  targetPage: 'USER_MIMA';
  parameters: Record<string, string>;
};

export type WsSubUIElementResponseData = {
  conversationId: Conversation['id'];
  enableElements: UIElement[];
  disableElements: UIElement[];
  UIElementData?: UIElementData;
};
