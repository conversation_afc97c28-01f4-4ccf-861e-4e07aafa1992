import { sleep, withTimeout } from '../../utils/functionUtils';
import { WsPublishEvent, WsSubscribeEvent } from './socketType';
import { getHeadersAndContext } from '../../network/headers';
import { LOG_LEVEL } from '../../utils/logger';
import { isEvaluationMode } from '../../utils/webUtils';

import {
  logBufferDiscardedBySocket,
  logBufferItemAdded,
  logBufferItemsNotProcessed,
  logBufferProcessed,
  logChatAbnormalClose,
  startNewSocketTrace,
  updateMessageTrace,
  updateSocketTrace,
} from '../../analytics/telemetry';
import { isNetworkAvailable } from '../../native/network';
import { WebSocketWrapper } from '../../native/websocket/WebSocketWrapper';
import { createWsObject } from '../../native/websocket/WebSocketFactory';
import { wsLogger as logger } from '../../native/websocket/WsLogger';

export interface SocketClientConfig {
  url: string;
  pingTimeout: number;
  pingInterval: number;
  connectionTimeout: number;
  reconnectDelay: number;
  maxReconnectAttempts: number;
  /**
   * Delay time for cleanup(close sockets, reset to initial state, ...).
   * This is to avoid unnecessary trashing of resources during successive setup->cleanup
   * */
  gcTime: number;
}

export enum WebSocketState {
  'CONNECTING',
  'OPEN',
  'CLOSED',
  'ERROR',
}

type SocketStateChangeListener = (
  newState: WebSocketState,
  isActive: boolean,
) => void;

let bufferCounter = 0;
type MsgBufferItem<T> = {
  value: T;
  scheduledAt: number;
  bufferId: number;
};

const WS_CLOSE_CODE_POLICY_VIOLATION = 1008;
const WS_CLOSE_CODE_NORMAL = 1000;

/**
 * @note: Pending items in this class:
 *    - Heartbeat check is not proper, we are not doing anything if heartbeat fails
 *    - Max wait time for msg ACK
 * */
export class SocketClient {
  private static websocket: WebSocketWrapper | null = null;
  private static config: SocketClientConfig;
  private static heartBeatAbortController: AbortController | null = null;
  private static messageBuffer: Array<MsgBufferItem<WsPublishEvent>> = [];
  private static reconnectAttempt = 0;
  /**
   * When this flag is true, it means that websocket is open, or we are trying to open.
   * When this flag is false, it means we have no active websocket connection aka 'CLOSED'
   * */
  private static _isActive = false;
  private static socketStatusChangeListener: Array<SocketStateChangeListener> = [];
  private static onMessageHandlers: Array<(msg: WsSubscribeEvent) => void> = [];
  private static socketTraceId: string | null = null;

  // ------------ Public methods ---------------

  /**
   * This should be called only once at the beginning of the app to establish a connection.
   * Multiple calls can cause unexpected behavior.
   * */
  static connect(config: SocketClientConfig): void {
    logger.info(`connect() isActive=${this._isActive}`);
    if (this._isActive) {
      // throw new Error('SocketClient is already active');
      logger.warn('SocketClient is already active');
      return;
    }
    this.config = config;
    this.reconnectAttempt = 0;
    this._isActive = true;
    if (isEvaluationMode()) {
      return;
    }
    this.createWebSocket();
  }

  static reconnectManually() {
    // reset reconnect attempt when manually triggered
    this.reconnectAttempt = 0;
    this.retryConnection();
  }

  /**
   * This should be called when the app is closed or when the connection is no longer needed.
   * */
  static disconnect(): void {
    logger.info(`disconnect() isActive=${this._isActive}`);
    this._isActive = false;
    this.disposeWebSocket(false);
    this.rejectBufferedEvents();
  }

  static isActive(): boolean {
    logger.info(`isActive() isActive=${this._isActive}`);
    return this._isActive;
  }

  static hasBufferedMessages(): boolean {
    return this.messageBuffer.length > 0;
  }

  static getSocketReadyState(): WebSocketState {
    logger.info('getSocketReadyState() called');
    if (this.websocket) {
      return this.websocket.getReadyState() as WebSocketState;
    }
    return WebSocketState.CLOSED;
  }

  /**
   * This method can be used to send messages to the server.
   * */
  static publish(
    payload: WsPublishEvent & {
      botMetadata?: Record<string, unknown>;
      quotedContentInfo?: Record<string, unknown>;
    },
    { skipBuffer = false } = {},
  ): void {
    logger.info(`called publish() isActive=${this._isActive}`);
    if (isWebSocketOpen(this.websocket)) {
      logger.debug('socket is open! sending message now', payload);
      sendEnrichedEvent(this.websocket, payload);
    } else if (!skipBuffer) {
      logBufferItemAdded();

      logger.debug('socket is not open! buffering message', payload);
      const bufferId = ++bufferCounter;
      this.messageBuffer.push({
        scheduledAt: Date.now(),
        value: payload,
        bufferId,
      });
      this.trackBufferStatus(bufferId);

      this.ensureWsTrulyActive();
    } else {
      logger.debug('socket is not open! skipping buffer message', payload);
    }
  }

  /**
   * This method can be used to subscribe to messages from the server.
   * @note Not calling `unsubscribe` can cause memory leaks.
   * @returns a function to unsubscribe.
   * */
  static subscribe(handler: (msg: WsSubscribeEvent) => void) {
    this.onMessageHandlers.push(handler);
    return () => {
      this.onMessageHandlers = this.onMessageHandlers.filter((h) => h !== handler);
    };
  }

  /**
   * This method can be used to subscribe to connection status changes.
   * @returns a function to unsubscribe.
   * */
  static onSocketStateChange(handler: SocketStateChangeListener): () => void {
    this.socketStatusChangeListener.push(handler);
    return () => {
      this.socketStatusChangeListener = this.socketStatusChangeListener.filter(
        (h) => h !== handler,
      );
    };
  }

  static updateConfig(newConfig: Partial<SocketClientConfig>): void {
    logger.info('updateConfig()', newConfig);
    this.config = { ...this.config, ...newConfig };
  }

  private static async trackBufferStatus(msgBufferId: number) {
    for (let i = 1; i <= 3; i++) {
      await sleep(10_000); // 10 secs
      const stillExistsInBuffer = this.messageBuffer.find(
        (value) => value.bufferId === msgBufferId,
      );
      if (!stillExistsInBuffer) {
        break; // buffer is cleared or flushed
      }
      logBufferItemsNotProcessed(10 * i); // 10s, 20s, 30s
    }
  }

  public static async waitForOpenWithTimeout(
    timeout = 10_000,
    pollInterval = 100,
  ): Promise<void> {
    let isOpen = isWebSocketOpen(this.websocket);
    if (isOpen) {
      return;
    }
    const start = Date.now();
    const end = start + timeout;
    while (!isOpen && Date.now() < end) {
      await sleep(pollInterval);
      isOpen = isWebSocketOpen(this.websocket);
    }
    if (!isOpen) {
      throw new Error('WebSocket connection timed out');
    }
  }

  // ------------ Private methods ---------------

  private static async createWebSocket(): Promise<void> {
    logger.info('createWebSocket()');
    if (this.websocket) {
      logger.debug('disposing existing websocket');
      await this.disposeWebSocket();
    }

    logger.debug('creating new websocket');
    this.socketTraceId = startNewSocketTrace();
    this.websocket = createWsObject({ url: this.config.url, useNative: true });
    logger.debug('waiting for connection to open');
    this.emitConnectionChange(WebSocketState.CONNECTING);
    try {
      await withTimeout(this.config.connectionTimeout, (resolve) => {
        this.websocket?.addOnOpenListener(() => resolve());
      });
      updateSocketTrace(this.socketTraceId, 'connectionEstablished');
    } catch (error) {
      updateSocketTrace(this.socketTraceId, 'connectError');
      this.emitConnectionChange(WebSocketState.ERROR);
      this.handleConnectionError(new Error('Connection timeout'));
      return;
    }

    logger.debug('connection opened successfully');

    this._isActive = true;
    this.emitConnectionChange(WebSocketState.OPEN);

    this.heartBeatAbortController = new AbortController();
    this.startHeartbeat(this.heartBeatAbortController.signal);

    this.sendBufferedMessages();

    this.websocket.addOnMessageListener((event) => {
      const data = JSON.parse((event as MessageEvent).data) as WsSubscribeEvent;
      if (data.eventType === 'HEART_BEAT') {
        logger.debug('websocket::onMessage', data.eventType);
      } else {
        logger.info(
          'websocket::onMessage',
          logger.level === LOG_LEVEL.VERBOSE ? data : data.eventType,
        );
      }
      this.emitMessage(data);
    });
    this.websocket.addOnErrorListener((error) => {
      logger.error('websocket::onerror', error);
      this.emitConnectionChange(WebSocketState.ERROR);
    });
    this.websocket.addOnCloseListener((closeEvent: CloseEvent) => {
      if (closeEvent.code !== WS_CLOSE_CODE_NORMAL) {
        logChatAbnormalClose(closeEvent.code);
      }
      if (closeEvent.code === WS_CLOSE_CODE_POLICY_VIOLATION) {
        logger.info('websocket::onClose()', (closeEvent as CloseEvent).code);
        this._isActive = false;
        this.disposeWebSocket(false);
        this.emitConnectionChange(WebSocketState.ERROR);
        return;
      } else {
        logger.info('websocket::onClose()');
        this.emitConnectionChange(WebSocketState.CLOSED);
        if (this._isActive) {
          this.retryConnection();
        }
      }
    });
  }

  private static async retryConnection(): Promise<void> {
    if (isEvaluationMode()) {
      return;
    }
    logger.info(`retryConnection() reconnectAttempt=${this.reconnectAttempt}`);
    if (this.reconnectAttempt >= this.config.maxReconnectAttempts) {
      logger.error('retryConnection:: Max reconnect attempts reached');
      this._isActive = false;
      this.emitConnectionChange(WebSocketState.CLOSED);
      await this.disposeWebSocket();
      return;
    }

    // dispose the existing socket, wait for reconnect delay
    await this.disposeWebSocket();

    const isNetworkConnected = await isNetworkAvailable();
    logger.info(`retryConnection:: isNetworkConnected=${isNetworkConnected}`);
    // Do not auto-reconnect if network is not available, let user manually retry
    if (!isNetworkConnected) {
      this._isActive = false;
      this.emitConnectionChange(WebSocketState.CLOSED);
      return;
    }

    this.reconnectAttempt++;
    this.createWebSocket();
  }

  private static handleConnectionError(error: Error): void {
    logger.error('handleConnectionError()', error);
    this.retryConnection();
  }

  private static async disposeWebSocket(waitAfterDispose = true): Promise<void> {
    this.heartBeatAbortController?.abort();
    const oldWs = this.websocket;
    if (oldWs) {
      // No removeOnXListener methods in the interface, so just close
      oldWs.close();
    }
    this.websocket = null;
    if (waitAfterDispose) {
      logger.info(`waiting for reconnect delay ${this.config.reconnectDelay}ms`);
      await sleep(this.config.reconnectDelay);
    }
  }

  private static sendBufferedMessages(): void {
    logger.info(
      `sendBufferedMessages buf=${this.messageBuffer.length}, isOpen=${isWebSocketOpen(this.websocket)}`,
    );
    let processedBufferItemCount = 0;
    while (this.messageBuffer.length > 0 && isWebSocketOpen(this.websocket)) {
      const bufferItem = this.messageBuffer.shift();
      if (bufferItem) {
        sendEnrichedEvent(this.websocket as any, bufferItem.value);
        processedBufferItemCount++;
      }
    }
    if (processedBufferItemCount) {
      logBufferProcessed(processedBufferItemCount);
    }
  }

  private static rejectBufferedEvents(): void {
    const bufferItems = this.messageBuffer.length;
    logger.info(`rejectBufferedEvents buf=${bufferItems}`);
    if (!bufferItems) {
      return;
    }
    this.messageBuffer.forEach(({ value: payload }) => {
      this.emitMessage({
        eventType: 'CLIENT_SEND_FAILED',
        payload: payload,
      });
    });
    this.messageBuffer = [];
    logBufferDiscardedBySocket(bufferItems);
  }

  private static emitMessage(msg: WsSubscribeEvent): void {
    this.onMessageHandlers.forEach((handler) => handler(msg));
  }

  private static emitConnectionChange(websocketState: WebSocketState): void {
    this.socketStatusChangeListener.forEach((listener) =>
      listener(websocketState, this._isActive),
    );
  }

  private static async startHeartbeat(abortSignal: AbortSignal): Promise<void> {
    logger.info(`startHeartbeat(). isActive=${this._isActive}`);
    while (this._isActive && !abortSignal.aborted) {
      logger.debug(
        `before heartbeat buf=${this.messageBuffer.length}, isOpen=${isWebSocketOpen(this.websocket)}`,
      );
      logger.debug('Send heartbeat');
      updateSocketTrace(this.socketTraceId, 'firstHeartBeatSent');
      SocketClient.publish(
        { eventType: 'HEART_BEAT', data: {} },
        { skipBuffer: true },
      );
      try {
        await withTimeout(this.config.pingTimeout, waitForHeartbeat(abortSignal));
        updateSocketTrace(this.socketTraceId, 'firstHeartBeatReceived');
      } catch (e) {
        updateSocketTrace(this.socketTraceId, 'heartBeatError');
        logger.error('Heartbeat timeout', e);
        if (abortSignal.aborted) {
          logger.debug('Heartbeat aborted');
          return;
        }
        return;
      }
      await sleep(this.config.pingInterval);
    }
  }

  /**
   * This function ensures that `isActive` flag is consistent with websocket's readyState
   *
   * if `isActive` == true && webSocketState !== 'OPEN'
   *  assert reconnect is in progress
   *  else:
   *    recreate websocket
   *
   * */
  private static async ensureWsTrulyActive() {
    if (!this._isActive) {
      return; // need to recover from this ??
    }
    if (isWebSocketOpen(this.websocket)) {
      return; // WS seems to be in readyState
    }

    if (this.websocket?.getReadyState() === WebSocket.CONNECTING) {
      await sleep(this.config.connectionTimeout);
      if (isWebSocketOpen(this.websocket)) {
        // should we flush buffer ??
        return;
      }
    }
    this.retryConnection();
  }
}

// ------------ Helper functions ---------------

const waitForHeartbeat =
  (abortSignal: AbortSignal | null) => async (done: () => void) => {
    if (abortSignal?.aborted) {
      return;
    }
    // create temporary listener for next heartbeat
    const unsubscribe = SocketClient.subscribe((msg) => {
      if (msg.eventType === 'HEART_BEAT') {
        unsubscribe(); // remove temporary listener, else it will cause memory leak
        done();
      }
    });

    // add abort listener to unsubscribe when the heartbeat is aborted, not doing this can cause memory leaks
    abortSignal?.addEventListener('abort', unsubscribe);
  };

/**
 * @param socket - OpenedWebSocket is a branded type of WebSocket,
 *    which is forces caller to check if the socket is open before calling this function
 *    to avoid runtime errors.
 * @param event - The event to be sent to the server.
 * */
const sendEnrichedEvent = async (
  socket: OpenedWebSocket,
  event: WsPublishEvent & {
    botMetadata?: Record<string, unknown>;
    quotedContentInfo?: Record<string, unknown>;
  },
): Promise<void> => {
  const { socketHeaders, socketContext } = await getHeadersAndContext();

  if (event.uiMetadata && 'clientTraceId' in event.uiMetadata) {
    updateMessageTrace(event.uiMetadata.clientTraceId as string, 'socketSend');
  }
  const context = socketContext();
  const finalEvent = {
    eventType: event.eventType,
    uiMetadata: event.uiMetadata,
    data: {
      ...socketContext(),
      ...event.data,
      contextId: event.contextId,
      botMetadata: { ...context.botMetadata, ...event.botMetadata },
      quotedContentInfo: event.quotedContentInfo,
    },
    headers: socketHeaders(),
    lobAppContext: event.lobAppContext,
  } as WsPublishEvent;

  if (finalEvent.eventType !== 'HEART_BEAT') {
    logger.debug(
      'publishing message',
      logger.level === LOG_LEVEL.VERBOSE ? finalEvent : finalEvent.eventType,
    );
  }
  socket.send(JSON.stringify(finalEvent));
};

type OpenedWebSocket = WebSocketWrapper & { __brand: 'OpenedWebSocket' };

function isWebSocketOpen(ws: WebSocketWrapper | null): ws is OpenedWebSocket {
  return ws?.getReadyState() === WebSocket.OPEN;
}
