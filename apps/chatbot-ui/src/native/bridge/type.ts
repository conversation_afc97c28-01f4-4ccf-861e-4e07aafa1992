/* eslint-disable unused-imports/no-unused-vars */
export type LoginResult = {
  authToken: string;
  metadata?: unknown;
};

export type AssistActionArgs = {
  lob: string;
  unreadCount?: string;
  payload: Record<string, unknown>;
};

export type TrackingActionArgs = {
  lob: string;
  payload: Record<string, unknown>;
};

// Interface defining the bridge functionality
export interface ITravelplexBridge {
  setCurrentInstanceId(instanceId: number | string): void;
  fireJsReadyEvent(): void;
  fireMinimizedEvent(): void;
  fireLoginAction(args?: Record<string, string>): Promise<LoginResult | undefined>;
  fireAssistAction(args: AssistActionArgs): void;
  fireTrackingAction(args: TrackingActionArgs): void;
}
