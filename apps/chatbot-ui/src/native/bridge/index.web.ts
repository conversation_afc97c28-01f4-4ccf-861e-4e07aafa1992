import { Logger } from '../../utils/logger';
import { isUserLoggedIn } from '../user';
import {
  ITravelplexBridge,
  LoginResult,
  AssistActionArgs,
  TrackingActionArgs,
} from './type';

let currentInstanceId: number | null = null;

const WEB_MESSAGES = {
  triggerAnalytics: 'CHAT_BOT__BRIDGE__TRIGGER_ANALYTICS',
  chatMessageUpdate: 'CHAT_BOT__BRIDGE__ON_CHAT_MESSAGE_UPDATE',
  triggerLogin: 'CHAT_BOT__BRIDGE__TRIGGER_LOGIN',
  minimizeView: 'CHAT_BOT__BRIDGE__MINIMIZE_VIEW',
  jsLoaded: 'CHAT_BOT__BRIDGE__JS_LOADED',
};
// Class implementation with static methods
export class Travelplex<PERSON>ridge implements ITravelplexBridge {
  private static readonly logger = Logger.createLogger({
    tag: 'TravelplexBridge',
    level: 'VERBOSE',
  });

  getCurrentInstanceId(): number {
    return currentInstanceId || 0;
  }

  setCurrentInstanceId(instanceId: number | string): void {
    if (typeof instanceId === 'string') {
      instanceId = parseInt(instanceId, 10);
    }
    currentInstanceId = instanceId;
  }

  fireJsReadyEvent(): void {
    if (typeof window === 'undefined') {
      return;
    }
    window.parent.postMessage(
      {
        type: WEB_MESSAGES.jsLoaded,
      },
      '*', // Using '*' for broad compatibility, consider restricting to specific origin in production
    );
  }

  fireMinimizedEvent(): void {
    if (typeof window === 'undefined') {
      return;
    }
    window.parent.postMessage(
      {
        type: WEB_MESSAGES.minimizeView,
      },
      '*', // Using '*' for broad compatibility, consider restricting to specific origin in production
    );
  }

  async fireLoginAction(
    args?: Record<string, string>,
  ): Promise<LoginResult | undefined> {
    if (typeof window === 'undefined') {
      return;
    }
    const isLoggedIn = await isUserLoggedIn();
    if (isLoggedIn) {
      return;
    }
    const { header, callbackData } = args || {};
    window.parent.postMessage(
      {
        type: WEB_MESSAGES.triggerLogin,
        payload: JSON.stringify({
          header,
          loginData: callbackData,
        }),
      },
      '*', // Using '*' for broad compatibility, consider restricting to specific origin in production
    );
  }

  fireAssistAction(args: AssistActionArgs): void {
    if (typeof window === 'undefined') {
      return;
    }
    const { payload, unreadCount, lob } = args;
    window.parent.postMessage(
      {
        type: WEB_MESSAGES.chatMessageUpdate,
        payload: JSON.stringify({
          unreadCount,
          lob,
          payload,
        }),
      },
      '*', // Using '*' for broad compatibility, consider restricting to specific origin in production
    );
  }

  fireTrackingAction(args: TrackingActionArgs): void {
    if (typeof window === 'undefined') {
      return;
    }
    const { payload, lob } = args;
    window.parent.postMessage(
      {
        type: WEB_MESSAGES.triggerAnalytics,
        payload: JSON.stringify({
          lob,
          payload,
        }),
      },
      '*', // Using '*' for broad compatibility, consider restricting to specific origin in production
    );
  }
}

export const bridge = new TravelplexBridge();

// Re-export the static methods for backward compatibility
export const setCurrentInstanceId = bridge.setCurrentInstanceId.bind(bridge);
export const fireJsReadyEvent = bridge.fireJsReadyEvent.bind(bridge);
export const fireMinimizedEvent = bridge.fireMinimizedEvent.bind(bridge);
export const fireLoginAction = bridge.fireLoginAction.bind(bridge);
export const fireAssistAction = bridge.fireAssistAction.bind(bridge);
export const fireTrackingAction = bridge.fireTrackingAction.bind(bridge);
