import { Platform } from 'react-native';
import { Logger } from '../../utils/logger';
import { loginWithPromise } from '../../utils/loginUtils';
import { getTravelplexFn } from '../modules/TravelplexModule';
import {
  ITravelplexBridge,
  LoginResult,
  AssistActionArgs,
  TrackingActionArgs,
} from './type';

let currentInstanceId: number | null = null;

// Class implementation with static methods
export class TravelplexBridge implements ITravelplexBridge {
  private static readonly logger = Logger.createLogger({
    tag: 'TravelplexBridge',
    level: 'VERBOSE',
  });

  getCurrentInstanceId(): number {
    return currentInstanceId || 0;
  }

  setCurrentInstanceId(instanceId: number | string): void {
    if (typeof instanceId === 'string') {
      instanceId = parseInt(instanceId, 10);
    }
    currentInstanceId = instanceId;
  }

  fireJsReadyEvent(): void {
    TravelplexBridge.logger.info('fireJsReadyEvent', this.getCurrentInstanceId());
    try {
      getTravelplexFn('onJsReady')?.(this.getCurrentInstanceId());
    } catch (e) {
      TravelplexBridge.logger.error('fireJsReadyEvent:: Error', e);
    }
  }

  fireMinimizedEvent(): void {
    const _currentInstanceId = this.getCurrentInstanceId();
    TravelplexBridge.logger.info('fireMinimizedEvent', _currentInstanceId);
    try {
      getTravelplexFn('onChatBotMinimized')(_currentInstanceId);
    } catch (e) {
      TravelplexBridge.logger.error('fireMinimizedEvent:: Error', e);
    }
  }

  async fireLoginAction(args?: Record<string, string>): Promise<LoginResult> {
    TravelplexBridge.logger.info('fireLoginAction', args);
    return await loginWithPromise(false, args?.header);
  }

  fireAssistAction(args: AssistActionArgs): void {
    TravelplexBridge.logger.info('fireAssistAction', args);
    const { lob, unreadCount = '', ...payload } = args || {};
    if (!lob || !payload) {
      TravelplexBridge.logger.error('handleChatMessageUpdate:: Invalid args ', args);
      return;
    }
    try {
      getTravelplexFn('onChatBotAction')?.(
        this.getCurrentInstanceId(),
        lob,
        unreadCount,
        Platform.OS === 'ios'
          ? JSON.stringify(payload.payload)
          : JSON.stringify(payload),
      );
    } catch (e) {
      TravelplexBridge.logger.error('fireAssistAction:: Error', e);
    }
  }

  fireTrackingAction(args: TrackingActionArgs): void {
    TravelplexBridge.logger.info('fireTrackingAction', args);
    const { lob, ...payload } = args || {};
    if (!lob || !payload) {
      TravelplexBridge.logger.error('fireTrackingAction:: Invalid args ', args);
      return;
    }
    try {
      getTravelplexFn('onChatBotAction')?.(
        this.getCurrentInstanceId(),
        lob,
        '',
        Platform.OS === 'ios'
          ? JSON.stringify(payload.payload.tracking)
          : JSON.stringify(payload.payload),
      );
    } catch (e) {
      TravelplexBridge.logger.error('fireTrackingAction:: Error', e);
    }
  }
}

export const bridge = new TravelplexBridge();

// Re-export the static methods for backward compatibility
export const setCurrentInstanceId = bridge.setCurrentInstanceId.bind(bridge);
export const fireJsReadyEvent = bridge.fireJsReadyEvent.bind(bridge);
export const fireMinimizedEvent = bridge.fireMinimizedEvent.bind(bridge);
export const fireLoginAction = bridge.fireLoginAction.bind(bridge);
export const fireAssistAction = bridge.fireAssistAction.bind(bridge);
export const fireTrackingAction = bridge.fireTrackingAction.bind(bridge);
