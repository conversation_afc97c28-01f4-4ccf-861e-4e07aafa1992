export const fireLobLinkClickedEvent = (args: { lobLink?: string } = {}) => {
  if (typeof window === 'undefined') {
    return;
  }
  const { lobLink } = args;
  // temporary fix for holiday chat
  const isHolidayChat = window?.location?.pathname?.includes('/chat');
  if (isHolidayChat && lobLink) {
    window.location.href = lobLink;
    return;
  }
  window.parent.postMessage(
    {
      type: 'CHAT_BOT__BRIDGE__LOB_LINK_CLICKED',
      payload: JSON.stringify({
        lobLink,
      }),
    },
    '*', // Using '*' for broad compatibility, consider restricting to specific origin in production
  );
};

export const fireOptionsClickEvent = (args: {
  lob: string;
  payload: Record<string, unknown>;
}) => {
  if (typeof window === 'undefined') {
    return;
  }
  const { lob, payload } = args;
  window.parent.postMessage(
    {
      type: 'CHAT_BOT__BRIDGE__OPTIONS_CLICKED',
      payload: JSON.stringify({ lob, payload }),
    },
    '*',
  );
};

export const fireContextChangeEvent = (args: {
  lob: string;
  payload: Record<string, unknown>;
}) => {
  if (typeof window === 'undefined') {
    return;
  }
  const { lob, payload } = args;
  window.parent.postMessage(
    {
      type: 'CHAT_BOT__BRIDGE__CONTEXT_CHANGE',
      payload: JSON.stringify({ lob, payload }),
    },
    '*',
  );
};
