import { NativeModules, Platform } from 'react-native';
import { map<PERSON>eysDeep, snakeToCamelCase } from '../../../utils/objectUtils';
import { BaseEvent } from '@mmt/events-schema';
import { default as UUID } from 'react-native-uuid';
import { getPageVars } from '../../../utils/pageUtils';
import { useMessageStore } from '../../../store/messages';
import { useAppStateStore } from '../../../store/app';
import { Logger } from '../../../utils/logger';
import { EventName, EventType, EventValue, EventCombinationFinal } from './pdtTypes';

type PdtV2ModuleAndroid = {
  platform: 'android';
  getSessionId: () => Promise<string>;
  newRequestId: () => Promise<string>;
  getPdtContext: () => Promise<string>;
};
type PdtV2ModuleIOS = {
  platform: 'ios';
  getSessionId: () => Promise<string>;
  getRequestId: () => Promise<string>;
  getPdtContext: () => Promise<PdtContext>;
};

function getPdtV2Module(): PdtV2ModuleAndroid | PdtV2ModuleIOS | null {
  return NativeModules.PdtV2Module;
}

export async function getPdtSessionId(): Promise<string | null> {
  const PdtV2Module = getPdtV2Module();
  if (!PdtV2Module) {
    return null;
  }
  return await PdtV2Module.getSessionId();
}

export async function newRequestId(): Promise<string | null> {
  const PdtV2Module = getPdtV2Module();
  if (!PdtV2Module) {
    return null;
  }
  if (Platform.OS === 'android') {
    return await (PdtV2Module as PdtV2ModuleAndroid).newRequestId();
  }
  if (Platform.OS === 'ios') {
    return await (PdtV2Module as PdtV2ModuleIOS).getRequestId();
  }
  return null;
}

export async function getPdtContext(): Promise<PdtContext | null> {
  const PdtV2Module = getPdtV2Module();
  if (!PdtV2Module) {
    return null;
  }
  if (Platform.OS === 'android') {
    const value = JSON.parse(
      await (PdtV2Module as PdtV2ModuleAndroid).getPdtContext(),
    );
    return mapKeysDeep(value, snakeToCamelCase);
  }

  if (Platform.OS === 'ios') {
    const pdtContext = await (PdtV2Module as PdtV2ModuleIOS).getPdtContext();
    return mapKeysDeep(pdtContext, snakeToCamelCase);
  }
  return null;
}
export interface DeviceContext {
  deviceId: string;
  ipAddr: string;
  osVersion: string;
  timeZone: string;
  platform: string;
  appVer: string;
  buildVer: string;
  os: string;
  networkType?: string;
  userAgent?: string;
  country?: string;
  region?: string;
  city?: string;
  lat?: number;
  long?: number;
  visitorId?: string;
  advertiserId?: string;
  vendorId?: string;
  deviceToken?: string;
  model?: string;
  appInstallTimestamp?: number;
}

export interface UserContext {
  isLoggedIn: boolean;
  uuid?: string;
  marketingCloudId?: string;
  loginChannel?: string; // "mmt-login", "fb", "google", "apple", "mmt|corporate", "guest"
  profileType?: string; // "personal", "business", "guest"
  blackTier?: string; // "preferred"
  walletBal?: number;
  loginType?: string;
  loyaltyStatus?: string;
  loyaltyType?: string;
  loyaltyLevel?: string; // "GOLD", "SILVER", "BRONZE"
  userSegmentList?: string[];
  userCategory?: string; // default: "NA"
}

export type PdtContext = {
  userContext: UserContext;
  deviceContext: DeviceContext;
};

interface EventDetails {
  event_id: string;
  event_name: EventName;
  event_timestamp: number;
  event_type: EventType;
  event_value: EventValue;
  components: {
    id: string;
    conversation_id?: string;
    content_details: Array<{
      id: string;
      type: string;
      participant_id: string;
    }>;
  };
}
const logger = Logger.createLogger({ tag: 'pdtLogger' });
interface TravelplexBaseEvent extends BaseEvent<EventDetails> {
  page_context: BaseEvent<EventDetails>['page_context'];
}
export type TravelplexBaseEventArg = Omit<
  TravelplexBaseEvent,
  'user_context' | 'device_context' | 'context' | 'event_tracking_context'
> & {
  event_tracking_context: Omit<
    TravelplexBaseEvent['event_tracking_context'],
    'timezone'
  >;
};

type PageData = {
  page?: string;
  prevPage?: string;
};

export type LogParam = {
  conversationId: string | undefined;
  event_detail: {
    event_name: EventDetails['event_name'];
    event_value: EventDetails['event_value'];
    event_type?: EventDetails['event_type'];
    components?: EventDetails['components'];
  };
};
const LOB = 'common';
const validLobs = [
  'common',
  'flights',
  'hotels',
  'forex',
  'bus',
  'rails',
  'cabs',
  'holidays',
  'travelinsurance',
  'giftcards',
  'visa',
  'w2g',
  'acme',
  'how2go',
  'travelcard',
  'hostapp',
  'postsales',
  'payment',
];
const validLobCategories = [
  'common',
  'df',
  'if',
  'dh',
  'ih',
  'bus',
  'train',
  'dc',
  'ic',
  'dom_acabs',
  'dom_ocabs',
  'dom_hrcabs',
  'intl_acabs',
  'intl_ocabs',
  'intl_hrcabs',
  'domhld',
  'intlhld',
  'travelinsurance',
  'forex',
  'forex_currency',
  'forex_card',
  'giftcards',
  'visa',
  'e-visa',
  'sticker-visa',
  'w2g',
  'dom_acme',
  'intl_acme',
  'how2go',
  'travelcards',
  'hostapp',
];
const formatLob = (lob: string | undefined | null) => {
  if (lob === 'COMMONS') {
    return 'common';
  }
  if (lob === 'FLIGHTS') {
    return 'flights';
  }
  if (lob === 'HOTELS') {
    return 'hotels';
  }
  if (lob === 'HOLIDAYS') {
    return 'holidays';
  }
  if (lob === 'CABS') {
    return 'cabs';
  }
  if (lob === 'BUS') {
    return 'bus';
  }
  if (lob === 'RAILS') {
    return 'rails';
  }
  return lob || LOB;
};

const formatLobCategory = (
  lobCategory: string | undefined | null,
  lob?: string | undefined | null,
) => {
  if (lobCategory === 'COMMONS') {
    return 'common';
  }
  if (lob === 'forex') {
    return 'forex';
  }
  if (lob === 'BUS') {
    return 'bus';
  }
  return lobCategory;
};

const formatBU = (bu: string | undefined | null) => {
  if (bu === 'COMMONS') {
    return 'common';
  }
  if (bu === 'FLIGHTS') {
    return 'flights';
  }
  if (bu === 'HOTELS') {
    return 'hotels';
  }
  if (bu === 'forex') {
    return 'tripmoney';
  }
  if (bu === 'HOLIDAYS') {
    return 'holidays';
  }
  if (bu === 'CABS' || bu === 'BUS' || bu === 'RAILS') {
    return 'ground_transport';
  }
  return bu;
};

const getLob = (lobFromContext: string | undefined | null) => {
  if (lobFromContext && validLobs.includes(lobFromContext)) {
    return lobFromContext;
  }
  return formatLob(lobFromContext);
};

const getLobCategory = (
  lobCategoryFromContext: string | undefined | null,
  lobFromContext: string | undefined | null,
) => {
  if (
    lobCategoryFromContext &&
    validLobCategories.includes(lobCategoryFromContext)
  ) {
    return lobCategoryFromContext;
  }
  return formatLobCategory(lobCategoryFromContext, lobFromContext);
};

class PdtLogging {
  #analyticsSdk: any = getPdtV2Module();
  #templateId = 165005;
  #topicName = 'mmt_travelplex_b2c_pdt_client_logging';
  #ORGS = { MMT: 'mmt' };
  async #logEvent(pageData: PageData, eventData: LogParam) {
    if (!this.#analyticsSdk || !this.#analyticsSdk?.trackEvent) {
      return;
    }
    const { PROP_PAGE_NAME: page } = getPageVars();
    const chatContext = useAppStateStore.getState().chatContext;
    const { conversationId, event_detail } = eventData;
    const loggerObj: TravelplexBaseEventArg = {
      page_context: {
        lob: getLob(chatContext?.context?.lob),
        lob_category: getLobCategory(
          chatContext?.context?.lobCategory,
          chatContext?.context?.lob,
        ),
        page_name: chatContext?.context?.view || 'NA',
        prev_page_name: chatContext?.context?.prevPage || 'NA',
        funnel_step: 'home',
      },
      event_tracking_context: {
        template_id: this.#templateId,
        topic_name: this.#topicName,
        request_id: this.getRequestId(),
        session_id: this.getSessionId() || '',
        journey_id: this.getJourneyId(conversationId),
        funnel_source: this.#ORGS.MMT,
        funnel_entry: page,
        session_visit_number: -1,
        traffic_source: null,
        env: 'prod',
        bu: chatContext?.context?.bu || formatBU(chatContext?.context?.lob) || LOB,
      },
      event_detail: {
        ...event_detail,
        event_id: UUID.v4(),
        event_timestamp: parseInt(`${Date.now() / 1000}`, 10),
        event_type: event_detail.event_type || 'action',
        // Right now we are hardcoding the components as empty array
        // In future we might need to send the actual components
        components: {
          id: UUID.v4(),
          conversation_id: conversationId,
          content_details: [],
        },
      },
    };
    try {
      await this.#analyticsSdk.trackEvent({
        payload: JSON.stringify(loggerObj),
      });
    } catch (e) {
      logger.error('Error in logging event', e);
    }
  }
  getRequestId = () => `${UUID.v4()}`;
  getSessionId = () => `${UUID.v4()}`;
  getJourneyId = (conversationId: string | undefined) =>
    conversationId ? conversationId + '_' + Date.now() : UUID.v4();

  public logEvent(
    eventDetail: LogParam['event_detail'],
    conversationId?: string | undefined,
  ) {
    const activeConversationId = useMessageStore.getState().activeConversationId;
    const { PROP_PAGE_NAME: page, PROP_PREV_PAGE_NAME: prevPage } = getPageVars();
    this.#logEvent(
      { page, ...(prevPage && { prevPage }) },
      {
        conversationId: conversationId || activeConversationId || 'NA',
        event_detail: eventDetail,
      },
    );
  }
}

const pdtLogging = new PdtLogging();

export const trackPDTEvent = ({
  eventName,
  eventValue,
  eventType,
  conversationId,
}: EventCombinationFinal & { conversationId?: string | undefined }) => {
  pdtLogging.logEvent(
    {
      event_name: eventName as EventName,
      event_value: eventValue,
      event_type: eventType as EventType,
    },
    conversationId,
  );
};
