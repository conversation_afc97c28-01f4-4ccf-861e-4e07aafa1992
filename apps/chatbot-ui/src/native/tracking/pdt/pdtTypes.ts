import { BaseEvent } from '@mmt/events-schema';

export const eventNameSchema = {
  PAGE_ENTRY: 'page-entry',
  CHAT_INTERACTED: 'chat-interacted',
  BUTTON_CLICKED: 'button-clicked',
} as const;

export const eventTypeSchema = {
  ACTION: 'action',
  ERROR: 'error',
  SYSTEM: 'system',
  LIFE_CYCLE: 'life_cycle',
  PAGE: 'page',
} as const;

export const eventValueSchema = {
  TRVLPLX_SHOWN: 'trvlplx_shown',
  TRVLPLX_CLICKED: 'trvlplx_clicked',
  TRVLPLX_COLLAPSED: 'trvlplx_collapsed',
  VIEW_MORE: 'view_more', // for cards
  READ_MORE: 'read_more',
  LIKE: 'like',
  DISLIKE: 'dislike',
  BOOKMARK_ADDED: 'bookmark_added',
  BOOKMARK_REMOVED: 'bookmark_removed',
  BOOKMARK_VIEWED: 'bookmark_viewed',
  BOOKMARK_CLICKED: 'bookmark_clicked',
  BOOKMARK_CATEGORY_CLICKED: 'tab_flt_clicked' as `tab_${string}_clicked`, // Ex - tab_hotel_clicked
  BOOKMARK_EDITED: 'bookmark_removed',
  BOOKMARK_CARD_CLICKED: 'bookmark_card_clicked',
  BOOKMARK_TEXT_CLICKED: 'bookmark_text_clicked',
  MEDIA_SHOWN: 'media_shown',
  MEDIA_CLICKED: 'media_clicked',
  LINKS_CLICKED: 'links_clicked',
  CHAT_MENU_CLICKED: 'chat_menu_clicked',
  CHAT_HISTORY_CLICKED: 'chat_history_clicked',
  ITINERARY_SHOWN: 'Itinerary_shown',
  ITINERARY_LIST_SHOWN: 'itinerary_list_shown',
  SUBMITTED: 'submitted',
  TIMEOUT: 'timeout',
  INGRESS_MESSAGE: 'ingress_message',
  CTA_CONNECT_TO_AGENT_CLICKED: 'cta_connect_to_agent_clicked',
  CTA_CONNECT_TO_AGENT_CARD: 'cta_connect_to_agent_card' as `cta_${string}`, // Ex - cta_connect_with_expert
  ASSIST_TRIGGERED: 'assist_triggered',
  WELCOME_PROMPTS_IMAGE: 'welcome_prompts_image',
  WELCOME_PROMPTS_TEXT: 'welcome_prompts_text',
  WELCOME_SUGGESTIVE_PROMPTS_WITH_ICON: 'welcome_suggestive_prompts_with_icon',
  WELCOME_SUGGESTIVE_PROMPTS_WITHOUT_ICON: 'welcome_suggestive_prompts_without_icon',
  INPUT_BOX: 'input_box',
  IMAGE_PROMPT: 'image_prompt',
  TEXT_PROMPT: 'text_prompt',
  CHAT_MENU_NEW_CHAT_CLICKED: 'chat_menu_new_chat_clicked',
  CHAT_MENU_HISTORY_CLICKED: 'chat_menu_history_clicked',
  CHAT_MENU_DELETE_CLICKED: 'chat_menu_delete_clicked',
  SESSION_MESSAGES: 'session_messages',
  PROMPTS_WITHOUT_IMAGES: 'prompts_without_images',
  PROMPTS_WITH_IMAGES: 'prompts_with_images',
  RECENT_CHATS: 'recent_chats',
  RECONNECT_CTA_SHOWN: 'reconnect_cta_shown',
  CONFIG_ERROR_SHOWN: 'config_error_shown',
  MESSAGES_ERROR_SHOWN: 'messages_error_shown',
  PAGE_NOT_FOUND_SHOWN: 'page_not_found_shown',
  CONFIG_ERROR_RETRY_CLICKED: 'config_error_retry_clicked',
  MESSAGES_ERROR_RETRY_CLICKED: 'messages_error_retry_clicked',
  PAGE_NOT_FOUND_RETRY_CLICKED: 'page_not_found_retry_clicked',
  GENERIC_ERROR_SHOWN: 'generic_error_shown',
  GENERIC_ERROR_RETRY_CLICKED: 'generic_error_retry_clicked',
  TRIPMODE_CTA_CLICKED: 'tripmode_clicked',
  TRIPMODE_ACTIVE: 'tripmode_active',
  MEDIA_SHOWN_SYNC_ITINERARY: 'media_shown_sync_itinerary',
  MEDIA_CLICKED_SYNC_ITINERARY: 'media_clicked_sync_itinerary',
  ITINERARY_DETAILS_SHOWN: 'itinerary_details_shown',
  GO_AHEAD_CLICKED: 'go_ahead_clicked',
  ASK_QUESTION_CLICKED: 'ask_question_clicked',
  CONTEXT_ITINERARY: 'context_itinerary',
  ITINERARY_CONTEXT_REMOVED: 'itinerary_context_removed',
  ITINERARY_PLACEHOLDER_CLICKED: 'itinerary_placeholder_clicked',
  ITINERARY_OPTION_CLICKED: 'itinerary_option_clicked',
  ASYNC_FLOW_TRIGGERED: 'async_flow_triggered',
  MEDIA_SHOWN_PACKAGE: 'media_shown_package',
  ASYNC_VIDEO_SHOWN: 'async_video_shown',
  ASYNC_VIDEO_AUTOPLAYED: 'async_video_autoplayed',
  MEDIA_CLICKED_ASYNC_VIDEO: 'media_clicked_async_video',
  MEDIA_CLICKED_PACKAGE_VIDEO: 'media_clicked_package_video',
  MEDIA_CLICKED_ASYNC_ITINERARY: 'media_clicked_async_itinerary',
  ASYNC_SHARE_CLICKED: 'async_share_clicked',
  PLAN_A_TRIP_CLICKED: 'plan_a_trip_clicked',
  CONNECT_TO_AN_AGENT: 'connect_to_an_agent',
  CALL_NOW_CLICKED: 'call_now_clicked',
  CALL_SLOT_SHOWN: 'call_slot_shown',
  CTA_SCHEDULE_CALLBACK_CLICKED: 'cta_schedule_callback_clicked',
  TRIPMODE_CTA_SHOWN: 'tripmode_cta_shown',
  TRIPMODE_CTA_DISABLED: 'tripmode_cta_disabled',
  VOICE_INPUT_CLICKED: 'voice_input_clicked',
  VOICE_INPUT_CANCELLED: 'voice_input_cancelled',
  VOICE_TO_TEXT_SUCCESS: 'voice_to_text_success',
  VOICE_INPUT_FAILED: 'voice_input_failed',
  PROMPT_SRC_VOICE: 'prompt_src_voice',
  VOICE_TALKBACK_STARTED: 'voice_talkback_started',
  VOICE_TALKBACK_FAILED: 'voice_talkback_failed',
  CONVERSATION_LONG_PRESS: 'conversation_long_press',
  CONVERSATION_CONTEXT_MENU_DELETE_CLICKED:
    'conversation_context_menu_delete_clicked',
  CONVERSATION_CONTEXT_MENU_OPEN_CLICKED: 'conversation_context_menu_open_clicked',
  DELETE_MODAL_DELETE_CLICKED:
    'delete_modal_delete_clicked' as `delete_modal_delete_clicked_${'menu' | 'history'}`,
  DELETE_MODAL_CANCEL_CLICKED:
    'delete_modal_cancel_clicked' as `delete_modal_cancel_clicked_${'menu' | 'history'}`,
  DELETE_CONVERSATION_FAILED:
    'delete_conversation_failed' as `delete_conversation_failed_${'menu' | 'history'}`,
  DELETE_CONVERSATION_SUCCESS:
    'delete_conversation_success' as `delete_conversation_success_${'menu' | 'history'}`,
} as const;

// Create a union type from the values of eventNameSchema
export type EventName = (typeof eventNameSchema)[keyof typeof eventNameSchema];
export type EventType = (typeof eventTypeSchema)[keyof typeof eventTypeSchema];
export type EventValue = (typeof eventValueSchema)[keyof typeof eventValueSchema];

const eventCombinationsFinal = {
  [eventNameSchema.PAGE_ENTRY]: {
    eventTypes: [eventTypeSchema.PAGE] as const,
    eventValues: [
      eventValueSchema.SESSION_MESSAGES,
      eventValueSchema.PROMPTS_WITHOUT_IMAGES,
      eventValueSchema.PROMPTS_WITH_IMAGES,
      eventValueSchema.RECENT_CHATS,
      eventValueSchema.RECONNECT_CTA_SHOWN,
    ] as const,
  },
  [eventNameSchema.CHAT_INTERACTED]: {
    eventTypes: [eventTypeSchema.ACTION] as const,
    eventValues: [
      eventValueSchema.WELCOME_PROMPTS_IMAGE,
      eventValueSchema.WELCOME_PROMPTS_TEXT,
      eventValueSchema.WELCOME_SUGGESTIVE_PROMPTS_WITH_ICON,
      eventValueSchema.WELCOME_SUGGESTIVE_PROMPTS_WITHOUT_ICON,
      eventValueSchema.INPUT_BOX,
      eventValueSchema.VIEW_MORE,
      eventValueSchema.READ_MORE,
      eventValueSchema.LIKE,
      eventValueSchema.DISLIKE,
      eventValueSchema.BOOKMARK_ADDED,
      eventValueSchema.BOOKMARK_REMOVED,
      eventValueSchema.BOOKMARK_VIEWED,
      eventValueSchema.BOOKMARK_CLICKED,
      eventValueSchema.BOOKMARK_EDITED,
      eventValueSchema.BOOKMARK_CATEGORY_CLICKED,
      eventValueSchema.BOOKMARK_CARD_CLICKED,
      eventValueSchema.BOOKMARK_TEXT_CLICKED,
      eventValueSchema.MEDIA_SHOWN,
      eventValueSchema.MEDIA_CLICKED,
      eventValueSchema.LINKS_CLICKED,
      eventValueSchema.CHAT_MENU_CLICKED,
      eventValueSchema.CHAT_HISTORY_CLICKED,
      eventValueSchema.ITINERARY_SHOWN,
      eventValueSchema.SUBMITTED,
      eventValueSchema.CHAT_MENU_NEW_CHAT_CLICKED,
      eventValueSchema.CHAT_MENU_HISTORY_CLICKED,
      eventValueSchema.MEDIA_SHOWN_SYNC_ITINERARY,
      eventValueSchema.MEDIA_CLICKED_SYNC_ITINERARY,
      eventValueSchema.CONTEXT_ITINERARY,
      eventValueSchema.ITINERARY_CONTEXT_REMOVED,
      eventValueSchema.ITINERARY_PLACEHOLDER_CLICKED,
      eventValueSchema.ITINERARY_OPTION_CLICKED,
      eventValueSchema.ASYNC_FLOW_TRIGGERED,
      eventValueSchema.MEDIA_SHOWN_PACKAGE,
      eventValueSchema.ASYNC_VIDEO_SHOWN,
      eventValueSchema.ASYNC_VIDEO_AUTOPLAYED,
      eventValueSchema.MEDIA_CLICKED_ASYNC_VIDEO,
      eventValueSchema.MEDIA_CLICKED_PACKAGE_VIDEO,
      eventValueSchema.MEDIA_CLICKED_ASYNC_ITINERARY,
      eventValueSchema.ASYNC_SHARE_CLICKED,
      eventValueSchema.ASK_QUESTION_CLICKED,
      eventValueSchema.GO_AHEAD_CLICKED,
      eventValueSchema.PROMPT_SRC_VOICE,
      eventValueSchema.VOICE_TALKBACK_STARTED,
      eventValueSchema.VOICE_TALKBACK_FAILED,
      eventValueSchema.TRVLPLX_SHOWN,
      eventValueSchema.TRVLPLX_CLICKED,
      eventValueSchema.TRIPMODE_CTA_CLICKED,
      eventValueSchema.TRIPMODE_ACTIVE,
      eventValueSchema.TRIPMODE_CTA_SHOWN,
      eventValueSchema.TRIPMODE_CTA_DISABLED,
      eventValueSchema.VOICE_INPUT_CLICKED,
      eventValueSchema.VOICE_INPUT_CANCELLED,
      eventValueSchema.VOICE_TO_TEXT_SUCCESS,
      eventValueSchema.ITINERARY_DETAILS_SHOWN,
      eventValueSchema.ITINERARY_LIST_SHOWN,
      eventValueSchema.GO_AHEAD_CLICKED,
      eventValueSchema.ASK_QUESTION_CLICKED,
      eventValueSchema.PLAN_A_TRIP_CLICKED,
      eventValueSchema.IMAGE_PROMPT,
      eventValueSchema.TEXT_PROMPT,
      eventValueSchema.CONFIG_ERROR_SHOWN,
      eventValueSchema.MESSAGES_ERROR_SHOWN,
      eventValueSchema.PAGE_NOT_FOUND_SHOWN,
      eventValueSchema.CONFIG_ERROR_RETRY_CLICKED,
      eventValueSchema.MESSAGES_ERROR_RETRY_CLICKED,
      eventValueSchema.PAGE_NOT_FOUND_RETRY_CLICKED,
      eventValueSchema.GENERIC_ERROR_SHOWN,
      eventValueSchema.GENERIC_ERROR_RETRY_CLICKED,
      eventValueSchema.ASSIST_TRIGGERED,
      eventValueSchema.CONVERSATION_LONG_PRESS,
      eventValueSchema.CONVERSATION_CONTEXT_MENU_DELETE_CLICKED,
      eventValueSchema.CONVERSATION_CONTEXT_MENU_OPEN_CLICKED,
      eventValueSchema.DELETE_MODAL_DELETE_CLICKED,
      eventValueSchema.DELETE_MODAL_CANCEL_CLICKED,
      eventValueSchema.DELETE_CONVERSATION_SUCCESS,
      eventValueSchema.DELETE_CONVERSATION_FAILED,
      eventValueSchema.CHAT_MENU_DELETE_CLICKED,
    ] as const,
  },
  [eventNameSchema.BUTTON_CLICKED]: {
    eventTypes: [eventTypeSchema.ACTION] as const,
    eventValues: [
      eventValueSchema.CTA_CONNECT_TO_AGENT_CLICKED,
      eventValueSchema.CTA_CONNECT_TO_AGENT_CARD,
      eventValueSchema.CONNECT_TO_AN_AGENT,
      eventValueSchema.CALL_NOW_CLICKED,
      eventValueSchema.CALL_SLOT_SHOWN,
      eventValueSchema.CTA_SCHEDULE_CALLBACK_CLICKED,
    ] as const,
  },
};

export type EventCombinationFinal = {
  [K in keyof typeof eventCombinationsFinal]: {
    eventName: K;
    eventType: (typeof eventCombinationsFinal)[K]['eventTypes'][number];
    eventValue: (typeof eventCombinationsFinal)[K]['eventValues'][number];
  };
}[keyof typeof eventCombinationsFinal];

interface EventDetails {
  event_id: string;
  event_name: EventName;
  event_timestamp: number;
  event_type: EventType;
  event_value: EventValue;
  conversation_id?: string;
  components: {
    id: string;
    content_details: Array<{
      id: string;
      type: string;
      participant_id: string;
    }>;
  };
}
interface TravelplexBaseEvent extends BaseEvent<EventDetails> {
  page_context: BaseEvent<EventDetails>['page_context'];
}
export type TravelplexBaseEventArg = Omit<
  TravelplexBaseEvent,
  'user_context' | 'device_context' | 'context' | 'event_tracking_context'
> & {
  event_tracking_context: Omit<
    TravelplexBaseEvent['event_tracking_context'],
    'timezone'
  >;
};

export type PageData = {
  page?: string;
  prevPage?: string;
};

export type LogParam = {
  conversationId: string | undefined;
  event_detail: {
    event_name: EventDetails['event_name'];
    event_value: EventDetails['event_value'];
    event_type?: EventDetails['event_type'];
    components?: EventDetails['components'];
  };
};
