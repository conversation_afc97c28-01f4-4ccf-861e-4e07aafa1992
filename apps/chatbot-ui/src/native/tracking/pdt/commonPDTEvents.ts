import { getPageVars } from '../../../utils/pageUtils';
import { trackPDTEvent } from './pdt';
import { eventNameSchema, eventTypeSchema, eventValueSchema } from './pdtTypes';

export const trackMediaClicked = () => {
  const { VAR_PAGE_NAME } = getPageVars();
  if (VAR_PAGE_NAME === 'chat:bookmark_page|travelplex') {
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.BOOKMARK_CARD_CLICKED,
    });
  } else {
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.MEDIA_CLICKED,
    });
  }
};
