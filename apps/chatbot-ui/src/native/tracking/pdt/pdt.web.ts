import { initAndGetAnalyticsSdk, getDeviceInfo } from '@mmt/event-logger';
import { default as UUID } from 'react-native-uuid';
import { DeviceContext, UserContext, BaseEvent } from '@mmt/events-schema';
import {
  getMmtAuth,
  getTimeZone,
  getUserId,
  NOT_AVAILABLE,
  getNavigationType,
  generateUUID,
  getCurrentDateStamp,
} from '../../../utils/webUtils';
import { getPageVars } from '../../../utils/pageUtils';
import { useMessageStore } from '../../../store/messages';
import {
  EventName,
  EventType,
  EventValue,
  EventCombinationFinal,
  eventValueSchema,
  eventTypeSchema,
  eventNameSchema,
} from './pdtTypes';

const PDT_HOST = 'https://pdt.makemytrip.com';
const SEND_EVENT_ENDPOINT = `${PDT_HOST}/dts/s/da/n`;
const PDT_REQ_HEADERS: Record<string, string> = {
  'Content-Type': 'application/json',
  'Request-Data-Encoding': 'json',
  'Accept-Encoding': 'gzip',
};

interface ChatFrameworkBaseEvent extends BaseEvent<EventDetails> {
  page_context: BaseEvent<EventDetails>['page_context'];
}

export type ChatFrameworkBaseEventArg = Omit<
  ChatFrameworkBaseEvent,
  'user_context' | 'device_context' | 'context'
>;

interface EventDetails {
  event_id: string;
  event_name: EventName;
  event_timestamp: number;
  event_type: EventType;
  event_value: EventValue;
  conversation_id?: string;
  components: {
    id: string;
    content_details: Array<{
      id: string;
      type: string;
      participant_id: string;
    }>;
  };
}

type LogParam = {
  conversationId: string | undefined;
  event_detail: EventDetails;
};
interface TravelplexBaseEvent extends BaseEvent<EventDetails> {
  page_context: BaseEvent<EventDetails>['page_context'];
}
export type TravelplexBaseEventArg = Omit<
  TravelplexBaseEvent,
  'user_context' | 'device_context' | 'context' | 'event_tracking_context'
> & {
  event_tracking_context: Omit<
    TravelplexBaseEvent['event_tracking_context'],
    'timezone'
  >;
};
const getUserContext = () => {
  const mmtAuth = getMmtAuth() || '';
  const uuid = getUserId() || '';
  let marketingCloudId: string | null = null;
  if (typeof window !== 'undefined' && typeof (window as any).s === 'object') {
    marketingCloudId = (window as any).s?.marketingCloudVisitorID ?? NOT_AVAILABLE;
  }
  return {
    is_logged_in: mmtAuth !== NOT_AVAILABLE,
    marketing_cloud_id: marketingCloudId,
    uuid,
  };
};

const LOB = 'travelPlex';
export class PDTLogging {
  #analyticsSdk: any = null;
  #deviceInfo: any = null;
  #funnel_step = 'newConversation';
  #templateId = 165005;
  #topicName = 'mmt_travelplex_b2c_pdt_client_logging';

  #ORGS = { MMT: 'mmt' };
  #logEvent = async (
    pageData: any = {},
    eventData: {
      conversationId: LogParam['conversationId'];
      event_detail: Omit<
        LogParam['event_detail'],
        'event_id' | 'components' | 'event_timestamp' | 'conversation_id'
      >;
    },
  ) => {
    if (typeof window === 'undefined' || !this.#analyticsSdk) return;
    const { conversationId, event_detail } = eventData;
    const loggerObj: ChatFrameworkBaseEventArg = {
      page_context: {
        lob: LOB,
        lob_category: LOB,
        page_name: pageData.page || NOT_AVAILABLE,
        prev_page_name: pageData.prevPage || NOT_AVAILABLE,
        funnel_step: this.#funnel_step,
        navigation: getNavigationType(),
      },
      event_tracking_context: {
        template_id: this.#templateId,
        topic_name: this.#topicName,
        env: process.env.ENVIRONMENT?.toLowerCase() || NOT_AVAILABLE,
        timezone: getTimeZone(),
        request_id: this.getRequestId(),
        session_id: (await this.getSessionId()) || '',
        journey_id: this.getJourneyId(conversationId),
        funnel_source: this.#ORGS.MMT,
        session_visit_number: -1,
        traffic_source: null, // 'meta' | 'cmp'
      },
      event_detail: {
        ...event_detail,
        event_id: generateUUID(),
        event_timestamp: Date.now(),
        event_type: event_detail.event_type || 'user_action',
        conversation_id: conversationId,
        // Right now we are hardcoding the components as empty array
        // In future we might need to send the actual components
        components: {
          id: UUID.v4(),
          content_details: [],
        },
      },
    };

    this.#analyticsSdk.track(loggerObj, LOB);
  };
  _deviceContext = () => {
    if (this.#deviceInfo) {
      return {
        app_ver: NOT_AVAILABLE,
        build_ver: NOT_AVAILABLE,
        device_id: this.#deviceInfo.deviceId || NOT_AVAILABLE,
        ip_addr: NOT_AVAILABLE,
        os: this.#deviceInfo.os || NOT_AVAILABLE,
        os_version: this.#deviceInfo.osVersion || NOT_AVAILABLE,
        network_type: this.#deviceInfo.networkType || NOT_AVAILABLE,
        platform: this.#deviceInfo.platform || NOT_AVAILABLE,
        time_zone: getTimeZone(),
      };
    }
  };
  public getRequestId = () => generateUUID();

  public getSessionId = async (): Promise<string | null> =>
    this.#analyticsSdk?.getSessionId();

  getJourneyId = (conversationId: string | undefined) =>
    conversationId ? conversationId + '_' + getCurrentDateStamp() : null;

  public init() {
    if (this.#analyticsSdk !== null) {
      return;
    }
    // if (!window?.location.hostname.includes(".makemytrip.com")) {
    //   return;
    // }
    const deviceInfo = getDeviceInfo(window);
    this.#deviceInfo = deviceInfo || null;
    this.#analyticsSdk = initAndGetAnalyticsSdk({
      runtimeContextProvider: {
        getDeviceContext(): DeviceContext {
          return {
            app_ver: NOT_AVAILABLE,
            build_ver: NOT_AVAILABLE,
            device_id: deviceInfo?.deviceId || NOT_AVAILABLE,
            ip_addr: NOT_AVAILABLE,
            os: deviceInfo?.os || NOT_AVAILABLE,
            os_version: deviceInfo?.osVersion || NOT_AVAILABLE,
            network_type: deviceInfo?.networkType || NOT_AVAILABLE,
            platform: deviceInfo?.platform || NOT_AVAILABLE,
            time_zone: getTimeZone(),
          };
        },
        getUserContext(): UserContext {
          return getUserContext();
        },
      },
      networkClient: {
        async sendEvents(
          events: Record<string, unknown>,
          priority: 'normal' | 'terminal',
        ) {
          const requestBody = JSON.stringify(events);
          if (
            priority === 'terminal' &&
            typeof navigator.sendBeacon === 'function'
          ) {
            navigator.sendBeacon(SEND_EVENT_ENDPOINT, requestBody);
          } else {
            try {
              await fetch(SEND_EVENT_ENDPOINT, {
                method: 'POST',
                body: requestBody,
                headers: PDT_REQ_HEADERS,
                keepalive: true,
              });
            } catch (error) {
              // eslint-disable-next-line no-console
              console.error('Error sending PDT event:', error);
            }
          }
          return Promise.resolve('success');
        },
      },
    });
  }

  public logEvent(
    eventDetail: Omit<
      LogParam['event_detail'],
      'event_id' | 'components' | 'event_timestamp' | 'conversation_id'
    >,
  ) {
    const activeConversationId = useMessageStore.getState().activeConversationId;
    const { PROP_PAGE_NAME: page, PROP_PREV_PAGE_NAME: prevPage } = getPageVars();
    this.#logEvent(
      { page, ...(prevPage && { prevPage }) },
      {
        conversationId: activeConversationId || 'NA',
        event_detail: {
          ...eventDetail,
        },
      },
    );
  }
}

const pdtLogging = new PDTLogging();
pdtLogging.init();
export async function getPdtSessionId(): Promise<string | null> {
  return await pdtLogging.getSessionId();
}

export async function newRequestId(): Promise<string | null> {
  return await pdtLogging.getRequestId();
}

export async function getPdtContext() {
  const deviceContext = getDeviceInfo(window);
  const userContext = getUserContext();
  return {
    deviceContext,
    userContext,
  };
}

export const trackPDTEvent = ({
  eventName,
  eventValue,
  eventType,
}: EventCombinationFinal) => {
  pdtLogging.logEvent({
    event_name: eventName as EventName,
    event_value: eventValue,
    event_type: eventType as EventType,
  });
};

export const trackMediaClicked = () => {
  const { VAR_PAGE_NAME } = getPageVars();
  if (VAR_PAGE_NAME === 'chat:bookmark_page|travelplex') {
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.BOOKMARK_CARD_CLICKED,
    });
  } else {
    trackPDTEvent({
      eventName: eventNameSchema.CHAT_INTERACTED,
      eventType: eventTypeSchema.ACTION,
      eventValue: eventValueSchema.MEDIA_CLICKED,
    });
  }
};
