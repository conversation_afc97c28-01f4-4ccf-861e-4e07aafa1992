import {
  ClickEventSchema,
  keyMapping,
  OmnitureEventData,
  OmnitureCallerArgs,
  ClickEventKey,
} from './omnitureConst';

import { getPageVars } from '../utils/pageUtils';
// Replace generic Record types with specific interfaces
interface OmnitureInstance extends Record<string, unknown> {
  linkTrackVars: string;
  t: () => string | false;
  tl: (
    this: unknown,
    linkObject: unknown,
    linkType: string,
    linkName: unknown,
  ) => void;
  channel: string;
}

declare global {
  interface Window {
    s_gi?: (arg: string) => Record<string, unknown>;
  }
}

export type setVarsFnType<T = Record<string, string>> = (args: {
  omniture: OmnitureInstance;
  params: T;
}) => void;

export const setVars: setVarsFnType<Record<string, unknown>> = ({
  omniture,
  params,
}: {
  omniture: Record<string, unknown>;
  params: Record<string, unknown>;
}) => {
  for (const key in params) {
    omniture.linkTrackVars = `${omniture.linkTrackVars ? omniture.linkTrackVars + ',' : ''}${key}`;
    omniture[key] = params[key];
  }
};

export class OmnitureService {
  #eventsQueue: Array<() => void> = [];
  #isOmnitureLoading = false;
  #isOmnitureLoaded = false;
  #omniture: OmnitureInstance | null = null;
  #store = null;
  #linkTrackVars = '';
  #setVars: setVarsFnType<Record<string, unknown>> | null = null;
  #omnitureJSFile = 'https://jsak.mmtcdn.com/pwa/assets/js/libs/pwatracker.js';

  // private methods
  /**
   * @function
   * @description fires all the events in the queue
   * (All events are stored in the queue until omniture is loaded)
   */
  #fireEventsInQueue = () => {
    this.#eventsQueue.forEach((cb: () => void) => cb());
    this.#eventsQueue = [];
  };

  /**
   * @function
   * @description - initializes omniture from the window object
   */
  #initializeOmniture = () => {
    try {
      this.#omniture = getOmniture();
    } catch (error) {}
  };

  /**
   * @function
   * @description - function to set common vars
   */
  #setCommonVars = () => {
    if (!this.#omniture) {
      return;
    }
    this.#omniture.channel = 'chat|travelplex';
  };
  /**
   * @function
   * @description - Loads the omniture js files dynamically
   */
  #loadOmniture = () => {
    if (this.#isOmnitureLoading) {
      return;
    }
    if (
      typeof window !== 'undefined' &&
      window.s_gi &&
      typeof window.s_gi === 'function'
    ) {
      this.#isOmnitureLoaded = true;
      this.#isOmnitureLoading = false;
      this.#fireEventsInQueue();
      return;
    }
    this.#isOmnitureLoading = true;
    loadJS(
      this.#omnitureJSFile,
      () => {
        this.#isOmnitureLoading = false;
        this.#isOmnitureLoaded = true;
        this.#fireEventsInQueue();
      },
      () => {
        return;
      },
    );
  };

  // public methods
  /**
   * @function
   * @description - Function to initialize omniture service
   * @param initialParams
   * @param initialParams.setLoadVars - Function to set load vars
   * @param initialParams.setClickVars - Function to set click vars
   */
  init = (initialParams: { setVars: setVarsFnType<Record<string, unknown>> }) => {
    this.#setVars = initialParams?.setVars;
    this.#loadOmniture();
  };
  updateParams = (params: Record<string, unknown>) => {
    const updatedParams: Record<string, unknown> = {};
    for (const key in params) {
      if (key.startsWith('m_v')) {
        const newPropName = key.replace('m_v', 'eVar');
        updatedParams[newPropName] = params[key];
      } else if (key.startsWith('m_c')) {
        const newPropName2 = key.replace('m_c', 'prop');
        updatedParams[newPropName2] = params[key];
      } else {
        updatedParams[key] = params[key];
      }
    }
    return updatedParams;
  };
  #track = (params: Record<string, unknown>, method: 'load' | 'click') => {
    if (!this.#isOmnitureLoaded) {
      this.#eventsQueue.push(() => this.#track(params, method));
      return;
    }

    if (!this.#omniture) {
      this.#initializeOmniture();
      if (!this.#omniture) {
        return;
      }
    }
    if (!this.#omniture) {
      return;
    }

    this.#omniture.linkTrackVars = this.#linkTrackVars || '';
    this.#setVars?.({ omniture: this.#omniture, params });

    if (method === 'load') {
      const s_code = typeof this.#omniture.t === 'function' && this.#omniture.t();
      if (s_code) {
        document.write(s_code);
      }
    } else {
      typeof this.#omniture.tl === 'function' &&
        this.#omniture.tl(this, 'o', params);
    }
  };
  /**
   * @function
   * @description - Function to track load event
   * @param params
   */
  trackLoadEvent = (_params: Record<string, unknown>) => {
    const params = this.updateParams(_params);
    this.#track(params, 'load');
  };

  /**
   * @function
   * @description - Function to track click event
   * @param params
   */
  trackClickEvent = (_params = {}) => {
    const params = this.updateParams(_params);
    this.#track(params, 'click');
  };
}

/**
 * @function
 * @description - Load JS file
 *
 * @param {string} fileURL - URL of the file to be loaded
 * @param {function} onLoad - Callback function to be called on load
 * @param {function} onError - Callback function to be called on error
 */
export const loadJS = (fileURL: string, onLoad: () => void, onError: () => void) => {
  const scriptEle = document.createElement('script');
  scriptEle.setAttribute('src', fileURL);
  document.body.appendChild(scriptEle);

  if (onLoad) {
    scriptEle.addEventListener('load', onLoad);
  }
  if (onError) {
    scriptEle.addEventListener('error', onError);
  }
};

/**
 * @function
 * @description - Get Omniture object
 *
 * @returns {object} - Omniture object
 */
export const getOmniture = () => {
  let s = null;
  if (window.s_gi && typeof window.s_gi === 'function') {
    s = window.s_gi('mmtprod');
  }
  return s as OmnitureInstance;
};

const omniture = new OmnitureService();
omniture.init({
  setVars,
});

export const trackOmnitureClickEvent = (
  key: ClickEventKey,
  data: Omit<OmnitureCallerArgs, 'CLICK_EVENT'> = {},
) => {
  trackOmnitureGenericEvent({
    CLICK_EVENT: ClickEventSchema[key],
    UI_TYPE: 'travelplex_newUI',
    ...getPageVars(),
    ...data,
  });
};

export const trackPageLoadEvent = () => {
  trackOmnitureGenericEvent({}, true); // this tracks load event. pageName will be autofilled internally
};

export const trackOmnitureGenericEvent = (
  data: OmnitureCallerArgs,
  isPageLoadEvent = false,
) => {
  const fullData: OmnitureEventData = {
    ...data,
    UI_TYPE: 'travelplex_newUI',
    ...getPageVars(),
  };
  // Transform Key Names from caller to omniture specific ones
  const mappedData: Record<string, unknown> = Object.entries(fullData).reduce(
    (acc, [_key, value]) => {
      const key = _key as keyof OmnitureEventData;
      const mappedKey = keyMapping[key];
      if (mappedKey) {
        acc[mappedKey] = value;
      }
      return acc;
    },
    {} as Record<string, unknown>,
  );
  if (isPageLoadEvent) {
    omniture.trackLoadEvent(mappedData);
  } else {
    omniture.trackClickEvent(mappedData);
  }
};

export const trackMsg = (
  data: Pick<
    OmnitureCallerArgs,
    | 'VAR_CONVERSATION_ID'
    | 'VAR_MSG_ROLE'
    | 'VAR_CHAT_TYPE'
    | 'VAR_CHAT_SRC'
    | 'CHAT_SRC'
  >,
) => trackOmnitureGenericEvent(data);
