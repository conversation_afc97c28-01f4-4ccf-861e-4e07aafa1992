// import { NativeModules, Platform } from 'react-native';

import { getMmtAuth } from './user';

export const isNetworkAvailable = () => {
  return true;
};

/**
 * Example value of iOS headers
 * ```
 *   {
 *     "__type": "ios",
 *     "Accept": "application/json",
 *     "Accept-Encoding": "gzip",
 *     "Content-Type": "application/json",
 *     "User-Agent": "MakeMyTrip/9.3.5 (iPhone; iOS 18.2)",
 *     "cookie": "mmtAuth=\"MAT1e3cab555e27f2cedbdbe89bf0d8c56a6b37a444d8a848ebcb8ab9f5c991ea43d9abb228e6a727dbe9167765839c3331cP\"",
 *     "currency": "inr",
 *     "deviceId": "97A092C8-3C99-47EC-B852-444624D44C6E",
 *     "entity-name": "india",
 *     "language": "eng",
 *     "mmt-auth": "MAT1e3cab555e27f2cedbdbe89bf0d8c56a6b37a444d8a848ebcb8ab9f5c991ea43d9abb228e6a727dbe9167765839c3331cP",
 *     "os": "iPhone OS_18.2",
 *     "osVersion": "18.2",
 *     "region": "in",
 *     "tid": "IM_97A092C8-3C99-22EC-B852-300624D4446E",
 *     "timezone": "Asia/Kolkata",
 *     "user-country": "in",
 *     "user-currency": "inr",
 *     "username": "MXIPH",
 *     "usr-mcid": "48160187439631741757907222291329055452",
 *     "ver": "9.3.5",
 *     "vid": ""
 *   }
 * ```
 * */
type IosAppNetworkHeaders = {
  __type: 'ios';
  os: string;
  osVersion: string;
  vid: string;
  tid: string;
  'usr-mcid': string;
  ver: string;
  cookie: string;
  'mmt-auth': string;
  deviceId: string;
  'User-Agent': string;
  currency: string;
  'user-currency': string;
  region: string;
  'user-country': string;
  language: string;
  'entity-name': string;
  timezone: string;
};

/**
 *  Example value of Android headers
 * ```
 * {
 *     "__type": "android",
 *     "Accept": "application/json",
 *     "Content-Type": "application/json",
 *     "User-Agent": "MakeMyTrip/10.2.0 (Android 16; Build/BP22.250221.010)",
 *     "appVersion": "10.2.0",
 *     "auth": "MAT196d8bd0010988ffce6507bb02414d588660a333c0316db849b90b8122210b14352a242eee16b58e4abf444727cdde9a9P",
 *     "backup_auth": "mmtAuth=\"MAT196d8bd0010988ffce6507bb02414d588660a333c0316db849b90b8122210b14352a242eee16b58e4abf444727cdde9a9P\"",
 *     "cookie": "mmtAuth=\"MAT196d8bd0010988ffce6507bb02414d588660a333c0316db849b90b8122210b14352a242eee16b58e4abf444727cdde9a9P\"",
 *     "currency": "inr",
 *     "deviceid": "89f123c336cd5d8c",
 *     "entity-name": "india",
 *     "language": "eng",
 *     "mcid": "54111272544674945336686720675019816003",
 *     "mmt-auth": "MAT196d8bd0010988ffce6507bb02414d588660a333c0316db849b90b8122210b14352a242eee16b58e4abf444727cdde9a9P",
 *     "org": "mmt",
 *     "os": "Android 16",
 *     "pemail": "",
 *     "platform": "mobile",
 *     "region": "in",
 *     "tenant": "MMT",
 *     "tid": "AI_89f224c336cd5d8c",
 *     "user-country": "in",
 *     "user-currency": "INR",
 *     "ver": "10.2.0",
 *     "vid": "vid"
 * ```}
 * */
export type AppNetworkHeaders = {
  __type: 'android';
  os: string;
  ver: string;
  'User-Agent': string;
  org: string;
  vid: string; // visitor id
  mcid: string;
  tid: string;
  cookie: string;
  'mmt-auth': string;
  backup_auth: string;
  deviceid: string;
  pemail: string; // primary email
  region: string;
  language: string;
  currency: string;
  'user-currency': string;
  'user-country': string;
  'entity-name': string;
  GDPR_C: string; // GDPR consent
  tenant: 'MMT';
  platform: 'mobile';
  appVersion: string;
};

export type DesktopNetworkHeaders = {
  __type: 'desktop';
  os: string;
  ver: string;
  'User-Agent': string;
  org: string;
  vid: string; // visitor id
  mcid: string;
  tid: string;
  cookie: string;
  'mmt-auth': string;
  backup_auth: string;
  deviceid: string;
  pemail: string; // primary email
  region: string;
  language: string;
  currency: string;
  'user-currency': string;
  'user-country': string;
  'entity-name': string;
  GDPR_C: string; // GDPR consent
  tenant: 'MMT';
  platform: 'mobile';
  appVersion: string;
};

export type AppHeaders =
  | IosAppNetworkHeaders
  | AppNetworkHeaders
  | DesktopNetworkHeaders;

export const getAppHeaders = async (): Promise<AppHeaders> => {
  const mmtAuth = await getMmtAuth();

  return {
    __type: 'desktop',
    os: 'Windows 10',
    ver: '',
    'User-Agent': '',
    org: '',
    vid: '',
    mcid: '',
    tid: '',
    cookie: mmtAuth ? `mmtAuth="${mmtAuth}"` : '',
    'mmt-auth': mmtAuth ?? '',
    backup_auth: mmtAuth ? `mmtAuth="${mmtAuth}"` : '',
    deviceid: '',
    pemail: '',
    region: '',
    language: '',
    currency: '',
    'user-currency': '',
    'user-country': '',
    'entity-name': '',
    GDPR_C: '',
    tenant: 'MMT',
    platform: 'mobile',
    appVersion: '',
  };
};
