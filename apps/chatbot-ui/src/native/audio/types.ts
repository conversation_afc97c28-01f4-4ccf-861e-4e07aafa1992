/**
 * TypeScript interface for AudioStreamModule React Native native module
 *
 * Provides real-time audio recording and streaming capabilities
 *
 * Platform Differences:
 * - Android: Uses configured sample rate and format
 * - iOS: Uses hardware sample rate (typically 48kHz) to avoid format conversion overhead
 *
 * @example
 * ```typescript
 * import { NativeModules } from 'react-native';
 * const { AudioStreamModule } = NativeModules;
 *
 * // Recommended flow: Request permission first
 * try {
 *   await AudioStreamModule.requestMicrophonePermission();
 *   const result = await AudioStreamModule.startRecording(config);
 * } catch (error) {
 *   console.error('Permission denied or recording failed:', error);
 * }
 * ```
 *
 * <AUTHOR>
 */
import { TtsModuleInterface } from './tts-types';

// Audio Configuration Interface
export interface AudioConfig {
  /** Sample rate in Hz (default: 16000 for speech) */
  sampleRate?: number;
  /** Number of audio channels (1 = mono, 2 = stereo, default: 1) */
  channels?: number;
  /** Bit depth (8 or 16, default: 16) */
  bitDepth?: number;
  /** Buffer size in bytes (default: 4096) */
  bufferSize?: number;
  /** Locale for speech recognition (default: "en-IN") */
  locale?: string;
}

// Streaming Configuration Interface
export interface StreamingConfig {
  /** WebSocket URL for streaming (must start with ws:// or wss://) */
  websocketUrl: string;
  /** Session ID for the streaming session */
  sessionId?: string;
  /** Custom headers for authentication */
  headers?: Record<string, any>;
  /** Number of reconnection attempts */
  reconnectAttempts?: number;
}

// Combined configuration for starting recording
export interface AudioStreamConfig extends AudioConfig, StreamingConfig {}

// Return Types
export interface StartRecordingResult {
  status: 'recording_started';
  sessionId: string;
}

export interface StopRecordingResult {
  status: 'recording_stopped';
  /** Recording duration in milliseconds */
  duration: number;
}

// TODO: remove this
export const ConnectionErrorCodes = {
  CONNECTION_FAILED: 'CONNECTION_FAILED',
} as const;

// Removed RecordingStatus interface

export interface MicrophonePermissionResult {
  /** Permission status */
  status: 'granted';
  /** Additional message about the permission */
  message: string;
}

type AudioModuleState = {
  isConnected: boolean;
  isRecording: boolean;
  isStopping: boolean;
};

// Main Module Interface
export interface AudioStreamModuleInterface {
  /**
   * Request microphone permission
   * @returns Promise that resolves with permission result or rejects if denied
   */
  requestMicrophonePermission(): Promise<MicrophonePermissionResult>;

  /**
   * Start audio recording and streaming
   * @param config Configuration object containing audio and streaming parameters
   * @returns Promise that resolves with recording start result
   * @note It's recommended to call requestMicrophonePermission() first
   */
  startRecording(config: AudioStreamConfig): Promise<StartRecordingResult>;

  /**
   * Stop audio recording and streaming
   * @returns Promise that resolves with recording stop result
   */
  stopRecording(): Promise<StopRecordingResult>;

  // Removed getRecordingStatus and configureAudio methods
  getStatus(): Promise<AudioModuleState>;

  speakText(
    text: string,
    config: { url: string; headers: Record<string, string>; timeoutMs: number },
  ): Promise<void>;
}

// Module Declaration for React Native
declare module 'react-native' {
  interface NativeModulesStatic {
    AudioStreamModule: AudioStreamModuleInterface & TtsModuleInterface;
  }
}

// Default export for easier importing
