import { default as UUID } from 'react-native-uuid';
import { Endpoints } from '../../network/endpoints';
import { getHeadersAndContext } from '../../network/headers';
import { AudioStreamEventManager } from './audio-event-manager';
import { getAudioStreamModule } from './audio-native-module';
import { sleep } from '../../utils/functionUtils';
import { GetNewChatViewResponse } from '../../network/api/newChatViewApi';
import { audioStreamLogger } from './audio-stream-logger';
import { isObject } from '../../types/typeUtils';
import {
  AudioError,
  EventEmitterError,
  NativeCallErrorCodes,
  StreamEventErrorCodes,
} from './audio-errors';

export type AudioStreamCallbacks = {
  onStartRecording: () => void;
  onStopRecording: (finished?: boolean, reason?: string) => void;
  onError: (error: AudioError) => void;
  onSpeechResultReceived: (data: string) => void;
};

/**
 * Controller for an active audio stream session.
 *
 * @property stop - Gracefully stop the audio stream and clean up listeners.
 * @property cancel - Force-cancel the audio stream and clean up listeners.
 */
export interface AudioStreamController {
  stop: (finished: boolean, reason?: string) => Promise<void>;
  isActive: boolean;
}

/**
 * Audio Stream Module (React Native)
 *
 * Provides a unified interface for starting, stopping, and managing real-time audio recording and streaming sessions
 * with speech-to-text (STT) capabilities. Handles permissions, event management, and error handling for both native and web platforms.
 *
 * This module interacts bidirectionally with the native layer: it sends commands (start/stop/cancel) to the native audio module
 * and receives events/results (recording status, errors, speech-to-text results) from the native side.
 *
 * Logging for audio streaming events is handled via the dedicated logger in './audio-stream-logger'.
 *
 * Key Exports:
 * - AudioStreamCallbacks: Callback interface for audio stream lifecycle events
 * - AudioStreamController: Interface for controlling an active audio stream session
 * - startAudioStream: Main function to initiate a new audio streaming session
 *
 * Usage:
 *   const controller = await startAudioStream(callbacks, configSettingsData);
 *   // ...
 *   await controller.stop(true, 'done');
 *
 * <AUTHOR>
 */

// Type guard for AudioError
function isAudioError(error: unknown): error is AudioError {
  return (
    isObject(error) &&
    typeof error._tag === 'string' &&
    (error._tag === 'NativeCallError' || error._tag === 'EventEmitterError') &&
    typeof error.code === 'string'
  );
}

function createNativeCallError(
  error: unknown,
  defaultCode: keyof typeof NativeCallErrorCodes,
): AudioError {
  const code =
    isObject(error) &&
    typeof error === 'object' &&
    'code' in error &&
    typeof error.code === 'string'
      ? (error.code as keyof typeof NativeCallErrorCodes)
      : defaultCode;
  return {
    _tag: 'NativeCallError',
    code,
    details: error,
  };
}

/**
 * Start a new audio streaming session with speech-to-text.
 *
 * Handles permission requests, event listener setup, and manages the audio stream lifecycle.
 *
 * @param callbacks - Object implementing AudioStreamCallbacks for lifecycle events.
 * @param configSettingsData - Optional configuration (e.g., WebSocket URL, timeouts) from chat view config.
 * @returns Promise resolving to an AudioStreamController, or null if permission denied.
 */
export async function startAudioStream(
  callbacks: AudioStreamCallbacks,
  configSettingsData?: GetNewChatViewResponse | undefined,
): Promise<AudioStreamController | null> {
  const eventManager = new AudioStreamEventManager();
  const AudioStreamModule = getAudioStreamModule();
  try {
    audioStreamLogger.info('requestMicrophonePermission');
    await AudioStreamModule.requestMicrophonePermission();
    audioStreamLogger.info('✅ Permission granted');
  } catch (error) {
    audioStreamLogger.error(
      'requestMicrophonePermission: ' + JSON.stringify(error, null, 4),
    );
    let err: AudioError;
    if (isAudioError(error)) {
      err = error;
    } else {
      err = createNativeCallError(
        error,
        NativeCallErrorCodes.MICROPHONE_PERMISSION_DENIED,
      );
    }
    callbacks.onError(err);
    audioStreamLogger.error('❌ Permission denied:', error);
    return null;
  }

  const requestId = UUID.v4();
  let didStart = false;
  let isActive = true;
  let eventListenersSetup = false;
  const { socketHeaders } = await getHeadersAndContext();
  // Start recording with speech recognition preset
  const websocketUrl =
    configSettingsData?.voiceConfig?.wsUrl || Endpoints.STT_WEB_SOCKET;
  const connectTimeoutMs =
    configSettingsData?.voiceConfig?.connectTimeoutMs || 10_000;
  const config = {
    websocketUrl,
    requestId,
    connectTimeoutMs,
    headers: { ...socketHeaders() },
  };

  // Set up event listeners
  const setupEventListeners = () => {
    if (eventListenersSetup) {
      return;
    }

    eventManager.onServerMessage((event) => {
      if (!isActive) {
        audioStreamLogger.error('onServerMessage called when isActive = false');
        return;
      }
      callbacks.onSpeechResultReceived(event.message);
    });

    eventManager.onConnectionStatus((event) => {
      audioStreamLogger.info('Connection status:', event.status);
      if (!isActive) {
        return;
      }
      if (event.status === 'connected') {
        didStart = true;
      }
      if (event.status === 'error') {
        const err: EventEmitterError = {
          _tag: 'EventEmitterError',
          code: StreamEventErrorCodes.WS_ERROR,
          details: event,
        };
        callbacks.onError(err);
      }
    });

    eventManager.onError((event) => {
      if (!isActive) {
        return;
      }
      audioStreamLogger.error('Audio stream error:', event);
      callbacks.onError(event);
    });

    eventListenersSetup = true;
  };

  const cleanupEventListeners = () => {
    eventManager.removeAllListeners();
    eventListenersSetup = false;
  };

  // Controller object to return
  const controller: AudioStreamController = {
    stop: async (finished: boolean, reason?: string) => {
      if (!isActive) {
        audioStreamLogger.warn('Audio stream already stopped');
        return;
      }
      if (!didStart) {
        audioStreamLogger.info(
          `Audio stream stopping before it got started. finished=${finished} reason=${reason}`,
        );
        return;
      }

      try {
        AudioStreamModule.stopRecording();
        callbacks.onStopRecording(finished, reason);
      } catch (error) {
        let err: AudioError;
        if (isAudioError(error)) {
          err = error;
        } else {
          err = createNativeCallError(error, NativeCallErrorCodes.AUDIO_STOP_ERROR);
        }
        audioStreamLogger.error('Failed to stop recording:', error);
        callbacks.onError(err);
        throw err;
      } finally {
        isActive = false;
        cleanupEventListeners();
      }
    },

    get isActive() {
      return isActive;
    },
  };

  // Start recording
  setupEventListeners();

  const startRecording = async () => {
    try {
      isActive = true;
      callbacks.onStartRecording();
      let audioModuleState = await AudioStreamModule.getStatus();
      let stopAttempts = 0;
      const start = Date.now();
      while (audioModuleState.isRecording && stopAttempts < 3) {
        stopAttempts++;
        if (!audioModuleState.isStopping) {
          await AudioStreamModule.stopRecording();
        }
        await sleep(stopAttempts * 300);
        audioModuleState = await AudioStreamModule.getStatus();
      }
      const result = await AudioStreamModule.startRecording(config);
      audioStreamLogger.info('Recording started:', result.sessionId);
    } catch (error) {
      let err: AudioError;
      if (isAudioError(error)) {
        err = error;
      } else {
        err = createNativeCallError(error, NativeCallErrorCodes.AUDIO_INIT_FAILED);
      }
      audioStreamLogger.error('Failed to start recording:', error);
      isActive = false;
      cleanupEventListeners();
      callbacks.onError(err);
    }
  };

  // Execute the async function but don't await it
  startRecording();

  return controller;
}
