// Audio error codes and types for audio streaming module

/**
 * Native call error codes for audio streaming setup failures.
 * These errors occur before the audio stream is considered active.
 */
export const NativeCallErrorCodes = {
  /** User denied microphone permission */
  MICROPHONE_PERMISSION_DENIED: 'MICROPHONE_PERMISSION_DENIED',
  /** Error occurred while requesting microphone permission */
  MICROPHONE_PERMISSION_ERROR: 'MICROPHONE_PERMISSION_ERROR',
  /** Failed to connect to the WebSocket server */
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  /** Failed to initialize audio recording device */
  AUDIO_INIT_FAILED: 'AUDIO_INIT_FAILED',
  /** Attempted to start recording when already recording */
  ALREADY_RECORDING: 'ALREADY_RECORDING',
  /** Invalid or missing configuration for audio or streaming */
  CONFIGURATION_ERROR: 'CONFIGURATION_ERROR',
  /** Failed to stop the recording */
  AUDIO_STOP_ERROR: 'AUDIO_STOP_ERROR',
} as const;

/**
 * Type for all possible initialization error codes.
 */
export type BridgeErrorCode =
  (typeof NativeCallErrorCodes)[keyof typeof NativeCallErrorCodes];

/**
 * Stream event error codes for runtime failures after the stream is active.
 * These errors are emitted as events during the audio stream lifecycle.
 */
export const StreamEventErrorCodes = {
  /** WebSocket connection dropped or protocol error */
  WS_ERROR: 'WS_ERROR',
  /** Audio device or recording failure during streaming */
  AUDIO_ERROR: 'AUDIO_ERROR',
  /** Speech recognition server returned an error */
  RECOGNITION_ERROR: 'RECOGNITION_ERROR',
  /** No audio or server response for a period of time */
  TIMEOUT: 'TIMEOUT',
  /** Recording stopped unexpectedly (e.g., system interruption) */
  UNEXPECTED_STOP: 'UNEXPECTED_STOP',
} as const;

/**
 * Type for all possible async error codes.
 */
export type EventEmitterErrorCode =
  (typeof StreamEventErrorCodes)[keyof typeof StreamEventErrorCodes];

/**
 * Union type for all audio error codes (init + async).
 */
export type AudioErrorCode = BridgeErrorCode | EventEmitterErrorCode;

/**
 * Error type for errors that occur during native method calls (bridge errors).
 */
export interface NativeCallError {
  _tag: 'NativeCallError';
  code: BridgeErrorCode;
  details?: unknown;
}

/**
 * Error type for errors that occur during async event emission (event emitter errors).
 */
export interface EventEmitterError {
  _tag: 'EventEmitterError';
  code: EventEmitterErrorCode;
  details?: unknown;
}

/**
 * Union type for all audio errors.
 */
export type AudioError = NativeCallError | EventEmitterError;

/**
 * Mapping from error code to user-displayable error message.
 * Extend this map to provide custom messages for each error code.
 */
type ErrorDisplayMap = Partial<Record<AudioErrorCode, string>>;

/**
 * Default error display messages for known error codes.
 */
export const errDisplayMessages: ErrorDisplayMap = {
  MICROPHONE_PERMISSION_DENIED: 'Please allow microphone access to continue.',
  MICROPHONE_PERMISSION_ERROR: 'Please allow microphone access to continue.',
};

/**
 * Returns a user-displayable error message for a given AudioError.
 * Falls back to a generic message if no mapping is found.
 * @param err The AudioError object
 * @returns User-friendly error message string
 */
export function getErrorMessage(err: AudioError): string {
  return errDisplayMessages[err.code] || 'Something went wrong!';
}
