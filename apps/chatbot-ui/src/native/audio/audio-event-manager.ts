// Event Data Types
import { EmitterSubscription } from 'react-native';

import { AudioError } from './audio-errors';

export interface ServerMessageEvent {
  type: 'server_message';
  /** Message from the server (e.g., transcription results) */
  message: string;
}

export interface ConnectionStatusEvent {
  /** Connection status ('connected', 'disconnected', 'connecting', etc.) */
  status: 'connected' | 'disconnected' | 'connecting' | 'error';
  /** Additional status message */
  message?: string;
}

// Event Names Constants
export const AudioStreamEvents = {
  SERVER_MESSAGE: 'AudioStreamModule_ServerMessage',
  CONNECTION_STATUS: 'AudioStreamModule_ConnectionStatus',
  ERROR: 'AudioStreamModule_Error',
  // TODO remove this, not used I guess
  PERMISSION_STATUS: 'AudioStreamModule_PermissionStatus',
} as const;
// Event Listener Types
export type MessageListener = (event: ServerMessageEvent) => void;
export type ConnectionStatusListener = (event: ConnectionStatusEvent) => void;
export type ErrorListener = (error: AudioError) => void;

/**
 * AudioStreamEventManager
 *
 * Helper class for managing event listeners between the JavaScript and native layers for audio streaming.
 * Handles registration and cleanup of listeners for server messages, connection status, and error events
 * emitted by the native audio streaming module (via DeviceEventEmitter).
 *
 * Usage:
 *   const eventManager = new AudioStreamEventManager();
 *   eventManager.onServerMessage((event) => { ... });
 *   eventManager.onConnectionStatus((event) => { ... });
 *   eventManager.onError((event) => { ... });
 *   // ...
 *   eventManager.removeAllListeners();
 */
export class AudioStreamEventManager {
  private listeners: Map<string, EmitterSubscription> = new Map();

  /**
   * Register a listener for server message events (e.g., transcription results).
   * @param listener Callback invoked with ServerMessageEvent when a message is received from the server.
   * @returns EmitterSubscription for the registered listener.
   */
  onServerMessage(listener: MessageListener): EmitterSubscription {
    const subscription = this.addListener(
      AudioStreamEvents.SERVER_MESSAGE,
      listener,
    );
    return subscription;
  }

  /**
   * Register a listener for connection status events (e.g., connected, disconnected, error).
   * @param listener Callback invoked with ConnectionStatusEvent when connection status changes.
   * @returns EmitterSubscription for the registered listener.
   */
  onConnectionStatus(listener: ConnectionStatusListener): EmitterSubscription {
    const subscription = this.addListener(
      AudioStreamEvents.CONNECTION_STATUS,
      listener,
    );
    return subscription;
  }

  /**
   * Register a listener for error events (e.g., recording or websocket errors).
   * @param listener Callback invoked with ErrorEvent when an error occurs.
   * @returns EmitterSubscription for the registered listener.
   */
  onError(listener: ErrorListener): EmitterSubscription {
    const subscription = this.addListener(AudioStreamEvents.ERROR, listener);
    return subscription;
  }

  /**
   * Remove all registered event listeners managed by this instance.
   * Call this to prevent memory leaks when the audio stream session ends.
   */
  removeAllListeners(): void {
    this.listeners.forEach((subscription) => subscription.remove());
    this.listeners.clear();
  }

  private addListener(
    eventName: string,
    listener: (...args: any[]) => void,
  ): EmitterSubscription {
    // Remove existing listener if any
    const existing = this.listeners.get(eventName);
    if (existing) {
      existing.remove();
    }

    // Add new listener using DeviceEventEmitter
    const { DeviceEventEmitter } = require('react-native');
    const subscription = DeviceEventEmitter.addListener(eventName, listener);

    this.listeners.set(eventName, subscription);
    return subscription;
  }
}
