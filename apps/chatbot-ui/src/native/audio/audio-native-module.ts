import { AudioStreamModuleInterface } from './types';
import { NativeModules } from 'react-native';
import { TtsModuleInterface } from './tts-types';

export function getAudioStreamModule(): AudioStreamModuleInterface &
  TtsModuleInterface {
  const { AudioStreamModule } = NativeModules;
  return AudioStreamModule;
}

export function hasAudioStreamModule(): boolean {
  const { AudioStreamModule } = NativeModules;
  return !!AudioStreamModule;
}

export function hasTtsNativeSupport(): boolean {
  if (!hasAudioStreamModule()) {
    return false;
  }
  const { speakText } = getAudioStreamModule();
  return typeof speakText === 'function';
}
