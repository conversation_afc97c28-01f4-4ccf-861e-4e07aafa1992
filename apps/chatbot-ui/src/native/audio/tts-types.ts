import { NativeModule } from 'react-native';

export interface TtsConfig {
  url: string;
  timeoutMs: number;
  readTimeoutMs: number;
  useStreaming?: boolean;
  cacheEnabled?: boolean;
  language?: string;
  modelOverride?: string;
  headers?: Record<string, string|undefined>;
}

export interface TtsModuleInterface extends NativeModule {
  // Constants
  EVENT_TTS_PROGRESS: string;
  EVENT_TTS_STATE: string;

  // Methods
  speakText(text: string, taskId: string, config: TtsConfig): Promise<void>;

  pauseTts(taskId: string): Promise<void>;

  resumeTts(taskId: string): Promise<void>;

  stopTts(taskId: string): Promise<void>;
}
