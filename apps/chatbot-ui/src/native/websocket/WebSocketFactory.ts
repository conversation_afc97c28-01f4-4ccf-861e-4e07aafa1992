import { WebSocketWrapper } from './WebSocketWrapper';
import { WsDefaultImpl } from './impl/WsDefaultImpl';
import { hasNativeSocketModule, WsNativeImpl } from './impl/WsNativeImpl';
import { wsLogger } from './WsLogger';

export function createWsObject(options: {
  url: string;
  protocols?: string | string[];
  useNative?: boolean;
}): WebSocketWrapper {
  wsLogger.info(`createWsObject: ${JSON.stringify(options, null, 2)}`);
  if (hasNativeSocketModule() && options.useNative) {
    return new WsNativeImpl(options.url);
  }
  return new WsDefaultImpl(options.url, options.protocols);
}
