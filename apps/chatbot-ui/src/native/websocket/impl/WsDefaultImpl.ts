import { WebSocketWrapper, WsReadyState } from '../WebSocketWrapper';

export class WsDefaultImpl implements WebSocketWrapper {
  private ws: WebSocket;
  private openListeners: Array<() => void> = [];
  private messageListeners: Array<(event: MessageEvent) => void> = [];
  private errorListeners: Array<(event: Event) => void> = [];
  private closeListeners: Array<(event: CloseEvent) => void> = [];

  constructor(url: string, protocols?: string | string[]) {
    this.ws = new WebSocket(url, protocols);
    this.ws.onopen = () => {
      this.openListeners.forEach((listener) => listener());
    };
    this.ws.onmessage = (event) => {
      this.messageListeners.forEach((listener) => listener(event));
    };
    this.ws.onerror = (event) => {
      this.errorListeners.forEach((listener) => listener(event));
    };
    this.ws.onclose = (event) => {
      this.closeListeners.forEach((listener) => listener(event));
    };
  }

  addOnOpenListener(listener: () => void): void {
    this.openListeners.push(listener);
  }

  addOnMessageListener(listener: (event: MessageEvent) => void): void {
    this.messageListeners.push(listener);
  }

  addOnErrorListener(listener: (event: Event) => void): void {
    this.errorListeners.push(listener);
  }

  addOnCloseListener(listener: (event: CloseEvent) => void): void {
    this.closeListeners.push(listener);
  }

  send(data: string): void {
    this.ws.send(data);
  }

  close(code?: number, reason?: string): void {
    this.ws.close(code, reason);
  }

  getReadyState(): WsReadyState {
    return this.ws.readyState as WsReadyState;
  }
}
