import { NativeEventEmitter, NativeModules } from 'react-native';
import {
  type WebSocketWrapper,
  WS_READY_STATE_CLOSED,
  WS_READY_STATE_CLOSING,
  WS_READY_STATE_CONNECTING,
  WS_READY_STATE_OPEN,
  type WsReadyState,
} from '../WebSocketWrapper';
import { wsLogger } from '../WsLogger';

let socketCounter = 0;

function generateSocketId() {
  socketCounter += 1;
  return `ws_${Date.now()}_${socketCounter}`;
}

export function hasNativeSocketModule(): boolean {
  const { NativeWebSocket } = NativeModules;
  return typeof NativeWebSocket?.connect === 'function';
}

export class WsNativeImpl implements WebSocketWrapper {
  private socketId: string;
  private openListeners: Array<() => void> = [];
  private messageListeners: Array<(event: MessageEvent) => void> = [];
  private errorListeners: Array<(event: Event) => void> = [];
  private closeListeners: Array<(event: CloseEvent) => void> = [];
  private readyState: WsReadyState = WS_READY_STATE_CONNECTING;
  private subscriptions: Array<{ event: string; sub: any }> = [];

  constructor(url: string) {
    wsLogger.info('creating native ws');
    this.socketId = generateSocketId();

    const { NativeWebSocket } = NativeModules;
    const emitter = new NativeEventEmitter(NativeWebSocket);

    // Register native event listeners and filter by socketId
    this.subscriptions.push({
      event: 'onOpen',
      sub: emitter.addListener('onOpen', (event) => {
        wsLogger.info(
          `onOpen this.socketId=${this.socketId}, event.socketId=${event.socketId}`,
        );
        if (event.socketId === this.socketId) {
          this.readyState = WS_READY_STATE_OPEN;
          this.openListeners.forEach((fn) => fn());
        }
      }),
    });
    this.subscriptions.push({
      event: 'onMessage',
      sub: emitter.addListener('onMessage', (event) => {
        if (event.socketId === this.socketId) {
          const msgEvent = { data: event.data } as MessageEvent;
          this.messageListeners.forEach((fn) => fn(msgEvent));
        }
      }),
    });
    this.subscriptions.push({
      event: 'onError',
      sub: emitter.addListener('onError', (event) => {
        wsLogger.info(
          `onError this.socketId=${this.socketId}, event.socketId=${event.socketId}`,
        );

        if (event.socketId === this.socketId) {
          const errEvent = { error: event.error } as unknown as Event;
          this.errorListeners.forEach((fn) => fn(errEvent));
        }
      }),
    });
    this.subscriptions.push({
      event: 'onClose',
      sub: emitter.addListener('onClose', (event) => {
        wsLogger.info(
          `onClose this.socketId=${this.socketId}, event.socketId=${event.socketId}`,
        );

        if (event.socketId === this.socketId) {
          this.readyState = WS_READY_STATE_CLOSED;
          const closeEvent = {
            code: event.code,
            reason: event.reason,
          } as CloseEvent;
          this.closeListeners.forEach((fn) => fn(closeEvent));
          this.cleanup();
        }
      }),
    });

    NativeWebSocket.connect(this.socketId, url)
      .then(() => {
        this.readyState = WS_READY_STATE_OPEN;
        return;
      })
      .catch(() => {
        this.readyState = WS_READY_STATE_CLOSED;
      });
  }

  addOnOpenListener(listener: () => void): void {
    this.openListeners.push(listener);
  }

  addOnMessageListener(listener: (event: MessageEvent) => void): void {
    this.messageListeners.push(listener);
  }

  addOnErrorListener(listener: (event: Event) => void): void {
    this.errorListeners.push(listener);
  }

  addOnCloseListener(listener: (event: CloseEvent) => void): void {
    this.closeListeners.push(listener);
  }

  send(data: string): void {
    const { NativeWebSocket } = NativeModules;
    NativeWebSocket.send(this.socketId, data);
  }

  close(code?: number, reason?: string): void {
    this.readyState = WS_READY_STATE_CLOSING;
    const { NativeWebSocket } = NativeModules;
    NativeWebSocket.close(this.socketId, code || 1000, reason || '');
    // cleanup will be called on onClose event
  }

  getReadyState(): WsReadyState {
    return this.readyState;
  }

  private cleanup() {
    // Remove all event listeners for this instance
    this.subscriptions.forEach(({ sub }) => sub.remove());
    this.subscriptions = [];
  }
}
