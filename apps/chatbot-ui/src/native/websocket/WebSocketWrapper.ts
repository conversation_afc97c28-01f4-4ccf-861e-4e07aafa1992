export const WS_READY_STATE_CONNECTING = 0;
export const WS_READY_STATE_OPEN = 1;
export const WS_READY_STATE_CLOSING = 2;
export const WS_READY_STATE_CLOSED = 3;
export type WsReadyState =
  | typeof WS_READY_STATE_CONNECTING
  | typeof WS_READY_STATE_OPEN
  | typeof WS_READY_STATE_CLOSING
  | typeof WS_READY_STATE_CLOSED;

export interface WebSocketWrapper {
  //  Listeners
  addOnOpenListener: (listener: () => void) => void;
  addOnMessageListener: (listener: (event: MessageEvent) => void) => void;
  addOnErrorListener: (listener: (event: Event) => void) => void;
  addOnCloseListener: (listener: (event: CloseEvent) => void) => void;
  //  Methods
  send: (data: string) => void;
  close: (code?: number, reason?: string) => void;
  getReadyState: () => WsReadyState;
}
