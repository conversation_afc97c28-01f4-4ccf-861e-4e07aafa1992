import { NativeModules, Platform } from 'react-native';
import { useAppStateStore } from '../store/app';
import { stopTtsIfPlaying } from '../store/audio-talkback/talkbackStore';

/*
 * @returns {boolean} Returns true if the deep link was not opened, false otherwise.
 * */
export function openDeepLink(url: string): boolean {
  if (!url) {
    return true;
  }
  stopTtsIfPlaying('deep_link_opened');
  const { deeplinkHandlers } = useAppStateStore.getState();
  if (deeplinkHandlers?.length) {
    const handled = deeplinkHandlers[0](url);
    if (handled) {
      return false;
    }
  }

  if (Platform.OS === 'ios') {
    const { triggerDismissHandler } = useAppStateStore.getState();
    if (typeof triggerDismissHandler === 'function') {
      triggerDismissHandler();
    }
  }

  // There is an issue in deeplink handling logic for bus and cabs. This is a workaround to fix it.
  let modifiedUrl: string = url;
  if (url.includes('?')) {
    modifiedUrl += '&keepRnInstance=true';
  } else {
    modifiedUrl += '?keepRnInstance=true';
  }
  NativeModules.GenericModule?.openDeepLink(modifiedUrl);
  return false;
}
