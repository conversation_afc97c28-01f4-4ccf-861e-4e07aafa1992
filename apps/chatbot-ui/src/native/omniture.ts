import { NativeModules } from 'react-native';
import { Logger } from '../utils/logger';
import { getPageVars } from '../utils/pageUtils';
import {
  ClickEventSchema,
  keyMapping,
  OmnitureEventData,
  OmnitureCallerArgs,
  ClickEventKey,
} from './omnitureConst';

export type BookmarkShownTypes = `bookmark_${string}`;
export type BookmarkClickTypes = `bookmark_${string}_clicked`;
export type CardMediaTypes = `media_${string}`;

export type H2GCardMediaTypes = 'link' | 'overlay';

export const trackOmnitureClickEvent = (
  key: ClickEventKey,
  data: Omit<OmnitureCallerArgs, 'CLICK_EVENT'> = {},
  debugPayload: string | null = null,
) => {
  trackOmnitureGenericEvent(
    {
      CLICK_EVENT: ClickEventSchema[key],
      UI_TYPE: 'travelplex_newUI',
      ...getPageVars(),
      ...data,
    },
    debugPayload || key,
  );
};

export const trackPageLoadEvent = (debugPayload: string | null = null) => {
  trackOmnitureGenericEvent({}, debugPayload); // this tracks load event. pageName will be autofilled internally
};

const omniLogger = Logger.createLogger({
  tag: 'MyraOmni',
  level: 'NONE',
});
export const trackOmnitureGenericEvent = (
  data: OmnitureCallerArgs,
  debugPayload: string | null = null,
) => {
  const { OmnitureModule } = NativeModules;
  if (!OmnitureModule || !OmnitureModule.trackState) {
    return;
  }
  const fullData: OmnitureEventData = {
    ...data,
    UI_TYPE: 'travelplex_newUI',
    ...getPageVars(),
  };
  // Transform Key Names from caller to omniture specific ones
  const mappedData: Record<string, unknown> = Object.entries(fullData).reduce(
    (acc, [_key, value]) => {
      const key = _key as keyof OmnitureEventData;
      const mappedKey = keyMapping[key];
      if (mappedKey) {
        acc[mappedKey] = value;
      }
      return acc;
    },
    {} as Record<string, unknown>,
  );
  omniLogger.debug(
    fullData.PROP_PAGE_NAME as string,
    mappedData,
    `debug = ${debugPayload}`,
  );
  OmnitureModule.trackState(fullData.PROP_PAGE_NAME, mappedData);
};

export const trackMsg = (
  data: Pick<
    OmnitureCallerArgs,
    | 'VAR_CONVERSATION_ID'
    | 'VAR_MSG_ROLE'
    | 'VAR_CHAT_TYPE'
    | 'VAR_CHAT_SRC'
    | 'CHAT_SRC'
  >,
  debugPayload: string | null = null,
) => trackOmnitureGenericEvent(data, debugPayload);
