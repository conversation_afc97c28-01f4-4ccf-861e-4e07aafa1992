import { getTrafficSource } from '../utils/webUtils';
import { canWriteToConversation } from '../utils/storeUtils';
import { fireLobLinkClickedEvent } from './bridge/webBridge';

export function openDeepLink(url: string): boolean {
  const trafficSource = getTrafficSource();
  const canOpenDeepLink = canWriteToConversation();
  if (!canOpenDeepLink || !url) {
    return false;
  }
  if (trafficSource === 'lob') {
    fireLobLinkClickedEvent({
      lobLink: url,
    });
  } else {
    window.location.href = url;
  }
  return true;
}
