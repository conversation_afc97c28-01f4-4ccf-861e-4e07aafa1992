export type OmniEntry<K, T> = {
  key: K;
  type: T;
};

export type ParentPageName = 'chat|travelplex';

export type SubPageName =
  | 'chat:home_page|travelplex'
  | 'chat:chat_page|travelplex'
  | 'chat:bookmark_page|travelplex'
  | 'chat:history_page|travelplex'
  | 'itinerary_list|travelplex'
  | 'itinerary_details|travelplex';

export type CardMediaTypes = `media_${string}`;

export type AskQuestionTypes = `ask_question_${string}`;

export type H2GCardMediaTypes = 'link' | 'overlay';

export type BookmarkShownTypes = `bookmark_${string}`;

export type BookmarkClickTypes = `bookmark_${string}_clicked`;

export type BookmarkEvents = BookmarkShownTypes | BookmarkClickTypes;

export type ChatSrc = `prompt_src_${'voice' | 'text' | 'sugg'}`;

export type MessageRole = 'msg_src_user' | 'msg_src_trvlplex';

export type ChatType = 'chat_new' | 'chat_old';

// Event Props
export const ClickEventSchema = {
  // BOOKMARK_OPTION_SHOWN: 'bookmark_shown',
  BOOKMARK_VIEWED: 'bookmark_viewed',
  BOOKMARK_ADDED: 'bookmark_added',
  BOOKMARK_REMOVED: 'bookmark_removed',
  BOOKMARK_CLICKED: 'bookmark_clicked',
  BOOKMARK_TAB_CLICK: 'bookmark_CATEGORY' as `bookmark_${string}`,
  // FEEDBACK_SHOWN: 'rating_shown',
  FEEDBACK_LIKE_CLICKED: 'rating_like',
  FEEDBACK_DISLIKE_CLICKED: 'rating_dislike',
  FEEDBACK_CHANGED_TO_LIKE: 'rating_changed_liked',
  FEEDBACK_CHANGED_TO_DISLIKE: 'rating_changed_disliked',
  LEAD_QUESTION_SHOWN: 'lead_ques_shown',
  SUGGESTION_PROMPTS_SHOWN: 'sugg_prompts_shown',
  // SUGGESTION_PROMPTS_VIEWED: 'sugg_prompts_viewed',
  SUGGESTION_PROMPTS_CLICKED: 'sugg_prompts_clicked',
  SUGGESTION_CTAS_CLICKED: 'sugg_ctas_clicked',
  CARDS_SHOWN: 'media_shown',
  CARDS_SCROLLED: 'media_scroll',
  CARD_CLICKED: 'media_clicked',
  CHAT_MENU_CLICK: 'chat_menu_clicked',
  NEW_CHAT_CLICK: 'new_chat_clicked',
  CHAT_HISTORY_CLICK: '_chat_history_clicked',
  OLD_CHAT_SELECTED: 'old_chat_selected',
  LOADER_TEXT_SHOWN: 'loader_text_shown',
  ITINERARY_SHOWN: 'chat_itinerary_shown',
  ITINERARY_CLICKED: 'chat_itinerary_viewed',
  VIEW_MORE_CLICKED: 'view_more_clicked',
  VIEW_LESS_CLICKED: 'view_less_clicked',
  H2G_CARD_CLICKED: 'h2g_card_button',
  LINK_IN_TEXT_CLICKED: 'textlink_clicked',
  // VIEW_MORE_SHOWN: 'view_more_shown',

  // MIMA
  TALK_TO_AGENT_CLICK: 'talk_to_agent_clicked',

  // Voice Input
  VOICE_OPTION_SHOWN: 'voice_option_shown',
  VOICE_INPUT_CLICKED: 'voice_input_clicked',
  VOICE_INPUT_CANCELLED: 'voice_input_cancelled',
  VOICE_TO_TEXT_SUCCESS: 'voice_to_text_success',
  VOICE_INPUT_FAILED: 'voice_input_failed',
  VOICE_ERROR: 'voice_error',

  // Trip Mode
  TRIPMODE_CTA_CLICKED: 'tripmode_clicked',
  TRIPMODE_ACTIVE: 'tripmode_active',
  TRIPMODE_CTA_SHOWN: 'tripmode_cta_shown',
  TRIPMODE_CTA_DISABLED: 'tripmode_cta_disabled',

  // Itinerary Actions
  GO_AHEAD_CLICKED: 'go_ahead_clicked',
  ASK_QUESTION_CLICKED: 'ask_question_clicked',
  CONTEXT_ITINERARY: 'context_itinerary',
  ITINERARY_CONTEXT_REMOVED: 'itinerary_context_removed',
  ITINERARY_PLACEHOLDER_CLICKED: 'itinerary_placeholder_clicked',
  ITINERARY_OPTION_CLICKED: 'itinerary_option_clicked',
  ASYNC_ITINERARY_INITIATED: 'ASYNC_ITINERARY_INITIATED',
  ASYNC_SHARE_CLICKED: 'ASYNC_SHARE_CLICKED',
  PLAN_A_TRIP_CLICKED: 'PLAN_A_TRIP_CLICKED',
  MYRA_CHAT_DESTINATION_SHOWN: 'myra_chat_destination_shown',
  MYRA_CHAT_DESTINATION_SELECTED: 'myra_chat_destination_selected',
  MYRA_CHAT_DESTINATION_CHANGE: 'myra_chat_destination_change_clicked',
  MYRA_CHAT_PHONE_CLICKED: 'myra_chat_phone',
  MYRA_CHAT_SUBMIT_CLICKED: 'submit clicked',

  //Page view events
  HELPING_HAND_SLOTS_SHOWN: 'Call_Slot_travelplex',
  BOOKMARK_SCREEN_SHOWN: 'Bookmark_Screen_Shown',
  HISTORY_SCREEN_SHOWN: 'History_Screen_Shown',
  ITINERARY_SCREEN_SHOWN: 'Itinerary_Screen_Shown',
  MESSAGE_LIST_SHOWN: 'MessageList_Shown',
  WELCOME_SHOWN: 'Welcome_Shown',
  ITINERARY_LIST_SHOWN: 'itinerary_list_shown',
} as const;
type Prop54Value = (typeof ClickEventSchema)[keyof typeof ClickEventSchema];
/**
 * format:
 *   [SEMANTIC_NAME]: [OMNITURE_KEY, VALUE_TYPE, IS_PROP]
 * */
type OmnitureSchema = {
  // Props. PROP_ suffix will be persisted in session
  PROP_LOB_DETAILS: OmniEntry<'m_c24', ParentPageName>;
  PROP_PAGE_NAME: OmniEntry<'m_c15', SubPageName>; // TODO check with Sajan -- do we need this prop? just eVar15 also work??
  PROP_PAGE_URL: OmniEntry<'m_c27', string>;
  PROP_PREV_PAGE_NAME: OmniEntry<'m_c23', SubPageName | null>;
  // PROP_CMP: OmniEntry<'m_c36', string>;
  VAR_JS_VERSION: OmniEntry<'m_list2', string>;
  // following props are not persisted in session
  CONTENT_TYPE: OmniEntry<
    'm_c14',
    | CardMediaTypes
    | BookmarkEvents
    | H2GCardMediaTypes
    | AskQuestionTypes
    | undefined
  >;
  CHAT_SRC: OmniEntry<'m_c12', ChatSrc>;
  // Vars
  VAR_MSG_ROLE: OmniEntry<'m_v14', MessageRole>;
  VAR_PAGE_NAME: OmniEntry<'m_v15', SubPageName>;
  VAR_CONVERSATION_ID: OmniEntry<'m_v16', string>;
  VAR_CHAT_TYPE: OmniEntry<'m_v19', ChatType>;
  VAR_CHAT_SRC: OmniEntry<'m_v12', ChatSrc>;
  VAR_COUNT: OmniEntry<'m_c44', `prompt_${number}`>;
  // Click Event
  CLICK_EVENT: OmniEntry<'m_c54', Prop54Value>;
  UI_TYPE: OmniEntry<'m_v50', 'travelplex_newUI' | 'travelplex_oldUI'>;
  // Error Event
  ERROR_EVENT: OmniEntry<'m_c22', string>;
};

type KeyMapping = {
  [P in keyof OmnitureSchema]: OmnitureSchema[P]['key'];
};
export const keyMapping: KeyMapping = {
  CHAT_SRC: 'm_c12',
  CLICK_EVENT: 'm_c54',
  CONTENT_TYPE: 'm_c14',
  PROP_LOB_DETAILS: 'm_c24',
  PROP_PAGE_NAME: 'm_c15',
  PROP_PAGE_URL: 'm_c27',
  PROP_PREV_PAGE_NAME: 'm_c23',
  VAR_CHAT_SRC: 'm_v12',
  VAR_CHAT_TYPE: 'm_v19',
  VAR_CONVERSATION_ID: 'm_v16',
  VAR_COUNT: 'm_c44',
  VAR_JS_VERSION: 'm_list2',
  VAR_MSG_ROLE: 'm_v14',
  VAR_PAGE_NAME: 'm_v15',
  UI_TYPE: 'm_v50',
  ERROR_EVENT: 'm_c22',
} as const;

// Payload for the caller
export type OmnitureEventData = {
  [P in keyof OmnitureSchema]?: OmnitureSchema[P]['type'];
};

export const CHANNEL = 'chat|travelplex';

// Session vars will be stored in this object and sent with every tracking call
export const VAR_JS_VERSION = 'm_list2';
export const JS_VERSION = '24.0'; // this is meaningless but required by native
export const sessionProps: Record<string, unknown> = {
  [VAR_JS_VERSION]: JS_VERSION,
};

export type PageData = Required<
  Pick<
    OmnitureEventData,
    | 'PROP_LOB_DETAILS'
    | 'PROP_PAGE_NAME'
    | 'VAR_PAGE_NAME'
    | 'PROP_PREV_PAGE_NAME'
    | 'PROP_PAGE_URL'
    | 'VAR_JS_VERSION'
  >
>;

export type OmnitureCallerArgs = Omit<OmnitureEventData, keyof PageData>;

export type ClickEventKey = keyof typeof ClickEventSchema;
