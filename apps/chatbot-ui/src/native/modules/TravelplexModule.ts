import { NativeModules } from 'react-native';

export interface TravelplexModuleType {
  onChatBotAsyncAction: (
    instanceId: number,
    actionName: string,
    payload: string,
  ) => Promise<unknown>;
  onChatBotAction: (
    instanceId: number,
    lob: string,
    unreadCount: string,
    payload: string | Record<string, unknown>,
  ) => void;
  onChatBotMinimized: (instanceId: number) => void;
  onJsReady: (instanceId: number) => void;
}

export function getTravelplexFn<
  K extends keyof TravelplexModuleType,
  R extends TravelplexModuleType[K],
>(key: K): R {
  const { TravelPlexModule } = NativeModules;
  if (typeof TravelPlexModule !== 'object') {
    // throw new Error('TravelPlexModule not found');
    return undefined as unknown as R;
  }
  if (typeof TravelPlexModule[key] !== 'function') {
    // throw new Error(`function '${key}' not found in TravelPlexModule`);
    return undefined as unknown as R;
  }
  return TravelPlexModule[key] as R;
}
