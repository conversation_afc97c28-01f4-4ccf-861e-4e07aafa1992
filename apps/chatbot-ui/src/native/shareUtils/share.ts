import { Linking } from 'react-native';
import { Logger } from '../../utils/logger';
const waShareLogger = Logger.createLogger({
  tag: 'waShare',
  level: 'VERBOSE',
});
export const waShare = async (waMessage: string) => {
  if (!waMessage) {
    return;
  }
  const waUrl = `https://wa.me?text=${encodeURIComponent(waMessage)}`;
  const supported = await Linking.canOpenURL(waUrl);
  if (!supported) {
    waShareLogger.debug('waShare - WA_NOT_INSTALLED');
    return 'WA_NOT_INSTALLED';
  }
  waShareLogger.debug('waShare - Linking.openURL', waUrl);
  return await Linking.openURL(waUrl);
};
