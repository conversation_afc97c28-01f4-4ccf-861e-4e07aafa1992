import { getMmtAuth as getMmtAuthUtils } from '../../utils/webUtils';
import { LoggedInUserDetails, IUser } from './type';

export class User implements IUser {
  async isUserLoggedIn(): Promise<boolean> {
    const mmtAuth = await getMmtAuthUtils();
    if (mmtAuth) {
      return true;
    }
    return false;
  }

  async getMmtAuth(): Promise<string | null> {
    return await getMmtAuthUtils();
  }

  async getLoggedInUserDetails(): Promise<LoggedInUserDetails | null> {
    return null;
  }
}

// Create a singleton instance
export const user = new User();

// Export individual methods for backward compatibility
export const isUserLoggedIn = user.isUserLoggedIn.bind(user);
export const getMmtAuth = user.getMmtAuth.bind(user);
export const getLoggedInUserDetails = user.getLoggedInUserDetails.bind(user);
