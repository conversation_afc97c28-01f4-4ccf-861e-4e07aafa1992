import { NativeModules } from 'react-native';
import { IUser, LoggedInUserDetails, UserDetailsRaw, Mobile } from './type';

export class User implements IUser {
  async isUserLoggedIn(): Promise<boolean> {
    const { UserSessionModule } = NativeModules;
    return UserSessionModule && UserSessionModule.isUserLoggedIn();
  }

  async getMmtAuth(): Promise<string | null> {
    try {
      if (await this.isUserLoggedIn()) {
        const userDetails = await this.getLoggedInUserDetails();
        return userDetails?.mmtAuth || null;
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  async getLoggedInUserDetails(): Promise<LoggedInUserDetails | null> {
    const { UserSessionModule } = NativeModules;
    try {
      const userDetailsJson = await UserSessionModule.getUserDetails();
      const userDetailsParsed: UserDetailsRaw =
        typeof userDetailsJson === 'string'
          ? JSON.parse(userDetailsJson)
          : (userDetailsJson as UserDetailsRaw);
      const {
        corpData,
        cupEmailid,
        cupFname,
        cupGender,
        cupLname,
        cupProfileType,
        emailVerified,
        imageUrl,
        loggedIn,
        loginType,
        mmtAuth,
        mobileContactNumberList,
        uuid,
        verifiedMobileNumber,
      } = userDetailsParsed;

      let mobileNumber: Mobile = { countryCode: '', mobileNumber: '' };

      if (verifiedMobileNumber && verifiedMobileNumber.length > 0) {
        mobileNumber = verifiedMobileNumber[0];
      } else if (mobileContactNumberList && mobileContactNumberList.length > 0) {
        mobileNumber = mobileContactNumberList[0];
      }

      return {
        createdAt: userDetailsParsed?.cupCrdt
          ? new Date(userDetailsParsed?.cupCrdt || 0)
          : null,
        email: cupEmailid,
        firstName: cupFname,
        fullName: `${cupFname} ${cupLname}`,
        gender: cupGender,
        imageUrl,
        isEmailVerified: emailVerified,
        isLoggedIn: loggedIn,
        lastName: cupLname,
        loginType,
        mmtAuth,
        mobileContactNumberList,
        mobileNumber,
        organizationId: corpData?.employee?.organizationId || null,
        profileType: cupProfileType,
        uuid,
        verifiedMobileNumbers: verifiedMobileNumber,
      };
    } catch (error) {
      return null;
    }
  }
}

// Create a singleton instance
export const user = new User();

// Export individual methods for backward compatibility
// Note: Using .bind() to ensure 'this' context is preserved when functions are called directly
export const isUserLoggedIn = user.isUserLoggedIn.bind(user);
export const getMmtAuth = user.getMmtAuth.bind(user);
export const getLoggedInUserDetails = user.getLoggedInUserDetails.bind(user);
