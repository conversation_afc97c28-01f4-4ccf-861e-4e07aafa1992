export type ProfileType = 'BUSINESS' | 'PERSONAL';

export interface Mobile {
  countryCode: string;
  mobileNumber: string;
}

interface AnyObj {
  [prop: string]: any;
}

// legacy
export interface UserDetailsIOS {
  FirstName: string;
  LastName: string;
  Email: string;
  dob: string;
  uuid: string;
  profileType: ProfileType;
  gender: string;
  imageUrl: string;
  loggedIn: boolean;
  loginChannel: string;
  mmtAuth: string;
  phoneContacts: string;
  verifiedNumbers: string;
  countryCode: string;
  Phone: string;
  organizationId: string | null;
}

// legacy
export interface UserDetailsAndroid {
  firstName: string;
  lastName: string;
  email: string;
  imageUrl: string;
  gender: string;
  mmtAuth: string;
  loggedIn: string;
  loginType: string;
  mobile: Mobile;
  verifiedMobileNumber: Mobile[];
  profileType: string;
  uuid: string;
  organizationId: string;
}

// ==========================================
export interface UserDetailsRaw {
  corpData?: AnyObj | null; // get data type
  cupAge: number;
  cupCrdt: number;
  cupEmailid: string;
  cupFname: string;
  cupGender: string;
  cupLandline: string;
  cupLname: string;
  cupPrCty: string;
  cupProfileType: ProfileType;
  cupUpdt: number;
  emailVerified: boolean;
  imageUrl: string;
  loggedIn: boolean;
  loginType: string; // get enum
  miscFields: AnyObj;
  mmtAuth: string;
  mmtAuthInHeaderFormat: string;
  mobileContactNumberList: Mobile[];
  travellerDocuments: any[]; // get data type
  uuid: string;
  verifiedMobileNumber: Mobile[];
}

export interface LoggedInUserDetails {
  createdAt: Date | null;
  email: string;
  firstName: string;
  fullName: string;
  gender: string;
  imageUrl: string;
  isEmailVerified: boolean;
  isLoggedIn: boolean;
  lastName: string;
  loginType: string;
  mmtAuth: string;
  mobileContactNumberList: Mobile[];
  mobileNumber: Mobile;
  organizationId: string | null;
  profileType: ProfileType;
  uuid: string;
  verifiedMobileNumbers: Mobile[];
}

export interface IUser {
  /**
   * Checks if a user is currently logged in
   * @returns Promise resolving to user login status
   */
  isUserLoggedIn(): Promise<boolean>;

  /**
   * Retrieves the MMT authentication token for the current user
   * @returns Promise resolving to the auth token or null if not available
   */
  getMmtAuth(): Promise<string | null>;

  /**
   * Gets the complete profile details of the logged-in user
   * @returns Promise resolving to user details or null if not available/logged out
   */
  getLoggedInUserDetails(): Promise<LoggedInUserDetails | null>;
}
