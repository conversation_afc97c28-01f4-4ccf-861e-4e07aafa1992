export const config = {
  askFeedbackMessage: 'was this helpful',
  positiveFeedbackMessage: 'Thank you for your response!',
  negativeFeedbackMessage: 'Thank you for your response!',
  askForFeedbackTimeout: 1500,
  showFeedbackMessageTimeout: 500,
  hideFeedbackMessageTimeout: 1500,
  heartBeatIntervalInMs: 30000,
  heartBeatTimeoutInMs: 5000,
  reconnectAttemptsLimit: 3,
  socketGcTime: 60_000,
  apolloApiTimeout: 5000,
  defaultChatExpertErrorMessages:
    'Sorry! This is currently unavailable. Please try again in sometime.',
  pageNotFoundTitle: 'Page not found',
  pageNotFoundSubTitle: 'We can’t seem to find the page\nyou’re looking for.',
  somethingWentWrongText: 'Something went wrong.',
  tryAgainText: 'Please try again after some time.',
  tryAgainTextErrorBoundary: 'Please try again after some time',
  defaultErrorMessage: 'Something went wrong. Please try again later.',
  defaultScheduleCallbackPageTitle:
    'Please select a suitable time for us to call you',
  defaultScheduleCallbackCTATitle: 'CONFIRM',
  disableUserInputOnLoginTimeout: 30000,
  defaultTalkToExpertPageTitle: 'Connect with an Expert',
  recentChatListTitle: 'Recent Conversations',
  chatListScreenTitle: 'Chat History',
  chatListScreenNoChatFoundTitle: 'No conversations Found',
  chatListScreenNoChatFoundSubTitle: 'Start a new Conversation from below',
  chatListScreenNewChatCta: 'Chat With Myra',
  defaultBottomWarningMsg: 'Uh Oh! Connection lost with Myra',
  deafultBottomErrorMsg:
    'Sorry! This is currently unavailable. Please try again in some time.',
  connectMeToAgentText: 'connect to agent',
  callAnAgentText: 'I want to call an agent',
  scheduleCallbackText: 'Schedule a Callback',
  sessionTimeInMs: 2 * 60 * 1000, // 4 hours
  conversationGroupsTitle: 'Chat with Myra',
  agentConversationGroupsTitle: 'MMT Support',
  startNewChatText: 'Start a new chat',
  itineraryMessageText: 'I want to go ahead with the itinerary',
  itineraryReplyMessageText: 'Please provide more details',
  preferenceSetMessage: 'Your preference has been set to ',
};
