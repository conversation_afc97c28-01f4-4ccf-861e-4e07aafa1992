export const HOLIDAY_TRACKING_CONSTANTS = {
  // Base tracking parameters
  MV15_BASE: 'home_page|travelplex',
  MV15_CHAT_DESTINATION: 'Holidays_Ch_dest | travelplex',

  // Event types
  EVENT_NAME: 'button-clicked',
  EVENT_TYPE: 'action',

  // Media actions
  MEDIA_ACTIONS: {
    SHOWN: 'media_shown',
    CLICKED: 'media_clicked',
  },

  // Property 14 values
  PROP14_VALUES: {
    CALL: 'call',
    CHAT_WITH_EXPERT: 'chat_with_expert',
    MEDIA_QUERY: 'media_query',
  },

  // Event values
  EVENT_VALUES: {
    MYRA_CALL: 'myra_call',
    MYRA_CHAT: 'myra_chat',
    MYRA_QUERY: 'myra_query',
    MYRA_CHAT_SUBMIT: 'myra_chat_submit',
    MYRA_CHAT_PHONE: 'myra_chat_phone',
  },

  // Media prefixes
  MEDIA_PREFIX: 'media_',
  <PERSON><PERSON><PERSON>_PREFIX: 'myra_',

  // Callback type mapping for tracking
  CALLBACK_TYPE_TRACKING: {
    SCHEDULE_CALLBACK: 'Q',   // Query/Form card
    CHAT_CARD_WITH_FORM: 'Ch', // Chat functionality
    CALL_US: 'C'              // Call functionality
  }
} as const;

// Type for tracking parameters
export type HolidayTrackingParams = {
  mv15?: string;
  m_c54?: string;
  prop14?: string;
  eventName?: string;
  eventType?: string;
  eventValue?: string;
};

// Enum for common tracking scenarios
export enum TrackingScenario {
  MEDIA_SHOWN = 'media_shown',
  MEDIA_CLICKED = 'media_clicked',
  CALL_CLICKED = 'call_clicked',
  CHAT_CLICKED = 'chat_clicked',
  QUERY_CLICKED = 'query_clicked',
  DESTINATION_SHOWN = 'destination_shown',
  DESTINATION_SELECTED = 'destination_selected',
  DESTINATION_CHANGE = 'destination_change',
  PHONE_CLICKED = 'phone_clicked',
  SUBMIT_CLICKED = 'submit_clicked',
}

export enum CLICK_TYPE {
  AUTO = ' | auto',
  MENU = ' | menu',
}
