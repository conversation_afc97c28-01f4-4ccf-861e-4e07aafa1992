import { Platform, Vibration } from 'react-native';

// Success pattern: A quick, gentle vibration
export const SUCCESS_VIBRATION = Platform.select({
  ios: 50, // 50ms vibration
  android: 50, // 50ms vibration
});

// Error pattern: Two short vibrations to indicate failure
export const ERROR_VIBRATION = Platform.select({
  ios: 100, // Longer vibration for iOS since it can't do patterns
  android: 50, // Default to 50ms for Android
});

export const triggerVibration = (type?: 'success' | 'error') => {
  const vibrationType = type || 'success';
  const pattern = vibrationType === 'success' ? SUCCESS_VIBRATION : ERROR_VIBRATION;
  if (pattern) {
    if (Platform.OS === 'android' && vibrationType === 'error') {
      Vibration.vibrate([50, 100, 50]); // Custom pattern for Android error
    } else {
      Vibration.vibrate(pattern);
    }
  }
};
