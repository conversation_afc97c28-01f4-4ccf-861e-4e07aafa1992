#!/bin/bash

# Function to watch and copy files for mmt
local_dev() {
  TARGET_PROJECT=$1
  echo "Watching and copying files to $TARGET_PROJECT"
  rm -rf "$TARGET_PROJECT/node_modules/@ptui/chatbot-ui/dist/"
  mkdir -p "$TARGET_PROJECT/node_modules/@ptui/chatbot-ui/dist/"
  cp package.json "$TARGET_PROJECT/node_modules/@ptui/chatbot-ui/"
  cp -R src/assets "$TARGET_PROJECT/node_modules/@ptui/chatbot-ui/dist/assets"
  tsc -p tsconfig.json -w --outDir "$TARGET_PROJECT/node_modules/@ptui/chatbot-ui/dist/"
}


# Check the first argument to determine which function to call
if [ "$1" == "android" ]; then
  local_dev "$MMT_RN_DIR"
elif [ "$1" == "ios" ]; then
  local_dev "$MMT_IOS_RN_DIR"
elif [ "$1" == "web" ]; then
  WED_DIR='../..'
  local_dev "$WED_DIR"
else
  echo "Usage: $0 {mmt|mmt-ios} <directory>"
  exit 1
fi