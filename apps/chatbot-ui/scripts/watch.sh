#!/usr/bin/env sh

echo "Number of arguments passed: $#"

echo "Project: $1"

PKG_NAME="@travelplex/react-native"
TARGET=$1

echo "target=TARGET"
echo $TARGET/node_modules/$PKG_NAME/dist/

# cleanup
rm -rf $TARGET/node_modules/$PKG_NAME/dist/
mkdir -p $TARGET/node_modules/$PKG_NAME/dist/

# copy files
cp package.json $TARGET/node_modules/$PKG_NAME/
cp -R src/assets $TARGET/node_modules/$PKG_NAME/dist/assets

# start tsc in watch mode
../../node_modules/.bin/tsc -p tsconfig.json -w --outDir $TARGET/node_modules/$PKG_NAME/dist/

