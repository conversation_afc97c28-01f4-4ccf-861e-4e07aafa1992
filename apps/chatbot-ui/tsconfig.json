{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "jsx": "preserve",
    "moduleSuffixes": [
      ".ios",
      ".android",
      ""
    ],
    "noEmit": false,
    "baseUrl": "./",
    "paths": {
      "*": [
        "types/*"
      ]
    }
  },
  "include": [
    "src/**/*",
    /* Include all TypeScript and JavaScript files in src */
    "types/declarations.d.ts"
    /* Include the declarations file */
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}
