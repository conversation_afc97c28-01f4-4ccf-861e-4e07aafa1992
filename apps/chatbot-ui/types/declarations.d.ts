// declarations.d.ts
declare module '*.png' {
  const value: string;
  export default value;
}

declare module '*.jpg' {
  const value: string;
  export default value;
}
declare module '*.webp' {
  const value: string;
  export default value;
}

// Add other asset types if needed

declare module '@react-native-community/masked-view' {
  import * as React from 'react';
  import { ViewProps } from 'react-native';

  export interface MaskedViewProps extends ViewProps {
    maskElement: React.ReactElement;
  }

  export default class MaskedView extends React.Component<MaskedViewProps> {}
}

declare module '@mmt/event-logger' {
  export type DeviceInfo = any;
  export const getDeviceInfo;
  export const initAndGetAnalyticsSdk;
}

declare module 'react-native-slider' {
  export interface SliderProps {
    style?: any;
    minimumValue?: number;
    maximumValue?: number;
    value?: number;
    onValueChange?: (value: number) => void;
    onSlidingStart?: () => void;
    onSlidingComplete?: (value: number) => void;
    minimumTrackTintColor?: string;
    maximumTrackTintColor?: string;
    thumbStyle?: any;
    trackStyle?: any;
    disabled?: boolean;
    animateTransitions?: boolean;
    animationType?: 'spring' | 'timing';
    orientation?: 'horizontal' | 'vertical';
    thumbTouchSize?: { width: number; height: number };
    debugTouchArea?: boolean;
    step?: number;
  }

  export default class Slider extends React.Component<SliderProps> {}
}
