module.exports = {
  extends: ['@react-native'],
  parserOptions: {
    tsconfigRootDir: __dirname,
    project: './tsconfig.json',
  },
  plugins: ['react-native', 'jest', 'unused-imports'],
  env: {
    'jest/globals': true,
    'react-native/react-native': true,
  },
  rules: {
    'no-console': 'error',
    'unused-imports/no-unused-imports': 'error',
    'unused-imports/no-unused-vars': 'error',
    // check the below rule later
    'import/no-unresolved': 'off',
    /*Refer rules here:
    https://github.com/Intellicode/eslint-plugin-react-native/tree/master/docs/rules
    react-native eslint
    */
    'react-native/no-unused-styles': 'error',
    'react-native/no-inline-styles': 'warn',
    'react-native/no-color-literals': 'error',
    'react-native/no-single-element-style-arrays': 'error',
    'react-native/no-raw-text': 'off',
    'promise/catch-or-return': ['error', { allowFinally: true, allowThen: true }],
  },
  globals: {},
  settings: {
    react: {
      version: '18', // instead of 'detect'
    },
    /*ignoring because of a known issue
    https://github.com/facebook/react-native/issues/28549 */
    'import/ignore': ['node_modules/react-native/index\\.js$'],
    'import/resolver': {
      node: {
        extensions: [
          '.js',
          '.jsx',
          '.ts',
          '.tsx',
          '.android.js',
          '.android.jsx',
          '.android.ts',
          '.android.tsx',
          '.ios.js',
          '.ios.jsx',
          '.ios.ts',
          '.ios.tsx',
        ],
      },
    },
  },
};
